### 查询AI对话记录 - POST方式
POST {{host}}/sys/policy/ai-conversation/records
Content-Type: application/json
Authorization: {{token}}

{
  "page": 1,
  "pageSize": 10,
  "keyword": "意外险",
  "userName": "唐钰惠",
  "startTime": "2025-07-10",
  "endTime": "2025-07-19"
}

### 查询AI对话记录 - GET方式
GET {{host}}/sys/policy/ai-conversation/records?page=1&pageSize=10&keyword=意外险&userName=唐钰惠&startTime=2025-07-10&endTime=2025-07-19
Authorization: {{token}}

### 查询AI对话记录 - 最小参数
POST {{host}}/sys/policy/ai-conversation/records
Content-Type: application/json
Authorization: {{token}}

{
  "page": 1,
  "pageSize": 10
}

### 查询AI对话记录 - 参数验证测试（缺少必填参数）
POST {{host}}/sys/policy/ai-conversation/records
Content-Type: application/json
Authorization: {{token}}

{
  "pageSize": 10
}
