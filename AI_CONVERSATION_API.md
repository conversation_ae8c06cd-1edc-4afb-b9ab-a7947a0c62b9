# AI对话记录接口文档

## 概述
本接口用于透传调用insurance-bff-project服务的AI聊天对话记录查询功能，位于policy模块下。

## 接口信息

### 1. 查询AI对话记录 (POST)
- **URL**: `/api/sys/policy/ai-conversation/records`
- **方法**: POST
- **权限**: `policy:ai:conversation:query`

#### 请求参数
```json
{
  "page": 1,           // 必填，页码，最小值1
  "pageSize": 10,      // 必填，每页大小，最小值1
  "keyword": "意外险",  // 可选，关键词
  "userName": "唐钰惠", // 可选，用户名
  "startTime": "2025-07-10", // 可选，开始时间
  "endTime": "2025-07-19"    // 可选，结束时间
}
```

#### 响应结果
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "records": [
      {
        "id": 1,
        "userName": "唐钰惠",
        "userQuestion": "什么是意外险？",
        "aiAnswer": "意外险是...",
        "conversationTime": "2025-07-10 10:30:00",
        "sessionId": "session123",
        "userId": "user456",
        "questionType": "产品咨询",
        "satisfactionScore": 5,
        "createTime": "2025-07-10 10:30:00",
        "updateTime": "2025-07-10 10:30:00"
      }
    ]
  }
}
```

### 2. 查询AI对话记录 (GET)
- **URL**: `/api/sys/policy/ai-conversation/records`
- **方法**: GET
- **权限**: `policy:ai:conversation:query`

#### 请求参数（URL参数）
- `page`: 页码（必填，默认1）
- `pageSize`: 每页大小（必填，默认10）
- `keyword`: 关键词（可选）
- `userName`: 用户名（可选）
- `startTime`: 开始时间（可选）
- `endTime`: 结束时间（可选）

#### 示例URL
```
GET /api/sys/policy/ai-conversation/records?page=1&pageSize=10&keyword=意外险&userName=唐钰惠&startTime=2025-07-10&endTime=2025-07-19
```

## 配置说明

### 环境配置
在各环境的bootstrap配置文件中已添加insurance-bff-project服务的URL配置：

- **开发环境** (bootstrap-dev.yml): `http://localhost:8080`
- **测试环境** (bootstrap-test.yml): `http://insurance-bff-project.tsg.cfpamf.com`
- **生产环境** (bootstrap-prod.yml): `http://insurance-bff-project.osg.cfpamf.com`
- **预生产环境** (bootstrap-pro.yml): `http://insurance-bff-project.osg.cfpamf.com`
- **VPN环境** (bootstrap-vpn.yml): `http://insurance-bff-project.tsg.cfpamf.com`

### 服务降级
当insurance-bff-project服务不可用时，会触发Hystrix降级机制，返回友好的错误信息。

## 文件结构

```
server/src/main/java/com/mpolicy/manage/modules/policy/
├── client/
│   ├── InsuranceBffClient.java              # Feign客户端接口
│   └── InsuranceBffClientFallback.java      # 服务降级处理
├── controller/
│   └── AiConversationController.java        # 控制器
├── service/
│   ├── AiConversationService.java           # 服务接口
│   └── impl/
│       └── AiConversationServiceImpl.java   # 服务实现
└── vo/ai/
    ├── ConversationRecordsQueryVo.java      # 查询请求参数
    ├── ConversationRecordVo.java            # 对话记录信息
    └── ConversationRecordsResponseVo.java   # 响应结果
```

## 测试
可以使用 `api-test/ai-conversation-records.http` 文件进行接口测试。

## 注意事项
1. 接口需要相应的权限才能访问
2. 必填参数不能为空，否则会返回参数校验错误
3. 服务异常时会触发降级机制
4. 所有请求和响应都会记录日志便于排查问题
