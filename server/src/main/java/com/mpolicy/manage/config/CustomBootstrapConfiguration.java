//package com.mpolicy.manage.config;
//
///**
// * <AUTHOR>
// * @Date 2024/9/10 17:33
// * @Version 1.0
// */
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @Date 2023/2/7 16:26
// * @Version 1.0
// */
//@Configuration
//public class CustomBootstrapConfiguration {
//    @Bean("customPropertySourceLocator")
//    public CustomPropertySourceLocator propertySourceLocator() {
//        return new CustomPropertySourceLocator();
//    }
//}
//
