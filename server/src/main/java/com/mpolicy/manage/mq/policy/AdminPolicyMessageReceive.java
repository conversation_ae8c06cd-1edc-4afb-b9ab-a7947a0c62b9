package com.mpolicy.manage.mq.policy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.manage.modules.sys.service.SystemBusinessFileManageService;
import com.mpolicy.manage.mq.policy.enums.PolicyMqOpeEnum;
import com.mpolicy.manage.mq.policy.service.PolicyEventService;
import com.mpolicy.service.common.woodpecker.MonitorWoodpeckerHelper;
import com.mpolicy.service.common.woodpecker.dto.MonitorWoodpeckerData;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import com.mpolicy.manage.config.RabbitConfig;

import java.io.IOException;
import java.util.Map;

/**
 * 渠道api 保单消息-消费者服务处理中心
 *
 * <AUTHOR>
 * @since 2022/03/09
 */
@Slf4j
@Component
@Profile({"test", "pro", "prod"})
public class AdminPolicyMessageReceive {

    @Autowired
    private PolicyEventService policyEventService;

    /**
     * <p>
     * exchange = policy_topic
     * routeKey = policy.global.#
     * </p>
     *
     * @param message 消息内容
     * @param headers headers信息
     * @param channel channel信息
     * <AUTHOR>
     * @since 2022/03/09
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitConfig.ADMIN_POLICY_GLOBAL_QUEUE)
    public void policyMessage(@Payload MQMessage msg, @Headers Map<String, Object> headers, Channel channel, Message message) throws IOException {
        // 消费者消息
        log.info("接受保单中心的消息，内容 = {},headers = {}", JSON.toJSONString(msg), headers.toString());

        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        // 操作客户 + 操作类型 + 消息体
        String opeType = msg.getOpeType();
        JSONObject data = msg.getData();
        try {
            // 获取操作类型枚举
            PolicyMqOpeEnum opeEnum = PolicyMqOpeEnum.deCode(opeType);
            if (opeEnum != null) {
                switch (opeEnum) {
                    case POLICY_INFO_CHANGE: {
                        log.info("接受到保单变更消息...");
                        policyEventService.policyChange(msg.getCode());
                        break;
                    }
                    case PRESERVE_SURRENDER: {
                        log.info("接受到保单保司保全退保变更消息...");
                        policyEventService.preserveSurrender(msg.getCode(), data);
                        break;
                    }
                    case PRESERVE_APPLY: {
                        log.info("接受到保单保司保全申请消息...");
                        policyEventService.preserveApply(msg.getCode(), data);
                        break;
                    }
                    case POLICY_FILE_IMPORT_RESULT: {
                        log.info("接受保单文件导入结果通知消息...");
                        policyEventService.policyFileImportEvent(msg.getCode(),data);
                        break;
                    }
                    default: {
                        log.warn("该类型消息无需处理.");
                    }
                }
            } else {
                log.warn("基本消息，收到为止操作类型，无法进行消息处理，操作类型={}", opeType);
            }
            // 手工确认签收消息 ACK
            channel.basicAck(deliveryTag, false);
        } catch (GlobalException g) {
            log.warn("获取到自定义异常警告,消息为={}", g);
            // 手工确认签收消息 ACK
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.warn("保全数据处理失败",e);
            if (message.getMessageProperties().getRedelivered()) {
                log.warn("消息已重复处理失败,拒绝再次接收...",e);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                log.warn("消息即将再次返回队列处理...",e);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            }
        }
    }
}
