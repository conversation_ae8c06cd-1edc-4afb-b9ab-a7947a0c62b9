package com.mpolicy.manage.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SellProductConfigEnum {
    FOR_THE_CROWD("适用人群", "FOR_THE_CROWD", ""),
    SELL_PRODUCT_AREA("售卖地区", "SELL_PRODUCT_AREA", ""),
    PRODUCT_PROHIBIT_BRANCH("产品禁销分支", "PRODUCT_PROHIBIT_BRANCH", ""),
    RURAL_SHOW_AREA("农保显示地区", "RURAL_SHOW_AREA", ""),
    SELL_PRODUCT_KEYWORD("售卖产品关键词", "SELL_PRODUCT_KEYWORD", ""),
    CPJS("产品介绍", "CPJS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f23bd287f24947bd9328e3456cf5ed00.png"),
    CPTK("产品条款", "CPTK", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/4269f3cc3e364465875edc0fd7cfc822.png"),
    TBTS("投保提示", "TBTS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/28a98e8f950d46f89a81aba62c0034a9.png"),
    CJWT("常见问题", "CJWT", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/83943f42f33446ffa06f2429002ed704.png"),
    LPZY("理赔指引", "LPZY", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/dcf5fc530db944b7ad3ac1a9c1424fd3.png"),
    KHXZ("客户须知", "KHXZ", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/54303d3aa39748458c49f82c2c6b6c87.png"),
    TBAL("投保案例", "TBAL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/5697aff854254401a65edde5ec9c5c84.png"),
    XSGL("销售攻略", "XSGL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/1d59bce8f5bb4dee880979da5b2e38b0.png"),
    ZYFL("职业分类表", "ZYFL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/bd04bd37123e4fdd880f91f42b414cb0.png"),
    PXZL("培训资料", "PXZL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f9ba96bba96b46ecbcfc37784c239330.png"),
    JKGZ("健康告知", "JKGZ", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/87e0d99500834f9baa5c6cad8fd9382e.png"),
    GSJS("公司介绍", "GSJS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/8dea6c1dadd94cb09ba18e38979f0b2c.png"),
    JHS("计划书", "JHS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/3eb682774d4e4718abf65b2dee9f0970.png"),
    BFSS("保费试算", "BFSS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/6659251282bf4bfb9e1da9b72865bd88.png"),
    YJFL("佣金费率", "YJFL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/769bc4ca1aed403682bcf9f6c1cb9d37.png"),
    TBLC("TBLC", "TBLC", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/SELL/product-tool/tblc.png"),
    CPHB("产品海报", "CPHB", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    BZZR("保障责任", "BZZR", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    CPTS("产品特色", "CPTS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    CPZL("产品资料", "CPZL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    CPDB("产品对比", "CPDB", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    LYYS("利益演示", "LYYS", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    BQGZ("保全规则", "BQGZ", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    BSFWZL("保司服务资料", "BSFWZL", "https://xjxhserver.oss-cn-beijing.aliyuncs.com/20210323/f5c463386165454baf738c890767b509.png"),
    NULL("未知类型", "NULL", ""),
    APPLY_INSURANCE_MUST_KNOW("投保必知", "AIMK", ""),
    ;
    private String label;

    private String configType;

    private String icon;

    SellProductConfigEnum(String label, String configType, String icon) {
        this.label = label;
        this.configType = configType;
        this.icon = icon;
    }

    public static SellProductConfigEnum matchSearchCode(String configType) {
        for (SellProductConfigEnum searchEnum : SellProductConfigEnum.values()) {
            if (searchEnum.configType.equals(configType)) {
                return searchEnum;
            }
        }
        return SellProductConfigEnum.NULL;
    }
}
