package com.mpolicy.manage.modules.policy.vo.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "险种变更", description = "险种变更")
public class PolicyInsuredProductVo implements Serializable {

    @ApiModelProperty(value = "主键Id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "序列号", example = "1")
    private Integer serialNo;

    @ApiModelProperty("合同编号")
    private String contractCode;

    @ApiModelProperty("被保人编码 系统唯一")
    private String insuredCode;

    @ApiModelProperty("被保人姓名")
    private String insuredName;

    @ApiModelProperty("被保人身份证")
    private String insuredIdCard;

    @ApiModelProperty("保单险种编码,保单中心唯一")
    private String policyProductCode;

    @ApiModelProperty("险种编码")
    private String productCode;

    @ApiModelProperty("险种名称")
    private String productName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("计划编码")
    private String planName;

    @ApiModelProperty("协议险种名称")
    private String protocolProductName;

    @ApiModelProperty("险种状态")
    private String productStatus;

    @ApiModelProperty("险种状态")
    private String prodTypeCode;

    @ApiModelProperty("是否主险")
    private String mainInsurance;

    @ApiModelProperty("附加险类型 0-其他类型附加险 1-附加投保人豁免")
    private Integer additionalRisksType;

    @ApiModelProperty("保额")
    private BigDecimal coverage;

    @ApiModelProperty("保额单位")
    private Integer coverageUnit;

    @ApiModelProperty("险种生效日期")
    private Date effectiveDate;

    @ApiModelProperty("险种截止时间")
    private Date endDate;

    @ApiModelProperty("退保时间")
    private Date surrenderTime;

    @ApiModelProperty("退保金额")
    private BigDecimal surrenderAmount;

    @ApiModelProperty("保障期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保障时长")
    private Integer insuredPeriod;

    @ApiModelProperty("缴费方式-年交/半年交/季交/月交/趸交/不定期交/短险一次交清")
    private String periodType;

    @ApiModelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ApiModelProperty("缴费时长")
    private Integer paymentPeriod;

    @ApiModelProperty("年金领取年龄")
    private String annDrawAge;

    @ApiModelProperty("受益人类型 1:法定 0：非法定")
    private Integer beneficiaryType;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("免赔额")
    private String deductible;

    @ApiModelProperty("按份数给付时的份数")
    private Integer copies;

    @ApiModelProperty("备注")
    private String remark;
}
