package com.mpolicy.manage.modules.sell.controller;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.common.utils.ExcelUtil;
import com.mpolicy.manage.modules.insurance.vo.CompanyItem;
import com.mpolicy.manage.modules.sell.entity.SellProductRenewalConfigEntity;
import com.mpolicy.manage.modules.sell.enums.RenewalChannelEnum;
import com.mpolicy.manage.modules.sell.enums.RenewalTypeEnum;
import com.mpolicy.manage.modules.sell.service.SellProductRenewalConfigService;
import com.mpolicy.manage.modules.sell.service.SellProductRenewalRecommendService;
import com.mpolicy.manage.modules.sell.vo.*;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.util.*;


/**
 * 商品续购配置表
 *
 * <AUTHOR>
 * @date 2022-11-09 15:29:28
 */
@RestController
@RequestMapping("sell/renewal_config")
@Api(tags = "商品续购配置表")
public class SellProductRenewalConfigController {


    @Autowired
    private SellProductRenewalConfigService sellProductRenewalConfigService;

    @Autowired
    private SellProductRenewalRecommendService sellProductRenewalRecommendService;

    /**
     * <p>
     * 【搜索】获取已完成续保配置的保司集合
     * </p>
     *
     * @param renewalChannel 续购渠道 XIAOWHALE/ZHNX
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.manage.modules.insurance.vo.CompanyInfoResp>>
     * <AUTHOR>
     * @since 2021/3/23
     */
    @ApiOperation(value = "搜索-保司集合", notes = "搜索-保司集合")
    @GetMapping("/query/company_list")
    public Result<List<CompanyItem>> queryRenewalConfigFinishCompanyList(@ApiParam(name = "renewalChannel", value = "续购渠道 XIAOWHALE/ZHNX") @RequestParam(value = "renewalChannel", required = false) String renewalChannel) {
        RenewalChannelEnum channel = Optional.ofNullable(
                RenewalChannelEnum.decode(renewalChannel)
        ).orElse(null);
        List<CompanyItem> list = sellProductRenewalConfigService.queryRenewalConfigFinishCompanyList(channel);
        return Result.success(list);
    }

    /**
     * <p>
     * 【搜索】续保产品信息获取
     * </p>
     *
     * @param type           产品类型 1原产品 2续购产品
     * @param renewalChannel 续购渠道 XIAOWHALE/ZHNX
     * @param companyCode    保险公司编码
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.manage.modules.sell.vo.RenewalProductItem>>
     * <AUTHOR>
     * @since 2022/11/9
     */
    @ApiOperation(value = "搜索-产品信息", notes = "搜索-产品信息")
    @GetMapping("/query/product_list/{type}")
    public Result<List<RenewalProductItem>> renewalConfigProductList(@ApiParam(name = "type", value = "产品类型 1原产品 2续购产品", required = true) @PathVariable("type") Integer type,
                                                                     @ApiParam(name = "renewalChannel", value = "续购渠道 XIAOWHALE/ZHNX") @RequestParam(value = "renewalChannel", required = false) String renewalChannel,
                                                                     @ApiParam(name = "companyCode", value = "保险公司编码") @RequestParam(value = "companyCode", required = false) String companyCode) {
        RenewalChannelEnum channel = Optional.ofNullable(
                RenewalChannelEnum.decode(renewalChannel)
        ).orElse(null);
        List<RenewalProductItem> list = sellProductRenewalConfigService.queryRenewalConfigFinishProductList(channel, companyCode, type);
        return Result.success(list);
    }


    /**
     * <p>
     * 根据【渠道】获取可配置续购的保险公司集合
     * </p>
     *
     * @param renewalChannel 渠道信息
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.manage.modules.insurance.vo.CompanyItem>>
     * <AUTHOR>
     * @since 2022/11/9
     */
    @ApiOperation(value = "渠道获取可配置的保险公司集合", notes = "渠道获取可配置的保险公司集合")
    @GetMapping("/company_list/{renewalChannel}")
    public Result<List<CompanyItem>> renewalConfigCompanyList(@ApiParam(name = "renewalChannel", value = "续购渠道 XIAOWHALE/ZHNX", required = true) @PathVariable("renewalChannel") String renewalChannel) {
        RenewalChannelEnum channel = Optional.ofNullable(
                RenewalChannelEnum.decode(renewalChannel)
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购配置渠道类型错误")));
        List<CompanyItem> list = sellProductRenewalConfigService.queryRenewalConfigCompanyList(channel);
        return Result.success(list);
    }

    /**
     * <p>
     * 根据 【续购渠道信息+保司编码】 获取可配置产品列表获取
     * </p>
     *
     * @param renewalChannel 续购渠道信息
     * @param companyCode    保司编码
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.manage.modules.sell.vo.RenewalProductItem>>
     * <AUTHOR>
     * @since 2022/11/9
     */
    @ApiOperation(value = "可配置产品列表获取", notes = "可配置产品列表获取")
    @GetMapping("/source_product_list/{renewalChannel}/{companyCode}")
    public Result<List<RenewalProductItem>> renewalConfigProductList(@ApiParam(name = "renewalChannel", value = "续购渠道 XIAOWHALE/ZHNX", required = true) @PathVariable("renewalChannel") String renewalChannel,
                                                                     @ApiParam(name = "companyCode", value = "产品类型 1原产品 2续购产品", required = true) @PathVariable("companyCode") String companyCode) {
        RenewalChannelEnum channel = Optional.ofNullable(
                RenewalChannelEnum.decode(renewalChannel)
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购配置渠道类型错误")));
        List<RenewalProductItem> list = sellProductRenewalConfigService.queryRenewalConfigProductList(channel, companyCode);
        return Result.success(list);
    }

    /**
     * <p>
     * 续保配置列表分页查询
     * </p>
     */
    @ApiOperation(value = "续保配置列表分页查询", notes = "续保配置列表分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "renewalChannel", dataType = "String", value = "续购类型"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码"),
            @ApiImplicitParam(paramType = "query", name = "sourceProductCode", dataType = "String", value = "产品编码"),
            @ApiImplicitParam(paramType = "query", name = "renewalProductCode", dataType = "String", value = "续购产品编码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<PageUtils<RenewalProductConfigBase>> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils<RenewalProductConfigBase> page = sellProductRenewalConfigService.queryRenewalConfigPage(params);
        return Result.success(page);
    }

    @ApiOperation(value = "续保配置列表分页查询", notes = "续保配置列表分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "renewalChannel", dataType = "String", value = "续购类型"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码"),
            @ApiImplicitParam(paramType = "query", name = "sourceProductCode", dataType = "String", value = "产品编码"),
            @ApiImplicitParam(paramType = "query", name = "renewalProductCode", dataType = "String", value = "续购产品编码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions(value = {"renewalConfigList:export"})
    public void export(HttpServletResponse response, @RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {

        List<RenewalProductConfigExcel> excels = sellProductRenewalConfigService.queryRenewalConfigExport(params);
        ExcelUtil.writeExcel(response, excels, "续保配置列表");
    }

    /**
     * 续保配置详情
     */
    @ApiOperation(value = "续保配置详情", notes = "续保配置详情")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<RenewalProductConfigInfo> info(@PathVariable("id") Integer id) {
        SellProductRenewalConfigEntity insureBlacklistInfo = Optional.ofNullable(sellProductRenewalConfigService.getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("续保配置信息不存在")));
        RenewalProductConfigInfo result = new RenewalProductConfigInfo();
        BeanUtils.copyProperties(insureBlacklistInfo, result);
        // 类型枚举转换
        Optional.ofNullable(RenewalChannelEnum.decode(insureBlacklistInfo.getRenewalChannel())).ifPresent(t -> {
            result.setRenewalChannelName(t.getName());
        });
        Optional.ofNullable(RenewalTypeEnum.decode(insureBlacklistInfo.getRenewalType())).ifPresent(t -> {
            result.setRenewalTypeName(t.getName());
        });
        return Result.success(result);
    }

    /**
     * 保存续保配置
     */
    @ApiOperation(value = "保存续保配置", notes = "保存续保配置")
    @SysDbLog("保存续保配置")
    @PostMapping("/save")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<String> save(@RequestBody RenewalProductConfigInfo info) {
        //数据校验
        ValidatorUtils.validateEntity(info, Default.class);

        RenewalChannelEnum renewalChannel = Optional.ofNullable(
                RenewalChannelEnum.decode(info.getRenewalChannel())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购配置渠道类型错误")));

        info.setRenewalChannelName(renewalChannel.getName());
        sellProductRenewalConfigService.saveRenewalConfig(info);
        return Result.success();
    }

    /**
     * 修改续保配置
     */
    @ApiOperation(value = "修改续保配置", notes = "修改续保配置")
    @SysDbLog("修改续保配置")
    @PostMapping("/update")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<String> update(@RequestBody RenewalProductConfigInfo info) {
        //数据校验
        ValidatorUtils.validateEntity(info, Default.class, UpdateGroup.class);

        RenewalChannelEnum renewalChannel = Optional.ofNullable(
                RenewalChannelEnum.decode(info.getRenewalChannel())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购配置渠道类型错误")));

        info.setRenewalChannelName(renewalChannel.getName());
        sellProductRenewalConfigService.updateRenewalConfig(info);
        return Result.success();
    }

    /**
     *
     *
     * 删除续保配置 单个
     *
     *
     */
    @ApiOperation(value = "删除续保配置（单个）", notes = "删除续保配置（单个）")
    @SysDbLog("删除续保配置（单个）")
    @PostMapping("/delete/{id}")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<String> delete(@PathVariable Long id) {
        sellProductRenewalConfigService.removeById(id);

        return Result.success();
    }


    /**
     *
     *
     * 删除续保配置 （批量）
     *
     *
     */
    @ApiOperation(value = "删除续保配置（批量）", notes = "删除续保配置（批量）")
    @SysDbLog("删除续保配置（批量）")
    @PostMapping("/delete")
    @RequiresPermissions(value = {"product:renewal:all"})
    public Result<String> delete(@RequestBody @ApiParam(name = "ids", value = "黑名单id集合", required = true) List<Long> ids) {
        // 判读集合是否为空
        if (CollectionUtils.isEmpty(ids)) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("操作删除id集合为空"));
        }
        // 去批量删除
        sellProductRenewalConfigService.removeByIds(ids);
        return Result.success();
    }

    /**
     * <p>
     * 产品推荐列表
     * </p>
     */
    @ApiOperation(value = "产品推荐列表", notes = "产品推荐列表")
    @GetMapping("/recommend/list")
    public Result<List<RenewalProductRecommendType>> recommendList() {
        List<RenewalProductRecommendType> list = sellProductRenewalRecommendService.recommendList();
        return Result.success(list);
    }

    /**
     * 添加推荐产品
     */
    @ApiOperation(value = "添加推荐产品", notes = "添加推荐产品")
    @SysDbLog("添加推荐产品")
    @PostMapping("/recommend/save")
    public Result<String> recommendSave(@RequestBody RenewalProductRecommendInfo info) {
        //数据校验
        ValidatorUtils.validateEntity(info, Default.class);
        sellProductRenewalRecommendService.recommendSave(info);
        return Result.success();
    }

    /**
     * 删除推荐产品
     */
    @ApiOperation(value = "删除推荐产品", notes = "删除推荐产品")
    @SysDbLog("删除推荐产品")
    @GetMapping("/recommend/delete")
    public Result<String> recommendDelete(@RequestParam(value = "id") @ApiParam(name = "id", value = "id", required = true) Integer id) {
        sellProductRenewalRecommendService.removeById(id);
        return Result.success();
    }
}
