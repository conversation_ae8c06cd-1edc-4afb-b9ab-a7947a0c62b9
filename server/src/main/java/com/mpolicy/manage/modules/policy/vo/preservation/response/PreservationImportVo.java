package com.mpolicy.manage.modules.policy.vo.preservation.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 导入数据结果返回信息
 *
 * <AUTHOR>
 * @date 2023/1/9 19:22
 */
@Data
public class PreservationImportVo {

    /**
     * 导入流水号
     */
    private String flowId;

    /**
     * 总条数
     */
    private int total;

    /**
     * 成功条数
     */
    private int success;

    /**
     * 失败条数
     */
    private int failures;

    private String createUser;

    @ApiModelProperty("创建时间")
    @JSONField(format = "yyyy-mm-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-mm-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "导入异常明细",hidden = true)
    private List<ErrorTips> errorList;

    @ApiModelProperty(value = "导入文件名")
    private String fileName;

    @ApiModelProperty(value = "导入文件地址")
    private String importFileUrl;

    @ApiModelProperty(value = "导入异常明细下载地址")
    private String errorDownloadUrl;

    /**
     * 添加失败条数记录
     * @param seq 行号
     * @param policyCode 保单号
     * @param message 提示信息
     */
    public void addErrorTips(String seq, String policyCode, String message) {
        ErrorTips tips = new ErrorTips();
        tips.setSeq(seq);
        tips.setPolicyCode(policyCode);
        tips.setMessage(message);
        errorList.add(tips);
        failures++;
        total++;
    }

    /**
     * 成功条数统计
     */
    public void incrSuccess() {
        success++;
        total++;
    }

}
