package com.mpolicy.manage.modules.insure.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.dao.InsureBlacklistInfoDao;
import com.mpolicy.manage.modules.insure.entity.InsureBlacklistInfoEntity;
import com.mpolicy.manage.modules.insure.enums.InsureBlackListCertificatesEnum;
import com.mpolicy.manage.modules.insure.enums.InsureBlacklistTypeEnum;
import com.mpolicy.manage.modules.insure.service.InsureBlacklistService;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistInfo;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistList;
import com.mpolicy.web.common.utils.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


@Service("insureBlacklistInfoService")
public class InsureBlacklistServiceImpl extends ServiceImpl<InsureBlacklistInfoDao, InsureBlacklistInfoEntity> implements InsureBlacklistService {

    @Override
    public PageUtils<InsureBlacklistList> queryPage(Map<String, Object> params) {

        String blacklistType = (String) params.get("blacklistType");
        String identificationName = (String) params.get("identificationName");
        String identificationNum = (String) params.get("identificationNum");

        // 分页查询
        IPage<InsureBlacklistInfoEntity> page = this.page(
                new Query<InsureBlacklistInfoEntity>().getPage(params),
                new LambdaQueryWrapper<InsureBlacklistInfoEntity>()
                        .eq(StringUtils.isNotBlank(blacklistType), InsureBlacklistInfoEntity::getBlacklistType, blacklistType)
                        .eq(StringUtils.isNotBlank(identificationName), InsureBlacklistInfoEntity::getIdentificationName, identificationName)
                        .eq(StringUtils.isNotBlank(identificationNum), InsureBlacklistInfoEntity::getIdentificationNum, identificationNum)
                        .orderByDesc(InsureBlacklistInfoEntity::getId)
        );

        // 构建返回的vo list
        List<InsureBlacklistList> result = new ArrayList<>();
        page.getRecords().forEach(x -> {
            InsureBlacklistList bean = new InsureBlacklistList();
            BeanUtils.copyProperties(x, bean);
            // 类型枚举转换
            Optional.ofNullable(InsureBlacklistTypeEnum.decode(x.getBlacklistType())).ifPresent(t -> {
                bean.setBlacklistType(t.getCode());
                bean.setBlacklistTypeName(t.getName());
            });
            result.add(bean);
        });

        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public void saveBlacklistUser(InsureBlacklistInfo blacklistUser) {
        // 校验是否还历史存在此黑名单用户
        List<InsureBlacklistInfoEntity> list = lambdaQuery()
                .eq(InsureBlacklistInfoEntity::getBlacklistType, blacklistUser.getBlacklistType())
                .eq(InsureBlacklistInfoEntity::getIdentificationName, blacklistUser.getIdentificationName())
                .eq(InsureBlacklistInfoEntity::getIdentificationNum, blacklistUser.getIdentificationNum()).list();
        if (!list.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("已存在黑名单纪录信息，姓名={},证件号码={}", blacklistUser.getIdentificationName(), blacklistUser.getIdentificationNum())));
        }

        InsureBlacklistInfoEntity bean = new InsureBlacklistInfoEntity();
        BeanUtils.copyProperties(blacklistUser, bean);
        save(bean);
    }


    @Override
    public void updateBlacklistUser(InsureBlacklistInfo blacklistUser) {
        // 校验除了更新的黑名单信息，是否还存在其他和此黑名单用户
        List<InsureBlacklistInfoEntity> list = lambdaQuery()
                .eq(InsureBlacklistInfoEntity::getBlacklistType, blacklistUser.getBlacklistType())
                .eq(InsureBlacklistInfoEntity::getIdentificationName, blacklistUser.getIdentificationName())
                .eq(InsureBlacklistInfoEntity::getIdentificationNum, blacklistUser.getIdentificationNum())
                .ne(InsureBlacklistInfoEntity::getId,blacklistUser.getId()).list();
        if (!list.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("已存在黑名单纪录信息，姓名={},证件号码={}", blacklistUser.getIdentificationName(), blacklistUser.getIdentificationNum())));
        }

        InsureBlacklistInfoEntity bean = new InsureBlacklistInfoEntity();
        BeanUtils.copyProperties(blacklistUser, bean);
        if (!updateById(bean)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("操作更新失败，请刷新后重试"));
        }
    }

}
