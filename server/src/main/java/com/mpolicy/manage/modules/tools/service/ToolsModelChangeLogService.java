package com.mpolicy.manage.modules.tools.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.tools.entity.ToolsModelChangeLogEntity;
import com.mpolicy.manage.modules.tools.vo.ToolsModelChangeLogVo;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-06-12 11:35
 * @description: 模型变更记录Service
 */
public interface ToolsModelChangeLogService extends IService<ToolsModelChangeLogEntity> {

    /**
     * 新建模型变更记录
     *
     * @param logVo
     */
    void save(ToolsModelChangeLogVo logVo);
}
