package com.mpolicy.manage.modules.insure.controller;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.modules.insure.entity.InsureBlacklistInfoEntity;
import com.mpolicy.manage.modules.insure.enums.InsureBlacklistTypeEnum;
import com.mpolicy.manage.modules.insure.service.InsureBlacklistService;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistInfo;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistList;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;

import javax.validation.groups.Default;


/**
 * 投保黑名单信息表
 *
 * <AUTHOR>
 * @date 2022-11-08 13:58:37
 */
@RestController
@RequestMapping("insure/blacklist")
@Api(tags = "投保黑名单信息表")
public class InsureBlacklistController {

    @Autowired
    private InsureBlacklistService insureBlacklistService;


    /**
     * <p>
     * 投保黑名单列表分页查询
     * </p>
     */
    @ApiOperation(value = "投保黑名单列表分页查询", notes = "投保黑名单列表分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "blacklistType", dataType = "String", value = "黑名单类型"),
            @ApiImplicitParam(paramType = "query", name = "identificationName", dataType = "String", value = "姓名"),
            @ApiImplicitParam(paramType = "query", name = "identificationNum", dataType = "String", value = "证件号码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"customer:black:all"})
    public Result<PageUtils<InsureBlacklistList>> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils<InsureBlacklistList> page = insureBlacklistService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 黑名单详情获取
     */
    @ApiOperation(value = "黑名单详情", notes = "黑名单详情")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"customer:black:all"})
    public Result<InsureBlacklistInfo> info(@PathVariable("id") Integer id) {
        InsureBlacklistInfoEntity insureBlacklistInfo = insureBlacklistService.getById(id);
        InsureBlacklistInfo result = new InsureBlacklistInfo();
        BeanUtils.copyProperties(insureBlacklistInfo, result);
        return Result.success(result);
    }

    /**
     * 保存
     */
    @ApiOperation(value = "保存投保黑名单用户", notes = "保存投保黑名单用户")
    @SysDbLog("保存投保黑名单")
    @PostMapping("/save")
    @RequiresPermissions(value = {"customer:black:all"})
    public Result<String> save(@RequestBody InsureBlacklistInfo info) {
        //数据校验
        ValidatorUtils.validateEntity(info, Default.class);

        Optional.ofNullable(
                InsureBlacklistTypeEnum.decode(info.getBlacklistType())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("黑名单类型错误")));

        insureBlacklistService.saveBlacklistUser(info);
        return Result.success();
    }

    /**
     * 修改
     */
    @ApiOperation(value = "修改黑名单", notes = "修改黑名单")
    @SysDbLog("修改黑名单")
    @PostMapping("/update")
    @RequiresPermissions(value = {"customer:black:all"})
    public Result<String> update(@RequestBody InsureBlacklistInfo info) {
        //数据校验
        ValidatorUtils.validateEntity(info, Default.class, UpdateGroup.class);

        Optional.ofNullable(
                InsureBlacklistTypeEnum.decode(info.getBlacklistType())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("黑名单类型错误")));

        insureBlacklistService.updateBlacklistUser(info);
        return Result.success();
    }

    /**
     * 删除用户
     */
    @ApiOperation(value = "删除黑名单", notes = "删除黑名单")
    @SysDbLog("删除黑名单")
    @PostMapping("/delete")
    @RequiresPermissions(value = {"customer:black:all"})
    public Result<String> delete(@RequestBody @ApiParam(name = "ids", value = "黑名单id集合", required = true) Long[] ids) {
        if (ids.length == 0) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("操作删除id集合为空"));
        }
        insureBlacklistService.removeByIds(Arrays.asList(ids));
        return Result.success();
    }
}
