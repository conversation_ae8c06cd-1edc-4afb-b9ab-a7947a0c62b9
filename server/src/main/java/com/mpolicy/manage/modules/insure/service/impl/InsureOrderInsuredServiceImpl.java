package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderInsuredService;
import org.springframework.stereotype.Service;

/**
 * 投保订单被保人信息接口实现
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@Service("insureOrderInsuredService")
public class InsureOrderInsuredServiceImpl extends ServiceImpl<InsureOrderInsuredDao, InsureOrderInsuredEntity> implements InsureOrderInsuredService {


}
