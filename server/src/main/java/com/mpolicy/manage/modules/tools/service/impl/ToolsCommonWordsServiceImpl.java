package com.mpolicy.manage.modules.tools.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.tools.enums.SchedulePushTypeEnum;
import com.mpolicy.web.common.utils.Query;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.tools.dao.ToolsCommonWordsDao;
import com.mpolicy.manage.modules.tools.entity.ToolsCommonWordsEntity;
import com.mpolicy.manage.modules.tools.service.ToolsCommonWordsService;


@Service("toolsCommonWordsService")
public class ToolsCommonWordsServiceImpl extends ServiceImpl<ToolsCommonWordsDao, ToolsCommonWordsEntity> implements ToolsCommonWordsService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<ToolsCommonWordsEntity> page = this.page(
                new Query<ToolsCommonWordsEntity>().getPage(params),
                new LambdaQueryWrapper<ToolsCommonWordsEntity>().eq(ToolsCommonWordsEntity::getSourceCode,"system")
        );
        return new PageUtils(page);
    }

    @Override
    public boolean saveEntity(ToolsCommonWordsEntity toolsCommonWordsEntity) {
        toolsCommonWordsEntity.setCode(CommonUtils.createCode("CYY"));
        toolsCommonWordsEntity.setSourceCode("system");
        toolsCommonWordsEntity.setModuleType(SchedulePushTypeEnum.INTE_EVALUATION.getCode());
        toolsCommonWordsEntity.setContentUsage("0");
        return this.save(toolsCommonWordsEntity);
    }
}
