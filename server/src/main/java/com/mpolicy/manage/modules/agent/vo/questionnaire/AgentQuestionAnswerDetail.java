package com.mpolicy.manage.modules.agent.vo.questionnaire;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AgentQuestionAnswerDetail {

    private Integer id;
    /**
     * bl_agent_questionnaire表主键id
     */
    private Integer questionnaireId;
    /**
     * 题目
     */
    private String title;
    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    private Integer type;

    /**
     * 选项内容
     */
    private String answer;

    /**
     * 是否选中 0 未选中 1 选中
     */
    private Integer isSelected;

}
