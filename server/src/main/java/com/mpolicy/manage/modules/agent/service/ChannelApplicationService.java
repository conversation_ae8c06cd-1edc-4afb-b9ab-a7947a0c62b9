package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationEntity;

import java.util.Map;

/**
 * 渠道应用
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 19:32:39
 */
public interface ChannelApplicationService extends IService<ChannelApplicationEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveEntity(ChannelApplicationEntity channelApplicationEntity);

    void updateEntity(ChannelApplicationEntity channelApplicationEntity);

    ChannelApplicationEntity getChannelApplication(String code);

    void removeEntity(String code);

    boolean changeEnable(String code, Integer enabled, long revision);

    void refreshCache();
}

