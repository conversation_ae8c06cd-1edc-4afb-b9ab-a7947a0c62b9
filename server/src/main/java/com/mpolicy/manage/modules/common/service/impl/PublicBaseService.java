package com.mpolicy.manage.modules.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.agent.common.model.agent.AgentInfoListOut;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.utils.ResultUtil;
import com.mpolicy.open.client.CfpamfOpenApiClient;
import com.mpolicy.open.client.HRMdmOpenApiClient;
import com.mpolicy.open.client.OpenApiClient;
import com.mpolicy.open.common.cfpamf.common.OpenApiResponse;
import com.mpolicy.open.common.cfpamf.pull.BmsBranchBaseData;
import com.mpolicy.open.common.cfpamf.hr.*;
import com.mpolicy.open.common.cfpamf.trust.PolicyTrustInput;
import com.mpolicy.open.common.referrer.vo.ReferrerBaseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/5 16:07
 */
@Service
@Slf4j
public class PublicBaseService {

    @Autowired
    private OpenApiClient openApiClient;
    @Autowired
    private CfpamfOpenApiClient cfpamfOpenApiClient;
    @Autowired
    private HRMdmOpenApiClient hRMdmOpenApiClient;

    public ReferrerBaseVo queryReferrerMasterInfo(String referrerCode){

        // 获取响应报文
        Result<Map<String, ReferrerBaseVo>> data = openApiClient.queryReferrerMasterInfo(referrerCode);

        log.info("客户经理编号={}", referrerCode);
        if (!data.isSuccess() || Objects.isNull(data.getData())) {
            return null;
        }
        log.info("master信息 ={}", JSON.toJSONString(data.getData().get(referrerCode)));
        return data.getData().get(referrerCode);
    }


    public Map<String, ReferrerBaseVo> listReferrerListMasterInfo(Collection<String> referrerCodeList){
        // 获取响应报文
        List<Map<String, ReferrerBaseVo>> data = ResultUtil.getData(openApiClient.queryReferrerMasterList(Lists.newArrayList(referrerCodeList)));
        log.info("响应报文={}",JSON.toJSONString(data));
        if (Objects.isNull(data)) {
            return Collections.emptyMap();
        }
        return data.stream().flatMap(x -> x.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (t1, t2) -> t1));
    }

    /**
     * 公众号发送通知接口
     * @param userId 客户经理工号
     * @param firstData  模板的first.DATA
     * @param data1 模板的keyword1.DATA
     * @param data2 模板的keyword2.DATA
     * @param data3 模板的keyword3.DATA
     * @param remarkData 模板的remarkData
     */
    public void whaleNotice(String userId,String firstData,String data1,String data2,String data3,String remarkData){
        PolicyTrustInput input = new PolicyTrustInput();
        input.setUserId(userId);
        input.setFirstData(firstData);
        input.setData1(data1);
        input.setData2(data2);
        input.setData3(data3);
        input.setRemarkData(remarkData);
        Result<OpenApiResponse<String>> openApiResponseResult = cfpamfOpenApiClient.whaleNotice(Constant.SERVER_NAME, input);
        if(!openApiResponseResult.isSuccess()){
            log.info("公众号发送通知失败，失败原因:{}",openApiResponseResult.getMsg());
        }
    }

    /**
     * 根据机构请求对象信息获取员工集合信息
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 员工信息集合
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<EmployeeResp> getByEmployeeListByPage(EmployeeByOrgCodeRequest request, boolean throwEx) {
        Result<PageUtils<EmployeeResp>> result = hRMdmOpenApiClient.queryByOrgCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获员工信息信息失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 根据员工编码+快照信息获取员工信息
     *
     * @param request 查询条件
     * @param throwEx 是否抛出异常
     * @return 员工信息
     * <AUTHOR>
     * @since 2023/11/17 16:04
     */
    public EmployeeResp getByEmployeeCode(EmployeeByCodeRequest request, boolean throwEx) {
        Result<EmployeeResp> result = hRMdmOpenApiClient.getByEmployeeCode(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获[getByEmployeeCode]失败，员工编码={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData();
    }

    /**
     * 获取分支集合
     *
     * @param request 请求对象信息
     * @param throwEx         是否抛出异常
     * @return 分支信息
     * <AUTHOR>
     * @since 2023/11/17 16:09
     */
    public List<OrganizationResp> queryBranchInfo(OrganizationRequest request, boolean throwEx) {
        Result<PageUtils<OrganizationResp>> result = hRMdmOpenApiClient.queryBranchInfo(request);
        if (!result.isSuccess()) {
            log.warn("调用公共服务获分支信息失败，分支信息={} ，是否需要抛出异常={} msg={} ", JSON.toJSONString(request), throwEx, result.getMsg());
            if (throwEx) {
                throw new GlobalException(result);
            }
            return null;
        }
        return result.getData().getList();
    }

    /**
     * 获取BMS分支
     *
     * <AUTHOR>
     * @date 2024/1/29 11:56
     * @return : java.util.List<com.mpolicy.open.common.cfpamf.pull.BmsBranchBaseData>
     */
    public List<BmsBranchBaseData> getBmsBranchBase() {
        Result<OpenApiResponse<List<BmsBranchBaseData>>> result = cfpamfOpenApiClient.getBmsBranchBase(Constant.SERVER_NAME);
        if (!result.isSuccess()) {
            log.warn("获取BMS分支信息失败， 响应信息={}", JSON.toJSONString(result));
            throw new GlobalException(result);
        }
        return result.getData().getData();
    }
}
