package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentTrainInfoEntity;
import com.mpolicy.manage.modules.agent.vo.train.*;
import com.mpolicy.manage.modules.common.model.SelectOut;

import java.util.List;

/**
 * 代理人培训信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
public interface AgentTrainInfoService extends IService<AgentTrainInfoEntity> {

    /**
     * 获取代理人培训信息表列表
     * @param params
     * @return
     */
    PageUtils<AgentTrainInfoListOut> findPageList(AgentTrainInfoListVo params);

    /**
     * 获取代理人培训信息详情
     * @param trainCode
     * @return
     */
    AgentTrainInfoOut findAgentTrainInfo(String trainCode);


    /**
     * 新增代理人培训信息
     * @param save
     */
    void saveAgentTrainInfo(SaveAgentTrainInfoVo save);


    /**
     * 修改代理人培训信息
     * @param update
     */
    void updateAgentTrainInfo(UpdateAgentTrainInfoVo update);


    /**
     * 删除代理人培训信息
     * @param trainCode
     */
    void deleteAgentTrainInfo(String trainCode);

    /**
     * 获取代理人培训时长
     * @param agentCode
     * @return
     */
    AgentTrainDurationOut findAgentTrainDuration(String agentCode);

    /**
     * 获取参会人员列表
     * @return
     */
    List<SelectOut> findParticipants();

    /**
     * 获取培训人员列表
     * @return
     */
    List<SelectOut> findTrainHostList();
}

