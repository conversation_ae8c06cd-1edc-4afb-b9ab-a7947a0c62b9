package com.mpolicy.manage.modules.policy.dao.preservation;

import com.mpolicy.manage.modules.policy.entity.PreservationCustomerManagerChangeEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationInsuredInfoChangeEntity;
import com.mpolicy.service.common.mapper.ImsBaseMapper;

public interface PreservationInsuredInfoChangeDao extends ImsBaseMapper<PreservationInsuredInfoChangeEntity> {
}
