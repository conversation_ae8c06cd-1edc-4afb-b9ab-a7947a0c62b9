package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("agent_company_account")
public class AgentCompanyAccountEntity implements Serializable {
    private static final long serialVersionUID = 1747881090437782236L;

    @TableId
    private Integer id;
    /**
     * 代理人编码
     */
    private String agentCode;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 保司简称
     */
    private String shortName;
    /**
     * 保司账号
     */
    private String companyAccount;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 是否删除 0否 1是
     */
    @TableLogic
    private Integer deleted;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
