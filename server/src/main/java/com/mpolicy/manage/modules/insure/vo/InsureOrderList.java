package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 投保订单列表
 *
 * <AUTHOR>
 * @date 2022-05-29 15:23
 */
@Data
@ApiModel(value = "投保订单列表")
public class InsureOrderList extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 投保订单单号
     */
    @ApiModelProperty(value = "投保订单唯一单号", example = "INS20220506103627429095")
    @ExcelProperty(value = "订单号")
    private String insureOrderCode;
    /**
     * 产品类型 个险、财险
     */
    @ApiModelProperty(value = "产品类型", example = "财险")
    @ExcelProperty(value = "产品类型")
    private String portfolioGroupDesc;
    /**
     * 投保订单类型 个险、家庭单、团单
     */
    @ExcelProperty(value = "订单类型")
    @ApiModelProperty(value = "投保订单类型", example = "家庭险")
    private String insureType;
    /**
     * 承保保单号
     */
    @ExcelProperty(value = "承保单号")
    @ApiModelProperty(value = "承保保单号", example = "XJXH20202020505050")
    private String policyCode;
    /**
     * 保司名称
     */
    @ExcelProperty(value = "保险公司")
    @ApiModelProperty(value = "保司名称", example = "北京人寿保险股份有限公司")
    private String companyName;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    @ApiModelProperty(value = "商品名称", example = "小鲸向海特殊医疗2022")
    private String commodityName;

    /**
     * 投保人姓名
     */
    @ExcelProperty(value = "投保人姓名")
    @ApiModelProperty(value = "投保人姓名", example = "张三")
    private String holderName;

    /**
     * 被保人姓名集合
     */
    @ExcelProperty(value = "被保人姓名")
    @ApiModelProperty(value = "被保人姓名集合", example = "张三,李四")
    private String insuredNames;
    /**
     * 所属用户名称
     */
    @ExcelProperty(value = "C端客户名")
    @ApiModelProperty(value = "所属用户名称", example = "张三")
    private String userName;
    /**
     * 所属用户编码
     */
    @ExcelProperty(value = "C端客户编码")
    @ApiModelProperty(value = "所属用户编码", example = "C20220425154054701417")
    private String userNo;

    /**
     * 代理人名称
     */
    @ExcelProperty(value = "代理人")
    @ApiModelProperty(value = "代理人名称", example = "张三")
    private String agentName;
    /**
     * 推荐人名称
     */
    @ExcelProperty(value = "渠道推荐人")
    @ApiModelProperty(value = "推荐人名称", example = "张三")
    private String referrerName;

    /**
     * 销售渠道名称
     */
    @ExcelProperty(value = "渠道")
    @ApiModelProperty(value = "销售渠道名称", example = "中和农信")
    private String userChannelName;
    /**
     * 支付总保费
     */
    @ExcelProperty(value = "订单金额")
    @ApiModelProperty(value = "总保费", example = "20205")
    private BigDecimal premium;
    /**
     * 投保订单状态
     */
    @ExcelProperty(value = "订单状态")
    @ApiModelProperty(value = "投保订单状态描述", example = "待支付")
    private String insureOrderStatusDesc;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty(value = "创建时间", example = "2021-08-20 08:00:00")
    private String createTime;
    /**
     * 投保订单单号
     */
    @ApiModelProperty(value = "原投保订单好", example = "INS20220506103627429092")
    private String sourceInsureOrderCode;

    /**
     * 产品类型 个险、财险
     */
    @ApiModelProperty(value = "产品类型", example = "PRODUCT:PRODUCTGROUP:1")
    private String portfolioGroup;

    /**
     * 投保模式 首投、续期
     */
    @ApiModelProperty(value = "投保订单唯一单号 FIRST-首投 CONTINUED-续期", example = "FIRST")
    private String insureMode;


    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId", example = "C20220425154054701417")
    private String openId;

    /**
     * 保单中心唯一编号
     */
    @ApiModelProperty(value = "保单中心-保单唯一编号", example = "C20202020505050")
    private String contractCode;

    /**
     * 投保/核保时间
     */
    @ApiModelProperty(value = "承保时间", example = "2021-08-20 08:00:00")
    private String underwriteTime;

    /**
     * 业务平台预投保号
     */
    @ApiModelProperty(value = "中台系统受理编号", example = "I20202020505050")
    private String insAdvancePolicyCode;

    /**
     * 家庭保单号
     */
    @ApiModelProperty(value = "保司响应家庭保单号-备用", example = "F20202020505050")
    private String familyPolicyCode;

    /**
     * 投保单号
     */
    @ApiModelProperty(value = "投保单号", example = "XJXH20202020505050")
    private String applicantPolicyNo;

    /**
     * 电子保单地址 电子保单地址
     */
    @ApiModelProperty(value = "保单电子保单地址", example = "https://api-test.xiaowhale.com/doc.html#/home")
    private String policyUrl;

    /**
     * 投保支付模式字典编码
     */
    @ApiModelProperty(value = "投保支付模式字典编码", example = "Applet_Weixin_Native_Code")
    private String insurePayType;

    /**
     * 投保支付号
     */
    @ApiModelProperty(value = "投保支付单号", example = "PAY20205020020")
    private String insurePayNo;

    /**
     * 投保支付时间
     */
    @ApiModelProperty(value = "投保支付单号", example = "2020-05-05 12:00:00")
    private String insurePayTime;

    /**
     * 保司编码
     */
    @ApiModelProperty(value = "保险公司编码", example = "BJRS000000")
    private String companyCode;


    /**
     * 保单类型
     */
    @ApiModelProperty(value = "保单类型", example = "医疗")
    private String policyType;

    /**
     * 组合编码
     */
    @ApiModelProperty(value = "投保组合编码", example = "CESHIZJ22042003")
    private String portfolioCode;

    /**
     * 组合名称
     */
    @ApiModelProperty(value = "组合名称", example = "小鲸向海特殊医疗2022")
    private String portfolioName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码", example = "XJXH2020505050")
    private String commodityCode;


    /**
     * 投保人证据号码
     */
    @ApiModelProperty(value = "投保人证据号码", example = "3147650")
    private String holderIdNo;

    /**
     * 被保人证据号码集合
     */
    @ApiModelProperty(value = "被保人证据号码集合", example = "002502123X,1252525456X")
    private String insuredIdNos;

    /**
     * 主险投保计划
     */
    @ApiModelProperty(value = "主险投保计划", example = "豪华版")
    private String planCode;

    /**
     * 主险保险期间
     */
    @ApiModelProperty(value = "主险保险期间", example = "终身")
    private String coverageYear;

    /**
     * 主险交费方式
     */
    @ApiModelProperty(value = "主险交费方式", example = "趸交")
    private String payMode;

    /**
     * 主险交费期间
     */
    @ApiModelProperty(value = "主险保险期间", example = "一次交清")
    private String payPeriod;

    /**
     * 主险保险金额
     */
    @ApiModelProperty(value = "主险保险金额", example = "20万")
    private String amount;

    /**
     * 份数
     */
    @ApiModelProperty(value = "份数", example = "1")
    private Integer numberOfIns;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间", example = "2021-08-20")
    private String effectiveDate;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间", example = "终身")
    private String invalidDate;

    /**
     * 是否开通续期 开通/不开通
     */
    @ApiModelProperty(value = "是否开通续期", example = "开通")
    private String autoPayment;

    /**
     * 管理机构编码
     */
    @ApiModelProperty(value = "管理机构编码", example = "OR20220105104923ng95gq")
    private String orgCode;

    /**
     * 管理机构名称
     */
    @ApiModelProperty(value = "管理机构名称", example = "北京永通保险代理有限公司镇赉分公司")
    private String orgName;

    /**
     * 销售渠道编码
     */
    @ApiModelProperty(value = "投保渠道编码", example = "zhnx")
    private String channelCode;

    /**
     * 销售渠道名称
     */
    @ApiModelProperty(value = "投保渠道名称", example = "中和农信")
    private String channelName;

    /**
     * 销售渠道编码
     */
    @ApiModelProperty(value = "销售渠道编码", example = "zhnx")
    private String userChannelCode;

    /**
     * 代理人编码
     */
    @ApiModelProperty(value = "代理人编码", example = "ag20210331153732urHyss")
    private String agentCode;

    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人编码", example = "R315750505X")
    private String referrerCode;

    /**
     * 操作终端
     */
    @ApiModelProperty(value = "操作终端mini_program、h5、pc", example = "mini_program")
    private String terminalType;

    /**
     * 村代名称
     */
    @ApiModelProperty(value = "村代名称", example = "张三")
    private String ruralProxyName;

    /**
     * 村代证件号码
     */
    @ApiModelProperty(value = "村代证件号码", example = "10202020301203102301230")
    private String ruralProxyIdNo;

    /**
     * 四级分销-订单类型 1：四级分销，0：非四级分销
     */
    @ApiModelProperty(value = "四级分销-订单类型 1：四级分销，0：非四级分销", example = "1")
    private Integer ruralProxyOrderType;

    /**
     * 自定义参数
     */
    @ApiModelProperty(value = "自定义参数", example = "xjxh_xz_app")
    private String customParam;

    /**
     * 投保ip
     */
    @ApiModelProperty(value = "投保ip", example = "127.0.0.1")
    private String insureIp;

    /**
     * 投保订单状态
     */
    @ApiModelProperty(value = "投保订单状态", example = "1")
    private Integer insureOrderStatus;

    /**
     * 投保时间
     */
    @ApiModelProperty(value = "投保/核保时间", example = "2021-08-20 08:00:00")
    private String insureTime;

}
