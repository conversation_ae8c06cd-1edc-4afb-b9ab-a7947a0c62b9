package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureFlowHistoryDao;
import com.mpolicy.manage.modules.insure.entity.InsureFlowHistoryEntity;
import com.mpolicy.manage.modules.insure.service.InsureFlowHistoryService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 投保操作流水表接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Service("insureFlowHistoryService")
public class InsureFlowHistoryServiceImpl extends ServiceImpl<InsureFlowHistoryDao, InsureFlowHistoryEntity> implements InsureFlowHistoryService {


}
