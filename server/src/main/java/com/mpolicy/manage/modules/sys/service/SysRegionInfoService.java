package com.mpolicy.manage.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;

import java.util.Map;

/**
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-02 14:54:12
 */
public interface SysRegionInfoService extends IService<SysRegionInfo> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 根据城市code获取地区信息
     *
     * @param cityCode 城市code
     * @return 获得的城市，省份信息对象
     */
    SysRegionInfo infoByCityCode(String cityCode);
}

