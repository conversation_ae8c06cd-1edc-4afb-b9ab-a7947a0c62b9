package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 投保黑名单类型模块
 *
 * <AUTHOR>
 * @since 2022/5/9
 */
@Getter
public enum InsureBlacklistTypeEnum {

    /**
     * 黑名单类型模块
     */
    FIRST("FIRST","首投"),
    CONTINUED("CONTINUED","续期");

    /**
     * 模式编码
     */
    private final String code;

    /**
     * 模式名称
     */
    private final String name;

    InsureBlacklistTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static InsureBlacklistTypeEnum decode(String code) {
        return Arrays.stream(InsureBlacklistTypeEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }
}
