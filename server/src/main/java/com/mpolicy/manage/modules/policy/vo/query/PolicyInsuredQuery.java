package com.mpolicy.manage.modules.policy.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel("被保人查询参数")
public class PolicyInsuredQuery implements Serializable {

    @ApiModelProperty("页码")
    private String page;

    @ApiModelProperty("分页大小")
    private String limit;

    @ApiModelProperty("合同编号")
    @NotBlank(message = "合同编号不能为空")
    private String contractCode;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;
}
