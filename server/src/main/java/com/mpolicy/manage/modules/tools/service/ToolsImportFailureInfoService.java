package com.mpolicy.manage.modules.tools.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.tools.entity.ToolsImportFailureInfoEntity;
import com.mpolicy.policy.common.ep.policy.renewal.RenewalTermImportVo;

import java.io.IOException;
import java.io.OutputStream;


/**
 * @author: yangdonglin
 * @create: 2023-05-05 18:46
 * @description: 导入失败信息记录service
 */
public interface ToolsImportFailureInfoService extends IService<ToolsImportFailureInfoEntity> {

    /**
     * 续保信息导入错误信息新建
     *
     * @param result
     * @return
     */
    Result<RenewalTermImportVo> insert(Result<RenewalTermImportVo> result);

    /**
     * 设置导出错误信息流
     *
     * @param entity
     * @param out
     * @throws IOException
     */
    void setImportFailureInfoFlow(ToolsImportFailureInfoEntity entity, OutputStream out) throws IOException;
}
