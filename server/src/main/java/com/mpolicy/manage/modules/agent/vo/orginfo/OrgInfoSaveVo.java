package com.mpolicy.manage.modules.agent.vo.orginfo;

import com.mpolicy.manage.modules.agent.entity.OrgInfoAccessoryEntity;
import com.mpolicy.web.common.validator.group.AddGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrgInfoSaveVo implements Serializable {
    private static final long serialVersionUID = 8775379544390913425L;

    @ApiModelProperty(value = "组织类型0:机构 1:部门", required = true)
    @NotNull(message = "组织类型不能为空")
    private Integer orgType;

    @ApiModelProperty(value = "组织名称", required = true)
    @NotBlank(message = "组织名称不能为空")
    private String orgName;

    @ApiModelProperty(value = "城市代码", required = true)
    private String orgCity;

    @ApiModelProperty(value = "详细住址", required = true)
    private String orgAddr;

    @ApiModelProperty(value = "上级组织编码", required = true)
    private String orgSuperiorCode;

    @ApiModelProperty(value = "营业状态", required = true)
    private Integer orgStatus;

    @ApiModelProperty(value = "负责人姓名", required = true)
    private String principalName;

    @ApiModelProperty(value = "联系人姓名", required = true)
    private String contactName;

    @ApiModelProperty(value = "联系电话", required = true)
    private String contactTel;

    @ApiModelProperty(value = "附件列表", required = true)
    private List<OrgInfoAccessoryEntity> fileList;

    @ApiModelProperty(value = "注册地址")
    @NotBlank(message = "注册地址不能为空",groups = AddGroup.class)
    private String orgRegisterAddr;
    @ApiModelProperty(value = "许可证编号")
    @NotBlank(message = "许可证编号不能为空",groups = AddGroup.class)
    private String orgLicence;
    @ApiModelProperty(value = "业务范围")
    @NotBlank(message = "业务范围不能为空",groups = AddGroup.class)
    private String orgScope;
}
