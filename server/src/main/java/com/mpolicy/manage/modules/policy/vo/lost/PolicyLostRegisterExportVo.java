package com.mpolicy.manage.modules.policy.vo.lost;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/20 12:47 上午
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PolicyLostRegisterExportVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 保单号
     */
    @ExcelProperty(value = "序号")
    private Integer index;
    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 被保人姓名
     */
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件号
     */
    @ExcelProperty(value = "被保人证件号")
    private String insuredIdNumber;
    /**
     * 处理状态 0:待处理 1:处理成功 2:处理失败
     */
    @ExcelProperty(value = "处理状态")
    private String handleStatusName;
    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    private String handleResultContent;
    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间",format = "yyyy-MM-dd")
    private Date registerTime;
    /**
     * 登记人工号
     */
    @ExcelProperty(value = "登记人工号")
    private String registerUserId;
    /**
     * 登记人姓名
     */
    @ExcelProperty(value = "登记人姓名")
    private String registerUserName;
    /**
     * 登记人所属区域
     */
    @ExcelProperty(value = "登记人所属区域")
    private String registerUserRegionName;
    /**
     * 登记人所属区域
     */
    @ExcelProperty(value = "登记人所属分支")
    private String referrerOgrName;
    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String recommendId;
    /**
     * 推荐人姓名
     */
    @ExcelProperty(value = "推荐人姓名")
    private String recommendName;

}
