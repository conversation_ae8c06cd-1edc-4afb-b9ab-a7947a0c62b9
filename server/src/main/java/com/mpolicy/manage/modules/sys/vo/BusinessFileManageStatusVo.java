package com.mpolicy.manage.modules.sys.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用导入导出状态信息
 *
 * <AUTHOR>
 * @date 2024-02-21 14:01
 */
@Data
@ApiModel(value = "通用导入导出状态信息", description = "通用导入导出状态信息")
public class BusinessFileManageStatusVo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 业务文件受理唯一编号
     */
    @ApiModelProperty(value = "业务文件受理唯一编号", example = "FM202020")
    private String fileManageCode;

    /**
     * 业务文件类型;0导入业务1导出业务
     */
    @ApiModelProperty(value = "业务文件类型 0导入业务1导出业务", example = "1")
    private Integer fileManageType;

    /**
     * 业务文件分组
     */
    @ApiModelProperty(value = "业务文件分组", example = "保单中心")
    private String opeGroup;

    /**
     * 业务文件模块
     */
    @ApiModelProperty(value = "业务文件模块", example = "个险导入")
    private String opeModule;

    /**
     * 业务操作状态;0待处理 1处理中 2处理成功 -1处理失败
     */
    @ApiModelProperty(value = "业务操作状态;0待处理 1处理中 2处理成功 -1处理失败", example = "1")
    private Integer fileManageStatus;
}
