package com.mpolicy.manage.modules.agent.vo;

import com.alibaba.fastjson.JSONArray;
import com.aliyun.openservices.log.common.QueriedLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;

@Data
public class SlsQueryLogOutputVo {

    private JSONArray data;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100")
    private int totalCount;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "总记录数", example = "10")
    private int pageSize;

    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数", example = "10")
    private int totalPage;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数", example = "1")
    private int currPage;
}
