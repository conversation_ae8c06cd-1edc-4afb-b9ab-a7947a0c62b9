package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 被保人基础信息变更保全
 */
@Data
@ApiModel("被保人基础信息变更明细")
public class PreservationInsuredBaseInfo {

    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("被保人类型")
    private Integer insuredType;

    @ApiModelProperty("投被保人关系")
    private String insuredRelation;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("被保人类型")
    private String insuredIdType;

    @ApiModelProperty("被保人证件")
    private String insuredIdCard;

    @ApiModelProperty("被保人性别")
    private String insuredGender;

    @ApiModelProperty("被保人盛瑞")
    private String insuredBirthday;
}
