package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保全-险种变更列表
 * <AUTHOR>
@Data
public class PreservationProductChangeVo {

    @ApiModelProperty("序号")
    @NotNull(message = "险种序号不能为空")
    private Integer serialNo;

    @ApiModelProperty("数据类型：0=原险种列表，1=变更后的险种列表")
    @NotNull(message = "数据类型不能为空")
    private Integer type;

    @ApiModelProperty("被保人姓名")
    private String insuredCode;

    @ApiModelProperty("被保人姓名")
    private String insuredName;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("生效日期")
    private Date effectiveDate;

    @ApiModelProperty("险种编码")
    @NotBlank(message = "险种编码不能为空")
    private String productCode;

    @ApiModelProperty("险种名称")
    @NotBlank(message = "险种名称不能为空")
    private String productName;

    @ApiModelProperty("主附险标志")
    private String mainInsurance;

    @ApiModelProperty("保单险种编码")
    private String policyProductCode;

    @ApiModelProperty("保额")
    private BigDecimal coverage;

    @ApiModelProperty("保额单位")
    private String coverageUnit;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("保障期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保障时长")
    private Integer insuredPeriod;

    @ApiModelProperty("缴费方式-年交/半年交/季交/月交/趸交/不定期交/短险一次交清")
    private String periodType;

    @ApiModelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ApiModelProperty("缴费时长")
    private Integer paymentPeriod;
}
