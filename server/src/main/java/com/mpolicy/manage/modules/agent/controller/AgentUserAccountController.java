package com.mpolicy.manage.modules.agent.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;

import com.mpolicy.manage.modules.agent.entity.AgentUserAccountEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserAccountService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;


/**
 * 经纪人账号表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-08 11:05:50
 */
@RestController
@RequestMapping("sys/agentuseraccount")
@Api(tags = "经纪人账号表")
public class AgentUserAccountController {

    @Autowired
    private AgentUserAccountService agentUserAccountService;


    /**
     * 列表
     */
    // @ApiOperation(value = "获取经纪人账号表列表", notes = "分页获取经纪人账号表列表")
    // @ApiImplicitParams({
    //         @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
    //         @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
    //         @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
    //         @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    // })
    @GetMapping("/list")
    public Result<PageUtils> list(@RequestParam Map<String, Object> params) {
        PageUtils page = agentUserAccountService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @GetMapping("/info/{account}")
    public Result<AgentUserAccountEntity> info(@PathVariable("account") String account) {
        AgentUserAccountEntity agentUserAccount = agentUserAccountService.getById(account);

        return Result.success(agentUserAccount);
    }
}
