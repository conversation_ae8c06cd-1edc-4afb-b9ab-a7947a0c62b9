package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附加险解约信息
 *
 * <AUTHOR>
 * @date 2022-04-24 13:53
 */
@ApiModel(value = "附加险解约信息")
@Data
public class ProductTerminationInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 险种编码
     */
    @ApiModelProperty(value = "附加险编码", example = "XJXH2020222020")
    private String productCode;

    /**
     * 退保金额
     */
    @ApiModelProperty(value = "附加险退保金额", example = "100.52")
    private BigDecimal surrenderCash;
}
