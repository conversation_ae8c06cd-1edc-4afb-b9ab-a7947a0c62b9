package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 保司产品升级控制
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-10 14:36:47
 */
@TableName("insure_company_confine")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureCompanyConfineEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@TableId
	private Integer id;
	/**
	 * 升级控制编码
	 */
	private String confineCode;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 保司编码
	 */
	private String companyCodes;
	/**
	 * 产品编码
	 */
	private String productCodes;
	/**
	 * 客户端类型 1:小程序
	 */
	private Integer clientType;
	/**
	 * 是否启用 0:停用;1:启用
	 */
	private Integer isStatus;
	/**
	 * 升级开始时间
	 */
	private Date confineStartTime;
	/**
	 * 升级结束时间
	 */
	private Date confineEndTime;
	/**
	 * 升级说明
	 */
	private String confineDesc;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 修改人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
