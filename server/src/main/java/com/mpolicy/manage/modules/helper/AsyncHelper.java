package com.mpolicy.manage.modules.helper;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2024/7/23 17:44
 */
@Data
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsyncHelper {

    /**
     * 异步执行给定的Runnable任务。
     *
     * 通过使用@Async注解，此方法将在调用时异步执行，不等待执行结果返回。
     * 这种方式适用于那些不需要立即返回结果且可能耗时的操作，以提高程序的并发性能。
     *
     * @param runnable 要执行的Runnable任务。这个参数封装了要异步执行的代码逻辑。
     *                 方法内部通过直接调用run方法来执行这个任务。
     */
    @Async
    public void run(Runnable runnable){
        runnable.run();
    }

}
