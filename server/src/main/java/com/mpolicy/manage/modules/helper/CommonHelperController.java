package com.mpolicy.manage.modules.helper;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.jobhandler.policy.PolicyPreservationHandler;
import com.mpolicy.manage.modules.helper.service.HelperApiService;
import com.mpolicy.manage.modules.helper.vo.PolicyComplianceQuery;
import com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationSurrenderEntity;
import com.mpolicy.manage.modules.policy.enums.PreservationProjectEnum;
import com.mpolicy.manage.modules.policy.enums.PreservationTopicEnum;
import com.mpolicy.manage.modules.policy.enums.PreservationWhyEnum;
import com.mpolicy.manage.modules.policy.service.common.EpPolicyBaseService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationService;
import com.mpolicy.manage.modules.policy.strategy.PreservationStrategyFactory;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationApplyInput;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationSurrenderDetailVo;
import com.mpolicy.manage.modules.sys.util.LambdaUtils;
import com.mpolicy.manage.mq.policy.service.PolicyEventService;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.enums.PolicyOperateModelEnum;
import com.mpolicy.policy.common.enums.PolicyProductTypeEnum;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveSurrenderResultVo;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveVo;
import com.mpolicy.policy.common.ep.policy.preserve.MemberPremiumTrialVo;
import com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.netflix.discovery.converters.Auto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "管理后台服务服务")
@RestController
@RequestMapping("/helper")
@Slf4j
public class CommonHelperController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private HelperApiService helperApiService;

    @Autowired
    private EpPolicyClient policyClient;

    @Autowired
    protected EpPolicyBaseService epPolicyBaseService;

    @Autowired
    private PreservationApplyService preservationApplyService;

    @Autowired
    private PreservationStrategyFactory preservationStrategyFactory;

    @Autowired
    private PolicyPreservationHandler preservationHandler;

    @Autowired
    private PolicyEventService eventService;

    @ApiOperation(value = "[台站]交单保单生成")
    @PostMapping("/compliance/policy/order_time/export")
    public Result<String> complianceExportPolicy(
            @RequestParam @ApiParam(name = "salesType", value = "0线上1线下") String salesType,
            @RequestParam(value = "orgCodes" ,required = false) @ApiParam(name = "orgCodes", value = "机构编码集合逗号分割") String orgCodes,
            @RequestParam(value = "queryOrderTime" ,required = false)  String queryOrderTimes,
            @RequestBody @ApiParam(name = "queryOrderTime", value = "交单日期起始时间_交单日期终止时间") List<String> queryOrderTime) {
        // 异步执行申请开始对账
        final List<String> orgCodeList = new ArrayList<>();

        if(StringUtils.isNotBlank(orgCodes)){
            orgCodeList.addAll(Arrays.stream(orgCodes.split(",")).collect(Collectors.toList()));
        }

        log.info("准备执行[台站]交单保单生成，orgCodes = {} 销售方式 = {} 机构总数={} 交单日期参数={}", orgCodeList, salesType, orgCodeList.size(), queryOrderTime);
        taskExecutor.submit(() -> {
            // 执行交单日期集合导出
            queryOrderTime.forEach(x -> {
                try {
                    Map<String, Object> params = new HashMap<>();
                    //交单日期起始时间 + 交单日期终止时间
                    final String[] s = x.split("_");
                    // params.put("orderTimeBegin", s[0]);
                    params.put("enforceTimeBegin", s[0]);
                    // params.put("orderTimeEnd", s[1]);
                    params.put("enforceTimeEnd", s[1]);
                    params.put("orgCodeList", orgCodeList);
                    params.put("salesType", salesType);
                    log.info("导入监管保单文件成功，orgCodes = {} 交单开始日期={}, 交单结束日期={}", orgCodeList, s[0], s[1]);
                    helperApiService.exportPolicy("辅助", params,null);
                } catch (Exception e) {
                    log.warn("导入监管保单文件失败：交单日期={},错误信息:", x, e);
                }
            });

        });
        return Result.success("申请导入成功");
    }

    @ApiOperation(value = "[台帐]交单保单生成V2")
    @PostMapping("/compliance/policy/export/v2")
    public Result<String> complianceExportPolicyV2(@RequestBody PolicyComplianceQuery query) {
        log.info("准备执行[台帐]保单生成={}", JSON.toJSONString(query));

        //1.线下单-代理人机构编码
        final List<String> orgCodeList = new ArrayList<>();
        String orgCodes = query.getOrgCodes();
        if(StringUtils.isNotBlank(orgCodes)){
            orgCodeList.addAll(Arrays.stream(orgCodes.split(",")).collect(Collectors.toList()));
        }

        String salesType = query.getSalesType();
        String orderTimeParam = query.getQueryOrderTimes();
        String enforceTimeParam = query.getQueryEnforceTimes();
        if(StringUtils.isBlank(orderTimeParam)){
            if(StringUtils.isBlank(enforceTimeParam)){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("导出查询条件中交单时间和生效时间不能同时为空"));
            }
        }

        taskExecutor.submit(() -> {
            Map<String, Object> params = new HashMap<>();
            String orderStartTime = null;
            String orderEndTime = null;
            if(StringUtils.isNotBlank(orderTimeParam)){
                String[] arr0 = orderTimeParam.split("_");
                orderStartTime = arr0[0];
                orderEndTime = arr0[1];
            }

            String enforceStartTime = null;
            String enforceEndTime = null;
            if(StringUtils.isNotBlank(enforceTimeParam)){
                String[] arr1 = enforceTimeParam.split("_");
                enforceStartTime = arr1[0];
                enforceEndTime = arr1[1];
            }
            try {
                    params.put("orderTimeBegin",orderStartTime);
                    params.put("orderTimeEnd", orderEndTime);
                    params.put("enforceTimeBegin",enforceStartTime);
                    params.put("enforceTimeEnd", enforceEndTime);
                    params.put("orgCodeList", orgCodeList);
                    params.put("salesType", salesType);
                    params.put("channelDistributionCode", query.getChannelDistributionCode());
                    log.info("开始执行导出流程:{}", JSON.toJSONString(params));
                    helperApiService.exportPolicy("辅助", params,query.getDingTalkToken());
                } catch (Exception e) {
                    log.warn("导入监管保单文件失败：查询参数={},错误信息:", JSON.toJSONString(params), e);
                }
        });
        return Result.success("申请导入成功");
    }

    @ApiOperation(value = "确认单与保单金额对比")
    @PostMapping("/compliance/policy/diff")
    public Result<String> policyCashDiff(@RequestBody @ApiParam(name = "queryOrderTime", value = "交单日期起始时间_交单日期终止时间") List<String> queryOrderTime) {

        // 异步执行申请开始对账
        taskExecutor.submit(() -> {
            helperApiService.policyCashDiff();
        });
        return Result.success("申请差异处理成功");
    }

    @ApiOperation(value = "确认单与保单金额对比")
    @GetMapping("/preservation/execute")
    public Result<?> executePreservation(@RequestParam("preservationCode") String preservationCode) {
        PreservationApplyEntity data = preservationApplyService.lambdaQuery().eq(PreservationApplyEntity::getPreservationCode, preservationCode)
                .one();
        if(data==null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全申请流程为空"));
        }
        String preservationProject = data.getPreservationProject();
        PreservationProjectEnum project = Optional.ofNullable(
                PreservationProjectEnum.decode(preservationProject)
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全项目不支持")));

        PreservationService<PreservationApplyInput> preservationService = preservationStrategyFactory.getPreservationService(project, PreservationApplyInput.class);
        preservationService.executeFlow(data.getContractCode(),data.getPreservationCode());

        return Result.success();
    }


    @ApiOperation(value = "确认单与保单金额对比")
    @GetMapping("/surrender-detail/fix")
    public Result<?> surrenderDetailFix(@RequestParam(value = "contractCode")String contractCode){
        List<PreservationApplyEntity> data = preservationApplyService.querySurrender4EmptyFix(contractCode,100);
        preservationHandler.preservationSurrenderDetailFix(data);
        return Result.success();
    }

//    @PostMapping("/group/correct/event/s0")
//    public Result<?> groupCorrectEvent(@RequestParam("contratCode") String contractCode,@RequestBody JSONObject data){
//        String messageBody = JSON.toJSONString(data);
//        EpPreserveVo preserveVo = JSON.parseObject(messageBody, EpPreserveVo.class);
//        PreservationApplyInput input = eventService.convertVo(contractCode, preserveVo);
//        String project = input.getPreservationProject();
//
//        PreservationProjectEnum projectEnum = PreservationProjectEnum.decode(project);
//
//        PreservationService<PreservationApplyInput> preservationService = preservationStrategyFactory.getPreservationService(projectEnum, PreservationApplyInput.class);
//        String preservationCode = preservationService.apply(input);
//        preservationService.executeFlow(contractCode,preservationCode);
//        log.info("保全申请完成后，发送mq完成，保全单号={}", preservationCode);
//        return Result.success();
//    }


    @ApiOperation(value = "确认单与保单金额对比")
    @PostMapping("/preservation/check/surrender")
    public Result<List<PreservationSurrenderEntity>> preservationCheck(@RequestBody @ApiParam(name = "applyInput", value = "保全申请信息") PreservationApplyInput applyInput) {
        ValidatorUtils.validateEntity(applyInput);
        // 获取保全项目
        PreservationProjectEnum project = Optional.ofNullable(
                PreservationProjectEnum.decode(applyInput.getPreservationProject())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全项目不支持")));
        // 根据项目类型获取保全策略服务 + 执行保全申请

        // 续期期数默认为1，目前有些保全，即使当前续期期数不为1，但是保全信息中也要存首期，但是退保需要存当前续期期数
        Integer renewalTermPeriod = applyInput.getRenewalTermPeriod();
        if (renewalTermPeriod == null) {
            applyInput.setRenewalTermPeriod(1);
        }

        log.info("执行保全项目-退保申请:{}", JSON.toJSONString(applyInput));

        String contractCode = applyInput.getContractCode();
        EpContractBriefInfoVo policy = epPolicyBaseService.queryPolicyContractBriefInfo(applyInput.getContractCode(), true);

        // 2. 生成保全唯一编号
        String preservationCode = CommonUtils.createCode("PR");

        // 3.3 退保险种明细
        List<PreservationSurrenderEntity> surrenderEntityList = convertSurrenderDetail(contractCode, preservationCode, policy,applyInput);

        if(Objects.equals(policy.getPolicyProductType(), PolicyProductTypeEnum.GROUP.getCode())) {
            if (CollectionUtils.isEmpty(surrenderEntityList)) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("团险在保人员信息为空，无法操作退保"));
            }
        }

        return Result.success(surrenderEntityList);
    }

    private List<PreservationSurrenderEntity> convertSurrenderDetail(String contractCode, final String preservationCode, EpContractBriefInfoVo policy,PreservationApplyInput applyInput) {
        Result<List<EpInsuredProductVo>> r = policyClient.activeInsuredProductList(contractCode);
        log.info("在保人员明细:{}",JSON.toJSONString(r));
        if (!r.isSuccess()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保单被保人承保险种列表查询失败，请稍后重试～"));
        }

        List<EpInsuredProductVo> data = r.getData();
        List<PreservationSurrenderDetailVo> surrenderDetailList = applyInput.getSurrenderDetailList();
        Map<String, PreservationSurrenderDetailVo> productMap = LambdaUtils.safeToMap(surrenderDetailList, product -> {
            String policyProductCode = product.getPolicyProductCode();
            String insuredCode = product.getInsuredCode();
            return insuredCode + "-" + policyProductCode;
        });

        List<MemberPremiumTrialVo> surrenderTrialPremium = Collections.emptyList();

        if(Objects.equals(policy.getPolicyProductType(),PolicyProductTypeEnum.GROUP.getCode())){
            surrenderTrialPremium = epPolicyBaseService.surrenderTrialPremium(contractCode,
                    PreservationProjectEnum.decode(applyInput.getPreservationProject()),
                    PreservationWhyEnum.decode(applyInput.getPreservationWhy()),
                    applyInput.getSurrenderCash(),
                    applyInput.getPreservationEffectTime(),
                    preservationCode);
        }

        Map<String,MemberPremiumTrialVo> surrenderPremiumMap = LambdaUtils.safeToMap(surrenderTrialPremium,MemberPremiumTrialVo::getInsuredCode);

        return data.stream()
                .map(product -> {
                    PreservationSurrenderEntity entity = new PreservationSurrenderEntity();
                    BeanUtils.copyProperties(product, entity);
                    entity.setPreservationCode(preservationCode);

                    String policyProductCode = product.getPolicyProductCode();
                    String insuredCode = product.getInsuredCode();
                    String key = insuredCode + "-" + policyProductCode;
                    PreservationSurrenderDetailVo vo = productMap.get(key);
                    if (vo != null) {
                        entity.setSurrenderPremium(vo.getSurrenderAmount());
                    }

                    MemberPremiumTrialVo surrenderPremium = surrenderPremiumMap.get(insuredCode);
                    if(surrenderPremium!=null){
                        entity.setInsuredSurrenderPremium(surrenderPremium.getSurrenderPremium());
                    }
                    return entity;
                })
                .collect(Collectors.toList());
    }

    @Autowired
    private PolicyEventService policyEventService;
    @ApiOperation(value = "Api-增减员")
    @PostMapping("/api/addSubtract/flow")
    public Result<?> mockAddSubtractApi(@RequestParam("contractCode") String contractCode,@RequestBody EpPreserveVo preserveVo) {
        String  data = JSON.toJSONString(preserveVo);
        JSONObject param = JSON.parseObject(data);
        policyEventService.preserveApply(contractCode,param);
        return Result.success(null);
    }
}
