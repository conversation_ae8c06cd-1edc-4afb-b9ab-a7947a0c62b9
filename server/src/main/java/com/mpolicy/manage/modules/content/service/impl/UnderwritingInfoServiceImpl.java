package com.mpolicy.manage.modules.content.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.CustomerKeys;
import com.mpolicy.manage.constant.QaSearchTypeConstant;
import com.mpolicy.web.common.utils.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.content.dao.UnderwritingInfoDao;
import com.mpolicy.manage.modules.content.entity.UnderwritingInfoEntity;
import com.mpolicy.manage.modules.content.service.UnderwritingInfoService;


@Service("underwritingInfoService")
public class UnderwritingInfoServiceImpl extends ServiceImpl<UnderwritingInfoDao, UnderwritingInfoEntity> implements UnderwritingInfoService {

    @Autowired
    IRedisService iRedisService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        boolean searchKeywordNotNull = params.get("searchType") != null && params.get("searchKeyword") != null;
        IPage<UnderwritingInfoEntity> page = this.page(
                new Query<UnderwritingInfoEntity>().getPage(params),
                new QueryWrapper<UnderwritingInfoEntity>().lambda()
                        .and(searchKeywordNotNull && StringUtils.isNotBlank(String.valueOf(params.get("searchKeyword"))), item ->
                                item.like(
                                                searchKeywordNotNull && String.valueOf(params.get("searchType")).equals(QaSearchTypeConstant.FULL_TEXT),
                                                UnderwritingInfoEntity::getUnderwritingSuggest, params.get("searchKeyword")
                                        )
                                        .or()
                                        .like(
                                                searchKeywordNotNull && String.valueOf(params.get("searchType")).equals(QaSearchTypeConstant.TITLE),
                                                UnderwritingInfoEntity::getIllnessName, params.get("searchKeyword")
                                        ).or()
                                        .like(
                                                searchKeywordNotNull && String.valueOf(params.get("searchType")).equals(QaSearchTypeConstant.TITLE),
                                                UnderwritingInfoEntity::getIllnessNickname, params.get("searchKeyword")
                                        )

                        )
        );

        List<UnderwritingInfoEntity> records = page.getRecords();
        if (records != null && records.size() > 0) {
            List<UnderwritingInfoEntity> collect = records.stream().peek(underwritingInfoEntity -> {
                String[] split = underwritingInfoEntity.getIllnessNickname().split(";");
                underwritingInfoEntity.setIllnessNicknameList(Arrays.asList(split));
            }).collect(Collectors.toList());
            page.setRecords(collect);
        }

        return new PageUtils(page);
    }

    @Override
    public Boolean saveEntity(UnderwritingInfoEntity underwritingInfoEntity) {
        invalidateUnderwritingCache();

        // 设置默认状态
        underwritingInfoEntity.setEnabled(StatusEnum.NORMAL.getCode());

        // 获取疾病别称列表
        String s = listToString(underwritingInfoEntity.getIllnessNicknameList());
        underwritingInfoEntity.setIllnessNickname(s);

        underwritingInfoEntity.setUnderwritingCode(CommonUtils.createCode("HB"));
        return this.save(underwritingInfoEntity);
    }

    @Override
    public boolean changeEnable(String code, Integer enabled, long revision) {
        invalidateUnderwritingCache();
        if (StatusEnum.getNameByCode(enabled) == null) {
            return false;
        }
        UnderwritingInfoEntity underwritingInfoEntity = new UnderwritingInfoEntity();
        underwritingInfoEntity.setEnabled(enabled);
        underwritingInfoEntity.setRevision(revision);
        return this.update(underwritingInfoEntity, new QueryWrapper<UnderwritingInfoEntity>().lambda().eq(UnderwritingInfoEntity::getUnderwritingCode, code));
    }

    @Override
    public boolean updateEntity(UnderwritingInfoEntity underwritingInfoEntity) {
        invalidateUnderwritingCache();

        // 获取疾病别称列表
        String s = listToString(underwritingInfoEntity.getIllnessNicknameList());
        underwritingInfoEntity.setIllnessNickname(s);

        boolean update = this.update(underwritingInfoEntity,
                Wrappers.<UnderwritingInfoEntity>lambdaQuery()
                        .eq(UnderwritingInfoEntity::getUnderwritingCode, underwritingInfoEntity.getUnderwritingCode()));

        return update;
    }

    @Override
    public void invalidateUnderwritingCache() {
        iRedisService.delete(CustomerKeys.SEARCH_TAG_NAME, Constant.HOME_SEARCH_TAGS);
        iRedisService.delete(CustomerKeys.SEARCH_TAG_NAME, Constant.SEARCH_UNDERWRITING_TAGS);
    }

    /**
     * list转成分号分隔的字符串
     *
     * @param list 要转换的list
     * @return 如果没问题正常返回，如果有问题抛异常
     */
    public String listToString(List<String> list) {
        String collect = "";

        // 将疾病别称列表转换为分号分隔的字符串
        if (list != null && list.size() > 0) {
            for (String s : list) {
                if (s.contains(";")) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("疾病名称中不可以有分号"));
                }
            }
            collect = String.join(";", list);
        }

        return collect;
    }
}
