package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelReferrerBindLogEntity;
import com.mpolicy.manage.modules.agent.enums.ReferrerQueryTypeEnum;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelCustomerService;
import com.mpolicy.manage.modules.agent.service.ChannelReferrerBindLogService;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelCustomerVo;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/9 14:09
 */
@Service("channelCustomerService")
public class ChannelCustomerServiceImpl implements ChannelCustomerService {
    @Autowired
    CustomerBasicInfoService customerBasicInfoService;
    @Autowired
    ChannelApplicationReferrerService channelApplicationReferrerService;

    @Autowired
    ChannelReferrerBindLogService channelReferrerBindLogService;

    @Override
    public PageUtils<ChannelCustomerVo> queryPage(String applicationCode, Map<String, Object> params) {
        return customerBasicInfoService.queryChannelCustomerPage(applicationCode, params);
    }

    @Override
    public Integer getTotal(String applicationCode) {
        return customerBasicInfoService.count(
                Wrappers.<CustomerBasicInfoEntity>lambdaQuery()
                        .eq(CustomerBasicInfoEntity::getChannelCode, applicationCode)
        );
    }

    @Override
    public void updateReferrer(String customerCode, String referrerCode) {
        Date bindingTime = null;
        if (StringUtils.isNotBlank(referrerCode)) {
            bindingTime = new Date();
        }
        //查询新老客户经理信息
        ChannelApplicationReferrerEntity oldReferrer = null;
        ChannelApplicationReferrerEntity newReferrer = channelApplicationReferrerService.lambdaQuery()
                .eq(ChannelApplicationReferrerEntity::getReferrerCode, referrerCode).one();//有效
        CustomerBasicInfoEntity customerBasicInfo = customerBasicInfoService.lambdaQuery()
                .eq(CustomerBasicInfoEntity::getCustomerCode, customerCode).one();
        if(StringUtils.isNotBlank(customerBasicInfo.getReferrerCode())){
            oldReferrer = channelApplicationReferrerService.lambdaQuery()
                    .eq(ChannelApplicationReferrerEntity::getReferrerCode, customerBasicInfo.getReferrerCode()).one();
        }
        //记录老客户经理解绑动作
        if(oldReferrer!=null){
            ChannelReferrerBindLogEntity entity = new ChannelReferrerBindLogEntity();
            entity.setType(2);
            entity.setModel(4);
            entity.setReferrerCode(oldReferrer.getReferrerCode());
            entity.setReferrerWno(oldReferrer.getReferrerWno());
            entity.setReferrerName(oldReferrer.getReferrerName());
            entity.setCustomerCode(customerCode);
            entity.setCreateTime(new Date());
            channelReferrerBindLogService.save(entity);
        }
        //记录新客户经理解绑动作
        if(newReferrer!=null){
            ChannelReferrerBindLogEntity entity = new ChannelReferrerBindLogEntity();
            entity.setType(1);
            entity.setModel(4);
            entity.setReferrerCode(newReferrer.getReferrerCode());
            entity.setReferrerWno(newReferrer.getReferrerWno());
            entity.setReferrerName(newReferrer.getReferrerName());
            entity.setCustomerCode(customerCode);
            entity.setCreateTime(new Date());
            channelReferrerBindLogService.save(entity);
        }


        customerBasicInfoService.update(
                Wrappers.<CustomerBasicInfoEntity>lambdaUpdate()
                        .set(CustomerBasicInfoEntity::getReferrerCode, referrerCode)
                        .set(CustomerBasicInfoEntity::getReferrerBindingTime, bindingTime)
                        .eq(CustomerBasicInfoEntity::getCustomerCode, customerCode)
        );
    }

    @Override
    public List<ChannelApplicationReferrerVo> queryReferrer(String applicationCode, ReferrerQueryTypeEnum referrerQueryTypeEnum, String text) {
        return channelApplicationReferrerService.list(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getApplicationCode, applicationCode)
                        .eq(ChannelApplicationReferrerEntity::getEnabled, StatusEnum.NORMAL.getCode())
                        .likeRight(ReferrerQueryTypeEnum.NAME.equals(referrerQueryTypeEnum), ChannelApplicationReferrerEntity::getReferrerName, text)
                        .likeRight(ReferrerQueryTypeEnum.WORK_NO.equals(referrerQueryTypeEnum), ChannelApplicationReferrerEntity::getReferrerWno, text)
        ).stream().map(x -> {
            ChannelApplicationReferrerVo channelApplicationReferrerVo = new ChannelApplicationReferrerVo();
            channelApplicationReferrerVo.setReferrerCode(x.getReferrerCode());
            channelApplicationReferrerVo.setReferrerName(x.getReferrerName() + " " + x.getReferrerWno());
            return channelApplicationReferrerVo;
        }).collect(Collectors.toList());
    }
}
