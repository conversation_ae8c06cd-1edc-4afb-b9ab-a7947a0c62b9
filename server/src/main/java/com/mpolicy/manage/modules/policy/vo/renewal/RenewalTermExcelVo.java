package com.mpolicy.manage.modules.policy.vo.renewal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "续期导出信息")
@Data
public class RenewalTermExcelVo implements Serializable {

    @ApiModelProperty(value = "投保单号")
    private String proposalNo;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("续期状态：0=未续期；1=已续期")
    private Integer status;

    @ApiModelProperty("销售方式：0=网销；1=线下")
    private String salesType;

    @ApiModelProperty("销售平台")
    private String salesPlatform;

    @ApiModelProperty("保险公司名字")
    private String companyName;

    @ApiModelProperty("主险名称")
    private String mainProductName;

    @ApiModelProperty("协议险种名称")
    private String protocolProductName;

    @ApiModelProperty("险种名称")
    private String productName;

    @ApiModelProperty("主险标志")
    private String mainInsurance;

    @ApiModelProperty("险种类型")
    private String prodTypeCode;

    @ApiModelProperty("保险期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保险期间")
    private String insuredPeriod;

    @ApiModelProperty("缴费方式")
    private String periodType;

    @ApiModelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ApiModelProperty("缴费时长")
    private String paymentPeriod;

    @ApiModelProperty("缴费期次")
    private String period;

    @ApiModelProperty("保单年度")
    private String policyPeriodYear;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("保额")
    private BigDecimal coverage;

    @ApiModelProperty("代理人机构名字")
    private String agentOrgName;

    @ApiModelProperty("代理人部门名字")
    private String agentDepartmentName;

    @ApiModelProperty("是否为主代理人")
    private int mainAgentFlag;

    @ApiModelProperty("代理人编码")
    private String agentCode;

    @ApiModelProperty("代理人编码")
    private String agentName;

    @ApiModelProperty("分佣比例")
    private String commissionRate;

    @ApiModelProperty("推荐人")
    private String policyReferrerCode;

    @ApiModelProperty("推荐人")
    private String policyReferrerName;

    @ApiModelProperty("销售渠道")
    private String channel;

    @ApiModelProperty("分支名称")
    private String channelBranchName;

    @ApiModelProperty("推荐人")
    private String referrerCode;

    @ApiModelProperty("推荐人")
    private String referrerName;

    @ApiModelProperty("生效时间")
    private String effectiveTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("应收日期")
    private String duePaymentTime;

    @ApiModelProperty("应收保费")
    private String duePaymentAmount;

    @ApiModelProperty("实收日期")
    private String paymentTime;

    @ApiModelProperty("实收金额")
    private String paymentAmount;

    @ApiModelProperty("宽限期")
    private String graceDay;

    @ApiModelProperty("实收操作人")
    private String creator;

    @ApiModelProperty("实收操作时间")
    private Date createTime;

    @ApiModelProperty("投保人姓名")
    private String applicantName;

    @ApiModelProperty("投保人证件类型")
    private String applicantIdType;

    @ApiModelProperty("投保人证件号")
    private String applicantIdCard;

    @ApiModelProperty("投保人性别")
    private String applicantGender;

    @ApiModelProperty("投保人手机号")
    private String applicantMobile;

    @ApiModelProperty("投保人生日")
    private Date applicantBirthday;

    @ApiModelProperty("投保人地址")
    private String applicantAddress;

    @ApiModelProperty("投被保人关系")
    private String insuredRelation;

    @ApiModelProperty("被保人名")
    private String insuredName;

    @ApiModelProperty("被保人证件类型")
    private String insuredIdType;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("被保人性别")
    private String insuredGender;

    @ApiModelProperty("被保人手机号")
    private String insuredMobile;

    @ApiModelProperty("被保人生日")
    private String insuredBirthday;

    @ApiModelProperty("被保人地址")
    private String insuredAddress;

    @ApiModelProperty("佣金发放状态")
    private String settlementStatus;

    @ApiModelProperty("佣金发放年份")
    private String settlementYear;

    @ApiModelProperty("佣金发放月份")
    private String settlementMonth;

}
