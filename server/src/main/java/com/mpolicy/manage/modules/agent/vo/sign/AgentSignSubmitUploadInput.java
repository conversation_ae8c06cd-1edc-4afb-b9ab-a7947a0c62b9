package com.mpolicy.manage.modules.agent.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ClassName: AgentSignSubmitUploadInput
 * Description: 代理人文件签署提交上传请求参数
 * date: 2023/5/30 11:38
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "代理人文件签署提交上传请求参数", description = "代理人文件签署提交上传请求参数")
public class AgentSignSubmitUploadInput {

    @ApiModelProperty(value = "上传文件信息集合")
    @NotEmpty(message = "上传文件信息不能为空")
    @Valid
    private List<AgentSignFileInfo> fileList;

    @ApiModelProperty(value = "启用状态 0:未启用,1:启用",example = "1")
    @NotNull(message = "启用状态不能为空")
    private Integer enabled;
}
