package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人人员附件
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-07 10:41:32
 */
@TableName("agent_accessory")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentAccessoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "经纪人工号", hidden = true)
    private String agentCode;
    /**
     * 附件类型
     */
    @ApiModelProperty(value = "附件类型", required = true)
    @NotBlank
    private String identificationType;
    /**
     * 附件格式
     */
    @ApiModelProperty(value = "附件格式", required = true)
    @NotBlank
    private String suffix;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径", required = true)
    @NotBlank
    private String filePath;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
