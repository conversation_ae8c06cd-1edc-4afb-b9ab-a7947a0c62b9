package com.mpolicy.manage.modules.insurance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.ProdTypeEnum;
import com.mpolicy.manage.modules.insurance.dao.InsuranceRecommendProductDao;
import com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioInfoEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceRecommendConditionEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceRecommendProductEntity;
import com.mpolicy.manage.modules.insurance.mapstruct.RecommendMapper;
import com.mpolicy.manage.modules.insurance.service.InsurancePortfolioInfoService;
import com.mpolicy.manage.modules.insurance.service.InsuranceRecommendConditionService;
import com.mpolicy.manage.modules.insurance.service.InsuranceRecommendProductService;
import com.mpolicy.manage.modules.insurance.vo.PortfolioBean;
import com.mpolicy.manage.modules.insurance.vo.PortfolioList;
import com.mpolicy.manage.modules.insurance.vo.RecommendCondtionBean;
import com.mpolicy.manage.modules.insurance.vo.RecommendGoodsBean;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Slf4j
@Service("insuranceRecommendProductService")
public class InsuranceRecommendProductServiceImpl extends ServiceImpl<InsuranceRecommendProductDao, InsuranceRecommendProductEntity> implements InsuranceRecommendProductService {

    @Autowired
    private RecommendMapper recommendMapper;

    @Autowired
    private InsurancePortfolioInfoService insurancePortfolioInfoService;

    @Autowired
    private InsuranceRecommendConditionService insuranceRecommendConditionService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        Integer conditionId = Integer.valueOf((String)params.get("conditionId"));
        log.info("!!!!!!!!!!!!category= {}",conditionId);
        IPage<InsuranceRecommendProductEntity> page = this.page(
                new Query<InsuranceRecommendProductEntity>().getPage(params),
                new LambdaQueryWrapper<InsuranceRecommendProductEntity>().eq(conditionId != null,InsuranceRecommendProductEntity::getRecommendConditionId,conditionId)
        );
//        String type = "CONDITION_DATA_TYPE";
//        List<DicCacheHelper.DicEntity> list = DicCacheHelper.getSons(type);
        List<RecommendGoodsBean> recommendCondtionBeanList = Lists.newArrayList();
        page.getRecords().forEach(x -> {
            InsurancePortfolioInfoEntity portfolioBean = insurancePortfolioInfoService.lambdaQuery().eq(InsurancePortfolioInfoEntity::getPortfolioCode,x.getPortfolioCode()).one();
            RecommendGoodsBean bean = recommendMapper.buildFromInsuranceInsuranceRecommendProductEntity(x);
            bean.setGoodsName(portfolioBean.getPortfolioName());
//            rangeHandleByCategory(bean);
            recommendCondtionBeanList.add(bean);
        });
        buildCustomizeTypeDesc(conditionId,recommendCondtionBeanList);
        return new PageUtils(recommendCondtionBeanList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Transactional
    @Override
    public void move(List<RecommendGoodsBean> recommendGoodsBeanList) {
        UpdateWrapper<InsuranceRecommendProductEntity> updateWrapper = null;
        for(RecommendGoodsBean bean : recommendGoodsBeanList){
            updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(InsuranceRecommendProductEntity::getSort,bean.getSort()).eq(InsuranceRecommendProductEntity::getId,bean.getInsuranceRecommendProductId());
            update(updateWrapper);
        }
    }

    private void buildCustomizeTypeDesc(Integer conditionId,List<RecommendGoodsBean> recommendCondtionBeanList){
        InsuranceRecommendConditionEntity insuranceRecommendConditionEntity = insuranceRecommendConditionService.getById(conditionId);
        if(ProdTypeEnum.ZJ.getProdType().equals(insuranceRecommendConditionEntity.getCategory())){
            List<DicCacheHelper.DicEntity> list = DicCacheHelper.getSons("STRICKEN_TYPE");
            Map<String, String> mapDict = Maps.newHashMap();
            for(DicCacheHelper.DicEntity entity : list){
                mapDict.put(entity.getKey(),entity.getValue());
            }
            recommendCondtionBeanList.stream().forEach(e->{
                e.setCustomizeTypeDesc(mapDict.get(e.getCustomizeType()));
            });
        }
    }
}
