package com.mpolicy.manage.modules.tools.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.tools.entity.ToolsCommonWordsEntity;

import java.util.Map;

/**
 * 测试报告常用语
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-23 16:50:03
 */
public interface ToolsCommonWordsService extends IService<ToolsCommonWordsEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    boolean saveEntity(ToolsCommonWordsEntity toolsCommonWordsEntity);
}

