package com.mpolicy.manage.modules.agent.dao;

import com.mpolicy.manage.modules.agent.entity.AgentAccessoryEntity;
import com.mpolicy.manage.modules.agent.vo.resp.AgentAccessoryRespVo;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 经纪人人员附件
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:48
 */
public interface AgentAccessoryDao extends ImsBaseMapper<AgentAccessoryEntity> {

    List<AgentAccessoryRespVo> listGroupByType(@Param("agentCode") String agentCode);
}
