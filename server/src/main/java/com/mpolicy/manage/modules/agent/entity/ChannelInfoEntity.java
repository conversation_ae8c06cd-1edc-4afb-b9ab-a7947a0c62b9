package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * 渠道信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 16:37:12
 */
@TableName("channel_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    @NotBlank(message = "渠道编码不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String channelCode;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称", required = true)
    @NotBlank(message = "渠道名称不能为空", groups = {AddGroup.class})
    private String channelName;
    /**
     * 社会信用统一代码
     */
    @ApiModelProperty(value = "社会信用统一代码", required = true)
    @Pattern(regexp = "^([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})$", message = "社会信用统一代码格式不正确", groups = {AddGroup.class, UpdateGroup.class})
    private String uniformSocialCreditCode;
    /**
     * 个人:姓名 企业:法人姓名
     */
    @ApiModelProperty(value = "个人:姓名 企业:法人姓名")
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class})
    private String name;
    /**
     * 个人:身份证号 企业:法人身份证号
     */
    @ApiModelProperty(value = "个人:身份证号 企业:法人身份证号")
    @NotBlank(message = "身份证号不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X|x)$", message = "身份证号格式不正确", groups = {AddGroup.class, UpdateGroup.class})
    private String idCard;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号格式不正确", groups = {AddGroup.class, UpdateGroup.class})
    private String mobile;
    /**
     * 渠道性质 0:企业;1:个人
     */
    @ApiModelProperty(value = "渠道类型 0:企业;1:个人", required = true)
    @Range(min = 0, max = 1, message = "渠道类型只能为0或1", groups = {AddGroup.class, UpdateGroup.class})
    private Integer channelType;
    /**
     * 渠道分类 0:普通渠道，1:高客渠道，2:农村业务
     */
    @ApiModelProperty(value = "渠道分类 0:普通渠道，1:高客渠道，2:农村业务，4：百川渠道", required = true)
    @Range(min = 0, max = 4, message = "渠道类型限制为0-4", groups = {AddGroup.class, UpdateGroup.class})
    private Integer channelClassification;
    /**
     * 启用状态 1:启用;0:关闭
     */
    @ApiModelProperty(value = "启用状态 1:启用;0:关闭")
    @Range(min = 0, max = 1, message = "启用状态之能为0或1", groups = {AddGroup.class, UpdateGroup.class})
    private Integer enabled;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;
    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    @Length(max = 20,message = "开户银行超过20字符", groups = {AddGroup.class, UpdateGroup.class})
    private String bandName;
    /**
     * 开户支行名称
     */
    @ApiModelProperty(value = "开户支行名称")
    @Length(max = 50,message = "开户支行名称超过50字符", groups = {AddGroup.class, UpdateGroup.class})
    private String bandBranchName;
    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    @Length(max = 30,message = "银行卡长度超过限制", groups = {AddGroup.class, UpdateGroup.class})
    private String bandCardNum;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁", notes = "更新时必须存在")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
}
