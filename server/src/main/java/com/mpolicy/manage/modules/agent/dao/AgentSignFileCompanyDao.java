package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileCompanyEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFileDetailPageList;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 代理人文件签约公司信息
 * 
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileCompanyDao extends BaseMapper<AgentSignFileCompanyEntity> {

    /**
     * 分页查询指定文件统计详情信息
     * @param page 分页信息
     * @param input 分页条件
     * @return com.mpolicy.manage.modules.agent.vo.sign.AgentSignFileDetailPageList 分页结果
     */
    IPage<AgentSignFileDetailPageList> detailPageList(@Param("page") IPage page, @Param("input") Map<String, Object> input);
}
