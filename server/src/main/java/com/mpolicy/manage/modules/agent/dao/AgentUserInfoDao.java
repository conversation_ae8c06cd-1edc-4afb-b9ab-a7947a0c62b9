package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agent.entity.AgentPageListOut;
import com.mpolicy.manage.modules.agent.entity.AgentPageListVo;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.vo.AgentUserInfoExportVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentUserInfoVo;
import com.mpolicy.manage.modules.chat.entity.AgentUseAppListOut;
import com.mpolicy.manage.modules.chat.entity.AgentUserAppListOutVo;
import com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 经纪人用户信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentUserInfoDao extends BaseMapper<AgentUserInfoEntity> {

    /**
     * 获取代理人详情信息
     *
     * @param agentCode
     * @return
     */
    AgentUserInfoVo findAgentInfo(String agentCode);

    /**
     * 分页获取经纪人用户信息表列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<AgentPageListOut> findAgentPageList(@Param("page") IPage page, @Param("input") AgentPageListVo input);

    /**
     * 获取导出数据列表
     *
     * @param input
     * @return
     */
    List<AgentUserInfoExportVo> findExportList(@Param("input") AgentPageListVo input);

    List<FastAgentUserInfo> listFastAgentUserInfo(@Param("agentCodeList") List<String> agentCodeList);

    /**
     * 获取代理人使用情况列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<AgentUseAppListOut> findPageAgentUseAppList(@Param("page") IPage<AgentUseAppListOut> page, @Param("input") AgentUserAppListOutVo input);

    FastAgentUserInfo queryOne(@Param("agentCode")String agentCode);

    /**
     * 根据时间查询在职代理人编码
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> findOnlineAgentCodeList(@Param("startTime") String startTime,@Param("endTime") String endTime);

    List<FastAgentUserInfo> listByBusinessCode(@Param("businessCodeList")List<String> businessCode);
}
