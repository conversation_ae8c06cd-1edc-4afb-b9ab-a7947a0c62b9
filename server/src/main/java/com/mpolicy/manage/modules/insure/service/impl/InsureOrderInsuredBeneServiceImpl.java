package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredBeneDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredBeneEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderInsuredBeneService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投保订单受益人信息接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Service("insureOrderInsuredBeneService")
public class InsureOrderInsuredBeneServiceImpl extends ServiceImpl<InsureOrderInsuredBeneDao, InsureOrderInsuredBeneEntity> implements InsureOrderInsuredBeneService {

}
