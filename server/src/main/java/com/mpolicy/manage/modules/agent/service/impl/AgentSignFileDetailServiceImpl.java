package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.agent.dao.AgentSignFileDetailDao;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileCompanyEntity;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileDetailEntity;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.enums.AgentSignFileStatusEnum;
import com.mpolicy.manage.modules.agent.service.AgentSignFileCompanyService;
import com.mpolicy.manage.modules.agent.service.AgentSignFileDetailService;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.agent.util.AgentConstantUtil;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonDetailPageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonFilePageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignNumOut;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description 代理人签约详细信息service实现
 * @return
 * @Date 2023/5/29 18:06
 * <AUTHOR>
 **/
@Service("agentSignFileDetailService")
@Slf4j
public class AgentSignFileDetailServiceImpl extends ServiceImpl<AgentSignFileDetailDao, AgentSignFileDetailEntity> implements AgentSignFileDetailService {

    @Autowired
    private AgentSignFileCompanyService agentSignFileCompanyService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private AgentUserInfoService agentUserInfoService;

    @Override
    public AgentSignNumOut querySignNumByFileCode(String fileCode,List<String> orgCodeList) {
        return querySignNum(fileCode,orgCodeList);
    }

    @Override
    public AgentSignNumOut querySignNumByCodeAndCompanyId(String fileCode, List<Integer> companyIdList) {
        AgentSignNumOut result = new AgentSignNumOut();
        List<AgentSignFileDetailEntity> list = this.lambdaQuery().eq(AgentSignFileDetailEntity::getFileCode, fileCode)
                .in(CollectionUtil.isNotEmpty(companyIdList),AgentSignFileDetailEntity::getCompanyId,companyIdList).list();
        result.setShouldNum(list.size());
        List<AgentSignFileDetailEntity> alreadyList = list.stream().filter(a->AgentSignFileStatusEnum.STATUS1.getCode().equals(a.getSignStatus())).collect(Collectors.toList());
        result.setAlreadyNum(alreadyList.size());
        List<AgentSignFileDetailEntity> unsignedList = list.stream().filter(a->AgentSignFileStatusEnum.STATUS0.getCode().equals(a.getSignStatus())).collect(Collectors.toList());
        result.setUnsignedNum(unsignedList.size());
        List<AgentSignFileDetailEntity> defaultList = list.stream().filter(a->AgentSignFileStatusEnum.STATUS2.getCode().equals(a.getSignStatus())).collect(Collectors.toList());
        result.setDefaultFinishNum(defaultList.size());
        return result;
    }

    /**
     * 统计签署人数
     * @param fileCode 文件编码
     * @param orgCodeList 组织机构orgCode
     */
    private AgentSignNumOut querySignNum(String fileCode, List<String> orgCodeList){
        AgentSignNumOut result = new AgentSignNumOut();
        //应签署人数
        //1.根据文件编码 统计公司orgCode
        List<AgentSignFileCompanyEntity> companyList = agentSignFileCompanyService.lambdaQuery().eq(AgentSignFileCompanyEntity::getFileCode, fileCode)
                .in(CollectionUtil.isNotEmpty(orgCodeList),AgentSignFileCompanyEntity::getOrgCode,orgCodeList).list();
        AtomicInteger shouldNum = new AtomicInteger(0);
        AtomicInteger alreadyNum = new AtomicInteger(0);
        AtomicInteger unsignedNum = new AtomicInteger(0);
        AtomicInteger defaultFinishNum = new AtomicInteger(0);
        companyList.forEach(a->{
            List<String> personOrgCodeList = Lists.newArrayList();
            personOrgCodeList.add(a.getOrgCode());
            List<OrgInfoVo> orgSonList = orgInfoService.getOrgSonList(a.getOrgCode());
            orgSonList.stream().forEach(b-> personOrgCodeList.add(b.getOrgCode()));
            //2.根据orgCode 统计代理人所属orgCode 人数
            List<AgentUserInfoEntity> agentList = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                    .eq(AgentUserInfoEntity::getAgentType,a.getAgentType())
                    .in(AgentUserInfoEntity::getOrgCode, personOrgCodeList).list();
            shouldNum.set(shouldNum.addAndGet(agentList.size()));
            //已签署人数
            List<AgentSignFileDetailEntity> signList = this.lambdaQuery().eq(AgentSignFileDetailEntity::getFileCode, fileCode)
                    .eq(AgentSignFileDetailEntity::getCompanyId,a.getId()).list();
            long alreadyNumTemp = signList.stream().filter(b-> AgentSignFileStatusEnum.STATUS1.getCode() == b.getSignStatus()).count();
            alreadyNum.set(alreadyNum.addAndGet(new Long(alreadyNumTemp).intValue()));
            //未签署人数
            List<String> signPersonCode = signList.stream().map(b->b.getAgentCode()).collect(Collectors.toList());
            long unsignedNumTemp = agentList.stream().filter(b -> !signPersonCode.contains(b.getAgentCode())).count();
            unsignedNum.set(unsignedNum.addAndGet(new Long(unsignedNumTemp).intValue()));
            //默认签署人数
            long defaultFinishNumTemp = signList.stream().filter(b-> AgentSignFileStatusEnum.STATUS2.getCode() == b.getSignStatus()).count();
            defaultFinishNum.set(defaultFinishNum.addAndGet(new Long(defaultFinishNumTemp).intValue()));
        });
        result.setAlreadyNum(alreadyNum.get());
        result.setShouldNum(shouldNum.get());
        result.setUnsignedNum(unsignedNum.get());
        result.setDefaultFinishNum(defaultFinishNum.get());
        return result;
    }


    @Override
    public PageUtils<AgentPersonFilePageList> personFileList(Map<String, Object> params) {
        Integer page = RequestUtils.objectValueToInteger(params,"page");
        Integer limit = RequestUtils.objectValueToInteger(params,"limit");
        String orgCode = RequestUtils.objectValueToString(params,"orgCode");
        //获取组织机构子集
        if(StringUtils.isNotBlank(orgCode)){
            List<String> childNode = orgInfoService.getChildNode(orgCode);
            params.put("orgCodeList",childNode);
        }
        //查询结果
        IPage<AgentPersonFilePageList> iPage = this.baseMapper.personFileList(new Page(page, limit), params);
        //代理人类型
        Map<String, String> agentTypeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_TYPE);
        List<AgentPersonFilePageList> list = iPage.getRecords().stream().map(a->{
            AgentPersonFilePageList bean = new AgentPersonFilePageList();
            BeanUtils.copyProperties(a,bean);
            bean.setQuitStatusDesc(a.getQuitStatus()==0?"在职":"离职");
            bean.setAgentTypeDesc(agentTypeMap.get(a.getAgentType()));
            List<AgentSignFileDetailEntity> detailList = this.baseMapper.queryAllEnabledFile(a.getAgentCode());
            bean.setShouldNum(detailList.size());
            List<AgentSignFileDetailEntity> alreadyList = detailList.stream().filter(b->AgentSignFileStatusEnum.STATUS1.getCode().equals(b.getSignStatus())).collect(Collectors.toList());
            bean.setAlreadyNum(alreadyList.size());
            List<AgentSignFileDetailEntity> unsignedList = detailList.stream().filter(b->AgentSignFileStatusEnum.STATUS0.getCode().equals(b.getSignStatus())).collect(Collectors.toList());
            bean.setUnsignedNum(unsignedList.size());
            List<AgentSignFileDetailEntity> defaultList = detailList.stream().filter(b->AgentSignFileStatusEnum.STATUS2.getCode().equals(b.getSignStatus())).collect(Collectors.toList());
            bean.setDefaultFinishNum(defaultList.size());
            return bean;
        }).collect(Collectors.toList());



        return new PageUtils<>(list,(int)iPage.getTotal(),(int)iPage.getSize(),(int)iPage.getCurrent());
    }

    /**
     * 构建代理人维度 应签署文件数
     * @return
     */
    private Integer personShouldNum(String agentType,String orgCode,List<AgentSignFileCompanyEntity> companyList,Map<String,List<String>> orgMap){
        companyList = companyList.stream().filter(a->a.getAgentType().equals(agentType)).filter(a->{
            List<String> orgCodeList = orgMap.get(a.getOrgCode());
            if(orgCodeList.contains(orgCode)){
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        return companyList.size();
    }

    @Override
    public PageUtils<AgentPersonDetailPageList > detailPersonFileList(String agentCode, Map<String, Object> params) {
        AgentUserInfoEntity user = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode, agentCode).one();
        if(Objects.isNull(user)){
            return new PageUtils<>(new ArrayList<>(), 0, 1, 1);
        }
        //文件信息查询
        Integer page = RequestUtils.objectValueToInteger(params,"page");
        Integer limit = RequestUtils.objectValueToInteger(params,"limit");

        IPage<AgentPersonDetailPageList> iPage = this.baseMapper.detailPersonFileList(new Page(page, limit), params);
        List<AgentPersonDetailPageList> list = iPage.getRecords().stream().map(a->{
            AgentPersonDetailPageList bean = new AgentPersonDetailPageList();
            BeanUtils.copyProperties(a,bean);
            if(Objects.isNull(a.getAgentCode())){
                bean.setAgentCode(agentCode);
            }
            if(Objects.isNull(a.getSignStatus())){
                bean.setSignStatus(0);
            }
            AgentSignFileStatusEnum agentSignFileStatusEnum = AgentSignFileStatusEnum.getValueByKey(bean.getSignStatus());
            if(Objects.nonNull(agentSignFileStatusEnum)){
                bean.setSignStatusDesc(agentSignFileStatusEnum.getName());
            }
            return bean;
        }).collect(Collectors.toList());
        return new PageUtils<>(list, (int)iPage.getTotal(),(int) iPage.getSize(),(int) iPage.getCurrent());
    }


    @Override
    public void generateFile(String agentCode) {
        log.info("代理人agentCode={},开始生成要签署文件信息");
        //0.头生成，删除以前存在的文件信息
//        this.lambdaUpdate().eq(AgentSignFileDetailEntity::getAgentCode,agentCode)
//                .set(AgentSignFileDetailEntity::getDeleted,-1)
//                .update();
        //1. 查询用户信息
        AgentUserInfoEntity user = this.agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode, agentCode).one();
        if(Objects.isNull(user)){
            return ;
        }
        List<String> orgCodeList = Lists.newArrayList();
        orgCodeList.add(user.getOrgCode());
        Map<String, OrgInfoEntity> firstOrgInfo = this.orgInfoService.findFirstOrgInfo(orgCodeList);
        OrgInfoEntity orgInfoEntity = firstOrgInfo.get(user.getOrgCode());
        if(Objects.isNull(orgInfoEntity)){
            return ;
        }
        //2.查询要生成的文件信息
        List<AgentSignFileCompanyEntity> list = this.agentSignFileCompanyService.lambdaQuery().eq(AgentSignFileCompanyEntity::getAgentType, user.getAgentType())
                .eq(AgentSignFileCompanyEntity::getOrgCode, orgInfoEntity.getOrgCode())
                .list();
        //3.生成未签署的文件信息
        if(CollectionUtil.isNotEmpty(list)){
            List<AgentSignFileDetailEntity> collect = list.stream().map(a -> {
                AgentSignFileDetailEntity bean = new AgentSignFileDetailEntity();
                bean.setAgentCode(agentCode);
                bean.setOrgCode(user.getOrgCode());
                bean.setCompanyId(a.getId());
                bean.setCompanyOrgCode(a.getOrgCode());
                bean.setFileCode(a.getFileCode());
                bean.setSignStatus(AgentSignFileStatusEnum.STATUS0.getCode());
                return bean;
            }).collect(Collectors.toList());
            log.info("代理人agentCode={},生成的文件内容为={}",agentCode, JSONObject.toJSONString(collect));
            this.saveBatch(collect);
        }
        log.info("代理人agentCode={},生成要签署文件信息结束");
    }
}
