package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人工作室IP
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 11:09:17
 */
@TableName("agent_studio_ip")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentStudioIpEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(hidden = true)
    private String agentCode;
    /**
     * 工作室勋章
     */
    @ApiModelProperty(value = "工作室勋章")
    private String studioTag;
    /**
     * 知乎名称
     */
    @ApiModelProperty(value = "知乎名称")
    private String zhihuName;
    /**
     * 知乎粉丝数
     */
    @ApiModelProperty(value = "知乎粉丝数")
    private String zhihuFans;
    /**
     * 知乎认证
     */
    @ApiModelProperty(value = "知乎认证")
    private String zhihuVerified;
    /**
     * 知乎标签
     */
    @ApiModelProperty(value = "知乎标签")
    private String zhihuTag;
    /**
     * 知乎文章数
     */
    @ApiModelProperty(value = "知乎文章数")
    private Integer zhihuCount;
    /**
     * 微博名
     */
    @ApiModelProperty(value = "微博名")
    private String weiboName;
    /**
     * 微博粉丝数
     */
    @ApiModelProperty(value = "微博粉丝数")
    private String weiboFans;
    /**
     * 微博认证
     */
    @ApiModelProperty(value = "微博认证")
    private String weiboVerified;
    /**
     * 微博介绍
     */
    @ApiModelProperty(value = "微博介绍")
    private String weiboIntro;
    /**
     * 微博文章数
     */
    @ApiModelProperty(value = "微博文章数")
    private Integer weiboCount;
    /**
     * 公众号账号
     */
    @ApiModelProperty(value = "公众号账号")
    private String wechatPublicPlatformName;
    /**
     * 公众号账号介绍
     */
    @ApiModelProperty(value = "公众号账号介绍")
    private String wechatPublicPlatformIntro;
    /**
     * 公众号账号文章数
     */
    @ApiModelProperty(value = "公众号账号文章数")
    private Integer wechatPublicPlatformCount;
    /**
     * 抖音名
     */
    @ApiModelProperty(value = "抖音名")
    private String douyinName;
    /**
     * 抖音粉丝数
     */
    @ApiModelProperty(value = "抖音粉丝数")
    private String douyinFans;
    /**
     * 抖音点赞数
     */
    @ApiModelProperty(value = "抖音点赞数")
    private String douyinLikeNum;
    /**
     * 抖音认证
     */
    @ApiModelProperty(value = "抖音认证")
    private String douyinVerified;
    /**
     * 抖音介绍
     */
    @ApiModelProperty(value = "抖音介绍")
    private String douyinIntro;
    /**
     * 抖音文章数
     */
    @ApiModelProperty(value = "抖音文章数")
    private Integer douyinCount;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
