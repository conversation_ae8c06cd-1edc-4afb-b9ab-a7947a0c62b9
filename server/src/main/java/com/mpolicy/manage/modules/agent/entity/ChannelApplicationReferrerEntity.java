package com.mpolicy.manage.modules.agent.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 推荐人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
@TableName("channel_application_referrer")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelApplicationReferrerEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty(value = "id 更新时必传")
    @TableId
    private Integer id;
    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人编码")
    private String referrerCode;
    /**
     * 渠道分支编码
     */
    @ApiModelProperty(value = "渠道分支编码")
    private String branchCode;
    /**
     * 应用编码
     */
    @ApiModelProperty(hidden = true)
    private String applicationCode;
    /**
     * 渠道名
     */
    @ApiModelProperty(value = "渠道名 仅返回")
    @TableField(exist = false)
    private String channelName;
    /**
     * 应用备注
     */
    @ApiModelProperty(value = "应用备注 仅返回")
    @TableField(exist = false)
    private String applicationComment;
    /**
     * 渠道编码
     */
    @ApiModelProperty(hidden = true)
    private String channelCode;
    /**
     * 渠道+分支编码
     */
    @ApiModelProperty(hidden = true)
    private String channelBranchCode;
    /**
     * 所属机构
     */
    private String referrerChannelName;
    /**
     * 渠道分类 0:普通渠道，1:高客渠道，2:农村业务
     */
    @ApiModelProperty(hidden = true)
    private Integer channelClassification;
    /**
     * 推荐人工号
     */
    @ApiModelProperty(value = "推荐人工号")
    @ExcelProperty(value = "推荐人工号")
    private String referrerWno;

    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人上级编码")
    private String referrerParentCode;
    /**
     * 上级姓名
     */
    @ApiModelProperty(value = "上级姓名 仅返回")
    @TableField(exist = false)
    private String referrerParentName;
    /**
     * 推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名", required = true)
    @NotBlank(message = "推荐人姓名不能为空")
    @ExcelProperty(value = "推荐人姓名")
    private String referrerName;
    /**
     * 推荐人手机号
     */
    @ApiModelProperty(value = "推荐人手机号", required = true)
    // 该版本暂时删除该验证 2021年7月12日17点41分
    /// @NotBlank(message = "推荐人手机号不能为空")
    @ExcelProperty(value = "推荐人手机号")
    private String referrerMobile;
    /**
     * 推荐人所属区域
     */
    @ApiModelProperty(value = "推荐人所属区域")
    @ExcelProperty(value = "推荐人所属区域")
    private String referrerRegion;
    @ApiModelProperty(value = "推荐人所属片区")
    @ExcelProperty(value = "推荐人所属片区")
    private String referrerSubregion;
    /**
     * 推荐人层级 1：推荐人；2：扩展人
     */
    @ApiModelProperty(value = "推荐人层级 1：推荐人；2：扩展人", required = true)
    @NotNull(message = "推荐人层级不能为空")
    @Range(min = 0, max = 2, message = "推荐人层级 只能为1和2")
    private Integer referrerLevel;
    /**
     * 推荐人二维码
     */
    @ApiModelProperty(value = "推荐人二维码 仅返回")
    @ExcelProperty(value = "推荐人二维码")
    private String filePath;
    /**
     * 启用状态 1:启用;0:关闭
     */
    @ApiModelProperty(value = "启用状态 1:启用;0:关闭", required = true)
    @NotNull(message = "启用状态不能为空")
    @Range(min = 0, max = 1, message = "启用状态 只能为0和1")
    @ExcelProperty(value = "启用状态 1:启用;0:关闭")
    private Integer enabled;
    /**
     * 业绩
     */
    @ApiModelProperty(value = "业绩")
    private BigDecimal performance;
    /**
     * 推荐人备注
     */
    @ApiModelProperty(value = "推荐人备注")
    private String referrerComment;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁 更新时必传")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

    @ApiModelProperty(value = "在职状态")
    private Integer referrerServiceStatus;

    @ApiModelProperty(value = "交接状态")
    private Integer handoverStatus;
    @ApiModelProperty(value = "交接人工号")
    private String handoverReferrerWno;
    @TableField(exist = false)
    private String handoverReferrerWnoName;
    @ApiModelProperty(value = "交接时间")
    private Date handoverTime;

    /**
     * 推荐人原渠道编码
     */
    private String referrerOgrCode;

    /**
     * 推荐人原渠道编码
     */
    private String referrerOgrName;
}
