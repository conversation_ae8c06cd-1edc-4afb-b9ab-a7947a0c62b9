package com.mpolicy.manage.modules.policy.vo.policy;

import com.mpolicy.policy.common.ep.policy.EpContractAttachInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("合同影像件变更对象")
public class PolicyImageUpdateInput {

    @ApiModelProperty(
            value = "合同编码",
            example = "ct20240913112400vcGBsH"
    )
    private String contractCode;

    @ApiModelProperty(
            value = "电子保单地址",
            example = "https://pc-zatest1.zhongan.com/open/common/downloadFileScreen/downloadDoc.json?data=e926531c53baba0ced8d9ccfdcd1e0f9282dc2bc"
    )
    private String policyUrl;

    @ApiModelProperty("附件列表")
    private List<EpContractAttachInfoVo> contractAttachList;
}
