package com.mpolicy.manage.modules.sell.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftInfoEntity;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 销售产品的赠品管理信息dao
 *
 *
 *
 * @create 2024/12/2
 * @since 1.0.0
 */
public interface SellProductGiftInfoDao extends BaseMapper<SellProductGiftInfoEntity> {
    /**
     *
     * 新增库存，number必须为正数
     *
     * @param id
     *
     * 库存id
     *
     * @param number
     *
     * 要新增的库存
     *
     * @return
     */
    Integer addStock(@Param("id") Long id, @Param("number") Integer number);
    /**
     *
     * 减少库存，number必须为正数
     *
     * @param id
     *
     * 库存id
     *
     * @param number
     *
     * 要扣减的库存
     *
     * @return
     */
    Integer subtractStock(Long id, Integer number);
}
