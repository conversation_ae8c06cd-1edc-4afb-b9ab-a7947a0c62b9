package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 渠道应用
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 19:32:39
 */
@TableName("channel_application")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelApplicationEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码")
    @NotNull(message = "应用编码不能为空", groups = UpdateGroup.class)
    private String applicationCode;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码", required = true)
    @NotNull(message = "渠道编码不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String channelCode;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    @TableField(exist = false)
    private String channelName;
    /**
     * 渠道类型 0:企业;1:个人
     */
    @ApiModelProperty(value = "渠道类型 0:企业;1:个人")
    @TableField(exist = false)
    private Integer channelType;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    /**
     * 应用类型 0:小程序获客
     */
    @ApiModelProperty(value = "应用类型 0:小程序获客", required = true)
    @Range(min = 0, max = 0, message = "应用类型只能为0", groups = {AddGroup.class, UpdateGroup.class})
    private Integer applicationType;
    /**
     * 应用备注
     */
    @ApiModelProperty(value = "应用备注", required = true)
    @NotNull(message = "应用备注必须存在", groups = {AddGroup.class, UpdateGroup.class})
    private String applicationComment;
    /**
     * 顾问规则 0:指定;1:范围;2:自由
     */
    @ApiModelProperty(value = "顾问规则 0:指定;1:范围;2:自由", required = true)
    @Range(min = 0, max = 2, message = "顾问规则只能为0，1，2", groups = {AddGroup.class, UpdateGroup.class})
    @NotNull(message = "顾问规则不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Integer agentSelectType;
    /**
     * 顾问列表
     */
    @ApiModelProperty(value = "顾问列表", required = true)
    @TableField(exist = false)
    private List<AgentInfoVo> agentInfoVoList;

    /**
     * 启用状态 1:启用;0:关闭
     */
    @ApiModelProperty(value = "启用状态 1:启用;0:关闭")
    @Range(min = 0, max = 1, message = "应用类型只能为0，1", groups = {AddGroup.class, UpdateGroup.class})
    private Integer enabled;

    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁", notes = "更新时必须存在")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
}
