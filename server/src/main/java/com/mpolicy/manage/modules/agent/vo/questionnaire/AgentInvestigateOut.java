package com.mpolicy.manage.modules.agent.vo.questionnaire;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AgentInvestigateOut {

    //文件名称
    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "问卷主键")
    private Integer questionnaireId;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private String submitTime;

    @ApiModelProperty(value = "执业证编码")
    private String practiceCertificateCode;

}
