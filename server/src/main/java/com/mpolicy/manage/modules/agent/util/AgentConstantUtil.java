package com.mpolicy.manage.modules.agent.util;

import com.mpolicy.service.common.service.DicCacheHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/31 14:11
 */
public class AgentConstantUtil {
    public static String AGENT_POSITION = "AGENT_POSITION";
    public static String ID_CARD_TYPE = "ID_CARD_TYPE";
    public static String ENTRY_TYPE = "ENTRY_TYPE";
    public static String AGENT_DEGREE = "AGENT_DEGREE";
    public static String AGENT_POLITICS = "AGENT_POLITICS";
    public static String AGENT_TYPE = "AGENT:AGENT_TYPE";
    public static String AGENT_NATURE = "AGENT:AGENT_NATURE";
    public static String ACQUISITION_AREA = "ACQUISITION_AREA";
    public static String NATIONALITY_LIST = "NATIONALITY_LIST";
    public static String COUNTRY_LIST = "COUNTRY_LIST";
    public static String SELL_PRODUCT_AREA = "SELL_PRODUCT_AREA";

    public static Map<String, String> getDic2Map(String type) {
        List<DicCacheHelper.DicEntity> sons = DicCacheHelper.getSons(type);
        if (sons == null) {
            return new HashMap<>();
        }
        return sons.stream().collect(
                Collectors.toMap(DicCacheHelper.DicEntity::getKey,
                        DicCacheHelper.DicEntity::getValue));
    }
}
