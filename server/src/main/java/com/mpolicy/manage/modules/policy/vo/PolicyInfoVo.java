package com.mpolicy.manage.modules.policy.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 保单管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 15:48
 */
@Data
public class PolicyInfoVo {
    private static final long serialVersionUID = 1L;
    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    /**
     * 上一年保单号
     */
    @ApiModelProperty(value = "上一年保单号")
    private String sourcePolicyNo;
    /**
     * 投保单号
     */
    @ApiModelProperty(value = "投保单号")
    private String applicantPolicyNo;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String policyProductType;
    /**
     * 产品类型Code
     */
    @ApiModelProperty(value = "产品类型Code")
    private String policyProductTypeCode;
    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    private String policyStatus;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String portfolioName;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal premiumTotal;
    /**
     * 交单日期
     */
    @ApiModelProperty(value = "交单日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date orderTime;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date enforceTime;
    /**
     * 保险公司
     */
    @ApiModelProperty(value = "保险公司")
    private String companyName;
    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    private String applicantName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 代理人
     */
    @ApiModelProperty(value = "代理人")
    private String agentName;
    /**
     * 推荐人
     */
    @ApiModelProperty(value = "推荐人")
    private String policyReferrerName;
    /**
     * 渠道推荐人
     */
    @ApiModelProperty(value = "渠道推荐人")
    private String referrerName;

    @ApiModelProperty(value = "渠道推荐人-工号")
    private String referrerWno;

    @ApiModelProperty(value = "初始渠道推荐人")
    private String customerManagerName;

    @ApiModelProperty(value = "初始渠道推荐人-工号")
    private String customerManagerChannelCode;
    /**
     * 渠道分支
     */
    @ApiModelProperty(value = "渠道分支")
    private String channelBranchName;
    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    private String channelName;
    /**
     * 保单类型
     */
    @ApiModelProperty(value = "保单类型")
    private String policyType;
    /**
     * 佣金发放状态
     */
    @ApiModelProperty(value = "佣金发放状态")
    private String  settlementStatus;
    /**
     * 保单提交状态
     */
    @ApiModelProperty(value = "保单提交状态")
    private String isSubmit;

}
