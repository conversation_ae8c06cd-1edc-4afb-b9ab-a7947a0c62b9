package com.mpolicy.manage.modules.insure.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.min;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 投保订单列表
 *
 * <AUTHOR>
 * @date 2022-05-29 15:23
 */
@Data
@ApiModel(value = "投保黑名单信息")
public class InsureBlacklistInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * id
     */
    @ApiModelProperty(value = "唯一id", example = "1")
    @NotNull(message = "唯一id缺失", groups = UpdateGroup.class)
    private Integer id;

    /**
     * 黑名单类型编码
     */
    @ApiModelProperty(value = "黑名单类型编码", required = true, example = "FIRST")
    @NotBlank(message = "黑名单类型编码不能为空")
    private String blacklistType;

    /*
    @ApiModelProperty(value = "证件类型编码", required = true, example = "SFZ")
    @NotBlank(message = "证件类型编码不能为空")
    private String identificationType;*/

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true, example = "张三")
    @NotBlank(message = "名称不能为空")
    @Size(max = 30, message = "名称不能长度不能超过30位")
    private String identificationName;

    /**
     * 认证证件号码
     */
    @ApiModelProperty(value = "认证证件号码", required = true, example = "20220506103627429095")
    @NotBlank(message = "证件号码不能为空")
    @Size(max = 30, message = "证件号码长度不能超过30位")
    private String identificationNum;

    /**
     * 黑名单说明
     */
    @ApiModelProperty(value = "黑名单说明", example = "黑名单说明")
    private String blacklistDesc;

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁", example = "1")
    @Min(value = 1, groups = UpdateGroup.class,message = "乐观锁id缺失")
    private long revision;
}
