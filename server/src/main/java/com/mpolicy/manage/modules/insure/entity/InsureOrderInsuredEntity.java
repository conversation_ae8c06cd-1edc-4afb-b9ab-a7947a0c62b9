package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投保订单被保人信息
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_insured")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderInsuredEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单编号
	 */
	private String insureOrderCode;
	/**
	 * 投保被保人唯一编号
	 */
	private String insureOrderInsuredCode;

	/**
	 * 保险业务中台被保人预保单号
	 */
	private String preOrderNo;

	/**
	 * 被保人保单号
	 */
	private String insuredPolicyCode;
	/**
	 * 被保险人是投保人的
	 */
	private String insuredOwnerRela;
	/**
	 * 姓名
	 */
	private String insuredName;
	/**
	 * 被保人总保费
	 */
	private BigDecimal totalPremium;
	/**
	 * 国籍
	 */
	private String insuredNationality;
	/**
	 * 民族
	 */
	private String insuredNation;
	/**
	 * 证件类型
	 */
	private String insuredCertiCode;
	/**
	 * 证件号码
	 */
	private String insuredCertiNo;
	/**
	 * 证件长期有效 是/否
	 */
	private String insuredCertiLongTerm;
	/**
	 * 证件生效日期
	 */
	private String insuredCertiEffectiveDate;
	/**
	 * 证件失效日期
	 */
	private String insuredCertiInvalidDate;
	/**
	 * 出生日期
	 */
	private String insuredBirthDay;
	/**
	 * 年龄
	 */
	private Integer insuredAge;
	/**
	 * 性别
	 */
	private String insuredGender;

	/**
	 * 有无社保
	 */
	private String insuredSocialTag;

	/**
	 * 省份编码
	 */
	private String provincesCode;
	/**
	 * 省份名称
	 */
	private String provincesName;
	/**
	 * 城市编码
	 */
	private String cityCode;
	/**
	 * 城市名称
	 */
	private String cityName;
	/**
	 * 区编码
	 */
	private String areaCode;
	/**
	 * 区名称
	 */
	private String areaName;
	/**
	 * 详细地址
	 */
	private String insuredAddress;
	/**
	 * 邮政编码
	 */
	private String insuredZip;
	/**
	 * 职业类别
	 */
	private String insuredJobType;
	/**
	 * 职业编码一级
	 */
	private String insuredJobCodeLevel1;

	/**
	 * 职业名称一级
	 */
	private String insuredJobCodeName1;

	/**
	 * 职业编码二级
	 */
	private String insuredJobCodeLevel2;

	/**
	 * 职业名称二级
	 */
	private String insuredJobCodeName2;

	/**
	 * 职业编码三级
	 */
	private String insuredJobCodeLevel3;
	/**
	 * 职业名称三级
	 */
	private String insuredJobCodeName3;
	/**
	 * 工作单位类型
	 */
	private String insuredWorkCompanyType;
	/**
	 * 单位/学校名称
	 */
	private String insuredWorkCompanyName;
	/**
	 * 手机号码
	 */
	private String insuredMobile;
	/**
	 * 邮箱
	 */
	private String insuredEmail;
	/**
	 * 收入
	 */
	private String insuredIncome;
	/**
	 * 身高
	 */
	private String insuredHeight;
	/**
	 * 体重
	 */
	private String insuredWeight;
	/**
	 * 有法定受益人
	 */
	private String insuredHaveLegalBeneficiary;
	/**
	 * 婚姻状况
	 */
	private String insuredMaritalStat;
	/**
	 * 健康状况
	 */
	private String insuredHealthStatus;
	/**
	 * 是否抽烟
	 */
	private String insuredIsSmoking;
	/**
	 * 办公电话
	 */
	private String insuredJobTel;
	/**
	 * 住宅电话
	 */
	private String insuredHomeTel;
	/**
	 * 受益人类型编码
	 */
	private String beneType;
	/**
	 * 健康告知信息
	 */
	private String healthData;
	/**
	 * 智能核保类型，内部/外部
	 */
	private String intHealthFlag;
	/**
	 * 智能核保信息
	 */
	private String intHealthData;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
