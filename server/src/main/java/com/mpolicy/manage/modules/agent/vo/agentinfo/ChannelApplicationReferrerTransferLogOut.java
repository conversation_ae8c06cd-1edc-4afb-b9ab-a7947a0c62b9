package com.mpolicy.manage.modules.agent.vo.agentinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ChannelApplicationReferrerTransferLogOut implements Serializable {
    private static final long serialVersionUID = -4993994072505904580L;

    /**
     * 转移时间
     */
    private String transferTime;
    /**
     * 转移描述
     */
    private String transferDesc;

    /**
     * 推荐人姓名
     */
    private String referrerName;
    /**
     * 工号
     */
    private String referrerWno;

    /**
     * 转移客户数
     */
    private Integer customerNum;
}
