package com.mpolicy.manage.modules.policy.vo.preservation;

import com.mpolicy.manage.modules.policy.entity.EpPolicyApplicantInfoEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.EpPreservationApplicantInfoChangeEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023/12/11 15:57
 * @description: 投保人信息变更保全
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EpPreservationApplicantInfoChangeVo extends EpPreservationApplicantInfoChangeEntity {

    /**
     * 是否存在保人为本人的
     */
    private Map<String, String> meInsuredNameMap;

    /**
     * 投保人转换
     * @param info
     * @return
     */
    public static EpPreservationApplicantInfoChangeVo toVo(EpPolicyApplicantInfoEntity info) {
        final EpPreservationApplicantInfoChangeVo vo = new EpPreservationApplicantInfoChangeVo();
        vo.setContractCode(info.getContractCode());
        vo.setApplicantId(info.getId());
        vo.setApplicantType(info.getApplicantType());
        vo.setApplicantName(info.getApplicantName());
        vo.setApplicantIdType(info.getApplicantIdType());
        vo.setApplicantIdCard(info.getApplicantIdCard());
        vo.setApplicantIdCardValidityStart(info.getApplicantIdCardValidityStart());
        vo.setApplicantIdCardValidityEnd(info.getApplicantIdCardValidityEnd());
        vo.setApplicantIdCardPermanent(info.getApplicantIdCardPermanent());
        vo.setApplicantGender(info.getApplicantGender());
        vo.setApplicantMobile(info.getApplicantMobile());
        vo.setApplicantAge(info.getApplicantAge());
        vo.setApplicantBirthday(info.getApplicantBirthday());
        vo.setApplicantNation(info.getApplicantNation());
        vo.setApplicantNationality(info.getApplicantNationality());
        vo.setApplicantEducation(info.getApplicantEducation());
        vo.setApplicantMarital(info.getApplicantMarital());
        vo.setApplicantAnnualIncome(info.getApplicantAnnualIncome());
        vo.setApplicantCareer(info.getApplicantCareer());
        vo.setApplicantCompany(info.getApplicantCompany());
        vo.setApplicantPosition(info.getApplicantPosition());
        vo.setApplicantEmail(info.getApplicantEmail());
        vo.setApplicantMedicare(info.getApplicantMedicare());
        vo.setApplicantOccupationalCategory(info.getApplicantOccupationalCategory());
        vo.setApplicantIndustryCategory(info.getApplicantIndustryCategory());
        vo.setCompanyNature(info.getCompanyNature());
        vo.setApplicantPostcode(info.getApplicantPostcode());
        vo.setApplicantProvinceCode(info.getApplicantProvinceCode());
        vo.setApplicantCityCode(info.getApplicantCityCode());
        vo.setApplicantRegionCode(info.getApplicantRegionCode());
        vo.setApplicantAddress(info.getApplicantAddress());
        vo.setApplicantTelephone(info.getApplicantTelephone());
        vo.setCompanySocialSecurityNum(info.getCompanySocialSecurityNum());
        vo.setCompanyEmployeeNum(info.getCompanyEmployeeNum());
        vo.setCompanyContactName(info.getCompanyContactName());
        vo.setCompanyContactMobile(info.getCompanyContactMobile());
        vo.setLegalPersonName(info.getLegalPersonName());
        vo.setLegalPersonIdType(info.getLegalPersonIdType());
        vo.setLegalPersonIdCard(info.getLegalPersonIdCard());
        vo.setLegalPersonIdCardValidityStart(info.getLegalPersonIdCardValidityStart());
        vo.setLegalPersonIdCardValidityEnd(info.getLegalPersonIdCardValidityEnd());
        vo.setLegalPersonIdCardPermanent(info.getLegalPersonIdCardPermanent());
        vo.setBankCode(info.getBankCode());
        vo.setBankName(info.getBankName());
        vo.setCardNo(info.getCardNo());
        vo.setCardName(info.getCardName());
        vo.setPaymentPhase(info.getPaymentPhase());
        vo.setRemark(info.getRemark());
        return vo;
    }

    /**
     * 投保人保全转换
     * @param info
     * @return
     */
    public static EpPreservationApplicantInfoChangeVo toVo(EpPreservationApplicantInfoChangeEntity info) {
        final EpPreservationApplicantInfoChangeVo vo = new EpPreservationApplicantInfoChangeVo();
        vo.setId(info.getId());
        vo.setPreservationCode(info.getPreservationCode());
        vo.setType(info.getType());
        vo.setCreateUser(info.getCreateUser());
        vo.setCreateTime(info.getCreateTime());
        vo.setUpdateUser(info.getUpdateUser());
        vo.setUpdateTime(info.getUpdateTime());

        vo.setContractCode(info.getContractCode());
        vo.setApplicantId(info.getId());
        vo.setApplicantType(info.getApplicantType());
        vo.setApplicantName(info.getApplicantName());
        vo.setApplicantIdType(info.getApplicantIdType());
        vo.setApplicantIdCard(info.getApplicantIdCard());
        vo.setApplicantIdCardValidityStart(info.getApplicantIdCardValidityStart());
        vo.setApplicantIdCardValidityEnd(info.getApplicantIdCardValidityEnd());
        vo.setApplicantIdCardPermanent(info.getApplicantIdCardPermanent());
        vo.setApplicantGender(info.getApplicantGender());
        vo.setApplicantMobile(info.getApplicantMobile());
        vo.setApplicantAge(info.getApplicantAge());
        vo.setApplicantBirthday(info.getApplicantBirthday());
        vo.setApplicantNation(info.getApplicantNation());
        vo.setApplicantNationality(info.getApplicantNationality());
        vo.setApplicantEducation(info.getApplicantEducation());
        vo.setApplicantMarital(info.getApplicantMarital());
        vo.setApplicantAnnualIncome(info.getApplicantAnnualIncome());
        vo.setApplicantCareer(info.getApplicantCareer());
        vo.setApplicantCompany(info.getApplicantCompany());
        vo.setApplicantPosition(info.getApplicantPosition());
        vo.setApplicantEmail(info.getApplicantEmail());
        vo.setApplicantMedicare(info.getApplicantMedicare());
        vo.setApplicantOccupationalCategory(info.getApplicantOccupationalCategory());
        vo.setApplicantIndustryCategory(info.getApplicantIndustryCategory());
        vo.setCompanyNature(info.getCompanyNature());
        vo.setApplicantPostcode(info.getApplicantPostcode());
        vo.setApplicantProvinceCode(info.getApplicantProvinceCode());
        vo.setApplicantCityCode(info.getApplicantCityCode());
        vo.setApplicantRegionCode(info.getApplicantRegionCode());
        vo.setApplicantAddress(info.getApplicantAddress());
        vo.setApplicantTelephone(info.getApplicantTelephone());
        vo.setCompanySocialSecurityNum(info.getCompanySocialSecurityNum());
        vo.setCompanyEmployeeNum(info.getCompanyEmployeeNum());
        vo.setCompanyContactName(info.getCompanyContactName());
        vo.setCompanyContactMobile(info.getCompanyContactMobile());
        vo.setLegalPersonName(info.getLegalPersonName());
        vo.setLegalPersonIdType(info.getLegalPersonIdType());
        vo.setLegalPersonIdCard(info.getLegalPersonIdCard());
        vo.setLegalPersonIdCardValidityStart(info.getLegalPersonIdCardValidityStart());
        vo.setLegalPersonIdCardValidityEnd(info.getLegalPersonIdCardValidityEnd());
        vo.setLegalPersonIdCardPermanent(info.getLegalPersonIdCardPermanent());
        vo.setBankCode(info.getBankCode());
        vo.setBankName(info.getBankName());
        vo.setCardNo(info.getCardNo());
        vo.setCardName(info.getCardName());
        vo.setPaymentPhase(info.getPaymentPhase());
        vo.setRemark(info.getRemark());
        return vo;
    }

//    public static boolean equalVo(EpPreservationApplicantInfoChangeVo vo){
//
//        final EpPreservationApplicantInfoChangeVo vo = new EpPreservationApplicantInfoChangeVo();
//        vo.setContractCode(info.getContractCode());
//        vo.setApplicantId(info.getId());
//        vo.setApplicantType(info.getApplicantType());
//        vo.setApplicantName(info.getApplicantName());
//        vo.setApplicantIdType(info.getApplicantIdType());
//        vo.setApplicantIdCard(info.getApplicantIdCard());
//        vo.setApplicantIdCardValidityStart(info.getApplicantIdCardValidityStart());
//        vo.setApplicantIdCardValidityEnd(info.getApplicantIdCardValidityEnd());
//        vo.setApplicantIdCardPermanent(info.getApplicantIdCardPermanent());
//        vo.setApplicantGender(info.getApplicantGender());
//        vo.setApplicantMobile(info.getApplicantMobile());
//        vo.setApplicantAge(info.getApplicantAge());
//        vo.setApplicantBirthday(info.getApplicantBirthday());
//        vo.setApplicantNation(info.getApplicantNation());
//        vo.setApplicantNationality(info.getApplicantNationality());
//        vo.setApplicantEducation(info.getApplicantEducation());
//        vo.setApplicantMarital(info.getApplicantMarital());
//        vo.setApplicantAnnualIncome(info.getApplicantAnnualIncome());
//        vo.setApplicantCareer(info.getApplicantCareer());
//        vo.setApplicantCompany(info.getApplicantCompany());
//        vo.setApplicantPosition(info.getApplicantPosition());
//        vo.setApplicantEmail(info.getApplicantEmail());
//        vo.setApplicantMedicare(info.getApplicantMedicare());
//        vo.setApplicantOccupationalCategory(info.getApplicantOccupationalCategory());
//        vo.setApplicantIndustryCategory(info.getApplicantIndustryCategory());
//        vo.setCompanyNature(info.getCompanyNature());
//        vo.setApplicantPostcode(info.getApplicantPostcode());
//        vo.setApplicantProvinceCode(info.getApplicantProvinceCode());
//        vo.setApplicantCityCode(info.getApplicantCityCode());
//        vo.setApplicantRegionCode(info.getApplicantRegionCode());
//        vo.setApplicantAddress(info.getApplicantAddress());
//        vo.setApplicantTelephone(info.getApplicantTelephone());
//        vo.setCompanySocialSecurityNum(info.getCompanySocialSecurityNum());
//        vo.setCompanyEmployeeNum(info.getCompanyEmployeeNum());
//        vo.setCompanyContactName(info.getCompanyContactName());
//        vo.setCompanyContactMobile(info.getCompanyContactMobile());
//        vo.setLegalPersonName(info.getLegalPersonName());
//        vo.setLegalPersonIdType(info.getLegalPersonIdType());
//        vo.setLegalPersonIdCard(info.getLegalPersonIdCard());
//        vo.setLegalPersonIdCardValidityStart(info.getLegalPersonIdCardValidityStart());
//        vo.setLegalPersonIdCardValidityEnd(info.getLegalPersonIdCardValidityEnd());
//        vo.setLegalPersonIdCardPermanent(info.getLegalPersonIdCardPermanent());
//        vo.setBankCode(info.getBankCode());
//        vo.setBankName(info.getBankName());
//        vo.setCardNo(info.getCardNo());
//        vo.setCardName(info.getCardName());
//        vo.setPaymentPhase(info.getPaymentPhase());
//        vo.setRemark(info.getRemark());
//    }
}
