package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;

import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import com.mpolicy.manage.modules.agent.validator.group.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人用户信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-07 10:41:32
 */
@TableName("agent_user_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentUserInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "经纪人工号")
    private String agentCode;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码", required = true, example = "OR20210222103029tIBLey")
    private String orgCode;
    /**
     * 性别:0:女 1:男 -1:未知
     */
    @ApiModelProperty(value = "性别:0:女 1:男 -1:未知", required = true, example = "1")
    @NotNull(message = "性别不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Range(min = -1, max = 2, message = "性别:0:女 1:男 -1:未知", groups = {AgentSaveOrUpdateGroup.class})
    private Integer gender;
    /**
     * 头像
     */
    @ApiModelProperty(value = "头像", required = true, example = "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fblog%2F202101%2F15%2F20210115170419_ed8cd.thumb.1000_0.jpeg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1615006708&t=21269ad")
    @NotBlank(message = "头像不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String avatar;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", required = true, example = "<EMAIL>")
    @NotBlank(message = "邮箱不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Email(message = "邮箱格式不正确", groups = {AgentSaveOrUpdateGroup.class})
    private String email;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true, example = "13452341234")
    @NotBlank(message = "联系电话不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号格式不正确", groups = {AgentSaveOrUpdateGroup.class})
    private String mobile;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true, example = "测试姓名")
    @NotBlank(message = "姓名不能为空", groups = {AgentTransientGroup.class, AgentSaveOrUpdateGroup.class})
    private String agentName;

    @ApiModelProperty(value = "昵称", required = true, example = "昵称")
    private String agentNickName;
    /**
     * 姓名拼音
     */
    @ApiModelProperty(value = "姓名拼音", hidden = true)
    private String pinyin;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型", required = true, example = "ID_CARD_TYPE:0")
    @NotBlank(message = "证件类型不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String idType;

    @ApiModelProperty(value = "证件有效期起", example = "2020-18-22")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date idStartDate;

    @ApiModelProperty(value = "证件有效期止", example = "2020-18-22")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date idEndDate;

    @ApiModelProperty(value = "是否长期有效", example = "true")
    private boolean idLongTerm;
    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型", required = true, example = "AGENT_TYPE:0")
    @NotBlank(message = "人员类型不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String agentType;
    /**
     * 区域经理所属区域
     */
    @ApiModelProperty(value = "区域经理所属区域", example = "REFERRER_REGION:0")
    private String areaManagerRegion;
    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true, example = "130720198610081234")
    @NotBlank(message = "证件号不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Pattern(regexp = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X|x)$", message = "身份证号格式不正确", groups = {AgentSaveOrUpdateGroup.class})
    private String idCard;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日", hidden = true)
    private String birthday;
    /**
     * 入职日期
     */
    @ApiModelProperty(value = "入职日期", required = true, example = "2021-02-17")
    @NotNull(message = "入职日期不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String entryDate;
    /**
     * 入职方式
     */
    @ApiModelProperty(value = "入职方式 目前为后台写死")
    private String entryType;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位", required = true, example = "AGENT_POSITION:1")
    private String position;


    @ApiModelProperty("初始档次")
    private String positionDegree;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历", required = true, example = "AGENT_DEGREE:0")
    @NotBlank(message = "学历不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String degree;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true, example = "发送到发水淀粉撒的粉撒地方2222")
    private String address;
    /**
     * 服务地区
     */
    @ApiModelProperty(value = "服务地区三级对象 仅作返回")
    @TableField(exist = false)
    private SysRegionInfo serverRegion;
    /**
     * 服务城市编码
     */
    @ApiModelProperty(value = "服务城市编码", required = true, example = "150400")
    @NotBlank(message = "服务城市编码不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String cityCode;
    /**
     * 展业地区编码
     */
    @ApiModelProperty(value = "展业地区编码", required = true, example = "ACQUISITION_AREA:3")
    @NotBlank(message = "展业地区不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String acquisitionArea;
    /**
     * 婚姻状态
     */
    @ApiModelProperty(value = "婚姻状态", hidden = true)
    private String marital;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌", required = true, example = "AGENT_POLITICS:0")
    private String politics;
    /**
     * 人员状态0:有效 1:无效
     */
    @ApiModelProperty(value = "人员状态0:有效 1:无效", required = true, example = "0")
    private Integer agentStatus;
    /**
     * 服务属性1:专属线上服务
     */
    @ApiModelProperty(value = "服务属性", required = true, example = "AGENT_SERVER_TYPE:1")
    @NotBlank(message = "服务属性不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String serviceAttribute;
    /**
     * 擅长领域
     */
    @ApiModelProperty(value = "擅长领域", required = true, example = "擅长领域，擅长领域，擅长领域擅长领域擅长领域擅长领域擅长领域")
    @NotBlank(message = "擅长领域不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String expertise;
    /**
     * 用户标签,号分割
     */
    @ApiModelProperty(value = "个人标签 ,号分割 最多四个", required = true, example = "特别好,哇哈哈")
    @NotBlank(message = "个人标签不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String agentLabel;
    /**
     * 初始化服务人数
     */
    @ApiModelProperty(value = "初始服务人数", required = true, example = "10")
    @NotNull(message = "初始服务人数不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Min(value = 0, message = "初始服务人数最少为0", groups = {AgentSaveOrUpdateGroup.class})
    private Integer initServiceNum;
    /**
     * 服务人数
     */
    @ApiModelProperty(value = "服务人数，仅展示")
    private Integer serviceCustomerNum;
    /**
     * 从业年限
     */
    @ApiModelProperty(value = "从业年限", required = true, example = "1.1")
    @NotEmpty(message = "从业年限不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String workTime;
    /**
     * 微信账号(非微信昵称)
     */
    @ApiModelProperty(value = "微信账号(非微信昵称)", required = true, example = "12345")
    @NotBlank(message = "微信账号不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String weChat;
    /**
     * 个人简介
     */
    @ApiModelProperty(value = "个人简介", required = true, example = "123456")
    @NotBlank(message = "个人简介不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String introduce;
    /**
     * 民族
     */
    @ApiModelProperty(value = "民族", hidden = true)
    private String nation;


    @ApiModelProperty("国籍")
    private String country;

    @ApiModelProperty("人员类别")
    private String agentCategory;

    @ApiModelProperty("人员性质")
    private String agentNature;
    /**
     * 经纪人推广二维码
     */
    @ApiModelProperty(value = "经纪人推广二维码", hidden = true)
    private String appletsCode;
    /**
     * 毕业学校
     */
    @ApiModelProperty(value = "毕业学校", required = true, example = "毕业学校")
    @NotBlank(message = "毕业学校不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String school;
    /**
     * 居住地区三级对象
     */
    @ApiModelProperty(value = "居住地区三级对象 仅作返回")
    @TableField(exist = false)
    private SysRegionInfo liveRegion;
    /**
     * 居住区域编码
     */
    @ApiModelProperty(value = "居住区域编码", required = true, example = "120102")
    private String areaCode;
    /**
     * 热度
     */
    @ApiModelProperty(value = "热度", required = true, example = "2")
    @NotNull(message = "热度不能为空", groups = {AgentSaveOrUpdateGroup.class})
    @Range(min = 0, max = 100, message = "热度范围为0-100", groups = {AgentSaveOrUpdateGroup.class})
    private Integer hot;
    /**
     * 增员人编码
     */
    @ApiModelProperty(value = "增员人编码 无增援人传空即可", required = true)
    private String recruitCode;
    /**
     * 增员人姓名 仅作返回
     */
    @ApiModelProperty(value = "增员人姓名 仅作返回")
    @TableField(exist = false)
    private String recruitName;
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码", required = true, example = "U12346")
    @NotBlank(message = "业务编码不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private String businessCode;
    /**
     * 推荐状态0:不推荐  1:推荐
     */
    @ApiModelProperty(value = "推荐状态 0:不推荐  1:推荐", required = true, example = "0")
    @NotNull(message = "推荐状态不能为空", groups = {AgentSaveOrUpdateGroup.class})
    private Integer recommendStatus;
    /**
     * 可被选为专属顾问 1:是 0:否
     */
    @ApiModelProperty(value = "可被选为专属顾问 1:是 0:否", required = true, example = "0")
    @Range(min = 0, max = 1, message = "可被选为专属顾问状态选择错误")
    private Integer isOptional;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁 更新时必须存在")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

    @ApiModelProperty("离职状态0:在职 1:离职")
    private Integer quitStatus;

    @ApiModelProperty("离职时间")
    private Date quitTime;

    @ApiModelProperty("片区")
    private String subregion;

    @ApiModelProperty("区域备注")
    private String managerRegionRemark;

    @ApiModelProperty("互联网营销告知书签署状态(0:未签约 2:已签约)")
    private Integer marketingSignStatus;

}
