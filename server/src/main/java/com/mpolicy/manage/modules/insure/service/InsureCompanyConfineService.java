package com.mpolicy.manage.modules.insure.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureCompanyConfineEntity;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineOut;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineVO;
import com.mpolicy.manage.modules.insure.vo.InsureConfineStatusVO;

import java.util.List;
import java.util.Map;

/**
 * 保司产品升级控制
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 10:34:28
 */
public interface InsureCompanyConfineService extends IService<InsureCompanyConfineEntity> {

    /**
     * 获取保司升级控制列表
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<InsureCompanyConfineOut> queryPage(Map<String, Object> paramMap);

    /**
     * 获取有产品的保司
     *
     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/10/9 13:50
     */
    List<JSONObject> getCompanyList();

    /**
     * 获取保司下的产品
     *
     * @param companyCodes:
     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/10/9 15:51
     */
    List<JSONObject> getProductList(List<String> companyCodes);

    /**
     * 修改保司升级控制控制状态
     *
     * @param insureConfineStatusVO:
     * @return : void
     * <AUTHOR>
     * @date 2022/10/9 16:36
     */
    void updateStatus(InsureConfineStatusVO insureConfineStatusVO);

    /**
     * 添加——修改保司升级控制
     *
     * @param insureCompanyConfineVO:
     * @return : void
     * <AUTHOR>
     * @date 2022/10/9 16:57
     */
    void saveUpdate(InsureCompanyConfineVO insureCompanyConfineVO);

    /**
     * 删除保司升级控制
     *
     * @param confineCode:
     * @return : void
     * <AUTHOR>
     * @date 2022/10/9 17:52
     */
    void deleteConfine(String confineCode);
}

