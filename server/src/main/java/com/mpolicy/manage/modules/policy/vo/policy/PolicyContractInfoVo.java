package com.mpolicy.manage.modules.policy.vo.policy;

import com.mpolicy.policy.common.enums.contract.EpPolicyOperationTypeEnum;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupInsuredInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("保单合同详细信息")
public class PolicyContractInfoVo {

    @ApiModelProperty(value = "保单合同编码", example = "保单合同编码")
    private String contractCode;

    @ApiModelProperty(value = "保单类型 0:预收件；1:承保件", example = "0", required = true)
    private Integer policyType;

    @ApiModelProperty(value = "保单产品类型(个、团、车、财)", example = "PRODUCT:PRODUCTGROUP:1", required = true)
    private String policyProductType;

    @ApiModelProperty(value = "保单操作类型")
    private EpPolicyOperationTypeEnum epPolicyOperationTypeEnum;

    @ApiModelProperty(value = "来源平台")
    private String sourcePlatform;

    @ApiModelProperty(value = "保单来源")
    private String policySource;

    @ApiModelProperty(value = "三方编码")
    private String serialNumber;

    @ApiModelProperty(value = "是否为提交保单 0:暂存;1:已提交")
    private Integer isSubmit;

    @ApiModelProperty(value = "操作用户")
    private String optUser;

    @ApiModelProperty(value = "电子保单")
    private String policyUrl;

    @ApiModelProperty(value = "小鲸电子保单")
    private String xjPolicyUrl;

    @ApiModelProperty(value = "分销渠道编码", example = "XJ1XXXXX")
    private String channelDistributionCode;

    @ApiModelProperty(value = "DataX同步保单时间")
    private Date syncUpdateTime;

    @ApiModelProperty(value = "数据来源：1=农保系统Api同步(最好区分DataX的同步方式)")
    private Integer dataSource;

    @ApiModelProperty(value = "原保单号")
    private String sourcePolicyNo;

    @ApiModelProperty(value = "保单基础信息")
    private EpContractBaseInfoVo contractBaseInfo;

    @ApiModelProperty(value = "保单状态信息")
    private EpContractExtendInfoVo contractExtendInfo;

    @ApiModelProperty(value = "附件列表")
    private List<EpContractAttachInfoVo> contractAttachList;

    @ApiModelProperty(value = "保单代理人信息", required = true)
    private List<EpAgentInfoVo> agentInfoList;

    @ApiModelProperty(value = "保单渠道信息")
    private EpPolicyChannelInfoVo channelInfo;

    @ApiModelProperty(value = "投保人信息")
    private EpApplicantInfoVo applicantInfo;

    @ApiModelProperty(value = "车险相关信息")
    private EpVehicleInfoVo vehicleInfo;

    @ApiModelProperty(value = "险种列表 团、车")
    private List<EpProductInfoVo> productInfoList;

    @ApiModelProperty(value = "计划列表")
    private List<GroupPolicyPlanVo> groupPolicyPlanList;

    @ApiModelProperty(value = "被保人信息列表-个险")
    private List<EpInsuredInfoVo> insuredInfoList;

    @ApiModelProperty(value = "被保人信息列表-团险")
    private List<EpGroupInsuredInfoVo> groupInsuredInfoList;

    @ApiModelProperty(value = "活动信息")
    private List<EpPolicyActivityVo> epPolicyActivitys;
}
