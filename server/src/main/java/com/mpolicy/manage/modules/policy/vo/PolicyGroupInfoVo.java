package com.mpolicy.manage.modules.policy.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 团险分单导入界面
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 15:48
 */
@Data
public class PolicyGroupInfoVo {
    private static final long serialVersionUID = 1L;
    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    private String policyStatus;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String portfolioName;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal premiumTotal;
    /**
     * 投保时间
     */
    @ApiModelProperty(value = "投保时间")
    @JSONField(format = "yyyy-MM-dd")
    private String applicantTime;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    @JSONField(format = "yyyy-MM-dd")
    private String enforceTime;
    /**
     * 保险公司
     */
    @ApiModelProperty(value = "保险公司")
    private String companyName;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String applicantName;
    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    private String channelName;
    /**
     * 是否为完整保单 0:不完整;1:完整;2:暂存
     */
    @ApiModelProperty(value = "是否为完整保单 0:不完整;1:完整;2:暂存")
    private String intact;

}
