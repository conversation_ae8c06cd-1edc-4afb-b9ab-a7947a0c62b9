package com.mpolicy.manage.modules.sys.fileManage.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务处理结果信息
 *
 * <AUTHOR>
 * @date 2024-02-21 14:35
 */
@Data
@ApiModel(value = "通用导入导出处理响应结果信息", description = "通用导入导出处理响应结果信息")
public class BusinessFileManageHandlerResult implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 业务执行结果 1成功，-1失败,0=处理中
     */
    private Integer businessRunResult;

    /**
     * 处理成功条数
     */
    private Integer importSuccessNumber;

    /**
     * 处理失败条数
     */
    private Integer importFailNumber;

    /**
     * 业务结果文件编码
     */
    private String resultFileCode;

    /**
     * 业务结果文件名称
     */
    private String resultFileName;

    /**
     * 业务操作结果地址
     */
    private String resultFilePath;

    /**
     * 业务处理结果
     */
    private String businessResultMsg;
}
