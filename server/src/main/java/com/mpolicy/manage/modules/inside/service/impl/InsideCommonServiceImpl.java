package com.mpolicy.manage.modules.inside.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.mpolicy.manage.modules.ins.dao.InsInsuranceCompanyNoticeDao;
import com.mpolicy.manage.modules.ins.dao.InsPolicyInfoDao;
import com.mpolicy.manage.modules.ins.entity.InsPolicyInfoEntity;
import com.mpolicy.manage.modules.inside.service.IInsideCommonService;
import com.mpolicy.manage.modules.policy.dao.EpPolicyContractInfoDao;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class InsideCommonServiceImpl implements IInsideCommonService {

    @Autowired
    private EpPolicyContractInfoDao epPolicyContractInfoDao;

    @Autowired
    private InsInsuranceCompanyNoticeDao insInsuranceCompanyNoticeDao;

    @Autowired
    private InsPolicyInfoDao insPolicyInfoDao;

    public Map<String, Object> getAgentInfoByPolicyNo(String policyNo) {
        Map<String, Object> result = Maps.newHashMap();

        EpPolicyContractInfoEntity epPolicyContractInfoEntity = epPolicyContractInfoDao.selectOne(
                new LambdaQueryWrapper<EpPolicyContractInfoEntity>()
                        .eq(EpPolicyContractInfoEntity::getPolicyNo, policyNo)
        );
        InsPolicyInfoEntity insPolicyInfoEntity = insPolicyInfoDao.selectOne(
                new LambdaQueryWrapper<InsPolicyInfoEntity>()
                        .eq(InsPolicyInfoEntity::getPolicyNo, policyNo)
        );
        result.put("EpPolicyContractInfo", epPolicyContractInfoEntity);
        result.put("InsPolicyInfo", insPolicyInfoEntity);
        return result;
    }
}
