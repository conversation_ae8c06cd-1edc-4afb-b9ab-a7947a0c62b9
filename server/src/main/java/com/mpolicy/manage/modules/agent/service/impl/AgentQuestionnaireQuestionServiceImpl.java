package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.web.common.utils.Query;

import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.agent.dao.AgentQuestionnaireQuestionDao;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireQuestionEntity;
import com.mpolicy.manage.modules.agent.service.AgentQuestionnaireQuestionService;


@Slf4j
@Service("agentQuestionnaireQuestionService")
public class AgentQuestionnaireQuestionServiceImpl extends ServiceImpl<AgentQuestionnaireQuestionDao, AgentQuestionnaireQuestionEntity> implements AgentQuestionnaireQuestionService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentQuestionnaireQuestionEntity> page = this.page(
                new Query<AgentQuestionnaireQuestionEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    public void deleteByQuestionnaireId(Integer questionnaireId) {
        boolean result = this.update(
                new UpdateWrapper<AgentQuestionnaireQuestionEntity>().lambda()
                        .eq(AgentQuestionnaireQuestionEntity::getQuestionnaireId, questionnaireId)
                        .set(AgentQuestionnaireQuestionEntity::getDeleted, Constant.DELETE_FLAG));
        if(!result){
            log.warn("问卷问题删除失败 questionnaireId= {}",questionnaireId);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("问卷问题删除失败"));
        }
    }

    @Override
    public List<AgentQuestionnaireQuestionEntity> queryByQuestionnaireId(Integer questionnaireId) {
        return this.list(new LambdaQueryWrapper<AgentQuestionnaireQuestionEntity>().in(AgentQuestionnaireQuestionEntity::getQuestionnaireId, questionnaireId));
    }

}
