package com.mpolicy.manage.modules.chat.entity;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentUserAppListOutVo extends BasePage implements Serializable {
    private static final long serialVersionUID = 165L;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "代理人业务编码")
    private String businessCode;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "展业地区")
    private String acquisitionArea;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "总数开始时间")
    private String totalBeginTime;

    @ApiModelProperty(value = "总数结束时间")
    private String totalEndTime;

    private List<String> agentCodeList;
}
