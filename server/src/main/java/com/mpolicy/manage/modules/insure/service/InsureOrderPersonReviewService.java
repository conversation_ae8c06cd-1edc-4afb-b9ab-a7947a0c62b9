package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureOrderPersonReviewEntity;

import java.util.Map;

/**
 * 订单人工审核信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-14 14:27:24
 */
public interface InsureOrderPersonReviewService extends IService<InsureOrderPersonReviewEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

