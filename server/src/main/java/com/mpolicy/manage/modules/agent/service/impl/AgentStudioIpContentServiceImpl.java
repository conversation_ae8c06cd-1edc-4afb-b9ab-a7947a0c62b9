package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.common.utils.MultiTextUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.enums.AgentIpContentPlatformEnum;
import com.mpolicy.manage.modules.agent.service.AgentStudioIpService;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.agent.util.AgentConstantUtil;
import com.mpolicy.manage.modules.agent.vo.resp.AgentStudioIpContentRespVo;
import com.mpolicy.web.common.utils.Query;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.agent.dao.AgentStudioIpContentDao;
import com.mpolicy.manage.modules.agent.entity.AgentStudioIpContentEntity;
import com.mpolicy.manage.modules.agent.service.AgentStudioIpContentService;


@Service("agentStudioIpContentService")
public class AgentStudioIpContentServiceImpl extends ServiceImpl<AgentStudioIpContentDao, AgentStudioIpContentEntity> implements AgentStudioIpContentService {
    @Autowired
    AgentUserInfoService agentUserInfoService;

    @Autowired
    AgentStudioIpService agentStudioIpService;

    @Autowired
    OrgInfoService orgInfoService;

    @Autowired
    StorageService storageService;

    @Value("${mp.domain.access.oss}")
    String oss;

    @Override
    public PageUtils<AgentStudioIpContentRespVo> queryPage(Map<String, Object> params) {
        // 1、构建查询条件
        String agentName = RequestUtils.objectValueToString(params, "agentName");
        String businessCode = RequestUtils.objectValueToString(params, "businessCode");
        String platform = RequestUtils.objectValueToString(params, "platform");

        // 1.1、按照经纪人姓名和业务编码查询
        List<String> agentCodeList = new ArrayList<>();
        if (agentName != null || businessCode != null) {
            agentCodeList = agentUserInfoService.list(
                    Wrappers.<AgentUserInfoEntity>lambdaQuery()
                            .like(agentName != null, AgentUserInfoEntity::getAgentName, agentName)
                            .eq(businessCode != null, AgentUserInfoEntity::getBusinessCode, businessCode)
            ).stream().map(AgentUserInfoEntity::getAgentCode).collect(Collectors.toList());
        }

        // 2、根据构建的条件查询结果
        IPage<AgentStudioIpContentEntity> page = this.page(
                new Query<AgentStudioIpContentEntity>().getPage(params),
                Wrappers.<AgentStudioIpContentEntity>lambdaQuery()
                        .in(!agentCodeList.isEmpty(), AgentStudioIpContentEntity::getAgentCode, agentCodeList)
                        .eq(platform != null, AgentStudioIpContentEntity::getPlatform, platform)
        );

        // 3、处理结果
        // 3.1、构建职级和组织信息map
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);
        Map<String, String> orgMap = orgInfoService.list().stream().collect(Collectors.toMap(OrgInfoEntity::getOrgCode, OrgInfoEntity::getOrgName
        ));


        // 3.2、构建基础信息
        List<AgentStudioIpContentEntity> records = page.getRecords();
        List<AgentStudioIpContentRespVo> result = null;
        if (!records.isEmpty()) {
            // 3.3、构建经纪人信息map
            Map<String, AgentUserInfoEntity> agentUserInfoEntityMap = agentUserInfoService.list(
                    Wrappers.<AgentUserInfoEntity>lambdaQuery()
                            .in(
                                    AgentUserInfoEntity::getAgentCode,
                                    records.stream().map(AgentStudioIpContentEntity::getAgentCode).collect(Collectors.toList())
                            )
            ).stream().collect(Collectors.toMap(AgentUserInfoEntity::getAgentCode, v -> v));

            result = records.stream().map(x -> {
                AgentUserInfoEntity agent = agentUserInfoEntityMap.get(x.getAgentCode());

                AgentStudioIpContentRespVo agentStudioIpContentRespVo = new AgentStudioIpContentRespVo();
                BeanUtils.copyProperties(x, agentStudioIpContentRespVo);

                agentStudioIpContentRespVo.setTextCount(x.getContentLength());
                agentStudioIpContentRespVo.setAgentName(agent.getAgentName());
                agentStudioIpContentRespVo.setBusinessCode(agent.getBusinessCode());
                agentStudioIpContentRespVo.setPosition(agentPositionMap.get(agent.getPosition()));
                agentStudioIpContentRespVo.setPlatform(AgentIpContentPlatformEnum.getValueByKey(x.getPlatform()).getValue());

                String agentOrgCode = agent.getOrgCode();
                if (agentOrgCode != null) {
                    agentStudioIpContentRespVo.setOrgName(orgMap.get(agentOrgCode));
                }

                return agentStudioIpContentRespVo;
            }).collect(Collectors.toList());
        }
        return new PageUtils<>(result, (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }

    @Override
    public AgentStudioIpContentEntity info(String code) {
        AgentStudioIpContentEntity entity = this.getOne(
                Wrappers.<AgentStudioIpContentEntity>lambdaQuery()
                        .eq(AgentStudioIpContentEntity::getIpContentCode, code)
        );
        if (entity == null) {
            entity = new AgentStudioIpContentEntity();
        }
        entity.setFilePath(DomainUtil.addOssDomainIfNotExist(entity.getFilePath()));
        return entity;
    }

    /**
     * 截取抖音首帧视频
     *
     * @param url 视频url
     * @return 上传成功后的图片url
     */
    private String douYinVideoCapture(String url) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        OssBaseOut ossBaseOut = storageService.uploadNetworkFlows(FileModelEnum.DOU_YIN_VIDEO.getFileSystem() + "/capture/" + uuid + ".jpg", url + "?x-oss-process=video/snapshot,t_0,f_jpg,w_642,h_0");
        return ossBaseOut.getAccessDomainPath();
    }

    @Override
    public boolean saveOrUpdateEntity(AgentStudioIpContentEntity agentStudioIpContentEntity) {
        // 如果是抖音就清空内容，否则就清空视频链接并处理文字
        String platform = agentStudioIpContentEntity.getPlatform();
        AgentIpContentPlatformEnum contentPlatformEnum = AgentIpContentPlatformEnum.getValueByKey(platform);
        if (AgentIpContentPlatformEnum.isDouYin(contentPlatformEnum)) {
            String filePath = DomainUtil.removeDomain(agentStudioIpContentEntity.getFilePath());
            agentStudioIpContentEntity.setFilePath(filePath);
            agentStudioIpContentEntity.setContentLength(0);
            agentStudioIpContentEntity.setContentDigest(StringUtils.EMPTY);

            agentStudioIpContentEntity.setImagePath(
                    douYinVideoCapture(DomainUtil.addDomainIfNotExist(filePath, oss))
            );
            agentStudioIpContentEntity.setContentText(StringUtils.EMPTY);
        } else {
            // 清除样式后的文本
            String richText = agentStudioIpContentEntity.getContentText();
            String content = HtmlUtil.cleanHtmlTag(richText);
            agentStudioIpContentEntity.setContentLength(content.length());
            agentStudioIpContentEntity.setContentDigest(StringUtils.left(content, 100));
            agentStudioIpContentEntity.setImagePath(MultiTextUtils.getFirstImgStr(richText));
            agentStudioIpContentEntity.setFilePath(StringUtils.EMPTY);
        }

        boolean result;

        // 不存在编码即为新增
        if (StringUtils.isBlank(agentStudioIpContentEntity.getIpContentCode())) {
            agentStudioIpContentEntity.setIpContentCode(CommonUtils.createCode("IP"));
            result = this.save(agentStudioIpContentEntity);
        } else {
            result = this.update(
                    agentStudioIpContentEntity,
                    Wrappers.<AgentStudioIpContentEntity>lambdaQuery()
                            .eq(AgentStudioIpContentEntity::getIpContentCode, agentStudioIpContentEntity.getIpContentCode())
            );
        }
        agentStudioIpService.changeCount(
                agentStudioIpContentEntity.getAgentCode(),
                contentPlatformEnum,
                this.list(Wrappers.<AgentStudioIpContentEntity>lambdaQuery()
                        .eq(AgentStudioIpContentEntity::getPlatform, contentPlatformEnum.getKey())
                        .eq(AgentStudioIpContentEntity::getAgentCode, agentStudioIpContentEntity.getAgentCode())
                ).size()
        );

        return result;
    }

    @Override
    public boolean deleteEntity(String code) {
        // 查询基本信息
        AgentStudioIpContentEntity agentStudioIpContentEntity = this.info(code);
        // 删除
        boolean remove = this.remove(Wrappers.<AgentStudioIpContentEntity>lambdaQuery().eq(AgentStudioIpContentEntity::getIpContentCode, code));

        // 重新计算数量
        String platform = agentStudioIpContentEntity.getPlatform();
        agentStudioIpService.changeCount(
                agentStudioIpContentEntity.getAgentCode(),
                AgentIpContentPlatformEnum.getValueByKey(platform),
                this.list(Wrappers.<AgentStudioIpContentEntity>lambdaQuery()
                        .eq(AgentStudioIpContentEntity::getPlatform, platform)
                        .eq(AgentStudioIpContentEntity::getAgentCode, agentStudioIpContentEntity.getAgentCode())
                ).size()
        );
        return remove;
    }
}
