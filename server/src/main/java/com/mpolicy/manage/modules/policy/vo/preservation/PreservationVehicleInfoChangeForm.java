package com.mpolicy.manage.modules.policy.vo.preservation;


import com.mpolicy.policy.common.ep.policy.EpApplicantInfoVo;
import com.mpolicy.policy.common.ep.policy.EpVehicleInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PreservationVehicleInfoChangeForm {

    @ApiModelProperty(value = "变更前车辆信息")
    private EpVehicleInfoVo beforeEpVehicleInfoVo;

    @ApiModelProperty(value = "变更后车辆信息")
    private EpVehicleInfoVo afterEpVehicleInfoVo;

    @ApiModelProperty(value = "变更前投保人信息")
    private EpApplicantInfoVo beforeEpApplicantInfoVo;

    @ApiModelProperty(value = "变更后投保人信息")
    private EpApplicantInfoVo afterEpApplicantInfoVo;

    @ApiModelProperty(value = "退补费")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退补费开关")
    private boolean refundAmountFlag;

}
