package com.mpolicy.manage.modules.policy.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-07-18 15:35
 * @description: 活动类型
 */
@Getter
@AllArgsConstructor
public enum EpPolicyActivityTypeEnum {
    WHOLE_VILLAGE_PROMOTION("100", "整村推进"),
    FOURTH_LEVEL_DISTRIBUTION("200", "四级分销"),
    ;
    /**
     * 编码
     */
    @EnumValue
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据编码获取值
     * @param typeCode
     * @return
     */
    public static EpPolicyActivityTypeEnum findCode(final String typeCode){
        if(Objects.isNull(typeCode)){
            return null;
        }
        for (EpPolicyActivityTypeEnum value : EpPolicyActivityTypeEnum.values()) {
            if(typeCode.equals(value.getCode())){
                return value;
            }
        }
        return null;
    }
}
