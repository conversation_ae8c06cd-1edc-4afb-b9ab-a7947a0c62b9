package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投保订单列表
 *
 * <AUTHOR>
 * @date 2022-05-29 15:23
 */
@Data
@ApiModel(value = "投保订单列表")
public class InsureBlacklistList implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * id
     */
    private Integer id;

    /**
     * 黑名单类型编码
     */
    @ApiModelProperty(value = "黑名单类型编码", example = "FIRST")
    private String blacklistType;

    /**
     * 黑名单类型编码
     */
    @ApiModelProperty(value = "黑名单类型名称", example = "首投")
    private String blacklistTypeName;


    /*@ApiModelProperty(value = "证件类型编码", example = "SFZ")
    private String identificationType;


    @ApiModelProperty(value = "证件类型名称", example = "身份证")
    private String identificationTypeName;*/

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", example = "张三")
    private String identificationName;

    /**
     * 认证证件号码
     */
    @ApiModelProperty(value = "认证证件号码", example = "20220506103627429095")
    private String identificationNum;

    /**
     * 黑名单说明
     */
    @ApiModelProperty(value = "黑名单说明", example = "黑名单说明")
    private String blacklistDesc;

    /**
     * 乐观锁id标记
     */
    @ApiModelProperty(value = "乐观锁", example = "1")
    private long revision;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "张三")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2019-01-01 15:12:20")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
