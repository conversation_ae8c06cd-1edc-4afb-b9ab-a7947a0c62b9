package com.mpolicy.manage.modules.agent.vo.agentinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: mpolicy-admin
 * @description: 区域代理人信息
 * @author: lsc
 * @created: 2022/09/20 13:57
 */
@Data
public class AreaManagerRegionAgentOut implements Serializable {
    private static final long serialVersionUID = -1201168127455747713L;

    private String agentCode;

    private String label;
}
