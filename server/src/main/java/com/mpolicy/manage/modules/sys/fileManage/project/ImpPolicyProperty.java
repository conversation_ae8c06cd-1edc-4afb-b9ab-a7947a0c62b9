package com.mpolicy.manage.modules.sys.fileManage.project;

import com.mpolicy.manage.helper.AdminBaseHelper;
import com.mpolicy.manage.modules.sys.entity.SystemBusinessFileManageEntity;
import com.mpolicy.manage.modules.sys.fileManage.abs.AbsBusinessFileManage;
import com.mpolicy.manage.modules.sys.fileManage.dto.BusinessFileManageHandlerResult;
import com.mpolicy.manage.modules.sys.fileManage.enums.FileManageProjectEnum;
import com.mpolicy.policy.common.ep.policy.OptUserInfoVo;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyResult;
import com.mpolicy.policy.common.ep.policy.common.PolicyFileImportApplyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 保单导入-个险PROPERTY
 *
 * <AUTHOR>
 * @date 2024-02-21 14:16
 */
@Service
@Slf4j
public class ImpPolicyProperty extends AbsBusinessFileManage {


    @Override
    public FileManageProjectEnum getFileManageProjectEnum() {
        return FileManageProjectEnum.IMP_POLICY_PROPERTY;
    }



    @Override
    public BusinessFileManageHandlerResult businessRun(SystemBusinessFileManageEntity bean) {
        log.info("业务处理-个险导入-个险导入开始....");

        String fileManagerCode = bean.getFileManageCode();
        log.info("业务处理-{}导入开始:{}....", fileManagerCode,getFileManageProjectEnum().getName());
        BusinessFileManageHandlerResult result = new BusinessFileManageHandlerResult();
        OptUserInfoVo optUserInfo = transform2OptUserInfoVo(AdminBaseHelper.getUserEntity(bean.getBusinessOpeUserName()));
        if (Objects.nonNull(optUserInfo)){
            optUserInfo.setOpId(bean.getFileManageCode());
        }

        PolicyFileImportApplyVo vo = new PolicyFileImportApplyVo();
        vo.setOperator(optUserInfo);
        vo.setFileCode(bean.getSourceFileCode());
        vo.setModuleType(getFileManageProjectEnum().getCode());
        vo.setSerialNo(bean.getFileManageCode());
        PolicyFileImportApplyResult data = policyBaseService.asyncImport(vo);
        int status = data.getApplyStatus();
        if (status==0) {
            result.setBusinessRunResult(0);
            result.setBusinessResultMsg("数据处理中");
        } else if(status==-1){
            result.setBusinessRunResult(-1);
            result.setBusinessResultMsg("数据处理失败");
        }
        log.info("业务处理-{}导入结束....", getFileManageProjectEnum().getName());
        return result;
    }
}
