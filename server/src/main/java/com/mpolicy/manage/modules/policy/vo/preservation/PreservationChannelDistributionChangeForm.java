package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保单分销渠道变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单分销渠道变更保全")
public class PreservationChannelDistributionChangeForm implements Serializable {

    @ApiModelProperty("变更前保费")
    @NotNull(message = "变更前分销渠道编码")
    private String beforeChannelDistributionCode;

    @ApiModelProperty("变更后保费")
    @NotNull(message = "变更后分销渠道编码")
    private  String correctedChannelDistributionCode;
}
