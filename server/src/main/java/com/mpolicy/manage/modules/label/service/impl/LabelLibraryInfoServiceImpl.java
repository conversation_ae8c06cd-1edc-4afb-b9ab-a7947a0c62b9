package com.mpolicy.manage.modules.label.service.impl;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.label.dao.LabelLibraryInfoDao;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoListOut;
import com.mpolicy.manage.modules.label.service.LabelLibraryInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service("labelLibraryInfoService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LabelLibraryInfoServiceImpl extends ServiceImpl<LabelLibraryInfoDao, LabelLibraryInfoEntity> implements LabelLibraryInfoService {

    private final LabelLibraryInfoDao labelLibraryInfoDao;

    @Override
    public List<LabelLibraryInfoListOut> findLabelLibraryInfoList() {
        List<LabelLibraryInfoEntity> libraryInfoList = new LambdaQueryChainWrapper<>(labelLibraryInfoDao)
                .ne(LabelLibraryInfoEntity::getLibraryCode, "LBB20210701160250996643")
                .eq(LabelLibraryInfoEntity::getDeleted, StatusEnum.INVALID.getCode())
                .list();
        List<LabelLibraryInfoListOut> list = new ArrayList<>();
        libraryInfoList.forEach(action -> {
            list.add(LabelLibraryInfoListOut.builder()
                    .libraryCode(action.getLibraryCode())
                    .libraryName(action.getLibraryName())
                    .remark(action.getRemark())
                    .build());
        });
        return list;
    }
}
