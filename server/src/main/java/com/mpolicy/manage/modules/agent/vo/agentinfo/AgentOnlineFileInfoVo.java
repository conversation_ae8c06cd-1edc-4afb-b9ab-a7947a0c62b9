package com.mpolicy.manage.modules.agent.vo.agentinfo;

import com.mpolicy.manage.modules.agentApply.vo.AgentApplyAttachmentOut;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: AgentOnlineInfoVo
 * Description: 代理人线上入职相关附件信息
 * date: 2023/3/1 16:12
 *
 * <AUTHOR>
 */
@Data
public class AgentOnlineFileInfoVo implements Serializable {
    /**
     * 面试申请表扫描件文件编码
     */
    @ApiModelProperty(value = "面试申请表扫描件文件编码")
    private String interviewUrlFileCode;
    /**
     * 面试申请表扫描件
     */
    @ApiModelProperty(value = "面试申请表扫描件")
    private String interviewUrl;
    /**
     * 身份证正面图片文件编码
     */
    @ApiModelProperty(value = "身份证正面图片文件编码")
    private String idCardFrontFileCode;
    /**
     * 身份证正面图片
     */
    @ApiModelProperty(value = "身份证正面图片")
    private String idCardFront;
    /**
     * 身份证反面图片文件编码
     */
    @ApiModelProperty(value = "身份证反面图片文件编码")
    private String idCardBackFileCode;
    /**
     * 身份证反面图片
     */
    @ApiModelProperty(value = "身份证反面图片")
    private String idCardBack;
    /**
     * 标准证件照片文件编码
     */
    @ApiModelProperty(value = "标准证件照片文件编码")
    private String certificatesPhoneFileCode;
    /**
     * 标准证件照片
     */
    @ApiModelProperty(value = "标准证件照片")
    private String certificatesPhone;
    /**
     * 银行卡正面照片文件编码
     */
    @ApiModelProperty(value = "银行卡正面照片文件编码")
    private String bankCardPhoneFileCode;
    /**
     * 银行卡正面照片
     */
    @ApiModelProperty(value = "银行卡正面照片")
    private String bankCardPhone;
    /**
     * 最高学历毕业证照片文件编码
     */
    @ApiModelProperty(value = "最高学历毕业证照片文件编码")
    private String degreePhoneFileCode;
    /**
     * 最高学历毕业证照片
     */
    @ApiModelProperty(value = "最高学历毕业证照片")
    private String degreePhone;
    /**
     * 已签署合同url
     */
    @ApiModelProperty(value = "已签署合同url")
    private String agreementUrl;

    /**
     * 其他附件
     */
    @ApiModelProperty(value = "其他附件url")
    private List<AgentApplyAttachmentOut> otherFileList;
}
