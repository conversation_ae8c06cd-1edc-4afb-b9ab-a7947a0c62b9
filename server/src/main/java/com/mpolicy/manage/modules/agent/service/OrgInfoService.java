package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.*;

import java.util.List;
import java.util.Map;

/**
 * 组织信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-10 11:14:18
 */
public interface OrgInfoService extends IService<OrgInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    List<OrgPageListOut> findOrgInfoList(Map<String, Object> paramMap);

    OrgInfoEntity info(String code);

    OrgInfoEntity getByCode(String code);

    /**
     * 根据code获取下级组织信息
     *
     * @param code 组织编码
     * @return 下属组织信息VO
     */
    List<OrgInfoVo> getOrgSonList(String code);

    /**
     * 获取所有父节点信息,包含当前节点
     *
     * @param code
     * @return
     */
    List<String> getParentNode(String code);

    /**
     * 获取上级节点信息
     * @param code
     * @return
     */
    List<List<String>> getParentNodeList(List<String> code);

    /**
     * 获取首个节点信息
     *
     * @param codeList 编码
     * @return
     */
    Map<String, OrgInfoEntity> findFirstOrgInfo(List<String> codeList);

    /**
     * 获取所有子点信息,包含当前节点
     *
     * @param code
     * @return
     */
    List<String> getChildNode(String code);

    /**
     * 根据code获取下级组织信息实体
     *
     * @param code 组织编码
     * @return 下属组织信息实体
     */
    List<OrgInfoEntity> getSonEntityList(String code);

    /**
     * 获取组织树列表
     *
     * @return
     */
    List<TreeListOut> findOrgTreeList();

    List<TreeListOut> findAllTreeList();

    /**
     * 保存组织信息
     *
     * @param save
     */
    void saveOrgInfo(OrgInfoSaveVo save);

    /**
     * 修改组织信息
     *
     * @param update
     */
    void updateOrgInfo(OrgInfoUpdateVo update);

    /**
     * 获取组织详情
     *
     * @param code
     * @return
     */
    OrgInfoOut findOrgInfoByCode(String code);

    /**
     * 获取所有组织列表
     * 该接口目前用在产品中心-协议管理
     *
     * @return
     */
    List<OrgInfoEntity> queryAllList();


}

