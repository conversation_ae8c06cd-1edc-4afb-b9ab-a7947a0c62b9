package com.mpolicy.manage.modules.tools.vo;

import com.mpolicy.policy.common.ep.policy.renewal.RenewalTermImportVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-05-05 19:08
 * @description: 续保导入拓展Vo
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RenewalTermImportExtendVo extends RenewalTermImportVo {
    /**
     *操作标记
     */
    private String operationSign;
}
