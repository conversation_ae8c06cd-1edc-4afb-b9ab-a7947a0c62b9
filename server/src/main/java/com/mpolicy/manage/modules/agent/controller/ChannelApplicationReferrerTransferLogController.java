package com.mpolicy.manage.modules.agent.controller;

import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerTransferLogService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerLogVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerTransferLogOut;
import com.mpolicy.manage.modules.agent.vo.agentinfo.SaveChannelApplicationReferrerLogVo;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Api(tags = "推荐人操作记录")
@RestController
@RequestMapping("channel/application/referrer/log")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelApplicationReferrerTransferLogController {

    private final ChannelApplicationReferrerTransferLogService channelApplicationReferrerTransferLogService;
    private final CustomerBasicInfoService customerBasicInfoService;


    /**
     * 获取推荐人客户转移记录
     *
     * @param input 查询请求参数
     * @return 转移记录
     */
    @GetMapping("list")
    @ApiOperation(value = "获取推荐人客户转移记录", notes = "获取推荐人客户转移记录", httpMethod = "GET")
    public Result<JSONObject> list(ChannelApplicationReferrerLogVo input) {
        List<ChannelApplicationReferrerTransferLogOut> list = channelApplicationReferrerTransferLogService.findTransferLogList(input);
        //获取推荐人客户数量
        int customerNum = Optional.ofNullable(customerBasicInfoService.lambdaQuery()
                .eq(CustomerBasicInfoEntity::getReferrerCode, input.getReferrerCode())
                .eq(CustomerBasicInfoEntity::getCancelStatus, StatusEnum.INVALID.getCode())
                .apply("COALESCE(inner_referrer_code, '') != {0}", input.getReferrerCode())
                .count()).orElse(0);
        JSONObject res = new JSONObject();
        res.put("transferLogList", list);
        res.put("customerNum", customerNum);
        return Result.success(res);
    }


    /**
     * 添加记录户转移记录
     *
     * @param input 请求参数
     * @return 添加结果
     */
    @PostMapping("save")
    @ApiOperation(value = "添加记录户转移记录", notes = "添加记录户转移记录", httpMethod = "POST")
    public Result save(@RequestBody @Valid SaveChannelApplicationReferrerLogVo input) {
        channelApplicationReferrerTransferLogService.saveChannelApplicationReferrerLog(input);
        return Result.success();
    }
}
