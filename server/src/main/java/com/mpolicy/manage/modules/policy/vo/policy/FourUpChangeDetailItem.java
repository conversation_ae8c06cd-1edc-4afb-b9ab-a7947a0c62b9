package com.mpolicy.manage.modules.policy.vo.policy;

import lombok.Data;

import java.io.Serializable;

@Data
public class FourUpChangeDetailItem implements Serializable {
    private static final long serialVersionUID = 2935308202210938018L;

    /**
     * 村代姓名
     */
    private String villageRepresentativeName;
    /**
     * 村代证件号
     */
    private String villageRepresentativeIdNumber;

    /**
     * 是否四级分销
     */
    private String fourPolicyDesc;
    /**
     * 渠道推荐人姓名+工号
     */
    private String managerName;
    /**
     * 渠道推荐人证件号
     */
    private String managerIdNumber;
    /**
     * 渠道推荐人来源
     */
    private String managerSource;
    /**

    /**
     * 渠道分支
     */
    private String branchName;
}
