package com.mpolicy.manage.modules.policy.vo.preservation.check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("初始渠道推荐人变更校验明细")
public class CustomerManagerChangeCheckItemVo {
    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("被保人身份证")
    private String idNumber;

    @ApiModelProperty("被保人身份证")
    private String insuredName;

    @ApiModelProperty("变更前农保的推荐人工号")
    private String chongHoRecommender;

    @ApiModelProperty("变更前农保的推荐人名字")
    private String chongHoRecommenderName;

    @ApiModelProperty("变更前小鲸的推荐人工号")
    private String whaleRecommender;

    @ApiModelProperty("变更前小鲸的推荐人名字")
    private String whaleRecommenderName;

    @ApiModelProperty("变更后推荐人工号")
    private String afterRecommender;

    @ApiModelProperty("变更后推荐人工号")
    private String afterRecommenderName;

    @ApiModelProperty("错误信息")
    private String message;

}
