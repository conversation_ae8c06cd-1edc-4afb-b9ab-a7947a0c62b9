package com.mpolicy.manage.modules.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 风险数据来源
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum RiskSourceEnum {

    SYSTEM("ORDER:CUSTOMER_RISK_SOURCE:0","系统识别"),
    IMPORT("ORDER:CUSTOMER_RISK_SOURCE:1","导入"),
    ;

    private final String code;
    private final String description;
}
