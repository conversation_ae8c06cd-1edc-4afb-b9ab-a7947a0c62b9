package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保订单受益人信息
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_insured_bene")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderInsuredBeneEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单编号
	 */
	private String insureOrderCode;
	/**
	 * 投保被保人唯一编号
	 */
	private String insureOrderInsuredCode;
	/**
	 * 受益人类型
	 */
	private String beneType;
	/**
	 * 受益人是投保人的
	 */
	private String beneOwnerRela;
	/**
	 * 受益人是被保险人的
	 */
	private String beneInsuredRela;
	/**
	 * 姓名
	 */
	private String beneName;
	/**
	 * 证件类型
	 */
	private String beneCertiCode;
	/**
	 * 证件号码
	 */
	private String beneCertiNo;
	/**
	 * 证件长期有效 是/否
	 */
	private String beneCertiLongTerm;
	/**
	 * 证件生效日期
	 */
	private String beneCertiEffectiveDate;
	/**
	 * 证件失效日期
	 */
	private String beneCertiInvalidDate;
	/**
	 * 性别
	 */
	private String beneGender;
	/**
	 * 出生日期
	 */
	private String beneBirthDay;
	/**
	 * 国籍
	 */
	private String beneNationality;
	/**
	 * 省份编码
	 */
	private String provincesCode;
	/**
	 * 省份名称
	 */
	private String provincesName;
	/**
	 * 城市编码
	 */
	private String cityCode;
	/**
	 * 城市名称
	 */
	private String cityName;
	/**
	 * 区编码
	 */
	private String areaCode;
	/**
	 * 区名称
	 */
	private String areaName;
	/**
	 * 受益人地址
	 */
	private String beneContactAddress;
	/**
	 * 受益顺序
	 */
	private String beneOrder;
	/**
	 * 受益比例
	 */
	private String beneRate;
	/**
	 * 手机号码
	 */
	private String beneMobile;
	/**
	 * 受益人邮箱
	 */
	private String beneEmail;
	/**
	 * 联系电话
	 */
	private String beneTel;
	/**
	 * 住宅电话
	 */
	private String beneTelPhone;

	/**
	 * 职业类别
	 */
	private String beneJobType;
	/**
	 * 职业编码一级
	 */
	private String beneJobCodeLevel1;

	/**
	 * 职业名称一级
	 */
	private String beneJobCodeName1;

	/**
	 * 职业编码二级
	 */
	private String beneJobCodeLevel2;

	/**
	 * 职业名称二级
	 */
	private String beneJobCodeName2;

	/**
	 * 职业编码三级
	 */
	private String beneJobCodeLevel3;
	/**
	 * 职业名称三级
	 */
	private String beneJobCodeName3;
	/**
	 * 收入
	 */
	private String beneIncome;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
