package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人服务台
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-11 19:10:33
 */
@TableName("agent_reception")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentReceptionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 消息的id
	 */
	@TableId(value = "message_id",type = IdType.INPUT)
	private String messageId;
	/**
	 * 经纪人编码
	 */
	private String agentCode;
	/**
	 * 服务标题
	 */
	private String title;
	/**
	 * 服务状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 服务类型 1:待办服务  2:待办销售  3:待办咨询
	 */
	private Integer receptionType;

	@ApiModelProperty(value = "点击跳转类型")
	private Integer jumpType;

	@ApiModelProperty(value = "点击跳转参数")
	private String jumpParam;

	@ApiModelProperty(value = "子标题")
	private String subtitle;
}
