package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.manage.modules.policy.service.preservation.project.PreservationTerminationProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 保全申请详细信息
 *
 * <AUTHOR>
 * @date 2022-03-21 10:39
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "保全申请详细信息")
@Data
public class PreservationApplyDetail extends PreservationApplyInfo{

    /**
     * 被保人姓名集合
     */
    @ApiModelProperty(value = "被保人姓名集合", example = "张三,李四")
    private String insuredNames;

    /**
     * 被保人证据号码集合
     */
    @ApiModelProperty(value = "被保人证据号码集合", example = "123,567")
    private String insuredIdNos;

    /**
     * 管理机构编码
     */
    @ApiModelProperty(value = "管理机构编码", example = "123567")
    private String manageOrgCode;

    /**
     * 管理机构名称
     */
    @ApiModelProperty(value = "管理机构名称", example = "北京分公司")
    private String manageOrgName;

    /**
     * 销售渠道编码
     */
    @ApiModelProperty(value = "销售渠道编码", example = "zhnx")
    private String sellChannelCode;

    /**
     * 销售渠道名称
     */
    @ApiModelProperty(value = "销售渠道编码", example = "中和农信")
    private String sellChannelName;

    /**
     * 代理人编码
     */
    @ApiModelProperty(value = "代理人编码", example = "ag50505020200")
    private String policyAgentCode;

    /**
     * 代理人名称
     */
    @ApiModelProperty(value = "代理人名称", example = "名称")
    private String policyAgentName;

    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人编码", example = "123567")
    private String referrerCode;

    /**
     * 推荐人名称
     */
    @ApiModelProperty(value = "推荐人名称", example = "张三")
    private String referrerName;

    /**
     * 渠道推荐人编码
     */
    @ApiModelProperty(value = "渠道推荐人编码", example = "123567")
    private String channelReferrerCode;

    /**
     * 渠道推荐人名称
     */
    @ApiModelProperty(value = "渠道推荐人名称", example = "张三")
    private String channelReferrerName;

    @ApiModelProperty(value = "分支名称", example = "总部")
    private String branchName;

    @ApiModelProperty(value = "分支名称", example = "总部")
    private String branchCode;

    @ApiModelProperty(value = "保单中心交互状态 0待处理1处理完成", example = "1")
    private Integer processingStatus;

    @ApiModelProperty(value = "保单中心交互完成时间", example = "2019-01-01 15:12")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date processingTime;

    @ApiModelProperty("保单初始渠道推荐人信息")
    private String customerManagerCode;

    @ApiModelProperty("保单初始渠道推荐人信息")
    private String customerManagerChannelCode;

    @ApiModelProperty("保全备注信息")
    private String remark;

    @ApiModelProperty(value = "保全补充说明")
    private String additionalRemark;

    @ApiModelProperty(value = "增减员信息")
    List<PreservationPolicyBasicChange> addOrSubtractList;
    @Deprecated
    @ApiModelProperty(value = "附加险解约信息集合(请使用terminationProductDetail属性)")
    List<ProductTerminationInfo> productTerminationList;

    @ApiModelProperty(value = "团险分单层-初始渠道推荐人变更集合")
    PreservationChannelReferrerChangeVo channelReferrerChangeForm;

    @ApiModelProperty(value = "保单层-初始渠道推荐人变更信息")
    PreservationCustomerManagerChangeForm preservationCustomerManagerChangeForm;

    @ApiModelProperty(value = "保单号变更信息")
    private PreservationPolicyNoChangeForm preservationPolicyNoChangeForm;

    @ApiModelProperty(value = "被保人变更信息")
    PreservationInsuredChangeForm insuredChangeForm;

    @ApiModelProperty(value = "投保人基础信息变更")
    PreservationApplicantChangeForm preservationApplicantChange;

    @ApiModelProperty(value = "保全基础信息变更")
    PreservationInfoChangeForm preservationInfoChangeForm;

    @ApiModelProperty(value = "车险信息变更")
    PreservationVehicleInfoChangeForm preservationVehicleInfoChangeForm;

    @ApiModelProperty(value = "退保明细")
    List<PreservationSurrenderDetailVo> surrenderDetailList;

    @ApiModelProperty(value = "解除附加险明细")
    private PreservationTerminationProductVo terminationProductDetail;

    @ApiModelProperty(value = "变更险种集合")
    private PreservationProductChangePack productChangePack;

    @ApiModelProperty(value = "保单保费变更明细")
    private PreservationPolicyPremiumChangeForm policyPremiumChangeForm;

    @ApiModelProperty("保单分销渠道编码变更明细")
    private PreservationChannelDistributionChangeForm channelDistributionChangeForm;

    @ApiModelProperty(value = "保单渠道变更明细")
    private PreservationChannelChangeVo preservationChannelChangeVo;

    @ApiModelProperty(value = "保单补退费明细")
    private PreservationPolicySupplementForm policySupplementForm;

}
