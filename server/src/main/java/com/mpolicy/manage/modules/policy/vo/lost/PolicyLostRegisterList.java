package com.mpolicy.manage.modules.policy.vo.lost;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.manage.modules.policy.enums.lost.HandleResultEnum;
import com.mpolicy.manage.modules.policy.enums.lost.HandleStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 丢单登记补录列表
 *
 * <AUTHOR>
 * @date 2023-10-12 10:31
 */
@ApiModel(value = "丢单登记补录列表")
@Data
public class PolicyLostRegisterList {

    private Integer id;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;
    /**
     * 处理状态 0:待处理 1:处理成功 2:处理失败
     */
    @ApiModelProperty(value = "处理状态 0:待处理 1:处理成功 2:处理失败")
    private Integer handleStatus;

    /**
     * 处理状态名称
     */
    @ApiModelProperty(value = "处理状态名称")
    private String handleStatusName;

    public String getHandleStatusName() {
        return Objects.isNull(handleStatus)?"":HandleStatusEnum.decode(handleStatus).getName();
    }

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    private Integer handleResult;

    /**
     * 处理结果内容
     */
    @ApiModelProperty(value = "处理结果内容")
    private String handleResultContent;

    public String getHandleResultContent() {
        if (!StringUtils.isBlank(manualHandleResult) && HandleResultEnum.MANUAL_HANDLE_REFUSE.getCode().equals(handleResult)) {
            return manualHandleResult;
        }
        if (!Objects.isNull(handleResult) && HandleResultEnum.EXIST_RECOMMEND.getCode().equals(handleResult)) {
            return StringUtils.isBlank(remark)?HandleResultEnum.EXIST_RECOMMEND.getDesc():remark;
        }
        return Objects.isNull(handleResult)?"":HandleResultEnum.decode(handleResult).getDesc();
    }

    private String manualHandleResult;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date registerTime;
    /**
     * 登记人工号
     */
    @ApiModelProperty(value = "登记人工号")
    private String registerUserId;
    /**
     * 登记人姓名
     */
    @ApiModelProperty(value = "登记人姓名")
    private String registerUserName;
    /**
     * 登记人所属区域
     */
    @ApiModelProperty(value = "登记人所属区域")
    private String registerUserRegionName;
    /**
     * 登记人所属区域
     */
    @ApiModelProperty(value = "登记人所属分支")
    private String referrerOgrName;

    /**
     * 推荐人工号
     */
    @ApiModelProperty(value = "推荐人工号")
    private String recommendId;
    /**
     * 推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendName;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    private String remark;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 处理成功类型
     */
    @ApiModelProperty(value = "处理成功类型 1:系统问题 2:操作问题 3:操作问题 4:其他",example = "1:系统问题 2:操作问题 3:操作问题 4:其他")
    private Integer handleSuccessType;
    /**
     * 处理成功结果
     */
    @ApiModelProperty(value = "处理成功结果")
    private String handleSuccessResult;

    /**
     * 失败原因
     */
    private String failResult;
}
