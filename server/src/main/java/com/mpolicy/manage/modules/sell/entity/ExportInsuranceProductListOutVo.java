package com.mpolicy.manage.modules.sell.entity;

import cn.hutool.core.annotation.Alias;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExportInsuranceProductListOutVo implements Serializable {
    private static final long serialVersionUID = 6339180201246343949L;

    @ApiModelProperty("险种编码")
    private String productCode;

    @ApiModelProperty("险种名称")
    private String productName;

    @ApiModelProperty("保司名称")
    private String companyName;
    /**
     * 主附险标记 1为主险 0为附加险
     */
    @ApiModelProperty("主附险")
    private String mainProduct;

    @ApiModelProperty(value = "创建时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String createTime;

    /**
     * 协议险种
     */
    private  String protocolFlag;
}
