package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireQuestionEntity;
import com.mpolicy.web.common.utils.Query;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.agent.dao.AgentQuestionnaireAnswerDao;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireAnswerEntity;
import com.mpolicy.manage.modules.agent.service.AgentQuestionnaireAnswerService;


@Slf4j
@Service("agentQuestionnaireAnswerService")
public class AgentQuestionnaireAnswerServiceImpl extends ServiceImpl<AgentQuestionnaireAnswerDao, AgentQuestionnaireAnswerEntity> implements AgentQuestionnaireAnswerService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentQuestionnaireAnswerEntity> page = this.page(
                new Query<AgentQuestionnaireAnswerEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    public void deleteByQuestionId(Integer questionId) {
        boolean result = this.update(
                new UpdateWrapper<AgentQuestionnaireAnswerEntity>().lambda()
                        .eq(AgentQuestionnaireAnswerEntity::getQuestionId, questionId)
                        .set(AgentQuestionnaireAnswerEntity::getDeleted, Constant.DELETE_FLAG));
        if(!result){
            log.warn("问卷选项删除失败 questionId= {}",questionId);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("问卷选项删除失败"));
        }
    }
}
