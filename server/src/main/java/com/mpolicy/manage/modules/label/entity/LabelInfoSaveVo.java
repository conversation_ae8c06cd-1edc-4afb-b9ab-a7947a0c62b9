package com.mpolicy.manage.modules.label.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LabelInfoSaveVo implements Serializable {
    private static final long serialVersionUID = 2862194243941390451L;

    @NotBlank(message = "标签名称不能为空")
    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @NotBlank(message = "标签分组(字典)不能为空")
    @ApiModelProperty(value = "标签分组(字典)")
    private String labelGroup;

    @NotBlank(message = "标签库编码不能为空")
    @ApiModelProperty(value = "标签库编码")
    private String libraryCode;

    @ApiModelProperty(value = "保障类型(字典)")
    private String portfolioType;

    @ApiModelProperty(value = "启用状态 1:启用;0:关闭")
    private Integer enabled;

    @ApiModelProperty(value = "标签备注信息")
    private String remark;
}
