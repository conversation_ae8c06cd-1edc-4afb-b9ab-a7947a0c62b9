package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 保全类型
 */
public enum EndorsementTypeEnum {

    /**
     * 保全类型
     */
    ADD_REDUCE("ENDORSEMENT_TYPE:1", "增减员"),
    ADD("ENDORSEMENT_TYPE:2", "增员"),
    REDUCE("ENDORSEMENT_TYPE:3", "减员");

    @Getter
    private String code;

    @Getter
    private String name;


    EndorsementTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static EndorsementTypeEnum decode(String code) {
        return Arrays.stream(EndorsementTypeEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }
}
