package com.mpolicy.manage.modules.agent.vo.agentinfo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentUserInfoVo implements Serializable {
    private static final long serialVersionUID = 3279024360461805807L;

    @ApiModelProperty("头像信息")
    private String avatar;

    @ApiModelProperty("人员类型")
    private String agentType;

    @ApiModelProperty("姓名")
    private String agentName;

    @ApiModelProperty("代理人昵称")
    private String agentNickName;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("婚姻状态")
    private String marital;

    @ApiModelProperty("政治面貌")
    private String politics;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("毕业学校")
    private String school;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号")
    private String idCard;

    @ApiModelProperty("证件有效期起")
    private Date idStartDate;

    @ApiModelProperty("证件有效期止")
    private Date idEndDate;

    @ApiModelProperty("证件号")
    private boolean idLongTerm;

    @ApiModelProperty("学历")
    private String degree;

    @ApiModelProperty("微信账号")
    private String weChat;

    @ApiModelProperty("是否推荐")
    private Integer recommendStatus;

    @ApiModelProperty("居住省份")
    private String liveProvinceCode;

    @ApiModelProperty("居住城市")
    private String liveCityCode;

    @ApiModelProperty("居住地区")
    private String areaCode;

    @ApiModelProperty("服务省份")
    private String serverProvince;

    @ApiModelProperty("服务城市")
    private String cityCode;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("服务属性")
    private String serviceAttribute;

    @ApiModelProperty("职位")
    private String position;

    @ApiModelProperty("初始档次")
    private String positionDegree;

    @ApiModelProperty("区域经理所属区域")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String areaManagerRegion;

    @ApiModelProperty("入职日期")
    private Date entryDate;

    @ApiModelProperty("组织编码")
    private String orgCode;

    private List<String> orgParentNodeCode;

    @ApiModelProperty("业务编码")
    private String businessCode;

    @ApiModelProperty("展业地区")
    private String acquisitionArea;

    @ApiModelProperty(value = "热度")
    private Integer hot;

    @ApiModelProperty(value = "人员状态")
    private Integer agentStatus;

    @ApiModelProperty(value = "增员人")
    private String recruitCode;

    @ApiModelProperty(value = "从业年限")
    private String workTime;

    @ApiModelProperty(value = "初始服务人数")
    private String initServiceNum;

    @ApiModelProperty(value = "服务客户数")
    private String serviceCustomerNum;

    @ApiModelProperty(value = "擅长领域")
    private String expertise;

    @ApiModelProperty(value = "个人标签")
    private String agentLabel;

    @ApiModelProperty(value = "个人简介")
    private String introduce;

    @ApiModelProperty(value = "是否选为专属顾问")
    private Integer isOptional;

    @ApiModelProperty("离职状态0:在职 1:离职")
    private Integer quitStatus;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("国籍")
    private String country;

    @ApiModelProperty("人员类别")
    private String agentCategory;

    @ApiModelProperty("人员性质")
    private String agentNature;

    @ApiModelProperty("版本号")
    private Long revision;

    @ApiModelProperty("代理人服务区域")
    private List<String> areaManagerRegionList;

    @ApiModelProperty("片区")
    private List<String> subregionList;

    @ApiModelProperty("片区")
    private String subregion;

    @ApiModelProperty("保险服务人员备注")
    private String managerRegionRemark;

    @ApiModelProperty("保险服务人员管理区域信息")
    private List<String> managerRegionCodeList;
}
