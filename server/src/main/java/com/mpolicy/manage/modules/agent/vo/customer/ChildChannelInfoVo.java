package com.mpolicy.manage.modules.agent.vo.customer;

import com.mpolicy.open.common.common.ChannelInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: ChildChannelInfoVo
 * Description: 获取客户渠道列表-包含子渠道
 * date: 2024/2/22 16:18
 *
 * <AUTHOR>
 */
@Data
public class ChildChannelInfoVo implements Serializable {
    private static final long serialVersionUID = 4399057312216212838L;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 渠道名
     */
    @ApiModelProperty(value = "渠道名")
    private String channelName;
    /**
     * 子渠道列表
     */
    @ApiModelProperty(value = "子渠道列表")
    private List<ChannelInfoVo> childChannelList;
}
