package com.mpolicy.manage.modules.policy.vo.preservation.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 保全-初始推荐人变更-批量导入
 *
 * <AUTHOR>
 * @date 2024/10/29 19:22
 */
@Data
public class PreservationReferrerExcelVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 98465132L;

    @ExcelProperty(value = "序号", index = 0)
    private String serialNo;

    @ExcelProperty(value = "保单号", index = 1)
    private String policyCode;

    @ExcelProperty(value = "变更前渠道推人姓名", index = 2)
    private String beforeReferrerName;

    @ExcelProperty(value = "变更前渠道推人工号", index = 3)
    private String beforeReferrerWno;

    @ExcelProperty(value = "变更后渠道推人姓名", index = 4)
    private String correctedReferrerName;

    @ExcelProperty(value = "变更后渠道推人工号", index = 5)
    private String correctedReferrerWno;

    @ApiModelProperty("业务Key")
    private String businessKey;
    public String valid() {
        StringBuilder str = new StringBuilder();
        if (StringUtils.isBlank(serialNo)) {
            str.append("缺少序号;");
        }
        if (StringUtils.isBlank(policyCode)) {
            str.append("缺少保单号;");
        }
        if (StringUtils.isBlank(beforeReferrerName)) {
            str.append("缺少变更前渠道推人姓名;");
        }
        if (StringUtils.isBlank(beforeReferrerWno)) {
            str.append("缺少变更前渠道推人工号;");
        }
        if (StringUtils.isBlank(correctedReferrerName)) {
            str.append("缺少变更后渠道推人姓名;");
        }
        if (StringUtils.isBlank(correctedReferrerWno)) {
            str.append("缺少变更后渠道推人工号;");
        }
        return str.toString();
    }

    public boolean isEmptyLine() {
        return StringUtils.isBlank(serialNo)&& StringUtils.isBlank(policyCode)
                && StringUtils.isBlank(beforeReferrerWno)
                && StringUtils.isBlank(correctedReferrerWno);
    }
}
