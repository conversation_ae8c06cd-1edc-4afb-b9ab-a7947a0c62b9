package com.mpolicy.manage.modules.insure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureOrderInsuredProductDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInsuredProductEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderInsuredProductService;
import org.springframework.stereotype.Service;

/**
 * 投保订单被保人产品信息接口实现
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@Service("insureOrderInsuredProductService")
public class InsureOrderInsuredProductServiceImpl extends ServiceImpl<InsureOrderInsuredProductDao, InsureOrderInsuredProductEntity> implements InsureOrderInsuredProductService {

}
