package com.mpolicy.manage.modules.agent.controller;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoExportVo;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.ChannelVo;
import com.mpolicy.manage.modules.agent.vo.customer.ChildChannelInfoVo;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 渠道信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 16:37:12
 */
@RestController
@RequestMapping("sys/channelinfo")
@Api(tags = "渠道信息")
public class ChannelInfoController {

    @Autowired
    private ChannelInfoService channelInfoService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取渠道信息列表", notes = "分页获取渠道信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "channelName", dataType = "String", value = "渠道名称", example = "平安好医生"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道编码", example = "Q123456"),
            @ApiImplicitParam(paramType = "query", name = "channelType", dataType = "int", value = "渠道类型 只能为0和1", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "channelClassification", dataType = "int", value = "渠道分类", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "string(date-time)", value = "创建时间", example = "2021-01-28"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result<PageUtils> list(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params) {
        PageUtils page = channelInfoService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation("根据code获取渠道信息")
    @GetMapping("/info/{code}")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result<ChannelInfoEntity> info(@PathVariable("code") String code) {
        ChannelInfoEntity channelInfo = channelInfoService.getOne(new QueryWrapper<ChannelInfoEntity>().lambda().eq(ChannelInfoEntity::getChannelCode, code));

        return Result.success(channelInfo);
    }

    /**
     * 保存
     */
    @ApiOperation("保存渠道信息")
    @PostMapping("/save")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result<ChannelInfoEntity> save(@RequestBody @Validated(AddGroup.class) ChannelInfoEntity channelInfoEntity) {
        if (channelInfoService.info(channelInfoEntity.getChannelCode()) != null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("渠道编码已存在"));
        }
        boolean save = channelInfoService.saveOrUpdateEntity(channelInfoEntity);

        if (save) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 修改
     */
    @ApiOperation("修改渠道信息")
    @PostMapping("/update")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result<ChannelInfoEntity> update(@RequestBody @Validated(UpdateGroup.class) ChannelInfoEntity channelInfoEntity) {
        if (channelInfoService.info(channelInfoEntity.getChannelCode()) == null) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("渠道编码所属记录不存在"));
        }
        boolean update = channelInfoService.saveOrUpdateEntity(channelInfoEntity);
        if (update) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }


    /**
     * 删除
     */
    @ApiOperation(value = "根据code删除渠道信息", hidden = true)
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result<ChannelInfoEntity> delete(@PathVariable("code") String code) {
        boolean delete = channelInfoService.remove(new QueryWrapper<ChannelInfoEntity>().lambda().eq(ChannelInfoEntity::getChannelCode, code));

        if (delete) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
    }

    /**
     * 修改渠道信息启用状态
     *
     * @param code    渠道信息code
     * @param enabled 状态
     * @return
     */
    @ApiOperation(value = "修改渠道信息启用状态", notes = "修改渠道信息启用状态")
    @PostMapping("/changeEnable/{code}")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result changeEnable(
            @ApiParam(name = "code", value = "渠道信息code", required = true)
            @PathVariable("code") String code,
            @ApiParam(name = "enabled", value = "启用状态 0:禁用 1:启用 ", required = true)
            @RequestParam("enabled") Integer enabled,
            @ApiParam(name = "revision", value = "版本号", required = true)
            @RequestParam("revision") long revision) {
        boolean result = channelInfoService.changeEnable(code, enabled, revision);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }

    /**
     * 渠道名称列表
     */
    @ApiOperation(value = "获取渠道名称列表", notes = "获取渠道名称列表")
    @GetMapping("/listChannelName")
    public Result<List<ChannelVo>> listChannelName() {
        List<ChannelVo> list = channelInfoService.list().stream().map(item -> {
            ChannelVo vo = new ChannelVo();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return Result.success(list);
    }

    /**
     * 获取销售渠道(核心业务-保单中心)
     */
    @ApiOperation(value = "获取销售渠道(核心业务-保单中心)", notes = "获取销售渠道(核心业务-保单中心)")
    @GetMapping("/getChannelList")
    public Result<List<ChannelInfoVo>> getChannelList(@RequestParam(value = "flag", required = false) String flag) {
        List<ChannelInfoVo> list = channelInfoService.getChannelList(false, flag);
        return Result.success(list);
    }

    /**
     * 获取销售渠道(核心业务-保单中心)
     */
    @ApiOperation(value = "获取销售渠道(核心业务-保单中心) 按权限", notes = "获取销售渠道(核心业务-保单中心) 按权限")
    @GetMapping("/getChannelListPermission")
    public Result<List<ChannelInfoVo>> getChannelListPermission(@RequestParam(value = "flag", required = false) String flag) {
        List<ChannelInfoVo> list = channelInfoService.getChannelList(true, flag);
        return Result.success(list);
    }

    /**
     * 渠道管理信息导出
     * @param response response返回
     * @param params map参数
     * @return
     */
    @ApiOperation(value = "渠道管理信息导出", notes = "渠道管理信息导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "channelName", dataType = "String", value = "渠道名称", example = "平安好医生"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道编码", example = "Q123456"),
            @ApiImplicitParam(paramType = "query", name = "channelType", dataType = "int", value = "渠道类型 只能为0和1", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "channelClassification", dataType = "int", value = "渠道分类", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "string(date-time)", value = "创建时间", example = "2021-01-28"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions(value = {"channel:info:all"})
    public Result export(
            HttpServletResponse response,
            @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils page = channelInfoService.queryPage(params);
        AtomicInteger i = new AtomicInteger(1);
        List<ChannelInfoExportVo> list = (List<ChannelInfoExportVo>) page.getList().stream().map(x -> {
            ChannelInfoEntity channelInfo = (ChannelInfoEntity) x;
            ChannelInfoExportVo target = new ChannelInfoExportVo();
            BeanUtils.copyProperties(x, target);
            target.setChannelType(channelInfo.getChannelType() == 1?"个人":"企业");
            String channelClassification = null;
            switch (channelInfo.getChannelClassification()){
                case 0:
                    channelClassification = "普通渠道";
                    break;
                case 1:
                    channelClassification = "高客渠道";
                    break;
                case 2:
                    channelClassification = "农村渠道";
                    break;
                default:
                    break;
            }
            target.setChannelClassification(channelClassification);
            target.setPersonName(channelInfo.getName());
            target.setPersonIdCard(channelInfo.getIdCard());
            target.setCreateTimeStr(DateUtils.format(channelInfo.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            target.setEnabled(channelInfo.getEnabled()==0?"关闭":"启用");
            return target;
        }).collect(Collectors.toList());

        ExcelUtil.writeExcel(response, list, URLUtil.encode("渠道信息", StandardCharsets.UTF_8), "sheet1", new ChannelInfoExportVo());
        return Result.success();
    }

    /**
     * 客户中心渠道修改--渠道列表
     *
     * @param flag 0:所有渠道 1:有效渠道
     * @return
     */
    @ApiOperation(value = "获取渠道信息(客户中心-客户渠道修改)", notes = "获取渠道信息(客户中心-客户渠道修改)")
    @GetMapping("/getCustomerChannelList")
    public Result<List<ChildChannelInfoVo>> getCustomerChannelList(
            @ApiParam(name = "flag", value = "1:所有渠道 0:有效渠道", required = true)
            @RequestParam(value = "flag", required = false) String flag) {
        List<ChannelInfoVo> list = channelInfoService.getChannelList(false, flag);
        //获取配置信息  "child_channel_name":["CAPP-相助APP","bdsh-相助生活","workWeChat-企业微信","officialAccount-获客牛断保话术","xzjf-相助金服","zctj-整村推进","SCQ-素材圈"]
        String commonChannelConfig = ConstantCacheHelper.getValue(Constant.COMMON_CHANNEL_CONFIG, null);
        if (StringUtils.isBlank(commonChannelConfig)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("渠道配置映射缺失"));
        }
        JSONObject json = JSONObject.parseObject(commonChannelConfig);
        JSONArray channelName = json.getJSONArray("child_channel_name");
        List<ChildChannelInfoVo> collect = list.stream().map(a -> {
            ChildChannelInfoVo bean = new ChildChannelInfoVo();
            BeanUtils.copyProperties(a, bean);
            if (StringUtils.equals(a.getChannelCode(), Constant.ZHNX_CHANNEL_CODE) && CollectionUtils.isNotEmpty(channelName)) {
                List<com.mpolicy.open.common.common.ChannelInfoVo> childChannelList = channelName.stream().map(b -> {
                    com.mpolicy.open.common.common.ChannelInfoVo childChannelInfo = new com.mpolicy.open.common.common.ChannelInfoVo();
                    childChannelInfo.setChannelCode(b.toString().split("-")[0]);
                    childChannelInfo.setChannelName(b.toString().split("-")[1]);
                    return childChannelInfo;
                }).collect(Collectors.toList());
                bean.setChildChannelList(childChannelList);
            }
            return bean;
        }).collect(Collectors.toList());
        return Result.success(collect);
    }
}
