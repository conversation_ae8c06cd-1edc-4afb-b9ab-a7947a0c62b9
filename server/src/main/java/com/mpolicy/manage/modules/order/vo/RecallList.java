package com.mpolicy.manage.modules.order.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;


/**
 * <p>
 *     回溯信息列表
 * </p>
 *
 *  @author: zcd
 */
@ApiModel(value = "回溯信息列表")
@Data
public class RecallList {


    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNO;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderCode;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    private String applicantName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "投保人证件号")
    private String applicantIdCard;

    /**
     * 保险公司
     */
    @ApiModelProperty(value = "保险公司")
    private String companyName;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 投保日期
     */
    @ApiModelProperty(value = "投保日期")
    private String applicantTime;



}
