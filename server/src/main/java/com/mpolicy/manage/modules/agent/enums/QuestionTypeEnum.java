package com.mpolicy.manage.modules.agent.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum QuestionTypeEnum {

    SINGLE_CHOICE(1, "单选题"),
    MULTIPLE_CHOICE(2, "多选题"),
    JUDGEMENT(3, "判断题")
    ;

    private Integer type;
    private String desc;

    QuestionTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static QuestionTypeEnum getValueByKey(Integer type) {
        return Arrays.stream(values()).filter((x) -> Objects.equals(x.type,type)).findFirst().orElse(null);
    }
}
