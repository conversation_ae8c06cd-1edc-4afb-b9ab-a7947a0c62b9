package com.mpolicy.manage.modules.agent.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: AgentSignNumOut
 * Description: 文件签署人数返回信息
 * date: 2023/6/1 16:40
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "文件签署人数返回信息",description = "文件签署人数返回信息")
public class AgentSignNumOut {

    /**
     * 应签署人数
     */
    @ApiModelProperty(value = "应签署人数")
    private Integer shouldNum;

    /**
     * 已签署人数
     */
    @ApiModelProperty(value = "已签署人数")
    private Integer alreadyNum;

    /**
     * 未签署人数
     */
    @ApiModelProperty(value = "未签署人数")
    private Integer unsignedNum;

    /**
     * 系统默认完成人数
     */
    @ApiModelProperty(value = "系统默认完成人数")
    private Integer defaultFinishNum;
}
