package com.mpolicy.manage.modules.order.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单商品信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-16 10:21:13
 */
@TableName("order_applicant_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderApplicantInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Integer id;
	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 投保人姓名
	 */
	private String applicantName;
	/**
	 * 投保人证件号
	 */
	private String applicantIdCard;
	/**
	 * 投保人证件类型
	 */
	private String applicantIdType;
	/**
	 * 投保人手机号
	 */
	private String applicantMobile;
	/**
	 * 投保人性别 0:女 1:男
	 */
	private Integer applicantGender;
	/**
	 * 投保人生日
	 */
	private String applicantBirthday;
	/**
	 * 投保人年龄
	 */
	private Integer applicantAge;
	/**
	 * 投保人职业 字典
	 */
	private String holderJob;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private long revision;
	/**
	 * 创建人
	 */
	private String createUser;
}
