package com.mpolicy.manage.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sys.entity.MpDictionaryEntity;

import java.util.List;
import java.util.Map;

/**
 * 字典表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-26 16:49:19
 */
public interface MpDictionaryService extends IService<MpDictionaryEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    boolean saveEntity(MpDictionaryEntity mpDictionaryEntity);

    boolean updateEntity(MpDictionaryEntity mpDictionaryEntity);

    void move(List<MpDictionaryEntity> mpDictionaryEntityList, String parentKey);

    boolean removeEntity(String key);

    void refreshDic();

}

