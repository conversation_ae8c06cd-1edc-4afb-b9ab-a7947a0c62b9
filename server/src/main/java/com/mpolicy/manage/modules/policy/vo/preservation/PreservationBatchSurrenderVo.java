package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.protocol.utils.ValidatePropertyUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PreservationBatchSurrenderVo extends BaseRowModel implements Serializable {

    @ApiModelProperty(value = "保单号", required = true)
    @NotBlank(message = "保单号不能为空")
    @ExcelProperty(value = "保单号", index = 0)
    private String policyCode;

    @ApiModelProperty(value = "保全批单号")
    @ExcelProperty(value = "保全批单号", index = 3)
    private String endorsementNo;

    @ApiModelProperty(value = "保全项目", required = true)
    @NotBlank(message = "保全项目不能为空")
    @ExcelProperty(value = "保全项目", index = 1)
    private String preservationProject;

    /**
     * 保全变更原因
     */
    @ApiModelProperty(value = "保全变更原因", required = true)
    @NotBlank(message = "保全变更原因不能为空")
    @ExcelProperty(value = "创建保全原因", index = 2)
    private String preservationWhy;

    /**
     * 保全生效日期
     */
    @ApiModelProperty(value = "保全生效日期", required = true)
    @NotNull(message = "保全生效日期不能为空")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "保全生效日期", index = 4)
    private String preservationEffectTime;

    /**
     * 退保金额
     */
    @ApiModelProperty(value = "退保金额", example = "100.52")
    @ExcelProperty(value = "退保金额", index = 5)
    private String surrenderCash;

    /**
     * 行号
     */
    private Integer numNo;

    /**
     * 保单中心保单唯一编号
     */
    @ApiModelProperty(value = "保单中心保单唯一编号", required = true)
    @NotBlank(message = "保单中心保单唯一编号不能为空")
    private String contractCode;

    public boolean isEmptyLine() {
        if (StringUtils.isBlank(endorsementNo)
                && StringUtils.isBlank(policyCode)
                && StringUtils.isBlank(preservationEffectTime)
                && StringUtils.isBlank(surrenderCash)) {
            return true;
        }
        return false;
    }

    public BigDecimal convertSurrenderCash() {
        if (ValidatePropertyUtil.isDecimal(surrenderCash) || StringUtils.equals("0", surrenderCash)) {
            return new BigDecimal(surrenderCash);
        }
        return null;
    }

    public Date convertPreservationEffectTime() {
        if (ValidatePropertyUtil.isExcelDate(preservationEffectTime)) {
            return DateUtils.formatExcelDate(preservationEffectTime);
        }
        return null;
    }
}
