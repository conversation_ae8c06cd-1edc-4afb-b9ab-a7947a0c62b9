package com.mpolicy.manage.modules.helper.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保险月度明细业务临时表 
 * 
 * <AUTHOR>
 * @date 2023-09-03 23:39:57
 */
@TableName("tmp_policy_supervise")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TmpPolicySuperviseEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 日期
	 */
	private Date policyData;
	/**
	 * 保单年度
	 */
	private Integer policyYear;
	/**
	 * 保单月度
	 */
	private Integer policyMonth;
	/**
	 * 保司名称
	 */
	private String companyName;
	/**
	 * 产品名称
	 */
	private String productName;
	/**
	 * 保单保费
	 */
	private BigDecimal policyCash;
	/**
	 * 佣金
	 */
	private BigDecimal policyCommission;
	/**
	 * 保单业务差额
	 */
	private BigDecimal policyDiffCash;
	/**
	 * 数据业务状态 0 待处理 1：匹配正常，2：存在差异
	 */
	private Integer dataStatus;
	/**
	 * 备注
	 */
	private String policyDesc;
}
