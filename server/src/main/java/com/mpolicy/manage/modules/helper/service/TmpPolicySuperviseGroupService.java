package com.mpolicy.manage.modules.helper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.helper.entity.TmpPolicySuperviseGroupEntity;

import java.util.List;
import java.util.Map;

/**
 * 保险月度明细金额临时表 
 *
 * <AUTHOR>
 * @date 2023-09-04 00:02:08
 */
public interface TmpPolicySuperviseGroupService extends IService<TmpPolicySuperviseGroupEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    List<TmpPolicySuperviseGroupEntity> policyGroupList(Map<String,Object> paramMap);

    void updateList(List<TmpPolicySuperviseGroupEntity> updateList);
}

