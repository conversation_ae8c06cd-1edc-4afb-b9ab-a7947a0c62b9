package com.mpolicy.manage.modules.sell.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.sell.dao.SellProductNoticeDao;
import com.mpolicy.manage.modules.sell.entity.SellProductNoticeEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductNoticeListOut;
import com.mpolicy.manage.modules.sell.service.ISellProductNoticeService;
import com.mpolicy.service.common.service.DicCacheHelper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: mpolicy-admin
 * @description: 产品告知
 * @author: lsc
 * @created: 2022/04/25 17:10
 */
@Service("sellProductNoticeService")
public class SellProductNoticeServiceImpl extends ServiceImpl<SellProductNoticeDao, SellProductNoticeEntity> implements ISellProductNoticeService {

    /**
     * 获取产品告知书
     *
     * @param productCode
     * @return
     */
    @Override
    public List<SellProductNoticeListOut> findSellProductNoticeList(String productCode) {
        List<SellProductNoticeListOut> resultList = baseMapper.findSellProductNoticeList(productCode);
        Map<String, String> map = DicCacheHelper.getSons("SELL_PRODUCT_NOTICE_TYPE").stream()
                .collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, key -> key.getValue()));
        resultList.forEach(action -> {
            action.setNoticeType(map.get(action.getNoticeType()));
            action.setIsReadDesc(action.getIsRead().equals(1) ? "必读" : "非必读");
        });
        return resultList;
    }
}
