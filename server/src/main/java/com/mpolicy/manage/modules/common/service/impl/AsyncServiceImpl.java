package com.mpolicy.manage.modules.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.google.common.collect.Lists;
import com.mpolicy.agent.common.enums.JumpTypeEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.customer.common.enums.MsgTypeEnum;
import com.mpolicy.im.client.feign.ImClient;
import com.mpolicy.im.common.model.chat.PushAppNoticeVo;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.enums.QueueEnum;
import com.mpolicy.manage.modules.agent.dao.AgentMessageInfoDao;
import com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao;
import com.mpolicy.manage.modules.agent.entity.AgentCustomerNewsEntity;
import com.mpolicy.manage.modules.agent.entity.AgentMessageInfoEntity;
import com.mpolicy.manage.modules.agent.entity.AgentReceptionEntity;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.enums.AgentTypeEnum;
import com.mpolicy.manage.modules.agent.service.AgentCustomerNewsService;
import com.mpolicy.manage.modules.agent.service.AgentMessageInfoService;
import com.mpolicy.manage.modules.agent.service.AgentReceptionService;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.CustomerCodeListByAgentCodeAndRegionOut;
import com.mpolicy.manage.modules.agent.vo.agentinfo.UpdateAreaManagerRegionVo;
import com.mpolicy.manage.modules.chat.entity.ChatListEntity;
import com.mpolicy.manage.modules.chat.entity.ChatWorkOrderEntity;
import com.mpolicy.manage.modules.chat.service.ChatListService;
import com.mpolicy.manage.modules.chat.service.ChatWorkOrderService;
import com.mpolicy.manage.modules.common.service.AsyncService;
import com.mpolicy.manage.modules.customer.dao.CustomerAgentMapDao;
import com.mpolicy.manage.modules.customer.entity.CustomerAgentMapEntity;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.entity.CustomerMessageInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerAgentMapService;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.customer.service.CustomerMessageInfoService;
import com.mpolicy.manage.modules.policy.entity.EpPolicyTransferTaskEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyTransferTaskService;
import com.mpolicy.manage.modules.sell.dao.SellProductInfoDao;
import com.mpolicy.manage.modules.sell.entity.SellProductInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncServiceImpl implements AsyncService {


    @Autowired
    private AgentMessageInfoDao agentMessageInfoDao;

    @Autowired
    private AgentUserInfoDao agentUserInfoDao;

    @Autowired
    private CustomerAgentMapDao customerAgentMapDao;

    @Autowired
    private ImClient imClient;

    @Autowired
    private CustomerAgentMapService customerAgentMapService;

    @Autowired
    private AgentUserInfoService agentUserInfoService;

    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;

    @Autowired
    private CustomerMessageInfoService customerMessageInfoService;

    @Autowired
    private ChatListService chatListService;

    @Autowired
    private ChatWorkOrderService chatWorkOrderService;

    @Autowired
    private AgentCustomerNewsService agentCustomerNewsService;

    @Autowired
    private AgentMessageInfoService agentMessageInfoService;

    @Autowired
    private AgentReceptionService agentReceptionService;
    @Autowired
    private EpPolicyTransferTaskService epPolicyTransferTaskService;
    @Resource
    private SellProductInfoDao sellProductInfoDao;

    @Autowired
    private RabbitMQService rabbitMQService;

    @Override
    public void sendSellStopTips(String productCode, String productName, DateTime stopSellTime) {

        List<AgentUserInfoEntity> list = new LambdaQueryChainWrapper<>(agentUserInfoDao).list();
        list.forEach(action -> {
            agentMessageInfoDao.insert(AgentMessageInfoEntity.builder()
                    .agentCode(action.getAgentCode())
                    .content(StrUtil.format("<{}>即将在{}下架。", productName, stopSellTime.toString("yyyy/MM/dd")))
                    .jumpParam(productCode)
                    .jumpType(JumpTypeEnum.PRODUCT_DETAILS.getCode())
                    .messageCode(CommonUtils.createCodeLastNumber("TZ"))
                    .unread(1)
                    .title("产品消息")
                    .build());
            imClient.pushAppNotice(PushAppNoticeVo.builder()
                    .content(StrUtil.format("<{}>即将在{}下架。", productName, stopSellTime.toString("yyyy/MM/dd")))
                    .agentCode(action.getAgentCode())
                    .title("产品消息")
                    .jumpParam(productCode)
                    .jumpType(JumpTypeEnum.PRODUCT_DETAILS.getCode() + "")
                    .build());
        });
    }

    /**
     * 商品上架提醒
     *
     * @param productCode
     * @param productName
     */
    @Override
    public void sendSellStartTips(String productCode, String productName) {
        List<AgentUserInfoEntity> list = new LambdaQueryChainWrapper<>(agentUserInfoDao).list();
        list.forEach(action -> {
            agentMessageInfoDao.insert(AgentMessageInfoEntity.builder()
                    .agentCode(action.getAgentCode())
                    .content(StrUtil.format("<{}>已于{}上架。", productName, DateUtil.date().toString("yyyy/MM/dd")))
                    .jumpParam(productCode)
                    .jumpType(JumpTypeEnum.PRODUCT_DETAILS.getCode())
                    .messageCode(CommonUtils.createCodeLastNumber("TZ"))
                    .unread(1)
                    .title("产品消息")
                    .build());
            imClient.pushAppNotice(PushAppNoticeVo.builder()
                    .content(StrUtil.format("<{}>已于{}上架。", productName, DateUtil.date().toString("yyyy/MM/dd")))
                    .agentCode(action.getAgentCode())
                    .title("产品消息")
                    .jumpParam(productCode)
                    .jumpType(JumpTypeEnum.PRODUCT_DETAILS.getCode() + "")
                    .build());
        });
    }

    @Override
    public void sendSellDownTips(String productCode, String productAbbreviation) {
        DateTime beginTime = DateUtil.beginOfDay(new Date());
        DateTime endTime = DateUtil.endOfDay(beginTime);
        //更新到了下架时间没有下架的产品列表
        List<SellProductInfoEntity> list = new LambdaQueryChainWrapper<>(sellProductInfoDao)
                .lt(SellProductInfoEntity::getStopSellTime, endTime)
                .gt(SellProductInfoEntity::getStopSellTime, beginTime)
                .list();
        list.forEach(action -> {
            //获取当日下架的产品,向用户推送通知
            imClient.pushAppNotice(PushAppNoticeVo.builder()
                    .jumpType(JumpTypeEnum.PRODUCT_DETAILS.getCode().toString())
                    .jumpParam(action.getProductCode())
                    .content(StrUtil.format("【{}】已于【{}】下架。", action.getProductAbbreviation(), beginTime.toString("yyyy/MM/dd")))
                    .title("下架提醒")
                    .build());
        });
    }

    @Async
    @Override
    public void transferCustomer(UpdateAreaManagerRegionVo update) {
        AgentUserInfoEntity sourceAgentInfo = Optional.ofNullable(agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode, update.getAgentCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人编码=" + update.getAgentCode() + "匹配不到信息")));

        AgentUserInfoEntity targetAgentInfo = Optional.ofNullable(agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentCode, update.getReplaceAgentCode()).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人编码=" + update.getReplaceAgentCode() + "匹配不到信息")));
        //1.获取需要转移的客户列表
        List<CustomerCodeListByAgentCodeAndRegionOut> customerList = customerAgentMapDao.findCustomerCodeListByAgentCodeAndRegion(update.getAgentCode(), update.getAreaManagerRegion());
        if (CollUtil.isEmpty(customerList)) {
            log.info("没有需要转移的客户信息");
            return;
        }
        //2.过滤客户数据
        List<String> customerCodeList;
        if (CollUtil.isNotEmpty(update.getSubregionList())) {
            //获取满足片区的客户列表
            customerCodeList = customerList.stream()
                    .filter(f -> (StrUtil.isNotBlank(f.getReferrerSubregion()) && update.getSubregionList().contains(f.getReferrerSubregion())))
                    .map(CustomerCodeListByAgentCodeAndRegionOut::getCustomerCode)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            customerCodeList = customerList.stream().map(CustomerCodeListByAgentCodeAndRegionOut::getCustomerCode)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(customerCodeList)) {
            log.info("没有需要转移的客户信息");
            return;
        }
        int customerNum = customerCodeList.size();
        log.info("需要转移的客户数:{}人", customerNum);
        //减少绑定客户数
        int serviceCustomerNum = sourceAgentInfo.getServiceCustomerNum() - customerNum;
        agentUserInfoService.lambdaUpdate()
                .set(AgentUserInfoEntity::getServiceCustomerNum, Math.max(serviceCustomerNum, 0))
                .eq(AgentUserInfoEntity::getAgentCode, update.getReplaceAgentCode())
                .update();
        Date currentTime = new Date();
        if (ShiroUtils.isLogin()) {
            update.setUpdateUser(ShiroUtils.getUserEntity().getUsername());
        }
        //切片处理,每次处理数据5000个
        Lists.partition(customerCodeList, 500).forEach(customerCodes -> {
            try {
                //3.解除绑定
                boolean remove = customerAgentMapService.lambdaUpdate()
                        .eq(CustomerAgentMapEntity::getAgentCode, update.getAgentCode())
                        .in(CustomerAgentMapEntity::getCustomerCode, customerCodes)
                        .remove();
                if (!remove) {
                    return;
                }
                //处理解绑
                unbindRelation(update, customerCodes);

                //处理绑定逻辑啦....
                // 判断是否为区域保险专家,区域保险专家，需要设置客户渠道为中和农信
                if (AgentTypeEnum.AREA_MANAGER.getKey().equals(targetAgentInfo.getAgentType())) {
                    customerBasicInfoService.lambdaUpdate()
                            .set(CustomerBasicInfoEntity::getChannelCode, Constant.ZHNX_CHANNEL_CODE)
                            .set(CustomerBasicInfoEntity::getChannelName, Constant.ZHNX_CHANNEL_NAME)
                            .in(CustomerBasicInfoEntity::getCustomerCode, customerCodes)
                            .eq(CustomerBasicInfoEntity::getRuralFlag, 1)
                            .update();
                }
                //批量插入数据
                customerAgentMapDao.saveBatch(customerCodes.stream().map(m -> {
                    CustomerAgentMapEntity customerAgentMap = new CustomerAgentMapEntity();
                    customerAgentMap.setAgentCode(update.getReplaceAgentCode());
                    customerAgentMap.setCustomerCode(m);
                    customerAgentMap.setBindSource("ADMIN_MANUAL_BIND");
                    customerAgentMap.setBindTime(currentTime);
                    customerAgentMap.setCreateTime(currentTime);
                    customerAgentMap.setUpdateTime(currentTime);
                    customerAgentMap.setDeleted(0);
                    customerAgentMap.setCreateUser(update.getUpdateUser());
                    customerAgentMap.setUpdateUser(update.getUpdateUser());
                    return customerAgentMap;
                }).collect(Collectors.toList()));
                //增加绑定客户数
                agentUserInfoService.lambdaUpdate()
                        .set(AgentUserInfoEntity::getServiceCustomerNum, targetAgentInfo.getServiceCustomerNum() + customerCodes.size())
                        .eq(AgentUserInfoEntity::getAgentCode, update.getReplaceAgentCode())
                        .update();
            } catch (Exception e) {
                log.error("sourceAgentCode={},targetAgentCode={},处理客户数据:{}失败", update.getAgentCode(), update.getReplaceAgentCode(), JSONUtil.toJsonStr(customerCodes), e);
            }
        });

    }

    /**
     * 转移保单
     *
     * @param update
     */
    @Async
    @Override
    public void transferPolicy(UpdateAreaManagerRegionVo update) {
        EpPolicyTransferTaskEntity save = new EpPolicyTransferTaskEntity();
        save.setTaskStatus(0);
        save.setTargetAgentCode(update.getReplaceAgentCode());
        save.setSourceAgentCode(update.getAgentCode());
        save.setCreateUser(ShiroUtils.getUserEntity().getUsername());
        save.setAreaManagerRegion(update.getAreaManagerRegion());
        save.setSubregion(CollUtil.join(update.getSubregionList(), ","));
        epPolicyTransferTaskService.save(save);
        // 设置代理人离职时 发送mq消息
        MQMessage msg = new MQMessage();
        msg.setCode(update.getAgentCode());
        msg.setOpeType(QueueEnum.transfer_policy.getRouteKey());
        JSONObject msgData = new JSONObject();
        msgData.put("sourceAgentCode", update.getAgentCode());
        msgData.put("targetAgentCode", update.getReplaceAgentCode());
        msgData.put("areaManagerRegion", update.getAreaManagerRegion());
        msgData.put("subregionList", CollUtil.join(update.getSubregionList(), ","));
        msgData.put("createUser", ShiroUtils.getUserEntity().getUsername());
        msgData.put("taskId", save.getId());
        msg.setData(msgData);
        rabbitMQService.sendTopicMessage(QueueEnum.transfer_policy.getExchange(), QueueEnum.transfer_policy.getRouteKey(), msg);
    }


    /**
     * 解除绑定
     *
     * @param update
     */
    public void unbindRelation(UpdateAreaManagerRegionVo update, List<String> customerCodes) {
        //4.删除客户与代理人的铃铛消息
        customerMessageInfoService.lambdaUpdate()
                .eq(CustomerMessageInfoEntity::getAgentCode, update.getAgentCode())
                .eq(CustomerMessageInfoEntity::getMsgType, MsgTypeEnum.IM_MSG.getMsgType())
                .in(CustomerMessageInfoEntity::getCustomerCode, customerCodes)
                .remove();

        //2.删除当前客户与经纪人的会话关系表    chat_list
        chatListService.lambdaUpdate()
                .eq(ChatListEntity::getAgentCode, update.getAgentCode())
                .in(ChatListEntity::getCustomerCode, customerCodes)
                .remove();

        //4.删除当前客户与经纪人的工单列表      chat_work_order
        chatWorkOrderService.lambdaUpdate()
                .eq(ChatWorkOrderEntity::getAgentCode, update.getAgentCode())
                .in(ChatWorkOrderEntity::getCustomerCode, customerCodes)
                .remove();

        //5.删除当前客户与经纪人的客户动态      agent_customer_news
        agentCustomerNewsService.lambdaUpdate()
                .eq(AgentCustomerNewsEntity::getAgentCode, update.getAgentCode())
                .in(AgentCustomerNewsEntity::getCustomerCode, customerCodes)
                .remove();

        //6.删除当前客户与经纪人的消息列表      agent_message_info
        agentMessageInfoService.lambdaUpdate()
                .eq(AgentMessageInfoEntity::getAgentCode, update.getAgentCode())
                .in(AgentMessageInfoEntity::getJumpParam, customerCodes)
                .remove();

        //7.删除当前客户与经纪人的服务台列表    agent_reception
        agentReceptionService.lambdaUpdate()
                .eq(AgentReceptionEntity::getAgentCode, update.getAgentCode())
                .in(AgentReceptionEntity::getJumpParam, customerCodes)
                .remove();

    }
}
