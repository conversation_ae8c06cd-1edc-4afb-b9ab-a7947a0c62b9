package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.agent.entity.AgentMessageInfoEntity;

import java.util.Collection;

/**
 * 经纪人消息列表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 11:09:17
 */
public interface AgentMessageInfoService extends IService<AgentMessageInfoEntity> {
    /**
     * 批量插入
     *
     * @param entityList 实体列表
     * @return 插入数量
     */
    Integer insertBatchSomeColumn(Collection<AgentMessageInfoEntity> entityList);
}

