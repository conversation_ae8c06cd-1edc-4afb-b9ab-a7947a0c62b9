package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.dao.AgentCompanyAccountDao;
import com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao;
import com.mpolicy.manage.modules.agent.entity.AgentCompanyAccountEntity;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentCompanyAccountService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ImportCompanyAccountExcel;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.protocol.vo.ImportInsuranceProductVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("agentCompanyAccountService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentCompanyAccountServiceImpl extends ServiceImpl<AgentCompanyAccountDao, AgentCompanyAccountEntity> implements AgentCompanyAccountService {

    private final AgentUserInfoDao agentUserInfoDao;

    private final InsuranceCompanyService insuranceCompanyService;

    /**
     * 导如保司账号
     *
     * @param input 文件
     */
    @Override
    public void importCompanyAccount(ImportInsuranceProductVo input) {
        try (InputStream inputStream = input.getFile().getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<ImportCompanyAccountExcel> readAll = reader.readAll(ImportCompanyAccountExcel.class);
            if (readAll.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保司编码/代理人业务编码/保司账号不能为空"));
            }
            //判断一下文件内容是否合法
            boolean isContainEmpty = readAll.stream()
                    .anyMatch(product -> StrUtil.isBlank(product.getCompanyAccount())
                            || StrUtil.isBlank(product.getBusinessCode())
                            || StrUtil.isBlank(product.getBusinessCode())
                            || StrUtil.isBlank(product.getCompanyCode()));
            if (isContainEmpty) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保司编码/代理人业务编码/保司账号不能为空"));
            }
            // 判断是否存在重复数据
            boolean hasDuplicates = readAll.stream()
                    .collect(Collectors.groupingBy(d -> d.getCompanyCode() + "-" + d.getBusinessCode() + "-" + d.getCompanyAccount()))
                    .values().stream()
                    .anyMatch(l -> l.size() > 1);
            if (hasDuplicates) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件中存在重复数据"));
            }
            // 1.获取代理人业务编码 判断业务编码是否正确
            List<String> businessCodeList = readAll.stream().map(m -> m.getBusinessCode()).distinct().collect(Collectors.toList());
            //获取代理人业务编码和agentCode关系
            Map<String, String> businessCodeToAgentCodeMap = new LambdaQueryChainWrapper<>(agentUserInfoDao)
                    .in(AgentUserInfoEntity::getBusinessCode, businessCodeList)
                    .list().stream().collect(Collectors.toMap(AgentUserInfoEntity::getBusinessCode, v -> v.getAgentCode(),
                            (k1, k2) -> k2));
            if (businessCodeList.size() > businessCodeToAgentCodeMap.size()) {
                String businessCodes = CollUtil.subtract(businessCodeList, businessCodeToAgentCodeMap.keySet()).stream().collect(Collectors.joining());
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("代理人业务编码[" + businessCodes + "]没有匹配到代理人!"));
            }
            // 2.判断账号+保司 是否存在
            //获取账号配置表
            List<String> companyAccountList = list().stream().map(m -> m.getCompanyAccount() + "_" + m.getCompanyCode()).distinct().collect(Collectors.toList());
            List<String> fileCompanyAccountList = readAll.stream().map(m -> m.getCompanyAccount() + "_" + m.getCompanyCode()).distinct().collect(Collectors.toList());
            String subCompanyAccount = CollUtil.intersection(fileCompanyAccountList, companyAccountList).stream().collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(subCompanyAccount)) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保司账号[" + subCompanyAccount + "]已经存在啦!"));
            }
            // 3.获取导入文件的保司编码
            List<String> companyCodeList = readAll.stream().map(m -> m.getCompanyCode()).distinct().collect(Collectors.toList());
            Map<String, InsuranceCompanyEntity> companyMap = insuranceCompanyService.lambdaQuery()
                    .in(InsuranceCompanyEntity::getCompanyCode, companyCodeList)
                    .list().stream().collect(Collectors.toMap(InsuranceCompanyEntity::getCompanyCode, v -> v,
                            (k1, k2) -> k2));

            // 4.插入数据库
            saveBatch(readAll.stream().map(m -> {
                InsuranceCompanyEntity companyInfo = companyMap.get(m.getCompanyCode());
                if (companyInfo == null) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保司编码[" + m.getCompanyCode() + "]没有匹配到保司信息"));
                }
                String agentCode = businessCodeToAgentCodeMap.get(m.getBusinessCode());
                if (agentCode == null) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("业务编码[" + m.getBusinessCode() + "]没有匹配到代理人信息"));
                }
                AgentCompanyAccountEntity db = new AgentCompanyAccountEntity();
                db.setCompanyCode(companyInfo.getCompanyCode());
                db.setCompanyName(companyInfo.getCompanyName());
                db.setShortName(companyInfo.getShortName());
                db.setAgentCode(agentCode);
                db.setCompanyAccount(m.getCompanyAccount());
                return db;
            }).collect(Collectors.toList()));

        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("导入文件失败"));
        }

    }
}
