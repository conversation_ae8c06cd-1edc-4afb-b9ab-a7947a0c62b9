package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftInfoEntity;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoQueryVo;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoSaveVo;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoUpdateVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 销售产品的赠品管理信息service
 *
 * @create 2024/12/2
 * @since 1.0.0
 */
public interface SellProductGiftInfoService extends IService<SellProductGiftInfoEntity> {
    /**
     *
     *
     * 根据vo进行保存
     *
     *
     * @param sellProductGiftInfoSaveVo
     *
     * 保存的vo
     *
     * @return
     */
    boolean save(SellProductGiftInfoSaveVo sellProductGiftInfoSaveVo);

    /**
     *
     *
     * 根据更新vo进行更新
     *
     *
     * @param sellProductGiftInfoUpdateVo
     *
     * 更新的vo
     *
     */
    void updateBySellProductGiftInfoUpdateVo(SellProductGiftInfoUpdateVo sellProductGiftInfoUpdateVo);

    /**
     *
     *
     *
     *
     *
     * @param sellProductGiftInfoQueryVo
     * @return
     */
    IPage<SellProductGiftInfoEntity> getPageOrderByRemainingGiftQuantityDesc(SellProductGiftInfoQueryVo sellProductGiftInfoQueryVo);

    /**
     *
     *
     * 获取生效中的，礼品剩余数量少于制定的数据的所有配置
     *
     *
     * @param customerGiftQuantityEmergencyNum
     *
     * 礼品剩余数量
     *
     * @return
     */
    List<SellProductGiftInfoEntity> getActiveLessQuantity(Integer customerGiftQuantityEmergencyNum);
}
