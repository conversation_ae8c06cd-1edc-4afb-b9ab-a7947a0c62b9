package com.mpolicy.manage.modules.policy.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.export.ExportApiService;
import com.mpolicy.manage.modules.agent.service.ChannelDistributionInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.policy.entity.EpPolicyAgentChangeLogEntity;
import com.mpolicy.manage.modules.policy.enums.PolicyExportMethodEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyAgentChangeLogService;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.common.EpPolicyBaseService;
import com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo;
import com.mpolicy.manage.modules.policy.vo.EpPolicyInsuredInfoVo;
import com.mpolicy.manage.modules.policy.vo.PolicyInfoVo;
import com.mpolicy.manage.modules.policy.vo.policy.FastPolicyContractInfoVo;
import com.mpolicy.manage.modules.policy.vo.policy.PolicyContractInfoVo;
import com.mpolicy.manage.modules.policy.vo.policy.PolicyImageUpdateInput;
import com.mpolicy.manage.modules.policy.vo.product.PolicyInsuredProductVo;
import com.mpolicy.manage.modules.policy.vo.query.PolicyInsuredQuery;
import com.mpolicy.manage.modules.policy.vo.product.PolicyInsuredProductVo;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.enums.ImportTypeEnum;
import com.mpolicy.policy.common.enums.PolicyContractStatusEnum;
import com.mpolicy.policy.common.enums.contract.PolicyCorrectedModuleTypeEnum;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.ep.policy.EpPolicyImportResultVo;
import com.mpolicy.policy.common.ep.policy.OptUserInfoVo;
import com.mpolicy.policy.common.enums.*;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.check.BusinessCheckVo;
import com.mpolicy.policy.common.ep.policy.product.EpPolicyInsuredProductVo;
import com.mpolicy.policy.common.ep.policy.corrected.PolicyCorrectedLogQuery;
import com.mpolicy.policy.common.ep.policy.corrected.PolicyCorrectedLogVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 保单信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-12-21 17:24:10
 */
@RestController
@RequestMapping("sys/policycontractinfo")
@Api(tags = "保单信息")
@Slf4j
public class PolicyContractInfoController {
    @Autowired
    private PolicyContractInfoService policyContractInfoService;

    @Autowired
    private EpPolicyInsuredInfoService policyInsuredInfoService;
    @Autowired
    private EpPolicyAgentChangeLogService policyAgentChangeLogService;

    @Autowired
    private EpPolicyClient epPolicyClient;
    @Autowired
    private EpPolicyBaseService epPolicyBaseService;
    @Autowired
    private ChannelDistributionInfoService channelDistributionInfoService;

    @Autowired
    private ExportApiService exportApiService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取保单列表", notes = "获取保单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "policyProductTypes", dataType = "String", value = "产品类型", example = "POLICY:PRODUCT_TYPE:PERSONAL"),
            @ApiImplicitParam(paramType = "query", name = "policyNo", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query", name = "applicantPolicyNo", dataType = "String", value = "投保单号"),
            @ApiImplicitParam(paramType = "query", name = "companyCodes", dataType = "String", value = "保险公司编码", example = "FXLH0000"),
            @ApiImplicitParam(paramType = "query", name = "portfolioNameOrCode", dataType = "String", value = "产品名称/编码"),
            @ApiImplicitParam(paramType = "query", name = "applicantName", dataType = "String", value = "投保人姓名"),
            @ApiImplicitParam(paramType = "query", name = "insuredName", dataType = "String", value = "被保人姓名"),
            @ApiImplicitParam(paramType = "query", name = "policySource", dataType = "String", value = "保单来源", example = "POLICY_SOURCE:0"),
            @ApiImplicitParam(paramType = "query", name = "salesType", dataType = "String", value = "业务分类", example = "SALES_MODE:0"),
            @ApiImplicitParam(paramType = "query", name = "policyStatus", dataType = "String", value = "保单状态", example = "POLICY_STATUS:6"),
            @ApiImplicitParam(paramType = "query", name = "channelCodes", dataType = "String", value = "销售渠道", example = "SALES_PLATFORM:0"),
            @ApiImplicitParam(paramType = "query", name = "referrerCodes", dataType = "String", value = "渠道推荐人姓名/编码"),
            @ApiImplicitParam(paramType = "query", name = "orgCodes", dataType = "String", value = "代理人机构"),
            @ApiImplicitParam(paramType = "query", name = "agentCodes", dataType = "String", value = "代理人姓名/编码"),
            @ApiImplicitParam(paramType = "query", name = "orderTimeBegin", dataType = "String", value = "交单日期起始时间"),
            @ApiImplicitParam(paramType = "query", name = "orderTimeEnd", dataType = "String", value = "交单日期终止时间"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeBegin", dataType = "String", value = "生效日期起始时间"),
            @ApiImplicitParam(paramType = "query", name = "enforceTimeEnd", dataType = "String", value = "生效日期终止时间"),
            @ApiImplicitParam(paramType = "query", name = "isSubmit", dataType = "int", value = "是否为提交保单 0:暂存;1:已提交"),
            @ApiImplicitParam(paramType = "query", name = "incomplete", dataType = "int", value = "是否信息不全 0:否;1:是"),
            @ApiImplicitParam(paramType = "query", name = "settlementStatus", dataType = "int", value = "佣金发放状态 0:不参与发放;1:已发放;2:未发放"),
            @ApiImplicitParam(paramType = "query", name = "suspendTypes", dataType = "String", value = "挂起类型 1:待回执 2:待回访"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "giveFlag", dataType = "int", value = "赠险标识：0：非赠险、1：赠险"),
            @ApiImplicitParam(paramType = "query", name = "renewalType", dataType = "int", value = "续保标识：0：新保保单、1：续保保单"),
            @ApiImplicitParam(paramType = "query", name = "renewalType", dataType = "int", value = "续保标识：0：新保保单、1：续保保单"),
            @ApiImplicitParam(paramType = "query", name = "superviseChannelCode", dataType = "int", value = "销售渠道")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"policy:query","policy:onLineVisible"},logical = Logical.OR)
    public Result<PageUtils<PolicyInfoVo>> list(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params) {
        PageUtils<PolicyInfoVo> page = policyContractInfoService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 获取保单总数
     */
    @ApiOperation(value = "获取保单总数 参数和分页一致", notes = "获取保单总数")
    @GetMapping("/count")
    public Result<Integer> count(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params) {
        Integer count = policyContractInfoService.queryCount(params);
        return Result.success(count);
    }

    /**
     * 根据编码获取保单对象
     */
    @ApiOperation(value = "根据编码获取保单对象")
    @GetMapping("/info/{contractCode}")
    @RequiresPermissions(value = {"policy:onLineVisible"})
    public Result<PolicyContractInfoVo> info(@ApiParam(value = "合同编码", required = true) @PathVariable("contractCode") String contractCode) {
        PolicyContractInfoVo data = policyContractInfoService.queryPolicyContractInfo(contractCode);
        return Result.success(data);
    }


    /**
     * 暂存手工录入的保单
     */
    @ApiOperation(value = "暂存保单")
    @PostMapping("/tempSaveOrUpdate")
    @RequiresPermissions("policy:add")
    public Result<String> tempSaveOrUpdateEntity(@RequestBody EpContractInfoVo entryContractInfoVo) {
        log.info("暂存保单提交的对象:{}", JSON.toJSONString(entryContractInfoVo));
        String submit = policyContractInfoService.tempSaveOrUpdateEntity(entryContractInfoVo);
        return Result.success(submit);
    }


    /**
     * 提交手工录入的保单
     */
    @ApiOperation(value = "手工录入的保单")
    @PostMapping("/submit")
    @RequiresPermissions("policy:add")
    public Result<String> submit(@RequestBody EpContractInfoVo entryContractInfoVo) {
        log.info("手工录入的保单提交的对象:{}", JSON.toJSONString(entryContractInfoVo));
        String submit = policyContractInfoService.submit(entryContractInfoVo);
        return Result.success(submit);
    }

    @ApiOperation(value = "录单前置校验")
    @PostMapping("/pre-check")
    public Result<BusinessCheckVo> preCheck(@RequestBody EpContractInfoVo policyInfo) {
        log.info("录单前置校验:{}", JSON.toJSONString(policyInfo));
        BusinessCheckVo data = epPolicyBaseService.preCheck(policyInfo);
        return Result.success(data);
    }

    /**
     * 修改预保件
     */
    @ApiOperation(value = "修改预保件")
    @PostMapping("/updateAdvance")
    @RequiresPermissions("policy:add")
    public Result<String> updateAdvance(@RequestBody EpContractInfoVo entryContractInfoVo) {
        log.info("修改预件:{}", JSON.toJSONString(entryContractInfoVo));
        String submit = policyContractInfoService.updateAdvance(entryContractInfoVo);
        return Result.success(submit);
    }

    // 1.3.4 迭代允许普通用户变更部分保单信息，前端直接调用超级管理员编辑接口
    /// @RequiresPermissions("policy:update")

    /**
     * 超级管理员修改保单
     */
    @ApiOperation(value = "超级管理员修改保单")
    @PostMapping("/adminUpdate")
    @RequiresPermissions("policy:add")
    public Result<String> adminUpdate(@RequestBody EpContractInfoVo entryContractInfoVo) {
        log.info("超级管理员修改的保单提交的对象:{}", JSON.toJSONString(entryContractInfoVo));
        String submit = policyContractInfoService.adminUpdate(entryContractInfoVo);
        return Result.success(submit);
    }

    @ApiOperation(value = "修正被保人险种的受益人")
    @PostMapping("/product/beneficiary/{contractCode}")
    public Result<String> correctedPolicyBeneficiary(@PathVariable("contractCode") String contractCode,@RequestBody List<EpPersonalProductInfoVo> productList) {
        log.info("管理人修正保单险种的受益人:{},{}", contractCode,JSON.toJSONString(productList));
        String submit = policyContractInfoService.correctedPolicyBeneficiary(contractCode,productList);
        return Result.success(submit);
    }

    /**
     * 预售件转承保件
     */
    @ApiOperation(value = "预售件转承保件")
    @PostMapping("/presaleToUnderwrite")
    @RequiresPermissions("policy:add")
    public Result presaleToUnderwrite(@RequestBody EpContractInfoVo entryContractInfoVo) {
        log.info("预售件转承保件提交的对象:{}", JSON.toJSONString(entryContractInfoVo));
        policyContractInfoService.presaleToUnderwrite(entryContractInfoVo);
        return Result.success();
    }


    @GetMapping("/exportPolicy_old")
    @ApiOperation(value = "导出保单", notes = "导出保单数据")
    @NoRepeatSubmit(keyName = "token")
    @Deprecated
    public void export(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        // 权限 机构 如果是部分才处理
        List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        if (orgCodeList != null && orgCodeList.size() == 0) {
            return;
        }
        params.put("orgCodeList", orgCodeList);
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        if (channelBranchCodeList != null && channelBranchCodeList.size() == 0) {
            return;
        }
        params.put("channelBranchCodeList", channelBranchCodeList);
        // 执行规则文件加载
        List<EpPolicyExportVo> result = policyContractInfoService.exportPolicy(params, ShiroUtils.getSubject().isPermitted("policy:query"));
        ExcelUtil.writeExcel(response, result, URLUtil.encode("保单列表", CharsetUtil.CHARSET_UTF_8), "sheet1", new EpPolicyExportVo());
    }

    @GetMapping("/exportPolicy/apply")
    @ApiOperation(value = "导出保单申请", notes = "导出保单申请")
    @RequiresPermissions(value = {"policy:export"})
    @NoRepeatSubmit(keyName = "token")
    public Result<String> exportApply(@RequestParam Map<String, Object> params) {
        log.info("执行基础保单报告导出，导出请求条件={}", JSON.toJSONString(params));
        exportApiService.exportReport(params);
        return Result.success("提交申请成功.");
    }

    @GetMapping("/exportPolicy/apply2DingTalk")
    @ApiOperation(value = "导出保单申请-钉钉", notes = "导出保单申请-钉钉")
    @RequiresPermissions(value = {"policy:export2dingTalk"})
    @NoRepeatSubmit(keyName = "token", lockTime = 60 * 60)
    public Result<String> exportApply2DingTalk(@RequestParam Map<String, Object> params) {
        log.info("执行钉钉保单报告导出，导出请求条件={}", JSON.toJSONString(params));
        exportApiService.policyExport(params);
        return Result.success("提交申请成功.");
    }

    @GetMapping("/exportPolicy")
    @ApiOperation(value = "导出保单", notes = "导出保单数据")
    @RequiresPermissions(value = {"policy:export"})
    @NoRepeatSubmit(keyName = "token", lockTime = 60 * 60)
    public void exportNew(@RequestParam Map<String, Object> params, final HttpServletResponse response) throws IOException {
        // 获取权限信息
        final boolean sensitiveDataAccess = ShiroUtils.getSubject().isPermitted("policy:query");
        try (OutputStream out = response.getOutputStream()) {
            String fileName = StrUtil.format("保单信息(不超过2万条)-{}.xlsx", DateUtil.today());
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode(fileName));
            policyContractInfoService.queryExportPage(sensitiveDataAccess, params, out, PolicyExportMethodEnum.PAGE);
        } catch (IOException e) {
            throw new IOException(e);
        }
    }

    /**
     * 导入保单
     */
    @ApiOperation(value = "导入保单C")
    @PostMapping("/importPolicy")
    @PassToken
    @RequiresPermissions(value = {"policy:importPolicy", "policy:policyImport"}, logical = Logical.OR)
    public Result<EpPolicyImportResultVo> importPolicy(@RequestParam @ApiParam(name = "fileCode", value = "oss文件编码") String fileCode,
                                                       @RequestParam @ApiParam(name = "policyProductType", value = "产品类型") String policyProductType) {
        OptUserInfoVo optUserInfo = transform2OptUserInfoVo(ShiroUtils.getUserEntity());
        Result<EpPolicyImportResultVo> result = epPolicyClient.importPolicy(fileCode, policyProductType, optUserInfo);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }

        return Result.success(result.getData());
    }

    @ApiOperation(value = "导入保单C")
    @PostMapping("/check/import")
    @PassToken
    public Result<BusinessCheckVo> checkImport(@RequestParam @ApiParam(name = "fileCode", value = "oss文件编码") String fileCode,
                                               @RequestParam @ApiParam(name = "policyProductType", value = "产品类型") String policyProductType) {
        return epPolicyClient.checkImport(fileCode, policyProductType);
    }

    @PostMapping("/importAcknowledgementBack")
    @ApiOperation(value = "导入回执回访", notes = "导入回执回访")
    @RequiresPermissions({"policy:add", "policy:importReceiptReview"})
    public Result importAcknowledgementBack(@RequestParam @ApiParam(name = "fileCode", value = "oss文件编码") String fileCode) {
        OptUserInfoVo optUserInfo = transform2OptUserInfoVo(ShiroUtils.getUserEntity());
        Result<?> result = epPolicyClient.importOtherPolicyInfo(ImportTypeEnum.ACKNOWLEDGEMENT_BACK.getCode(), fileCode, optUserInfo);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        return Result.success();
    }

    @GetMapping("/documentTemplate")
    @ApiOperation(value = "下载模板", notes = "下载模板")
    @Deprecated
    public Result<String> documentTemplate(@RequestParam @ApiParam(name = "policyProductType", value = "产品类型") String policyProductType) {
        return Result.success();
    }

    @GetMapping("/queryPolicyAgentChangeLog")
    @ApiOperation(value = "查询代理人变更记录", notes = "查询代理人变更记录")
    public Result<List<EpPolicyAgentChangeLogEntity>> queryPolicyAgentChangeLog(@RequestParam @ApiParam(name = "contractCode", value = "合同编码") String contractCode) {
        List<EpPolicyAgentChangeLogEntity> result = policyAgentChangeLogService.getPolicyAgentChangeLog(contractCode);

        return Result.success(result);
    }

    @GetMapping("/queryPolicyStatus")
    @ApiOperation(value = "保单状态列表", notes = "保单状态")
    public Result<List<DicCacheHelper.DicEntity>> queryPolicyStatus() {
        List<DicCacheHelper.DicEntity> advanceStatus = DicCacheHelper.getSons("POLICY:ADVANCE_STATUS");
        List<DicCacheHelper.DicEntity> policyStatus = DicCacheHelper.getSons("POLICY_STATUS");

        Stream<DicCacheHelper.DicEntity> policyStatusStream = policyStatus.stream().filter(x -> !PolicyContractStatusEnum.ACTIVE.getCode().equals(x.getKey()));

        List<DicCacheHelper.DicEntity> result = Stream.concat(advanceStatus.stream(), policyStatusStream).collect(Collectors.toList());

        return Result.success(result);
    }

    @PostMapping("/queryPolicyContractBriefInfo")
    @ApiOperation(value = "根据保单号查询保单摘要信息 带验证", notes = "根据保单号查询保单摘要信息 带验证")
    public Result<EpContractBriefInfoVo> queryPolicyContractBriefInfo(@RequestParam @ApiParam(name = "policyNo", value = "保单号") String policyNo) {
        EpContractBriefInfoVo contractBriefInfoVo = policyContractInfoService.queryPolicyContractBriefInfoByPolicyNo(policyNo);

        return Result.success(contractBriefInfoVo);
    }

    @PostMapping("/queryPolicyContractBriefInfoByPolicy")
    @ApiOperation(value = "根据保单号查询保单摘要信息", notes = "根据保单号查询保单摘要信息")
    public Result<FastPolicyContractInfoVo> queryPolicyContractBriefInfoByPolicy(@RequestParam @ApiParam(name = "policyNo", value = "保单号") String policyNo) {
        FastPolicyContractInfoVo contractInfo = policyContractInfoService.fastQueryPolicyInfo(policyNo,true);
        return Result.success(contractInfo);
    }

    @PostMapping("/queryPreserveContractBriefInfoByPolicyNo")
    @ApiOperation(value = "根据保单号查询保全保单摘要信息", notes = "根据保单号查询保全保单摘要信息")
    public Result<FastPolicyContractInfoVo> queryPreserveContractBriefInfoByPolicyNo(@RequestParam @ApiParam(name = "policyNo", value = "保单号") String policyNo) {
        FastPolicyContractInfoVo contractBriefInfoVo = policyContractInfoService.preservationFastPolicyContractInfo(policyNo);

        return Result.success(contractBriefInfoVo);
    }

    /**
     * 转化为保单中心操作员对象
     *
     * @param userEntity 对象实体
     * @return 保单中心操作员对象
     */
    private OptUserInfoVo transform2OptUserInfoVo(SysUserEntity userEntity) {
        int isAllChannelDistribution = userEntity.getIsAllChannelDistribution();
        List<ChannelInfoVo> channelInfoVoList = null;
        List<String> channelDisCodeList = new ArrayList<>();
        if (isAllChannelDistribution == 1) {
            channelInfoVoList = channelDistributionInfoService.getChannelList(true);
        } else {
            channelInfoVoList = channelDistributionInfoService.getChannelList(false);
        }
        if (!CollectionUtils.isEmpty(channelInfoVoList)) {
            channelDisCodeList = channelInfoVoList.stream().map(ChannelInfoVo::getChannelCode).collect(Collectors.toList());
        }
        return OptUserInfoVo.builder()
                .username(userEntity.getUsername())
                .isAllChannel(userEntity.getIsAllChannel())
                .isAllChannelDistribution(userEntity.getIsAllChannelDistribution())
                .channelDistributionCodeList(channelDisCodeList)
                .channelBranchPermissionList(StrUtil.split(userEntity.getChannelBranchCodeList(), ','))
                .isAllOrg(userEntity.getIsAllOrg())
                .orgPermissionList(StrUtil.split(userEntity.getOrgCodeList(), ',')).build();
    }

    @PostMapping("/importContractSettle")
    @ApiOperation(value = "导入佣金发放信息", notes = "导入佣金发放信息")
    @RequiresPermissions({"policy:add", "policy:importSettlement"})
    public Result importContractSettle(@RequestParam @ApiParam(name = "fileCode", value = "oss文件编码") String fileCode) {
        OptUserInfoVo optUserInfo = transform2OptUserInfoVo(ShiroUtils.getUserEntity());
        Result<?> result = epPolicyClient.importOtherPolicyInfo(ImportTypeEnum.SETTLE.getCode(), fileCode, optUserInfo);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        return Result.success();
    }

    @PostMapping("/importContractReferrer")
    @ApiOperation(value = "导入渠道推荐人", notes = "导入渠道推荐人")
    @RequiresPermissions({"policy:add", "policy:importReferrer"})
    public Result importContractReferrer(@RequestParam @ApiParam(name = "fileCode", value = "oss文件编码") String fileCode) {
        OptUserInfoVo optUserInfo = transform2OptUserInfoVo(ShiroUtils.getUserEntity());
        Result<?> result = epPolicyClient.importOtherPolicyInfo(ImportTypeEnum.REFERRER.getCode(), fileCode, optUserInfo);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        return Result.success();
    }

    /**
     * 代理人离职检查
     *
     * @param agentCode 代理人编码
     * @return 结果列表
     */
    @GetMapping("/queryAgentExitPolicyCheck")
    @ApiOperation(value = "代理人离职前保单检查", notes = "代理人离职前保单检查")
    public Result<List<String>> queryAgentExitPolicyCheck(@RequestParam @ApiParam(name = "agentCode", value = "代理人编码") String agentCode) {
        List<String> result = policyContractInfoService.getAgentExitPolicyCheck(agentCode);

        return Result.success(result);
    }

    /**
     * 在保的被保人险种集合
     *
     * @param contractCode
     * @return 结果列表
     */
    @GetMapping("/active/insured/product")
    @ApiOperation(value = "在保的被保人险种集合", notes = "在保的被保人险种集合")
    public Result<List<PolicyInsuredProductVo>> activeInsuredProductList(@RequestParam @ApiParam(name = "contractCode", value = "代理人在保的被保人险种集合编码") String contractCode) {
        List<PolicyInsuredProductVo> data = policyContractInfoService.activeInsuredProductList(contractCode);
        return Result.success(data);
    }

    @GetMapping("/insured/product/v2")
    @ApiOperation(value = "取当前保单的业务可操作的被保人险种清单", notes = "取当前保单的业务可操作的被保人险种清单")
    public Result<List<PolicyInsuredProductVo>> policyInsuredProductList(@RequestParam @ApiParam(name = "contractCode", value = "代理人在保的被保人险种集合编码") String contractCode) {
        List<PolicyInsuredProductVo> data = policyContractInfoService.currentInsuredProductList(contractCode);
        return Result.success(data);
    }

    @GetMapping("/insured/list")
    @ApiOperation(value = "被保人列表", notes = "在保的被保人集合")
    public Result<PageUtils<EpPolicyInsuredInfoVo>> insuredList(@Validated @ModelAttribute PolicyInsuredQuery query) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("insuredName",query.getInsuredName());
        paramMap.put("insuredIdCard",query.getInsuredIdCard());
        paramMap.put("page",query.getPage());
        paramMap.put("limit",query.getLimit());
        String contractCode = query.getContractCode();

        PageUtils<EpPolicyInsuredInfoVo> page = policyInsuredInfoService.queryPage(contractCode, paramMap);
        return Result.success(page);
    }

    @PostMapping("/insured/list/all")
    @ApiOperation(value = "获取保单所有被保人列表", notes = "获取保单所有被保人列表")
    public Result<List<EpPolicyInsuredInfoVo>> policyInsuredList(@RequestBody PolicyInsuredQuery query) {
        List<EpPolicyInsuredInfoVo> data = policyInsuredInfoService.policyInsuredList(query);
        return Result.success(data);
    }

    @GetMapping("/exportPolicy/applyRiskChannelsDingTalk")
    @ApiOperation(value = "导出保单险种渠道报表到钉钉", notes = "导出保单申请-钉钉")
    @RequiresPermissions(value = {"policy:export2dingTalk"})
    @NoRepeatSubmit(keyName = "token", lockTime = 60 * 60)
    public Result<String> applyRiskChannelsDingTalk(@RequestParam Map<String, Object> params) {
        log.info("执行钉钉保单报告导出，导出请求条件={}",JSON.toJSONString(params));
        exportApiService.applyRiskChannelsDingTalk(params);
        return Result.success("提交申请成功.");
    }

    /**
     * 被保人险种列表
     *
     * @param
     * @return 结果列表
     */
    @GetMapping("/list/insured/product")
    @ApiOperation(value = "查询被保人险种列表", notes = "查询被保人险种列表")
    public Result<PageUtils<PolicyInsuredProductVo>> listInsuredProduct(@RequestParam("policyNo") @ApiParam(name = "policyNo", value = "保单号") String policyNo) {
        //1. 从被保人险种信息表获取列表数据
        Result<List<EpPolicyInsuredProductVo>> result = epPolicyClient.listInsuredProduct(policyNo);
        List<PolicyInsuredProductVo> productVoList = Collections.emptyList();
        if (!result.isSuccess()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(result.getMsg()));
        }
        int total = 0;
        List<EpPolicyInsuredProductVo> data = result.getData();
        AtomicInteger counter = new AtomicInteger();
        if (!CollectionUtils.isEmpty(data)) {
            total = data.size();
            productVoList = data.stream().map(entry -> {
                PolicyInsuredProductVo vo = new PolicyInsuredProductVo();
                BeanUtils.copyProperties(entry, vo);
                Integer mainInsurance = entry.getMainInsurance();
                String mainFlag = Objects.equals(mainInsurance, 0) ? "否" : "是";
                vo.setSerialNo(counter.addAndGet(1));
                vo.setMainInsurance(mainFlag);

                String insuredPeriodType = convertInsuredPeriodType(entry.getInsuredPeriodType());
                vo.setInsuredPeriodType(insuredPeriodType);

                String periodType = convertPeriodType(entry.getPeriodType());
                vo.setPeriodType(periodType);

                String paymentPeriodType = convertPaymentPeriodType(entry.getPaymentPeriodType());
                vo.setPaymentPeriodType(paymentPeriodType);
                return vo;
            }).collect(Collectors.toList());
            PageUtils pageUtils = new PageUtils<>(productVoList, total, total, 1);
            return Result.success(pageUtils);
        }
        //2. 从保单险种列表获取数据(兼容没有被保人的场景)
        Result<List<EpProductInfoVo>> productResult = epPolicyClient.listProductByPolicyNo(policyNo);
        if (!productResult.isSuccess()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(productResult.getMsg()));
        }
        List<EpProductInfoVo> p1 = productResult.getData();
        if (CollectionUtils.isEmpty(p1)) {
            return Result.success();
        }
        total = p1.size();
        productVoList = p1.stream().map(entry -> {
            PolicyInsuredProductVo vo = new PolicyInsuredProductVo();
            BeanUtils.copyProperties(entry, vo);
            Integer mainInsurance = entry.getMainInsurance();
            String mainFlag = Objects.equals(mainInsurance, 0) ? "否" : "是";
            vo.setSerialNo(counter.addAndGet(1));
            vo.setMainInsurance(mainFlag);

            String insuredPeriodType = convertInsuredPeriodType(entry.getInsuredPeriodType());
            vo.setInsuredPeriodType(insuredPeriodType);

            String periodType = convertPeriodType(entry.getPeriodType());
            vo.setPeriodType(periodType);

            String paymentPeriodType = convertPaymentPeriodType(entry.getPaymentPeriodType());
            vo.setPaymentPeriodType(paymentPeriodType);
            return vo;
        }).collect(Collectors.toList());
        PageUtils pageUtils = new PageUtils<>(productVoList, total, total, 1);
        return Result.success(pageUtils);
    }


    private String convertInsuredPeriodType(String code) {
        PolicyInsuredPeriodTypeEnum[] enums = PolicyInsuredPeriodTypeEnum.values();
        for (PolicyInsuredPeriodTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getInsuredDesc();
            }
        }
        return "";
    }

    private String convertPeriodType(String code) {
        PolicyPaymentTypeEnum[] enums = PolicyPaymentTypeEnum.values();
        for (PolicyPaymentTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getPaymentDesc();
            }
        }
        return "";
    }

    private String convertPaymentPeriodType(String code) {
        PolicyPaymentPeriodTypeEnum[] enums = PolicyPaymentPeriodTypeEnum.values();
        for (PolicyPaymentPeriodTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getPeriodDesc();
            }
        }
        return "";
    }

    /**
     * 更新电子保单地址
     */
    @ApiOperation(value = "影像件更新")
    @PostMapping("/policyImageUpdate")
    @RequiresPermissions({"policy:add", "policy:vedioEdit"})
    public Result<Boolean> policyImageUpdate(@RequestBody PolicyImageUpdateInput policyImageUpdateInput) {
        Boolean result = policyContractInfoService.policyImageUpdate(policyImageUpdateInput);
        return Result.success(result);
    }

    @ApiOperation(value = "修正保单基础信息")
    @PostMapping("/corrected/base")
    @RequiresPermissions({"policy:add"})
    public Result<String> correctedPolicyBaseInfo(@RequestBody EpContractInfoVo policyInfo) {
        String result = policyContractInfoService.commonCorrectedPolicy(PolicyCorrectedModuleTypeEnum.POLICY_BASE_INFO,policyInfo);
        return Result.success(result);
    }

    @ApiOperation(value = "修正保单扩展信息")
    @PostMapping("/corrected/extend")
    @RequiresPermissions({"policy:add"})
    public Result<String> correctedPolicyExtendInfo(@RequestBody EpContractInfoVo policyInfo) {
        String result = policyContractInfoService.commonCorrectedPolicy(PolicyCorrectedModuleTypeEnum.POLICY_EXTEND_INFO,policyInfo);
        return Result.success(result);
    }

    @ApiOperation(value = "修正保单扩展信息")
    @PostMapping("/corrected/log/page")
    public Result<PageUtils<PolicyCorrectedLogVo>> policyCorrectedLogPage(@RequestBody PolicyCorrectedLogQuery query) {
        PageUtils<PolicyCorrectedLogVo> data = policyContractInfoService.policyCorrectedLogPage(query);
        return Result.success(data);
    }

    @ApiOperation(value = "修正保单扩展信息")
    @GetMapping("/query/corrected/detail")
    public Result<EpContractInfoVo> policyCorrectedSnapshot(@RequestParam("id") Integer id) {
        EpContractInfoVo data = policyContractInfoService.policyCorrectedSnapshot(id);
        return Result.success(data);
    }
}
