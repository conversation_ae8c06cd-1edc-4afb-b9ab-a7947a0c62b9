package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentStudioIpEntity;
import com.mpolicy.manage.modules.agent.enums.AgentIpContentPlatformEnum;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentStudioIpVo;

import java.util.Map;

/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 11:09:17
 */
public interface AgentStudioIpService extends IService<AgentStudioIpEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 修改该平台总数量
     *
     * @param agentCode           代理人编码
     * @param contentPlatformEnum 平台枚举
     * @param size                总数量
     * @return
     */
    boolean changeCount(String agentCode, AgentIpContentPlatformEnum contentPlatformEnum, int size);


    /**
     * 获取代理人工作室信息
     * @param agentCode
     * @return
     */
    AgentStudioIpVo findAgentStudioIpByCode(String agentCode);
}

