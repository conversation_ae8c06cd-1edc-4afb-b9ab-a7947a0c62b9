package com.mpolicy.manage.modules.label.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.label.dao.LabelInfoDao;
import com.mpolicy.manage.modules.label.entity.LabelInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelInfoListOut;
import com.mpolicy.manage.modules.label.entity.LabelInfoListVo;
import com.mpolicy.manage.modules.label.service.LabelInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service("labelInfoService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LabelInfoServiceImpl extends ServiceImpl<LabelInfoDao, LabelInfoEntity> implements LabelInfoService {

    private final LabelInfoDao labelInfoDao;

    /**
     * 获取列表数据
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<LabelInfoListOut> findPageList(LabelInfoListVo params) {
        IPage<LabelInfoListOut> page = labelInfoDao.findPageList(
                new Page(params.getPage(), params.getLimit()), params);
        return new PageUtils(page);
    }
}
