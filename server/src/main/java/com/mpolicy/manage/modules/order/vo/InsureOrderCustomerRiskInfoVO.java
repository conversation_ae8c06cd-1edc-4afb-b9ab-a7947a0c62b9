package com.mpolicy.manage.modules.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 风险客户的视图类
 *
 *
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Data
public class InsureOrderCustomerRiskInfoVO extends InsureOrderCustomerRiskInfoEntity {

    /**
     *
     * 是否承保
     *
     */
    @ExcelProperty(value = "是否承保", index = 12)
    private Boolean isAccept;
}
