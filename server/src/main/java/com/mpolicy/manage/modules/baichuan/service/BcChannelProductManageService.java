package com.mpolicy.manage.modules.baichuan.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.baichuan.vo.BcChannelConfigInfo;
import com.mpolicy.manage.modules.baichuan.vo.BcChannelProductInfo;

import java.util.Map;

/**
 * 百川渠道应用产品管理
 *
 * <AUTHOR>
 * @since 2023-08-27 00:26:27
 */
public interface BcChannelProductManageService {

    /**
     * 百川渠道销售产品列表
     *
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.baichuan.vo.BcChannelProductInfo>
     * <AUTHOR>
     * @since 2023/8/27 01:48
     */
    PageUtils<BcChannelProductInfo> queryPage(Map<String, Object> params);

    /**
     * 百川渠道配置列表
     *
     * @param params:
     * @return : com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.baichuan.vo.BcChannelConfigInfo>
     * <AUTHOR>
     * @date 2023/9/8 14:22
     */
    PageUtils<BcChannelConfigInfo> queryConfigPage(Map<String, Object> params);
    /**
     * 百川渠道配置保存
     *
     * <AUTHOR>
     * @date 2023/9/8 17:55
     * @param configInfo:
     * @return : void
     */
    void saveBcConfigInfo(BcChannelConfigInfo configInfo);
    /**
     * 百川渠道配置修改
     *
     * <AUTHOR>
     * @date 2023/9/8 17:55
     * @param configInfo:
     * @return : void
     */
    void updateBcChannel(BcChannelConfigInfo configInfo);
}

