package com.mpolicy.manage.modules.helper.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("台账导出参数")
public class PolicyComplianceQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("销售方式:0=线上；1=线下")
    private String salesType;

    @ApiModelProperty("机构编码([,]分割)")
    private String orgCodes;

    @ApiModelProperty("交单时间[yyyy-MM-dd]([_]分割)")
    private String queryOrderTimes;

    @ApiModelProperty("生效时间[yyyy-MM-dd]([_]分割)")
    private String queryEnforceTimes;

    @ApiModelProperty("分销渠道编码")
    private String channelDistributionCode;

    @ApiModelProperty("导出的配置项")
    private String dingTalkToken;
}
