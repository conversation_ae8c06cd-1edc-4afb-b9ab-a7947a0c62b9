package com.mpolicy.manage.modules.agent.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: AgentSignFileDetailPageList
 * Description: 文件管理-文件详情分页查询返回信息
 * date: 2023/6/1 18:06
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "文件管理-文件详情分页查询返回信息", description = "文件管理-文件详情分页查询返回信息")
public class AgentSignFileDetailPageList implements Serializable {
    private static final long serialVersionUID = -504228069318678469L;

    /**
     * 上传文件编码
     */
    @ApiModelProperty(value = "上传文件编码")
    private String fileCode;
    /**
     * 上传文件名称
     */
    @ApiModelProperty(value = "上传文件名称")
    private String fileName;

    /**
     * 公司组织编码
     */
    @ApiModelProperty(value = "公司组织编码")
    private String orgCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String orgName;

    /**
     * 公司组织编码
     */
    @ApiModelProperty(value = "公司顶层组织编码")
    private String topOrgCode;

    /**
     * 应签署人数
     */
    @ApiModelProperty(value = "应签署人数")
    private Integer shouldNum;

    /**
     * 已签署人数
     */
    @ApiModelProperty(value = "已签署人数")
    private Integer alreadyNum;

    /**
     * 未签署人数
     */
    @ApiModelProperty(value = "未签署人数")
    private Integer unsignedNum;

    /**
     * 系统默认完成人数
     */
    @ApiModelProperty(value = "系统默认完成人数")
    private Integer defaultFinishNum;

    private String idList;

    private Integer enabled;
}

