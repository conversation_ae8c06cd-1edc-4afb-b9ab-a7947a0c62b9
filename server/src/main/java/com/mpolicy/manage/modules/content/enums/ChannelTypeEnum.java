//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.mpolicy.manage.modules.content.enums;

import java.util.Arrays;

/**
 * 渠道类型 0:企业;1:个人
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 17:42:21
 */
public enum ChannelTypeEnum {
    // 企业
    ENTERPRISE(0, "企业"),
    // 个人
    PERSONAL(1, "个人");

    private final Integer code;
    private final String name;

    ChannelTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static ChannelTypeEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
