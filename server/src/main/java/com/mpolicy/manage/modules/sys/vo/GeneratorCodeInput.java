package com.mpolicy.manage.modules.sys.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GeneratorCodeInput implements Serializable {
    private static final long serialVersionUID = -9188251651871986331L;


    @NotEmpty(message = "表不能为空")
    @ApiModelProperty(value = "表名称")
    private List<String> tables;

    @NotBlank(message = "模块名不能为空")
    private String modelName;

    @NotBlank(message = "项目包名不能为空")
    private String packageName;

    @NotBlank(message = "作者不能为空")
    private String author;

    @NotBlank(message = "作者邮箱不能为空")
    private String mailbox;

    @ApiModelProperty(value = "数据库表前缀")
    private String[] tablePrefix;
}
