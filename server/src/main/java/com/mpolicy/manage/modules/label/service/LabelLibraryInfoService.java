package com.mpolicy.manage.modules.label.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoListOut;

import java.util.List;

/**
 * 标签库信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
public interface LabelLibraryInfoService extends IService<LabelLibraryInfoEntity> {

    /**
     * 获取标签库列表
     * @return
     */
    List<LabelLibraryInfoListOut> findLabelLibraryInfoList();
}

