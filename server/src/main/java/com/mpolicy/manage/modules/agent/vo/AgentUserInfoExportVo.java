package com.mpolicy.manage.modules.agent.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 15:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentUserInfoExportVo extends BaseRowModel {
    private static final long serialVersionUID = 1L;

    private String agentCode;
    @ExcelProperty(value = "姓名")
    private String agentName;

    @ExcelProperty(value = "业务编码")
    private String businessCode;

    @ExcelProperty(value = "性别")
    private String gender;

    @ExcelProperty(value = "学历")
    private String degree;

    @ExcelProperty(value = "民族")
    private String nation;

    @ExcelProperty(value = "政治面貌")
    private String politics;

    @ExcelProperty(value = "出生日期")
    private String birthday;

    @ExcelProperty(value = "证件类型")
    private String idType;

    @ExcelProperty(value = "证件号")
    private String idCard;

    @ExcelProperty(value = "联系电话")
    private String mobile;

    @ExcelProperty(value = "电子邮箱")
    private String email;

    @ExcelProperty(value = "职级")
    private String position;

    @ExcelProperty(value = "银行卡号")
    private String bandCardNum;

    @ExcelProperty(value = "开户省份")
    private String bandLocationProvince;

    @ExcelProperty(value = "开户城市")
    private String bandLocationCity;

    @ExcelProperty(value = "开户银行")
    private String bandName;

    @ExcelProperty(value = "开户支行名称")
    private String bandBranchName;

    @ExcelProperty(value = "联行号")
    private String bankNumber;

    @ExcelProperty(value = "人员类型")
    private String agentType;

    @ExcelProperty(value = "组织机构")
    private String orgName;

    @ExcelProperty(value = "展业地区")
    private String acquisitionArea;

    @ExcelProperty(value = "人员状态")
    private String agentStatus;

    @ExcelProperty(value = "离职状态")
    private String quitStatus;

    @ExcelProperty(value = "离职日期")
    private String quitTime;

    @ExcelProperty(value = "入职日期")
    private String entryDate;

    @ExcelProperty(value = "增员人业务编码")
    private String recruitBusinessCode;

    @ExcelProperty(value = "增员人")
    private String recruitName;

    @ExcelProperty(value = "执业证编码")
    private String certificateNum;

    @ExcelProperty(value = "开始日期")
    private String startDate;

    @ExcelProperty(value = "国家")
    private String country;

    @ExcelProperty(value = "人员性质")
    private String agentNature;


    @ExcelProperty(value = "身份证起效日期")
    private String idStartDate;

    @ExcelProperty(value = "身份证到期日期")
    private String idEndDate;
    @ExcelProperty(value = "人员类型所属区域")
    private String areaManagerRegion;

    @ExcelProperty(value = "备注")
    private String managerRegionRemark;

    @ExcelProperty(value = "初始档次")
    private String positionDegree;

    @ExcelProperty(value = "互联网规范")
    private String marketingSignStatus;
    @ExcelProperty(value = "知乎账号")
    private String zhihuName;
    @ExcelProperty(value = "抖音账号")
    private String douyinName;
    @ExcelProperty(value = "微博账号")
    private String weiboName;
    @ExcelProperty(value = "公众号账号")
    private String wechatPublicPlatformName;
}
