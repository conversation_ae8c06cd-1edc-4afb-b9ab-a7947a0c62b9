package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AgentCertificateVo implements Serializable {
    private static final long serialVersionUID = -7252247476534437731L;
    @ApiModelProperty("证件类型")
    private String identificationType;

    @ApiModelProperty("执业证编码")
    private String certificateNum;

    @ApiModelProperty("开始日期")
    private Date startDate;

    @ApiModelProperty("截至日期")
    private Date endDate;

    @ApiModelProperty("长期有效")
    private boolean longTerm;
}
