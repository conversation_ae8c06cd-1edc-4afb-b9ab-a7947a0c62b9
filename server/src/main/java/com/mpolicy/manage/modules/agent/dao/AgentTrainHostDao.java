package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agent.entity.AgentTrainHostEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AgentTrainHostDao extends BaseMapper<AgentTrainHostEntity> {

    /**
     * 新增培训人员
     *
     * @param trainCode
     * @param trainHostList
     */
    void beachInsertTrainHost(@Param("trainCode") String trainCode, @Param("trainHostList") List<String> trainHostList);
}
