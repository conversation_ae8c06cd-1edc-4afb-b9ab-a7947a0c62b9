package com.mpolicy.manage.modules.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 回溯采集信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-15 15:53:34
 */
@TableName("trace_serial_recall_data")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TraceSerialRecallDataEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 采集凭证ID
	 */
	private String traceSerialId;
	/**
	 * 影像件oss存储地址
	 */
	private String shotImgPath;
	/**
	 * 页面描述
	 */
	private String operatePage;
	/**
	 * 采集页面地址
	 */
	private String currentUrl;
	/**
	 * 采集事件
	 */
	private String pageEvent;

	/**
	 * 进入页面时间
	 */
	private Date enterPageTime;

	/**
	 * 离开页面时间
	 */
	private Date quitPageTime;
	/**
	 * 平台
	 */
	private String platform;

	/**
	 * 页面描述
	 */
	private String operateIp;

	/**
	 * 回溯类型
	 */
	private String recallType;
	/**
	 * sts记录编号
	 */
	private String recordId;

	/**
	 * 是否删除 0否/1是
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
