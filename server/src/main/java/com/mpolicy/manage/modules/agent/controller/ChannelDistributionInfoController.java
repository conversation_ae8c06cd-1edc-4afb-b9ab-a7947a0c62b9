package com.mpolicy.manage.modules.agent.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelDistributionInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelDistributionInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 分销渠道信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-02-06 15:55:51
 */
@RestController
@RequestMapping("sys/channel/distribution")
@Api(tags = "分销渠道信息")
public class ChannelDistributionInfoController {

    @Autowired
    private ChannelDistributionInfoService channelDistributionInfoService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取分销渠道信息表列表", notes = "分页获取分销渠道信息表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "channelName", dataType = "String", value = "渠道名称", example = "小鲸向海"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道编码", example = "XJXH001"),
            @ApiImplicitParam(paramType = "query", name = "enabled", dataType = "Integer", value = "启用状态 1:启用;0:关闭", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"channel:distribution:all"})
    public Result<PageUtils> list(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params) {
        PageUtils page = channelDistributionInfoService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation("根据code获取分销渠道信息")
    @GetMapping("/info/{channelCode}")
    @RequiresPermissions(value = {"channel:distribution:all"})
    public Result<ChannelDistributionInfoEntity> info(@PathVariable("channelCode") String channelCode) {
        ChannelDistributionInfoEntity channelDistributionInfo = channelDistributionInfoService.getOne(new LambdaQueryWrapper<ChannelDistributionInfoEntity>().eq(ChannelDistributionInfoEntity::getChannelCode, channelCode));
        return Result.success(channelDistributionInfo);
    }

    /**
     * 新增
     */
    @ApiOperation("分销渠道新增")
    @PostMapping("/save")
    @RequiresPermissions(value = {"channel:distribution:all"})
    public Result save(@RequestBody @Validated(AddGroup.class) ChannelDistributionInfoEntity entity) {
        ChannelDistributionInfoEntity channelDistributionInfo = channelDistributionInfoService.getOne(new LambdaQueryWrapper<ChannelDistributionInfoEntity>().eq(ChannelDistributionInfoEntity::getChannelCode, entity.getChannelCode()));
        if (Objects.nonNull(channelDistributionInfo)) {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("该分销渠道编码已存在"));
        }
        boolean result = channelDistributionInfoService.saveOrUpdateEntity(entity);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 修改
     *
     * @param entity 修改参数
     * @return
     */
    @ApiOperation("分销渠道修改")
    @PostMapping("/update")
    @RequiresPermissions(value = {"channel:distribution:all"})
    public Result update(@RequestBody @Validated(UpdateGroup.class) ChannelDistributionInfoEntity entity) {
        boolean result = channelDistributionInfoService.saveOrUpdateEntity(entity);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 启用状态
     *
     * @param channelCode 渠道编码
     * @param enabled     启用状态
     * @return
     */
    @ApiOperation(value = "修改分销渠道信息启用状态", notes = "修改分销渠道信息启用状态")
    @PostMapping("/changeEnable/{channelCode}")
    @RequiresPermissions(value = {"channel:distribution:all"})
    public Result changeEnable(
            @ApiParam(name = "channelCode", value = "channelCode", required = true)
            @PathVariable("channelCode") String channelCode,
            @ApiParam(name = "enabled", value = "启用状态 0:禁用 1:启用 ", required = true)
            @RequestParam("enabled") Integer enabled) {
        boolean result = channelDistributionInfoService.changeEnable(channelCode, enabled);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改分销渠道状态失败"));
        }
    }

    /**
     * 获取所有分销渠道列表
     */
    @ApiOperation(value = "获取所有分销渠道列表(核心业务-保单中心)", notes = "获取所有分销渠道列表(核心业务-保单中心)")
    @GetMapping("/getAllChannelList")
    public Result<List<ChannelInfoVo>> getAllChannelList() {
        List<ChannelInfoVo> list = channelDistributionInfoService.getChannelList(false);
        return Result.success(list);
    }

    @ApiOperation(value = "获取用户拥有权限分销渠道列表(核心业务-保单中心)", notes = "获取用户拥有权限分销渠道列表(核心业务-保单中心)")
    @GetMapping("/getPurviewChannelList")
    public Result<List<ChannelInfoVo>> getPurviewChannelList() {
        List<ChannelInfoVo> list = channelDistributionInfoService.getChannelList(true);
        return Result.success(list);
    }

    @ApiOperation(value = "获取渠道分销树", notes = "获取渠道分销树")
    @GetMapping("findTreeList")
    public Result<List<TreeListOut>> findTreeList() {
        List<TreeListOut> list = channelDistributionInfoService.findTreeList();
        return Result.success(list);
    }
}
