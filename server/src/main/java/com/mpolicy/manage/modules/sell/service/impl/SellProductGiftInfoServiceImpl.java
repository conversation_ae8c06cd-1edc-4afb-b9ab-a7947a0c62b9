package com.mpolicy.manage.modules.sell.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.lock.DistributedLocker;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.sell.dao.SellProductGiftInfoDao;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftInfoEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftLogEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductInfoEntity;
import com.mpolicy.manage.modules.sell.enums.RenewalChannelEnum;
import com.mpolicy.manage.modules.sell.enums.SellProductGiftLogTypeEnum;
import com.mpolicy.manage.modules.sell.service.SellProductGiftInfoService;
import com.mpolicy.manage.modules.sell.service.SellProductGiftLogService;
import com.mpolicy.manage.modules.sell.utils.SellMapper;
import com.mpolicy.manage.modules.sell.vo.ReferrerOgrVo;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoQueryVo;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoSaveVo;
import com.mpolicy.manage.modules.sell.vo.SellProductGiftInfoUpdateVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 销售产品的赠品管理信息service的实现类
 *
 *
 * @create 2024/12/2
 * @since 1.0.0
 */
@Slf4j
@Service("sellProductGiftInfoService")
public class SellProductGiftInfoServiceImpl extends ServiceImpl<SellProductGiftInfoDao, SellProductGiftInfoEntity> implements SellProductGiftInfoService {
    private static final String LOCK_KEY = "STOCK_CHANGE_LOCK_KEY";

    @Resource
    private SellMapper sellMapper;

    @Resource
    private SellProductGiftLogService sellProductGiftLogService;

    @Resource
    private DistributedLocker distributedLocker;

    /**
     *
     *
     * 检测销售商品的保司数据
     *
     *
     *
     * @param sellProductGiftInfoSaveVo
     */
    private void checkSellProductGiftInfoSaveVo(SellProductGiftInfoSaveVo sellProductGiftInfoSaveVo) {
        String channelCode = sellProductGiftInfoSaveVo.getChannelCode();
        RenewalChannelEnum channelEnum = RenewalChannelEnum.decode(channelCode);
        if (Objects.isNull(channelEnum)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("渠道编码不正确，并且不能为空"));
        }

        List<ReferrerOgrVo> referrerOgrVoList = sellProductGiftInfoSaveVo.getReferrerOgrVoList();
        if (CollectionUtils.isEmpty(referrerOgrVoList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("礼品分支不能为空"));
        }

        List<SellProductInfoEntity> sellProductInfoEntityList = sellProductGiftInfoSaveVo.getSellProductInfoEntityList();
        if (CollectionUtils.isEmpty(sellProductInfoEntityList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("商品配置不能为空"));
        }

        Date effectiveDate = sellProductGiftInfoSaveVo.getEffectiveDate();
        Date expirationDate = sellProductGiftInfoSaveVo.getExpirationDate();
        // 失效日期必须大于生效效日期
        if (DateUtil.compare(effectiveDate,expirationDate) > 0) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("失效日期必须大于生效效日期"));
        }


        String giftCode = sellProductGiftInfoSaveVo.getGiftCode();
        String giftCodeValue = DicCacheHelper.getValue(giftCode);
        if (StringUtils.isBlank(giftCodeValue)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该礼物不存在"));
        }

        for (SellProductInfoEntity sellProductInfoEntity : sellProductInfoEntityList) {
            String productCode = sellProductInfoEntity.getProductCode();
            for (ReferrerOgrVo referrerOgrVo : referrerOgrVoList) {
                String referrerOgrCode = referrerOgrVo.getReferrerOgrCode();
                if (StringUtils.isBlank(referrerOgrCode) || countByReferrerOgrCodeAndProductCodeAndGiftCode(referrerOgrCode,productCode,giftCode) > 0) {
                    String referrerOgrName = referrerOgrVo.getReferrerOgrName();
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(String.format("%s机构已添加过商品%s的礼品%s，请通过编辑配置",referrerOgrName,sellProductInfoEntity.getProductName(),sellProductGiftInfoSaveVo.getGiftName())));
                }
            }
        }
    }

    /**
     *
     *
     * 根据礼品区域
     * 产品编码
     * 礼品名称
     * 鲸喜查询
     *
     *
     * @param referrerOgrCode
     *
     * 礼品分支编码
     *
     * @param productCode
     *
     * 产品编码
     *
     * @param giftCode
     *
     * 礼品编码
     *
     * @return
     *
     */
    private int countByReferrerOgrCodeAndProductCodeAndGiftCode(String referrerOgrCode,String productCode,String giftCode){
        return this.lambdaQuery()
                .eq(StringUtils.isNotBlank(referrerOgrCode),SellProductGiftInfoEntity::getReferrerOgrCode,referrerOgrCode)
                .eq(StringUtils.isNotBlank(productCode),SellProductGiftInfoEntity::getProductCode,productCode)
                .eq(StringUtils.isNotBlank(giftCode),SellProductGiftInfoEntity::getGiftCode,giftCode)
                .count();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(SellProductGiftInfoSaveVo sellProductGiftInfoSaveVo) {
        distributedLocker.lock(LOCK_KEY,5);
        boolean saved ;
        try {
            log.info("销售商品礼品保存 sellProductGiftInfoSaveVo={}", JSON.toJSONString(sellProductGiftInfoSaveVo));
            checkSellProductGiftInfoSaveVo(sellProductGiftInfoSaveVo);
            List<SellProductGiftInfoEntity> sellProductGiftInfoEntityList = buildSellProductGiftInfoEntityListBySellProductGiftInfoSaveVo(sellProductGiftInfoSaveVo);
            List<SellProductGiftLogEntity> sellProductGiftLogEntityList = buildSellProductGiftLogEntityList(sellProductGiftInfoEntityList);
            int num = sellProductGiftLogService.saveForLogType(SellProductGiftLogTypeEnum.INI, sellProductGiftLogEntityList);
            log.info("销售商品礼品插入日志成功 num={}",num);
            saved = super.saveBatch(sellProductGiftInfoEntityList);
        }catch (GlobalException e) {
            log.warn("礼品管理系统保存失败，sellProductGiftInfoSaveVo={}", JSON.toJSONString(sellProductGiftInfoSaveVo),e);
            throw e;
        }finally {
            distributedLocker.unlock(LOCK_KEY);
        }
        return saved;
    }

    private List<SellProductGiftLogEntity> buildSellProductGiftLogEntityList(List<SellProductGiftInfoEntity> sellProductGiftInfoEntityList) {
        return sellProductGiftInfoEntityList
                .stream()
                .map(sellProductGiftInfoEntity -> sellMapper.sellProductGiftInfoEntity2SellProductGiftLogEntity(sellProductGiftInfoEntity))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySellProductGiftInfoUpdateVo(SellProductGiftInfoUpdateVo sellProductGiftInfoUpdateVo) {
        distributedLocker.lock(LOCK_KEY,5);
        log.info("销售商品礼品更新 sellProductGiftInfoUpdateVo={}", JSON.toJSONString(sellProductGiftInfoUpdateVo));
        try {
            Date effectiveDate = sellProductGiftInfoUpdateVo.getEffectiveDate();
            Date expirationDate = sellProductGiftInfoUpdateVo.getExpirationDate();
            Integer remainingGiftQuantityChange = sellProductGiftInfoUpdateVo.getRemainingGiftQuantityChange();
            // 失效日期必须大于生效效日期
            if (DateUtil.compare(effectiveDate,expirationDate) > 0) {
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("失效日期必须大于生效效日期"));
            }
            // 库存是小于扣减的
            if (remainingGiftQuantityChange < 0){
                Long id = sellProductGiftInfoUpdateVo.getId();
                SellProductGiftInfoEntity productGiftInfoEntity = this.getById(id);
                Integer remainingGiftQuantity = productGiftInfoEntity.getRemainingGiftQuantity();
                if (remainingGiftQuantity < Math.abs(remainingGiftQuantityChange)){
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("库存不足"));
                }
            }
            // 库存单独处理
            handlerStockChange(sellProductGiftInfoUpdateVo);
            SellProductGiftInfoEntity sellProductGiftInfoEntity = sellMapper.sellProductGiftInfoUpdateVo2SellProductGiftInfoEntity(sellProductGiftInfoUpdateVo);
            this.updateById(sellProductGiftInfoEntity);
        }catch (GlobalException e) {
            log.warn("礼品管理系统更新失败，sellProductGiftInfoSaveVo={}", JSON.toJSONString(sellProductGiftInfoUpdateVo),e);
            throw e;
        }finally {
            distributedLocker.unlock(LOCK_KEY);
        }
    }

    private void handlerStockChange(SellProductGiftInfoUpdateVo sellProductGiftInfoUpdateVo) {
        Long giftInfoUpdateVoId = sellProductGiftInfoUpdateVo.getId();
        Integer remainingGiftQuantityChange = sellProductGiftInfoUpdateVo.getRemainingGiftQuantityChange();
        int remainingGiftQuantityChangeAbs = Math.abs(remainingGiftQuantityChange);
        Integer stockChangeNum;
        if (remainingGiftQuantityChange > 0) {
            stockChangeNum = this.baseMapper.addStock(giftInfoUpdateVoId,remainingGiftQuantityChange);
        }else if (remainingGiftQuantityChange < 0){
            SellProductGiftInfoEntity productGiftInfoEntity = this.getById(giftInfoUpdateVoId);
            Integer remainingGiftQuantity = productGiftInfoEntity.getRemainingGiftQuantity();
            if (remainingGiftQuantity < remainingGiftQuantityChangeAbs){
                log.warn("库存不足,当前剩余库存remainingGiftQuantity={},需要扣减库存={}",remainingGiftQuantity,remainingGiftQuantityChangeAbs);
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(String.format("库存不足,当前剩余库存remainingGiftQuantity=%s,需要扣减库存=%s",remainingGiftQuantity,remainingGiftQuantityChangeAbs)));
            }
            stockChangeNum = this.baseMapper.subtractStock(giftInfoUpdateVoId,remainingGiftQuantityChangeAbs);
        }else {
            return;
        }

        if (stockChangeNum == 0){
            log.warn("库存更新失败 sellProductGiftInfoUpdateVo={}", JSON.toJSONString(sellProductGiftInfoUpdateVo));
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("库存更新失败"));
        }

        SellProductGiftInfoEntity sellProductGiftInfoEntity = this.getById(giftInfoUpdateVoId);
        SellProductGiftLogEntity sellProductGiftLogEntity = sellMapper.sellProductGiftInfoEntity2SellProductGiftLogEntity(sellProductGiftInfoEntity);
        sellProductGiftLogEntity.setStockChangeNumber(remainingGiftQuantityChange);
        sellProductGiftLogService.saveForLogType(SellProductGiftLogTypeEnum.ADD_OR_DECREASE, sellProductGiftLogEntity);
    }

    @Override
    public IPage<SellProductGiftInfoEntity> getPageOrderByRemainingGiftQuantityDesc(SellProductGiftInfoQueryVo sellProductGiftInfoQueryVo) {
        String giftCode = sellProductGiftInfoQueryVo.getGiftCode();
        List<String> referrerOgrCodeList = sellProductGiftInfoQueryVo.getReferrerOgrCode();
        String productName = sellProductGiftInfoQueryVo.getProductName();
        Date effectiveDate = sellProductGiftInfoQueryVo.getEffectiveDate();
        Date expirationDate = sellProductGiftInfoQueryVo.getExpirationDate();
        return  this.lambdaQuery()
                    .eq(StringUtils.isNotBlank(giftCode),SellProductGiftInfoEntity::getGiftCode,giftCode)
                    .in(CollectionUtils.isNotEmpty(referrerOgrCodeList),SellProductGiftInfoEntity::getReferrerOgrCode,referrerOgrCodeList)
                    .likeRight(StringUtils.isNotBlank(productName),SellProductGiftInfoEntity::getProductName,productName)
                    .lt(Objects.nonNull(expirationDate),SellProductGiftInfoEntity::getExpirationDate,expirationDate)
                    .ge(Objects.nonNull(effectiveDate),SellProductGiftInfoEntity::getEffectiveDate,effectiveDate)
                    .orderByDesc(SellProductGiftInfoEntity::getRemainingGiftQuantity)
                    .page(sellProductGiftInfoQueryVo);
    }

    @Override
    public List<SellProductGiftInfoEntity> getActiveLessQuantity(Integer customerGiftQuantityEmergencyNum) {
        DateTime currentDate = DateUtil.date();
        return this.lambdaQuery()
                .le(SellProductGiftInfoEntity::getRemainingGiftQuantity,customerGiftQuantityEmergencyNum)
                .ge(SellProductGiftInfoEntity::getExpirationDate,currentDate)
                .le(SellProductGiftInfoEntity::getEffectiveDate,currentDate)
                .list();
    }


    private List<SellProductGiftInfoEntity> buildSellProductGiftInfoEntityListBySellProductGiftInfoSaveVo(SellProductGiftInfoSaveVo sellProductGiftInfoSaveVo) {
        log.info("销售商品礼品构建 sellProductGiftInfoSaveVo={}", JSON.toJSONString(sellProductGiftInfoSaveVo));
        List<ReferrerOgrVo> giftRegionCodeList = sellProductGiftInfoSaveVo.getReferrerOgrVoList();
        List<SellProductInfoEntity> sellProductInfoEntityList = sellProductGiftInfoSaveVo.getSellProductInfoEntityList();
        List<SellProductGiftInfoEntity> sellProductGiftInfoEntityList = Lists.newLinkedList();
        for (SellProductInfoEntity sellProductInfoEntity : sellProductInfoEntityList) {
            for (ReferrerOgrVo referrerOgrVo : giftRegionCodeList) {
                String referrerOgrCode = referrerOgrVo.getReferrerOgrCode();
                String referrerOgrName = referrerOgrVo.getReferrerOgrName();
                SellProductGiftInfoEntity sellProductGiftInfoEntity = sellMapper.sellProductGiftInfoSaveVo2SellProductGiftInfoEntity(sellProductGiftInfoSaveVo);
                sellProductGiftInfoEntity.setReferrerOgrCode(referrerOgrCode);
                sellProductGiftInfoEntity.setReferrerOgrName(referrerOgrName);
                sellProductGiftInfoEntity.setProductCode(sellProductInfoEntity.getProductCode());
                sellProductGiftInfoEntity.setProductName(sellProductInfoEntity.getProductName());
                sellProductGiftInfoEntityList.add(sellProductGiftInfoEntity);
            }
        }

        log.info("销售商品礼品构建后VO sellProductGiftInfoEntityList={}", JSON.toJSONString(sellProductGiftInfoEntityList));
        return sellProductGiftInfoEntityList;
    }
}
