package com.mpolicy.manage.modules.insurance.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: CompanyInfoInput
 * @description: 产品险种列表信息
 * @author: haijun.sun
 * @date: 2021-03-22 16:14
 **/
@ApiModel(value = "产品险种列表信息")
@Data
public class ProductList {

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品险种名称")
    private String productName;


    /**
     * 保司编码
     */
    @ApiModelProperty(value = "保司编码")
    private String companyCode;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保司名称")
    private String companyName;

    /**
     * 条款上传标识 0否 1是
     */
    @ApiModelProperty(value = "条款上传标识 0否 1是", example = "1")
    private Integer termsFlag;

    /**
     * 是否推送农保 0.不需要，1.需要
     */
    private Integer isPushRural;

    /**
     * 协议列表
     */
    @ApiModelProperty(value = "协议列表")
    List<ProductProtocolInfo> productProtocolInfoList;

    /**
     * 责任定义状态 0否 1是
     */
    @ApiModelProperty(value = "责任定义状态 0否 1是", example = "1")
    private Integer dutyFlag;

    /**
     * 主附险说明
     */
    @ApiModelProperty(value = "主附险说明", example = "附加险")
    private String mainFlag;

    /**
     * 费率表上传状态 0否 1是
     */
    @ApiModelProperty(value = "费率表上传状态 0否 1是", example = "1")
    private Integer premTableFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2019-01-01 15:12:20")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
