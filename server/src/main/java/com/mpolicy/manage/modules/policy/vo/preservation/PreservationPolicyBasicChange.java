package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.policy.common.ep.policy.preserve.AddSubtractMemberProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保全保单基本信息变更增减员
 *
 * <AUTHOR>
 * @date 2022-04-06 10:39
 */
@ApiModel(value = "保全保单基本信息变更增减员")
@Data
public class PreservationPolicyBasicChange extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private String id;

    @ApiModelProperty(value = "保全-被保人流水号",hidden = true)
    private String serialNo;

    @ApiModelProperty(value = "被保人编号",hidden = true)
    private String insuredCode;

    @ApiModelProperty(value = "增减员类型 加人/减人", example = "加人")
    @ExcelProperty(value = "增减员类型", index = 1)
    private String type;

    @ApiModelProperty(value = "主被保人标识")
    private String mainInsuredFlag;

    @ApiModelProperty(value = "对应主被保人")
    private String mainInsuredName;

    @ApiModelProperty(value = "被保人姓名", example = "张三")
    @ExcelProperty(value = "被保人姓名", index = 2)
    private String insuredName;

    @ApiModelProperty(value = "被保人姓名", example = "张三")
    private String insuredRelation;

    @ApiModelProperty(value = "与主被保人关系", example = "配偶")
    @ExcelProperty(value = "与员工关系", index = 3)
    private String firstInsuredRelation;

    @ApiModelProperty(value = "被保人性别 0：女 1：男")
    @ExcelProperty(value = "性别", index = 4)
    private String insuredGender;

    @ApiModelProperty(value = "被保人出生日期")
    @ExcelProperty(value = "出生日期", index = 5)
    private String insuredBirthday;

    @ApiModelProperty(value = "被保人证件类型")
    @ExcelProperty(value = "证件类型", index = 6)
    private String insuredIdType;

    @ApiModelProperty(value = "被保人证件号码")
    @ExcelProperty(value = "证件号码", index = 7)
    private String insuredIdCard;

    @ApiModelProperty(value = "证件有效期")
    @ExcelProperty(value = "证件有效期", index = 8)
    private String insuredIdCardValidityEnd;

    @ApiModelProperty(value = "国籍")
    @ExcelProperty(value = "国籍", index = 9)
    private String insuredNation;

    @ApiModelProperty(value = "地址")
    @ExcelProperty(value = "地址", index = 10)
    private String insuredAddress;

    @ApiModelProperty(value = "产品计划编码")
    @ExcelProperty(value = "产品计划编码", index = 11)
    private String planCode;

    @ApiModelProperty(value = "产品计划名称")
    private String planName;

    @ApiModelProperty(value = "保障计划编码")
    @ExcelProperty(value = "保障计划编码", index = 12)
    private String virtualPlanCode;

    @ApiModelProperty(value = "保障计划名称")
    private String virtualPlanName;

    @ApiModelProperty(value = "保费")
    @ExcelProperty(value = "保费", index = 13)
    private BigDecimal singlePremium;
    /**
     * 职业代码
     */
    @ApiModelProperty(value = "职业代码")
    @ExcelProperty(value = "职业代码", index = 14)
    private String insuredCareer;
    /**
     * 职业类别
     */
    @ApiModelProperty(value = "职业类别")
    @ExcelProperty(value = "职业类别", index = 15)
    private String insuredOccupationalCategory;

    /**
     * 主被保人证件号码
     */
    @ApiModelProperty(value = "主被保人证件号码")
    @ExcelProperty(value = "主被保人证件号码", index = 16)
    private String mainInsuredIdCard;
    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式", index = 17)
    private String insuredMobile;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    @ExcelProperty(value = "电子邮箱", index = 18)
    private String insuredEmail;

    @ApiModelProperty(value = "工作单位")
    @ExcelProperty(value = "工作单位", index = 19)
    private String insuredCompany;

    @ApiModelProperty(value = "被保人保单生效时年龄")
    private Integer insuredPolicyAge;

    @ExcelProperty(value = "推荐人(代理人)工号", index = 20)
    @ApiModelProperty(value = "上游传递参数，后台根据该字段信息解析数据")
    private String referrerWno;


    @ExcelProperty(value = "渠道推荐人工号", index = 21)
    @ApiModelProperty(value = "上游传递参数，后台根据该字段信息解析数据")
    private String channelReferrerWno;

    @ExcelProperty(value = "备注", index = 22)
    private String remark;

    @ApiModelProperty(value = "推荐人编码", hidden = true)
    private String referrerCode;

    @ApiModelProperty(value = "推荐人名字")
    private String referrerName;

    @ApiModelProperty(value = "渠道推荐人编码", hidden = true)
    private String channelReferrerCode;

    @ApiModelProperty(value = "渠道推荐人名字")
    private String channelReferrerName;

    @ApiModelProperty(value = "销售渠道")
    private String channelCode;

    @ApiModelProperty(value = "销售渠道")
    private String channelName;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchCode;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchName;

    @ApiModelProperty(value = "机构区域")
    private String orgCode;

    @ApiModelProperty("客户经理")
    private String customerManagerCode;

    @ApiModelProperty("客户经理渠道编码")
    private String customerManagerChannelCode;

    @ApiModelProperty("客户经理所属分支机构")
    private String customerManagerOrgCode;

    @ApiModelProperty("客户经理渠道机构编码")
    private String customerManagerChannelOrgCode;

    @ApiModelProperty("客户经理督导")
    private String customerManagerSupervisor;

    @ApiModelProperty(value = "被保人险种")
    private List<AddSubtractMemberProductVo> productList;
}
