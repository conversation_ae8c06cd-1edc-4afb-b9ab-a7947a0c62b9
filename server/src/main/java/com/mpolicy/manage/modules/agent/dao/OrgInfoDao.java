package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.vo.orginfo.OrgInfoOut;
import com.mpolicy.manage.modules.agent.vo.orginfo.OrgPageListOut;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 组织信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-10 11:14:18
 */
public interface OrgInfoDao extends BaseMapper<OrgInfoEntity> {

    /**
     * 获取组织管理列表
     * @param params
     * @return
     */
    List<OrgPageListOut> findOrgInfoList(@Param("params") Map<String, Object> params);


    /**
     * 获取组织详情
     * @param code
     * @return
     */
    OrgInfoOut findOrgInfoByCode(@Param("orgCode") String code);
}
