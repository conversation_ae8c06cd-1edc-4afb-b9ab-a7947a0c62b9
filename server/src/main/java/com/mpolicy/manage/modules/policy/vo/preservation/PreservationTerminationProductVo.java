package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("解除附加险详情")
public class PreservationTerminationProductVo {

    @ApiModelProperty(value = "附加险解约信息集合")
    List<ProductTerminationInfo> productTerminationList;

    @ApiModelProperty("被保人险种明细")
    private List<PreservationProductInsuredMapVo> insuredProductList;
}
