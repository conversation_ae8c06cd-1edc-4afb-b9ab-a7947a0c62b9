package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 白名单信息
 *
 * <AUTHOR>
 * @date 2023-5-17 13:42
 */
@Data
@ApiModel(value = "白名单信息")
public class InsureUserWhiteRosterVO {

    @ApiModelProperty(value = "id", example = "1")
    private Integer id;


    @ApiModelProperty(value = "姓名", required = true, example = "张三")
    @NotBlank(message = "姓名不能为空")
    private String realName;

    @ApiModelProperty(value = "身份证号", required = true, example = "123456789098765")
    @NotBlank(message = "身份证号不能为空")
    private String cardNo;

    @ApiModelProperty(value = "白名单类型 0：实名认证 1：人脸识别", required = true, example = "1")
    @NotNull(message = "白名单类型不能为空")
    private Integer rosterType;

    @ApiModelProperty(value = "影响平台", required = true, example = "APP")
    @NotBlank(message = "影响平台不能为空")
    private String platformType;

    @ApiModelProperty(value = "有效开始时间", example = "2023-05-17 13:42")
    private String startDate;

    @ApiModelProperty(value = "有效结束时间", example = "2023-05-17 13:42")
    private String endDate;

    @ApiModelProperty(value = "是否长期有效：0：是，1：否", required = true, example = "1")
    @NotNull(message = "长期有效状态不能为空")
    private Integer isLongTerm;

    @ApiModelProperty(value = "身份证照片", example = "OSS1234567890")
    private String cardFileCode;

    @ApiModelProperty(value = "识别视频", example = "OSS1234567890")
    private String faceVideoCode;

    @ApiModelProperty(value = "是否启用 0:停用;1:启用", required = true, example = "1")
    @NotNull(message = "是否启用状态不能为空")
    private Integer isStatus;

    @ApiModelProperty(value = "是否导入 0:不是;1:是", example = "1")
    private Integer isImport;

    @ApiModelProperty(value = "视频信息")
    private JSONObject videoObject;

    @ApiModelProperty(value = "图片信息")
    private JSONObject imgObject;
}
