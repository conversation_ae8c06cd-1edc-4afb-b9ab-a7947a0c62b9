package com.mpolicy.manage.modules.policy.vo.trust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description 保单托管想象中配置返回信息
 * @return
 * @Date 2023/11/9 15:39
 * <AUTHOR>
 **/
@ApiModel(value = "保单托管想象中配置返回信息")
@Data
public class PolicyUserRecordRiskConfigPageOut {
    /**
     * 社保配置编码code
     */
    @ApiModelProperty(value = "社保配置编码code")
    private String code;
    /**
     * 社保类型 1：职工医保 2：居民医保 3：新农合
     */
    @ApiModelProperty(value = "社保类型 1：职工医保 2：居民医保 3：新农合")
    private Integer socialType;
    /**
     * 保司编码
     */
    @ApiModelProperty(value = "保司编码")
    private String companyCode;
    /**
     * 保司名称
     */
    @ApiModelProperty(value = "保司名称")
    private String companyName;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 保单保额
     */
    @ApiModelProperty(value = "保单保额")
    private BigDecimal policyCoverage;
    /**
     * 保障期间
     */
    @ApiModelProperty(value = "保障期间")
    private String insuredPeriodBrief;
    /**
     * 保障期间详情
     */
    @ApiModelProperty(value = "保障期间详情")
    private String insuredPeriod;
    /**
     * 缴费期间
     */
    @ApiModelProperty(value = "缴费期间")
    private String paymentPeriodBrief;
    /**
     * 缴费期间详情
     */
    @ApiModelProperty(value = "缴费期间详情")
    private String paymentPeriod;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    private BigDecimal premium;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer deleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 险种配置地区
     */
    @ApiModelProperty(value = "险种配置地区")
    private List<PolicyUserRecordRiskAreaOut> list;
}
