package com.mpolicy.manage.modules.agent.controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionnaireQuestionVo;
import com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionnaireVo;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.service.AgentQuestionnaireService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 代理人问卷表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
@RestController
@RequestMapping("agent/questionnaire")
@Api(tags = "代理人问卷")
public class BlAgentQuestionnaireController {

    @Autowired
    private AgentQuestionnaireService blAgentQuestionnaireService;


    /**
	 * 列表
	 */
    @ApiOperation(value = "获取代理人问卷列表", notes = "分页获取代理人问卷列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "title", dataType = "String", value = "问卷名称", example = "问卷")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"agent:questionnaire:all"})
    public Result<PageUtils> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params){
        PageUtils page = blAgentQuestionnaireService.queryQuestionnairePage(params);
        return Result.success(page);
    }

    @GetMapping("/detail/{questionnaireId}")
    @RequiresPermissions(value = {"agent:questionnaire:all"})
    @ApiOperation(value = "问卷详情", notes = "问卷详情")
    public Result<AgentQuestionnaireVo> detail(@PathVariable("questionnaireId") Integer  questionnaireId) {
        AgentQuestionnaireVo agentQuestionnaireVo = blAgentQuestionnaireService.detail(questionnaireId);
        return Result.success(agentQuestionnaireVo);
    }

    /**
     * 信息
     */
    @ApiOperation(value = "保存并发布问卷", notes = "保存并发布问卷",httpMethod = "POST")
    @PostMapping("/save")
    @RequiresPermissions(value = {"agent:questionnaire:all"})
    public Result save(@RequestBody AgentQuestionnaireVo agentQuestionnaireVo){
        blAgentQuestionnaireService.save(agentQuestionnaireVo);
        return Result.success();
    }

    /**
     * 信息
     */
    /**
     * 列表
     */
    @ApiOperation(value = "问卷进展列表", notes = "问卷进展列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "questionnaireId", dataType = "Integer", value = "问卷id", example = "1", required = true)
    })
    @GetMapping("/questionnaire/info/list")
    public Result<PageUtils> investigateList(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params){
        if(StringUtils.isBlank((String)params.get("questionnaireId"))){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("问卷id不能为空"));
        }
        PageUtils page = blAgentQuestionnaireService.queryInvestigatePage(params);
        return Result.success(page);
    }

    /**
     * 列表
     */
    @ApiOperation(value = "代理人答题详情", notes = "代理人答题详情")
    @GetMapping("/questionnaire/answer/list")
    public Result<List<AgentQuestionnaireQuestionVo>> answerList(@RequestParam(required = false) @NotBlank(message = "代理人编码不能为空") @Valid String agentCode, @RequestParam(required = false) @NotNull(message = "问卷Id不能为空") @Valid Integer questionnaireId){
        //枚举值未生效先采用编码校验
        if(StringUtils.isBlank(agentCode)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("代理人编码不能为空"));
        }
        if(Objects.isNull(questionnaireId)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("问卷Id不能为空"));
        }
        List<AgentQuestionnaireQuestionVo> list = blAgentQuestionnaireService.queryAgentAnswerPage(agentCode,questionnaireId);
        return Result.success(list);
    }

    @GetMapping("/delete/{questionnaireId}")
    @RequiresPermissions(value = {"agent:questionnaire:all"})
    @ApiOperation(value = "删除问卷", notes = "删除问卷",httpMethod = "GET")
    public Result delete(@PathVariable("questionnaireId") Integer questionnaireId) {
        blAgentQuestionnaireService.delete(questionnaireId);
        return Result.success();
    }
}
