package com.mpolicy.manage.modules.agent.vo.train;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentTrainInfoOut implements Serializable {
    private static final long serialVersionUID = -7727449815581137285L;

    @ApiModelProperty(value = "是否为暂存状态")
    private Integer isTemp;

    @ApiModelProperty(value = "培训记录code")
    private String trainCode;

    @ApiModelProperty(value = "培训主题")
    private String trainTopic;

    @ApiModelProperty(value = "培训内容")
    private String trainContent;

    @ApiModelProperty(value = "培训开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @ApiModelProperty(value = "培训结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "签到状态0:未处理  1:已处理")
    private Integer signStatus;

    @ApiModelProperty(value = "参会人员列表")
    private List<String> participants;

    @ApiModelProperty(value = "培训的主持")
    private List<String> trainHostList;

}
