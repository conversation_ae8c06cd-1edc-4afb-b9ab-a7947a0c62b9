package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人消息列表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-02-05 14:01:00
 */
@TableName("agent_message_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentMessageInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */

    private Integer id;
    /**
     * 消息的编码
     */
    @TableId(value = "message_code", type = IdType.INPUT)
    private String messageCode;

    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 经纪人编码
     */
    private String agentCode;
    /**
     * 标题
     */
    private String title;
    /**
     * 点击跳转类型
     */
    private Integer jumpType;
    /**
     * 点击跳转参数
     */
    private String jumpParam;
    /**
     * 简单描述-简述
     */
    private String content;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 未读消息数
     */
    private Integer unread;
}
