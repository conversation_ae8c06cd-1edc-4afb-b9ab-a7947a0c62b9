package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.insure.entity.InsureInsurancePlatformHistoryEntity;

/**
 * 投保于中台交互流水表
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
public interface InsureInsurancePlatformHistoryService extends IService<InsureInsurancePlatformHistoryEntity> {


     /**
      * <p>
      * 报错投保与中台交互纪录
      * </p>
      *
      * @param history history
      * @return void
      * <AUTHOR>
      * @since 2022/5/27
      */
     void asyncSaveInsureInsurancePlatform(InsureInsurancePlatformHistoryEntity history);
}

