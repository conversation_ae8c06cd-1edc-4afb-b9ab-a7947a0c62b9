package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.agent.common.enums.AgentTypeEnum;
import com.mpolicy.authorize.client.AuthorizeCenterClient;
import com.mpolicy.authorize.common.sms.SendMsgInput;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.customer.client.CustomerClient;
import com.mpolicy.manage.common.AdminCommonKeys;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.enums.SmsCodeEnum;
import com.mpolicy.manage.modules.agent.dao.ChannelApplicationReferrerDao;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.enums.AgentStatusEnum;
import com.mpolicy.manage.modules.agent.enums.ReferrerHandoverStatusEnum;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.vo.ChannelApplicationVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer;
import com.mpolicy.manage.modules.policy.vo.EpPolicyChannelInfoVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("channelApplicationReferrerService")
public class ChannelApplicationReferrerServiceImpl extends ServiceImpl<ChannelApplicationReferrerDao, ChannelApplicationReferrerEntity> implements ChannelApplicationReferrerService {
    @Autowired
    AuthorizeCenterClient authorizeCenterClient;

    @Autowired
    private ChannelApplicationService channelApplicationService;

    @Autowired
    private ChannelInfoService channelInfoService;

    @Autowired
    private ChannelBranchInfoService channelBranchInfoService;

    @Autowired
    private AgentUserInfoService agentUserInfoService;

    @Autowired
    OrgInfoService orgInfoService;

    @Autowired
    CustomerClient customerClient;
    @Autowired
    IRedisService redisService;

    @Value("${wx.miniapp.appid}")
    private String appid;

    private static final String ZHNX_CHANNEL_CODE = "zhnx";

    private static final String ZHNX_CHANNEL_NAME = "中和农信";

    @Override
    public PageUtils<ChannelApplicationReferrerEntity> queryPage(String applicationCode, Map<String, Object> params) {
        // 获取权限标识
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        if (channelBranchCodeList != null && channelBranchCodeList.isEmpty()) {
            return new PageUtils<>(Collections.emptyList(), 0, 0, 1);
        }
        String referrerWno = RequestUtils.objectValueToString(params, "referrerWno");
        String referrerName = RequestUtils.objectValueToString(params, "referrerName");
        String referrerCode = RequestUtils.objectValueToString(params, "referrerCode");
        String referrerMobile = RequestUtils.objectValueToString(params, "referrerMobile");
        String referrerRegion = RequestUtils.objectValueToString(params, "referrerRegion");
        String referrerServiceStatus = RequestUtils.objectValueToString(params, "referrerServiceStatus");
        String handoverStatus = RequestUtils.objectValueToString(params, "handoverStatus");
        IPage<ChannelApplicationReferrerEntity> page = this.page(
                new Query<ChannelApplicationReferrerEntity>().getPage(params),
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .in(channelBranchCodeList != null, ChannelApplicationReferrerEntity::getChannelBranchCode, channelBranchCodeList)
                        .eq(StringUtils.isNotBlank(applicationCode), ChannelApplicationReferrerEntity::getApplicationCode, applicationCode)
                        .eq(StringUtils.isNotBlank(referrerCode), ChannelApplicationReferrerEntity::getReferrerCode, referrerCode)
                        .likeRight(referrerWno != null, ChannelApplicationReferrerEntity::getReferrerWno, referrerWno)
                        .likeRight(referrerName != null, ChannelApplicationReferrerEntity::getReferrerName, referrerName)
                        .likeRight(referrerMobile != null, ChannelApplicationReferrerEntity::getReferrerMobile, referrerMobile)
                        .eq(referrerRegion != null, ChannelApplicationReferrerEntity::getReferrerRegion, referrerRegion)
                        .eq(StringUtils.isNotBlank(referrerServiceStatus), ChannelApplicationReferrerEntity::getReferrerServiceStatus, referrerServiceStatus)
                        .eq(StringUtils.isNotBlank(handoverStatus), ChannelApplicationReferrerEntity::getHandoverStatus, handoverStatus)
        );

        Map<String, String> referrerMap = this.list(
                        Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                                .eq(StringUtils.isNotBlank(applicationCode), ChannelApplicationReferrerEntity::getApplicationCode, applicationCode)
                                .eq(ChannelApplicationReferrerEntity::getReferrerLevel, 1)
                ).stream()
                .collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerCode, ChannelApplicationReferrerEntity::getReferrerName));
        Map<String, ChannelInfoEntity> channelMap = channelInfoService.list().stream()
                .collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, x -> x));
        Map<String, ChannelApplicationEntity> applicationMap = channelApplicationService.list().stream()
                .collect(Collectors.toMap(ChannelApplicationEntity::getApplicationCode, x -> x));
        Map<String, String> regionMap = Optional.ofNullable(DicCacheHelper.getSons("REFERRER_REGION")).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
        page.getRecords().forEach(x -> {
            x.setFilePath(DomainUtil.addOssDomainIfNotExist(x.getFilePath()));
            x.setReferrerRegion(regionMap.get(x.getReferrerRegion()));
            x.setReferrerSubregion(DicCacheHelper.getValue(x.getReferrerSubregion()));
            x.setReferrerParentName(referrerMap.get(x.getReferrerParentCode()));
            ChannelApplicationEntity channelApplicationEntity = applicationMap.get(x.getApplicationCode());
            if (channelApplicationEntity != null) {
                x.setApplicationComment(channelApplicationEntity.getApplicationComment());
            }
            ChannelInfoEntity channelInfoEntity = channelMap.get(x.getChannelCode());
            if (channelInfoEntity != null) {
                x.setChannelName(channelInfoEntity.getChannelName());
            }
            //特殊处理交接失败
            if (x.getHandoverStatus().equals(ReferrerHandoverStatusEnum.STATUS3.getCode()) || x.getHandoverStatus().equals(ReferrerHandoverStatusEnum.STATUS4.getCode())) {
                x.setHandoverStatus(ReferrerHandoverStatusEnum.STATUS2.getCode());
            }
            if (StringUtils.isNotBlank(x.getHandoverReferrerWno())) {
                List<ChannelApplicationReferrerEntity> list = this.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerWno, x.getHandoverReferrerWno()).list();
                if (CollectionUtils.isNotEmpty(list)) {
                    x.setHandoverReferrerWnoName(list.get(0).getReferrerName());
                }
            }
        });


        return new PageUtils<>(page);
    }

    @Override
    public ChannelApplicationReferrerEntity info(Integer id) {
        ChannelApplicationReferrerEntity entity = this.getById(id);
        if (StringUtils.isBlank(entity.getFilePath())) {
            String qrCode = createQrCode(
                    entity.getApplicationCode(),
                    entity.getReferrerCode(),
                    entity.getReferrerWno()
            );
            entity.setFilePath(qrCode);
            this.updateById(entity);
        }
        entity.setFilePath(DomainUtil.addOssDomainIfNotExist(entity.getFilePath()));
        return entity;
    }

    @Override
    public Map<String, Integer> getTotalAndEffective(String applicationCode) {
        HashMap<String, Integer> totalAndEffective = new HashMap<>(8);
        List<ChannelApplicationReferrerEntity> list = this.list(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getApplicationCode, applicationCode));
        int count = list.size();
        int effective = Math.toIntExact(
                list.stream()
                        .filter(x -> x.getEnabled().equals(StatusEnum.NORMAL.getCode()))
                        .count());
        totalAndEffective.put("total", count);
        totalAndEffective.put("effective", effective);
        return totalAndEffective;
    }

    @Override
    public boolean changeEnable(Integer id, Integer enabled, long revision) {
        if (StatusEnum.getNameByCode(enabled) == null) {
            return false;
        }
        ChannelApplicationReferrerEntity channelApplicationReferrerEntity = new ChannelApplicationReferrerEntity();
        channelApplicationReferrerEntity.setId(id);
        channelApplicationReferrerEntity.setEnabled(enabled);
        channelApplicationReferrerEntity.setRevision(revision);
        return this.updateById(channelApplicationReferrerEntity);
    }

    @Override
    public boolean sendMessage(Integer id) {
        ChannelApplicationReferrerEntity entity = this.getById(id);
        if (StringUtils.isBlank(entity.getReferrerMobile())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("手机号不存在，请设置手机号后再点击发送"));
        }
        SendMsgInput sendMsgInput = new SendMsgInput();

        sendMsgInput.setMobile(entity.getReferrerMobile());
        sendMsgInput.setSmsCode(SmsCodeEnum.REFERRAL_CODE_SUCCESS.getCodeType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", entity.getFilePath());
        sendMsgInput.setData(jsonObject);

        Result<String> smsResult = authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, sendMsgInput);
        return smsResult.isSuccess();
    }

    @Override
    public List<ChannelApplicationReferrerVo> getReferrerList(String applicationCode) {
        return this.list(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery()
                        .eq(StringUtils.isNotBlank(applicationCode), ChannelApplicationReferrerEntity::getApplicationCode, applicationCode)
                        .eq(ChannelApplicationReferrerEntity::getReferrerLevel, 1)
        ).stream().map(x -> {
            ChannelApplicationReferrerVo referrerVo = new ChannelApplicationReferrerVo();
            BeanUtils.copyProperties(x, referrerVo);
            return referrerVo;
        }).collect(Collectors.toList());
    }

    @Override
    public String createQrCode(String channelApplicationCode, String referrerCode, String referrerWno) {
        Result<String> wxaCode = customerClient.createWxaCodeFillText(appid, AdminPublicConstant.WX_QR_APPLICATION, channelApplicationCode, referrerCode, referrerWno);
        if (wxaCode.isSuccess()) {
            return DomainUtil.removeDomain(wxaCode.getData());
        }
        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("二维码生成失败，请重试"));
    }

    @Override
    public boolean saveOrUpdateEntity(ChannelApplicationReferrerEntity channelApplicationReferrerEntity) {
        Integer id = channelApplicationReferrerEntity.getId();
        // 获取对应应用及其渠道类型和编码
        Optional.ofNullable(redisService.hget(AdminCommonKeys.CHANNEL_APPLICATION, "application_info", channelApplicationReferrerEntity.getApplicationCode(), ChannelApplicationVo.class))
                .ifPresent(x -> {
                    channelApplicationReferrerEntity.setChannelClassification(x.getChannelClassification());
                    channelApplicationReferrerEntity.setChannelCode(x.getChannelCode());
                });
        if (id == null) {
            channelApplicationReferrerEntity.setReferrerCode(CommonUtils.createCode("R"));
        } else {
            ChannelApplicationReferrerEntity one = this.getById(id);
            channelApplicationReferrerEntity.setReferrerCode(one.getReferrerCode());
        }
        // 生成二维码
        String qrCode = this.createQrCode(
                channelApplicationReferrerEntity.getApplicationCode(),
                channelApplicationReferrerEntity.getReferrerCode(),
                channelApplicationReferrerEntity.getReferrerWno());
        channelApplicationReferrerEntity.setFilePath(qrCode);

        // 如果是推荐人则删除所属推荐人
        if (channelApplicationReferrerEntity.getReferrerLevel().equals(1)) {
            channelApplicationReferrerEntity.setReferrerParentCode("");
        } else {
            channelApplicationReferrerEntity.setReferrerWno("");
        }
        // 冗余渠道字段
        String branchCode = channelApplicationReferrerEntity.getBranchCode();
        channelApplicationReferrerEntity.setChannelBranchCode(
                StrUtil.concat(true,
                        channelApplicationReferrerEntity.getChannelCode(),
                        StringUtils.isNotBlank(branchCode) ? StrUtil.concat(true, "-", branchCode) : null)
        );

        return this.saveOrUpdate(channelApplicationReferrerEntity);
    }

    @Override
    public List<EpPolicyChannelInfoVo> getChannelRecommender(String referrerCodeOrName, boolean isNeedPermission) {
        // 获取权限标识
        List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        List<EpPolicyChannelInfoVo> referrerInfoVoList = new ArrayList<>();
        boolean isFreeChannel = channelBranchCodeList == null
                || channelBranchCodeList.stream().anyMatch(x -> StrUtil.startWith(x, "free"))
                || channelBranchCodeList.stream().anyMatch(x -> StrUtil.startWith(x, "gdyw"));
        if (isFreeChannel && (orgCodeList == null || !orgCodeList.isEmpty())) {
            //获取代理人信息
            List<AgentUserInfoEntity> agentUserInfoEntityList = agentUserInfoService.lambdaQuery()
                    .in(orgCodeList != null, AgentUserInfoEntity::getOrgCode, orgCodeList)
                    .and(StringUtils.isNotBlank(referrerCodeOrName), x -> x.like(AgentUserInfoEntity::getBusinessCode, referrerCodeOrName)
                            .or()
                            .like(AgentUserInfoEntity::getAgentName, referrerCodeOrName))
                    .in(AgentUserInfoEntity::getAgentStatus, AgentStatusEnum.INVALID.getCode(), AgentStatusEnum.VALID.getCode())
                    .last("limit 30")
                    .list();
            //构建机构信息map
            Map<String, OrgInfoEntity> orgInfoEntityMap = orgInfoService.list().stream()
                    .collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));
            //构建vo（代理人信息）
            agentUserInfoEntityList.forEach(x -> {
                EpPolicyChannelInfoVo referrerInfoVo = new EpPolicyChannelInfoVo();
                referrerInfoVo.setQuitStatus(x.getQuitStatus());
                referrerInfoVo.setReferrerName(x.getAgentName());
                referrerInfoVo.setReferrerWno(x.getBusinessCode());
                referrerInfoVo.setReferrerCode(x.getAgentCode());
                OrgInfoEntity orgInfo = orgInfoEntityMap.get(x.getOrgCode());
                if (orgInfo != null) {
                    referrerInfoVo.setBranchName(orgInfo.getOrgName());
                }
                referrerInfoVo.setChannelBranchCode(x.getOrgCode());
                //当代理人为保险专家时对应渠道为zhnx
                if (AgentTypeEnum.AREA_MANAGER.getKey().equals(x.getAgentType())
                        || AgentTypeEnum.INSURANCE_SERVICE_ADVISOR.getKey().equals(x.getAgentType())) {
                    referrerInfoVo.setChannelCode(ZHNX_CHANNEL_CODE);
                    referrerInfoVo.setChannelName(ZHNX_CHANNEL_NAME);
                } else {
                    referrerInfoVo.setChannelName("小鲸向海");
                    referrerInfoVo.setChannelCode("gdyw");
                }
                referrerInfoVo.setReferrerType(1);
                referrerInfoVoList.add(referrerInfoVo);
            });
        }

        if (channelBranchCodeList == null || !channelBranchCodeList.isEmpty()) {
            //获取推荐人信息
            List<ChannelApplicationReferrerEntity> channelApplicationReferrerEntityList = this.lambdaQuery()
                    .in(channelBranchCodeList != null, ChannelApplicationReferrerEntity::getChannelBranchCode, channelBranchCodeList)
                    .and(StringUtils.isNotBlank(referrerCodeOrName),
                            x -> x.like(ChannelApplicationReferrerEntity::getReferrerWno, referrerCodeOrName)
                                    .or()
                                    .like(ChannelApplicationReferrerEntity::getReferrerName, referrerCodeOrName)
                    )
                    .last("limit 70")
                    .list();
            //构建销售渠道map
            Map<String, ChannelInfoEntity> channelMap = channelInfoService.list().stream()
                    .collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, x -> x));
            //构建渠道分支map
            Map<String, ChannelBranchInfoEntity> channelBranchInfoMap = channelBranchInfoService.list().stream()
                    .collect(Collectors.toMap(ChannelBranchInfoEntity::getBranchCode, x -> x));
            //构建vo（推荐人信息）
            channelApplicationReferrerEntityList.forEach(x -> {
                EpPolicyChannelInfoVo referrerInfoVo = new EpPolicyChannelInfoVo();
                BeanUtils.copyProperties(x, referrerInfoVo);
                referrerInfoVo.setQuitStatus(x.getReferrerServiceStatus());
                referrerInfoVo.setReferrerWno(x.getReferrerWno());
                referrerInfoVo.setReferrerType(0);
                referrerInfoVo.setChannelCode(x.getChannelCode());
                ChannelInfoEntity channelInfoEntity = channelMap.get(x.getChannelCode());
                if (channelInfoEntity != null) {
                    //获取渠道名称
                    referrerInfoVo.setChannelName(channelInfoEntity.getChannelName());
                }
                ChannelBranchInfoEntity channelBranchInfoEntity = channelBranchInfoMap.get(x.getBranchCode());
                referrerInfoVo.setChannelBranchCode(x.getBranchCode());
                if (channelBranchInfoEntity != null) {
                    //获取分支名称
                    referrerInfoVo.setBranchName(channelBranchInfoEntity.getBranchName());
                }
                referrerInfoVoList.add(referrerInfoVo);
            });
        }
        return referrerInfoVoList;
    }

    @Override
    public List<ChannelApplicationReferrerVo> getReferrerByBranchCodeList(String branchCode) {
        List<ChannelApplicationReferrerEntity> list = this.lambdaQuery().eq(StringUtils.isNotBlank(branchCode), ChannelApplicationReferrerEntity::getBranchCode, branchCode).last("limit 1000").list();
        ArrayList<ChannelApplicationReferrerVo> objects = new ArrayList<>();
        list.forEach(x -> {
            ChannelApplicationReferrerVo channelApplicationReferrerVo = new ChannelApplicationReferrerVo();
            BeanUtils.copyProperties(x, channelApplicationReferrerVo);
            objects.add(channelApplicationReferrerVo);
        });
        return objects;
    }

    @Override
    public List<FastChannelApplicationReferrer> listFastEntity(List<String> referrerCodeList) {
        return baseMapper.listFastEntity(referrerCodeList);
    }

    @Override
    public List<FastChannelApplicationReferrer> listByJobNumber(List<String> referrerCodeList) {
        return baseMapper.listByJobNumber(referrerCodeList);
    }

    @Override
    public FastChannelApplicationReferrer queryOne(String referrerCode) {
        return baseMapper.queryOne(referrerCode);
    }

    @Override
    public ChannelApplicationReferrerEntity getByReferrerCode(String referrerCode) {
        return baseMapper.selectOne(
                Wrappers.<ChannelApplicationReferrerEntity>lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, referrerCode)
        );
    }
}
