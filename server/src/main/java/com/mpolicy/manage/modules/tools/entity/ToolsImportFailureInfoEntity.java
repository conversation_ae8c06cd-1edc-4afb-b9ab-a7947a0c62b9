package com.mpolicy.manage.modules.tools.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-05-05 18:37
 * @description: 导入失败信息记录entity
 */
@TableName("tools_import_failure_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ToolsImportFailureInfoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     *操作标记
     */
    private String operationSign;
    /**
     * 失败信息
     */
    private String failureInformation;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
