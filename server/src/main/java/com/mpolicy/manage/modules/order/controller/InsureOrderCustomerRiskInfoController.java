package com.mpolicy.manage.modules.order.controller;

import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import com.mpolicy.manage.modules.order.enums.RatingLevelEnum;
import com.mpolicy.manage.modules.order.service.InsureOrderCustomerRiskInfoService;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoExportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoQueryVO;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 订单客户风险控制器
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@RestController
@Api(tags = "订单风险客户信息")
@RequestMapping("/orderInfo/customer/risk")
public class InsureOrderCustomerRiskInfoController {

    @Resource
    private InsureOrderCustomerRiskInfoService insureOrderCustomerRiskInfoService;

    /**
     * 风险等级列表
     */
    @ApiOperation(value = "风险等级列表", notes = "风险等级列表")
    @GetMapping("/ratingLevel/list")
    public Result<List<RatingLevelEnum>> ratingLevelList() {
        return Result.success(Arrays.asList(RatingLevelEnum.values()));
    }

    /**
     * 新增订单风险客户信息
     */
    @ApiOperation(value = "新增订单风险客户信息", notes = "新增订单风险客户信息")
    @PostMapping("")
    @RequiresPermissions(value = {"customer:risk:all"})
    public Result<Object> save(@RequestBody InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoEntity) {
        insureOrderCustomerRiskInfoService.save(insureOrderCustomerRiskInfoEntity);
        return Result.success();
    }


    /**
     * 编辑订单风险客户信息
     */
    @ApiOperation(value = "编辑订单风险客户信息", notes = "编辑订单风险客户信息")
    @PostMapping("/update")
    @RequiresPermissions(value = {"customer:risk:all"})
    public Result<Object> update(@RequestBody InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoEntity) {
        insureOrderCustomerRiskInfoService.updateById(insureOrderCustomerRiskInfoEntity);
        return Result.success();
    }


    /**
     * 删除订单风险客户信息
     */
    @ApiOperation(value = "删除订单风险客户信息", notes = "删除订单风险客户信息")
    @PostMapping("/{id}")
    @RequiresPermissions( value = {"customer:risk:all"})
    public Result<Object> delete(@PathVariable Integer id) {
        insureOrderCustomerRiskInfoService.removeById(id);
        return Result.success();
    }

    /**
     * 查询订单风险客户信息
     */
    @ApiOperation(value = "查询订单风险客户信息", notes = "查询订单风险客户信息")
    @GetMapping("/")
    @RequiresPermissions(value = {"customer:risk:all"})
    public Result<IPage<InsureOrderCustomerRiskInfoVO>> query(InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO) {
        IPage<InsureOrderCustomerRiskInfoVO> queryVOPage = insureOrderCustomerRiskInfoService.getByQueryVO(insureOrderCustomerRiskInfoQueryVO);
        return Result.success(queryVOPage);
    }


    /**
     * 批量导入订单风险客户信息
     */
    @ApiOperation(value = "批量导入订单风险客户信息", notes = "批量导入订单风险客户信息")
    @GetMapping("/import/{fileCode}")
    @RequiresPermissions(value = {"customer:risk:all"})
    public Result<Object> importData(@PathVariable String fileCode) {
        String message = insureOrderCustomerRiskInfoService.importInsureOrderCustomerRiskInfoData(fileCode);
        Result<Object> result = Result.success();
        result.setMsg(message);
        return result;
    }

    /**
     *
     *
     * 批量导出订单风险客户信息
     *
     *
     *
     * @param response
     *
     *
     *
     * @return
     */
    @GetMapping("/export")
    @RequiresPermissions(value = {"customer:risk:all"})
    @ApiOperation(value = "批量导出订单风险客户信息", notes = "批量导出订单风险客户信息",produces = "application/octet-stream")
    public Result export(HttpServletResponse response,InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO) {
        List<InsureOrderCustomerRiskInfoExportExcel> insureOrderCustomerRiskInfoVOList = insureOrderCustomerRiskInfoService.getExportByQueryVO(insureOrderCustomerRiskInfoQueryVO);
        ExcelUtil.writeExcel(response, insureOrderCustomerRiskInfoVOList, URLUtil.encode("订单风险客户导出", StandardCharsets.UTF_8), "sheet1", new InsureOrderCustomerRiskInfoExportExcel());
        return Result.success();
    }

}
