package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 白名单信息列表
 *
 * <AUTHOR>
 * @date 2023-5-17 13:42
 */
@Data
@ApiModel(value = "白名单信息列表")
public class InsureUserWhiteRosterOut {

    @ApiModelProperty(value = "id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "身份证号", example = "123456789098765")
    private String cardNo;

    @ApiModelProperty(value = "白名单类型 0：实名认证 1：人脸识别", example = "1")
    private String rosterTypeDesc;

    @ApiModelProperty(value = "有效开始时间", example = "2023-05-17 13:42:12")
    private String validDate;

    @ApiModelProperty(value = "是否启用 0:停用;1:启用", example = "1")
    private Integer isStatus;

    @ApiModelProperty(value = "操作人", example = "张三")
    private String updateUser;

    @ApiModelProperty(value = "操作时间", example = "2023-05-17 13:42:12")
    private String updateTime;
}
