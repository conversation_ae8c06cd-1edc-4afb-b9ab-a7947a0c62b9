package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelDistributionInfoEntity;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;

import java.util.List;
import java.util.Map;

/**
 * 分销渠道信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-02-06 15:55:51
 */
public interface ChannelDistributionInfoService extends IService<ChannelDistributionInfoEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    /**
     * 分销渠道新增or修改
     * @param channelDistributionInfo
     * @return
     */
    boolean saveOrUpdateEntity(ChannelDistributionInfoEntity channelDistributionInfo);

    /**
     * 修改分销渠道启用状态
     * @param code
     * @param enabled
     * @return
     */
    boolean changeEnable(String code, Integer enabled);

    /**
     * 获取所有渠道列表
     * @param isNeedPermission 是否过滤用户权限
     */
    List<ChannelInfoVo> getChannelList(boolean isNeedPermission);

    /**
     * 获取渠道分销树
     * @return
     */
    List<TreeListOut> findTreeList();
}

