package com.mpolicy.manage.modules.agent.vo.questionnaire;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AgentQuestionnaireVo {

    /**
     * 问卷标题
     */
    private String title;

    /**
     * 开始采集时间
     */
    private String investigateStartTime;
    /**
     * 结束采集时间
     */
    private String investigateEndTime;


    private List<AgentQuestionnaireQuestionVo> questionList;
}
