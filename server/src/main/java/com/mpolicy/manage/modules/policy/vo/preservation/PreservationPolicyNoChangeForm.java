package com.mpolicy.manage.modules.policy.vo.preservation;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2023/6/19 16:57
 * @Version 1.0
 */
@Data
public class PreservationPolicyNoChangeForm{

    /**
     * 批改前保单号
     */
    @NotBlank(message = "原保单号不能为空")
    private String sourcePolicyNo;

    /**
     * 保全保单号
     */
    @NotBlank(message = "修改后保单号不能为空")
    private String preservationPolicyNo;

}
