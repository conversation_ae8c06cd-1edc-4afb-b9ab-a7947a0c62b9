package com.mpolicy.manage.modules.policy.vo.trust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: PolicyUserRecordRiskAreaOut
 * Description: 保单托管险种地区信息
 * date: 2023/11/9 15:57
 *
 * <AUTHOR>
 */
@ApiModel(value = "保单托管险种地区信息")
@Data
public class PolicyUserRecordRiskAreaOut {

    /**
     * 社保险种配置code
     */
    @ApiModelProperty(value = "社保险种配置code")
    private String purrcCode;
    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provincesCode;
    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provincesName;
    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;
    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String areaCode;
    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String areaName;
}
