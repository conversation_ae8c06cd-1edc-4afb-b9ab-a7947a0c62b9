package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureUserWhiteRosterEntity;
import com.mpolicy.manage.modules.insure.vo.InsureUserWhiteRosterVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 投保实名认证人脸识别白名单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-17 14:36:49
 */
public interface InsureUserWhiteRosterService extends IService<InsureUserWhiteRosterEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 白名单详细信息
     *
     * @param id:
     * @return : java.lang.Object
     * <AUTHOR>
     * @date 2023/5/17 18:14
     */
    InsureUserWhiteRosterVO getWhiteRosterInfo(Integer id);

    /**
     * 白名单添加修改
     *
     * @param insureUserWhiteRosterVO: 白名单详细信息
     * @return : void
     * <AUTHOR>
     * @date 2023/5/18 10:04
     */
    void whiteRosterSaveOrUpdate(InsureUserWhiteRosterVO insureUserWhiteRosterVO);

    /**
     * 白名单人脸识别导入
     *
     * @param file:
     * @return : void
     * <AUTHOR>
     * @date 2023/5/18 10:52
     */
    List<String> listUpload(String fileCode);

    /**
     * 白名单状态变更
     *
     * @param id:       编号
     * @param isStatus: 状态
     * @return : void
     * <AUTHOR>
     * @date 2023/5/18 13:48
     */
    void updateStatus(Integer id, Integer isStatus);

    /**
     * 白名单删除
     *
     * @param id:
     * @return : void
     * <AUTHOR>
     * @date 2023/5/18 16:03
     */
    void delete(Integer id);
}

