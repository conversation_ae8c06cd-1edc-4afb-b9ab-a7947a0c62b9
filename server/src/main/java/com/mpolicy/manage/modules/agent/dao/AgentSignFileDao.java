package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFilePageList;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 代理人签约文件信息表
 * 
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileDao extends BaseMapper<AgentSignFileEntity> {

    IPage<AgentSignFilePageList> filePageList(@Param("page") IPage page, @Param("input") Map<String, Object> input);
}
