package com.mpolicy.manage.modules.order.util;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.order.enums.InsureOrderCustomerRiskDictionaryEnum;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 字段转换工具
 *
 *
 * @create 2024/11/22
 * @since 1.0.0
 */
@Slf4j
public class InsureOrderCustomerRiskDictionaryUtil {


    public static String getKeyByValue(InsureOrderCustomerRiskDictionaryEnum insureOrderCustomerRiskDictionaryEnum,String value){
        if (Objects.isNull(insureOrderCustomerRiskDictionaryEnum)){
            log.warn("风控客户枚举没能为空");
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR);
        }

        String parentKey = insureOrderCustomerRiskDictionaryEnum.getParentKey();
        List<DicCacheHelper.DicEntity> dicEntityList = DicCacheHelper.getSons(parentKey);
        for (DicCacheHelper.DicEntity dicEntity : dicEntityList) {
            String dicEntityValue = dicEntity.getValue();
            if (dicEntityValue.equals(value)){
                return dicEntity.getKey();
            }
        }

        return null;
    }
}
