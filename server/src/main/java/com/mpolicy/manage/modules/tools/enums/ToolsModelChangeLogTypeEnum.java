package com.mpolicy.manage.modules.tools.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-06-12 11:15
 * @description:
 */
@Getter
public enum ToolsModelChangeLogTypeEnum {
    ESCROW_ORDER_ROLLBACK("托管单回退", "100", "托管单回退，记录回退前的托管单信息"),
    POLICY_ADMIN_UP("保单管理员变更", "120", "保单管理员变更"),
    FOUR_UP("四级分销信息修改","200","四级分销信息修改"),
    VILLAGE_PROMOTION("整村推进活动变更","300","整村推进活动变更"),
    FOUR_TO_ORDINARY("四级=》普通","210","四级=》普通"),
    ;

    /**
     * 名称
     */
    private String name;
    /**
     * 值
     */
    @EnumValue
    private String code;
    /**
     * 描述
     */
    private String desc;

    ToolsModelChangeLogTypeEnum(String name, String code, String desc) {
        this.name = name;
        this.code = code;
        this.desc = desc;
    }
}
