package com.mpolicy.manage.modules.policy.vo.lost;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:47 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PolicyLostRegisterExcelVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 保单号
     */
    @ExcelProperty(value = "序号")
    private Integer index;
    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间",format = "yyyy-MM-dd")
    private Date registerTime;
    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 被保人姓名
     */
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件号
     */
    @ExcelProperty(value = "被保人身份证号")
    private String insuredIdNumber;
    /**
     * 推荐人姓名
     */
    @ExcelProperty(value = "推荐人姓名")
    private String recommendName;
    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String recommendId;
    /**
     * 处理状态 0:待处理 1:处理成功 2:处理失败
     */
    @ExcelProperty(value = "状态")
    private String handleStatusName;
    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    private String handleResultContent;
    /**
     * 处理成功备注
     */
    @ExcelProperty(value = "处理成功备注")
    private String handleSuccessResult;
    /**
     * 修改时间
     */
    @ExcelProperty(value = "最新处理时间")
    private Date updateTime;

    /**
     * 修改人
     */
    @ExcelProperty(value = "处理人")
    private String updateUser;
    /**
     * 登记人姓名
     */
    @ExcelProperty(value = "登记人")
    private String registerUserName;
    /**
     * 登记人工号
     */
    @ExcelProperty(value = "登记人工号")
    private String registerUserId;

    /**
     * 登记人所属区域
     */
    @ExcelProperty(value = "所属区域")
    private String registerUserRegionName;

    /**
     * 登记人所属区域
     */
    @ExcelProperty(value = "所属分支")
    private String referrerOgrName;
}
