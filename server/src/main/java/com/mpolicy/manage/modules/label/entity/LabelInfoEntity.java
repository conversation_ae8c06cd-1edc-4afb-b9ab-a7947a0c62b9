package com.mpolicy.manage.modules.label.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
@TableName("label_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Integer id;
	/**
	 * 标签编码
	 */
	@TableId(value = "label_code",type = IdType.INPUT)
	private String labelCode;
	/**
	 * 标签名称
	 */
	private String labelName;
	/**
	 * 标签分组(字典)
	 */
	private String labelGroup;
	/**
	 * 标签库编码
	 */
	private String libraryCode;
	/**
	 * 保障类型(字典)
	 */
	private String portfolioType;
	/**
	 * 启用状态 1:启用;0:关闭
	 */
	private Integer enabled;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
