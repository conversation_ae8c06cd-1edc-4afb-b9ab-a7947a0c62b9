package com.mpolicy.manage.modules.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.service.SlsQueryLogService;
import com.mpolicy.manage.modules.agent.util.AliLogProperty;
import com.mpolicy.manage.modules.agent.vo.SlsQueryLogInputVo;
import com.mpolicy.manage.modules.agent.vo.SlsQueryLogOutputVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("slsQueryLogService")
public class SlsQueryLogServiceImpl implements SlsQueryLogService {

    @Autowired
    private AliLogProperty aliLogProperty;
    @Override
    public SlsQueryLogOutputVo queryLog(SlsQueryLogInputVo inputVo) {
        if(StringUtils.isBlank(inputVo.getQuery())){
            log.warn("日志查询query语句必填 inputVo= {}",JSON.toJSONString(inputVo));
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("日志查询query语句必填"));
        }
        SlsQueryLogOutputVo slsQueryLogOutputVo = new SlsQueryLogOutputVo();
        JSONArray data = new JSONArray();
        slsQueryLogOutputVo.setData(data);
        // 本示例从环境变量中获取AccessKey ID和AccessKey Secret。
        String accessId = aliLogProperty.getAccessId();
        String accessKey = aliLogProperty.getAccessKey();
        // 输入Project名称。
        String project = inputVo.getProject();
        // 设置日志服务的服务接入点。此处以杭州为例，其它地域请根据实际情况填写。
        String host = aliLogProperty.getHost();
        // 输入Logstore名称。
        String logStore = inputVo.getLogStore();

        // 创建日志服务Client。
        Client client = new Client(host, accessId, accessKey);

        // 在指定的Logstore内执行查询。
        try {
            //开始时间戳秒
            int from = Long.valueOf(inputVo.getStartTime().getTime()/1000).intValue();
            //结束时间戳秒
            int to = Long.valueOf(inputVo.getEndTime().getTime()/1000).intValue();
            int size = inputVo.getSize();
            int current = inputVo.getCurrent();
            log.info("queryLog query= {}",inputVo.getQuery());
            // 本示例中，query参数用于设置查询语句；line参数用于控制返回日志条数，取值为3，最大值为100。
            GetLogsResponse logsResponse = client.GetLogs(project, logStore, from, to, "", inputVo.getQuery(), size, current,inputVo.getReverse());
            log.info("queryLog response= {}",JSON.toJSONString(logsResponse));
            slsQueryLogOutputVo.setTotalCount(logsResponse.getLogs().size());
            JSONObject jsonObject = null;
            for (QueriedLog log : logsResponse.getLogs()) {
                LogItem item = log.GetLogItem();
                jsonObject = JSON.parseObject(item.ToJsonString());
                data.add(jsonObject);
            }

        } catch (LogException e) {
            log.warn("查询阿里日志异常 inputVo= {}",JSON.toJSONString(inputVo),e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("查询阿里日志异常"));
        }
        return slsQueryLogOutputVo;
    }
}
