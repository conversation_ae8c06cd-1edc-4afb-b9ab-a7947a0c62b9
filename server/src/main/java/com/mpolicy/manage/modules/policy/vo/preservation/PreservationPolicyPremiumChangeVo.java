package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保单保费变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单-保费变更详情")
public class PreservationPolicyPremiumChangeVo implements Serializable {

    @ApiModelProperty("变更前保费")
    @NotNull(message = "变更前保单保费不能为空")
    private BigDecimal beforePremium;

    @ApiModelProperty("变更后保费")
    @NotNull(message = "变更后保单保费不能为空")
    private  BigDecimal correctedPremium;

    @ApiModelProperty("是否由系统生成明细保费:0=系统生成，1=用户录入")
    private Integer inputType;

    @ApiModelProperty("被保人保费变更明细")
    List<PreservationInsuredPremiumChangeForm> insuredPremiumChangeList;
}
