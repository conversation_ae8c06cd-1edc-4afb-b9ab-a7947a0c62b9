package com.mpolicy.manage.modules.contract.vo;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合约附件信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-16 11:03:58
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractEnclosureSaveInput implements Serializable {
	private static final long serialVersionUID = 1L;

			/**
	 * 协议编号
	 */
	private String contractCode;
			/**
	 * 文件类型 0:协议  1:佣金费率表
	 */
	private Integer enclosureType;
			/**
	 * 文件编码
	 */
	private String fileCode;
			/**
	 * 文件类型
	 */
	private String fileType;
			/**
	 * 文件名称
	 */
	private String fileName;
			/**
	 * 文件后缀
	 */
	private String fileExt;
			/**
	 * 文件大小
	 */
	private Integer fileSize;
			/**
	 * oss存储路径
	 */
	private String filePath;
			/**
	 * 外部访问路径
	 */
	private String domainPath;
						
}
