package com.mpolicy.manage.modules.agent.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/1 11:11
 */
@Data
public class AgentInfoVo {
    /**
     * 代理人编号
     */
    @ApiModelProperty(value = "代理人编号")
    private String agentCode;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String agentName;

    @ApiModelProperty(value = "业务编码", required = true, example = "U12346")
    private String businessCode;
}
