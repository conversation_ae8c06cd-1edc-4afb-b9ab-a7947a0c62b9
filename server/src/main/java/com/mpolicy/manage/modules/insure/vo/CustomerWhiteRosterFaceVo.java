package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.excel.annotation.ExcelColumnNum;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.protocol.utils.ValidatePropertyUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/2 17:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerWhiteRosterFaceVo extends BaseRowModel {
    /**
     * 姓名
     */
    @ExcelColumnNum(0)
    private String realName;
    /**
     * 身份证号
     */
    @ExcelColumnNum(1)
    private String cardNo;
    /**
     * 手机号
     */
    @ExcelColumnNum(2)
    private String mobile;
    /**
     * 有效开始时间
     */
    @ExcelColumnNum(3)
    private String startDate;

    /**
     * 有效结束时间
     */
    @ExcelColumnNum(4)
    private String endDate;
    /**
     * 备注说明
     */
    @ExcelColumnNum(5)
    private String remark;

    public Date convertStartDate() {
        if (ValidatePropertyUtil.isExcelDate(startDate)) {
            return DateUtils.formatExcelDate(startDate);
        }
        return DateUtils.stringToDate(startDate, "yyyy/MM/dd");
    }

    public Date convertEndDate() {
        if (ValidatePropertyUtil.isExcelDate(endDate)) {
            return DateUtils.formatExcelDate(endDate);
        }
        return DateUtils.stringToDate(endDate, "yyyy/MM/dd");
    }
}
