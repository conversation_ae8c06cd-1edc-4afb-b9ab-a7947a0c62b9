package com.mpolicy.manage.modules.policy.vo.policy;

import lombok.Data;

import java.io.Serializable;
@Data
public class PolicyBaseInfoOut implements Serializable {
    private static final long serialVersionUID = -5144307260738757386L;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 投保人姓名
     */
    private String applicantName;
    /**
     * 投保人证件号
     */
    private String applicantIdCard;
    /**
     * 投保人手机号
     */
    private String applicantMobile;
    /**
     * 被保人姓名集合
     */
    private String insuredsName;
}
