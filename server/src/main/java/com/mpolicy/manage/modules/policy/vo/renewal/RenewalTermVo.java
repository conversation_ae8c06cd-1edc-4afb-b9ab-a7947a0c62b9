package com.mpolicy.manage.modules.policy.vo.renewal;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RenewalTermVo {

    @ApiModelProperty("合同编号")
    private String contractCode;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("产品类型编码")
    private String policyProductType;

    @ApiModelProperty("续期状态：0=未续期；1=已续期")
    private String status;

    @ApiModelProperty("业务类型：0=网销，1=线下")
    private Integer salesType;

    @ApiModelProperty("产品名称")
    private String mainProductName;

    @ApiModelProperty("保险公司")
    private String companyName;

    @ApiModelProperty("保险期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保险期间")
    private String insuredPeriod;

    @ApiModelProperty("缴费方式")
    private String periodType;

    @ApiModelProperty("保单年度")
    private String policyPeriodYear;

    @ApiModelProperty("缴费期次")
    private Integer period;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty("应收日期")
    private String duePaymentTime;

    @ApiModelProperty("应收金额")
    private BigDecimal duePaymentAmount;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty("实收日期")
    private String paymentTime;

    @ApiModelProperty("实收金额")
    private String paymentAmount;

    @ApiModelProperty("保单状态")
    private String policyStatus;

    @ApiModelProperty("宽限期剩余天数")
    private Integer graceDay;

    @ApiModelProperty("投保人")
    private String applicantName;

    @ApiModelProperty("被保人")
    private String insuredName;

    @ApiModelProperty("代理人")
    private String agentName;

    @ApiModelProperty("推荐人")
    private String referrerName;

    @ApiModelProperty("渠道推荐人")
    private String policyReferrerName;

    @ApiModelProperty("渠道分支")
    private String channelBranchName;

    @ApiModelProperty("销售渠道")
    private String channelName;

    @ApiModelProperty("操作人")
    private String operator;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("操作时间")
    private Date createTime;

}
