package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.ChannelApplicationReferrerTransferCustomerDao;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerTransferCustomerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerTransferCustomerService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("channelApplicationReferrerTransferCustomerService")
public class ChannelApplicationReferrerTransferCustomerServiceImpl extends ServiceImpl<ChannelApplicationReferrerTransferCustomerDao, ChannelApplicationReferrerTransferCustomerEntity> implements ChannelApplicationReferrerTransferCustomerService {




}
