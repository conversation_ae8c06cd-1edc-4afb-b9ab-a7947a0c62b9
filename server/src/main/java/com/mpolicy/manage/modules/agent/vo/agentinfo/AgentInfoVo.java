package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentInfoVo implements Serializable {
    private static final long serialVersionUID = -5488206945138126181L;

    @ApiModelProperty("代理人编码")
    private String agentCode;

    @ApiModelProperty("附件列表")
    private List<AgentAccessoryVo> accessoryList;

    @ApiModelProperty(value = "证件信息列表")
    private List<AgentCertificateVo> certificateList;

    @ApiModelProperty(value = "工作室信息")
    private AgentStudioIpVo agentStudioIp;

    @ApiModelProperty(value = "代理人基本信息", required = true)
    private AgentUserInfoVo agentUserInfo;

    @ApiModelProperty(value = "代理人扩展信息", required = true)
    private AgentExtendVo agentExtend;

    @ApiModelProperty(value = "代理人线上入职信息")
    private AgentOnlineFileInfoVo agentOnlineFileInfo;
}
