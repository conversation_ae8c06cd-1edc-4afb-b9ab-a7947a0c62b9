package com.mpolicy.manage.modules.helper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.helper.dao.TmpPolicySuperviseGroupDao;
import com.mpolicy.manage.modules.helper.entity.TmpPolicySuperviseGroupEntity;
import com.mpolicy.manage.modules.helper.service.TmpPolicySuperviseGroupService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service("tmpPolicySuperviseGroupService")
public class TmpPolicySuperviseGroupServiceImpl extends ServiceImpl<TmpPolicySuperviseGroupDao, TmpPolicySuperviseGroupEntity> implements TmpPolicySuperviseGroupService {

    @Override
    public List<TmpPolicySuperviseGroupEntity> policyGroupList(Map<String, Object> params) {
        Integer betweenSize = RequestUtils.objectValueToInteger(params, "betweenSize");
        Integer limitSize = RequestUtils.objectValueToInteger(params, "limitSize");
        return lambdaQuery().between(TmpPolicySuperviseGroupEntity::getId,betweenSize,limitSize).list();
    }

    @Override
    public void updateList(List<TmpPolicySuperviseGroupEntity> updateList) {
        baseMapper.updateBatch(updateList);
    }
}
