package com.mpolicy.manage.modules.agent.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.web.common.validator.group.AddGroup;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.entity.AgentExtendEntity;
import com.mpolicy.manage.modules.agent.service.AgentExtendService;
import com.mpolicy.common.result.Result;


/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
@RestController
@RequestMapping("sys/agentextend")
@Api(tags = "人员扩展信息")
public class AgentExtendController {

    @Autowired
    private AgentExtendService agentExtendService;

    /**
     * 信息
     */
    @GetMapping("/info/{agentCode}")
    public Result<AgentExtendEntity> info(@PathVariable("agentCode") String agentCode) {
        AgentExtendEntity agentExtend = agentExtendService.getOne(
                Wrappers.<AgentExtendEntity>lambdaQuery()
                        .eq(AgentExtendEntity::getAgentCode, agentCode)
        );

        return Result.success(agentExtend);
    }

    /**
     * 保存经纪人人员扩展信息
     */
    // @ApiOperation(value = "保存经纪人人员扩展信息", notes = "保存经纪人人员扩展信息")
    @PostMapping("/save")
    public Result saveEntity(
            @ApiParam(value = "要保存人员扩展信息的对象", required = true)
            @Validated(AddGroup.class) @RequestBody AgentExtendEntity agentExtendEntity) {
        boolean save = agentExtendService.save(agentExtendEntity);
        if (save) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 修改经纪人人员扩展信息
     */
    // @ApiOperation(value = "修改经纪人人员扩展信息")
    @PostMapping("/update")
    public Result update(
            @ApiParam(value = "修改经纪人人员扩展信息的对象", required = true)
            @RequestBody AgentExtendEntity agentExtendEntity) {
        boolean update = agentExtendService.update(agentExtendEntity,
                Wrappers.<AgentExtendEntity>lambdaQuery()
                        .eq(AgentExtendEntity::getAgentCode, agentExtendEntity.getAgentCode())
        );
        if (update) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }
}
