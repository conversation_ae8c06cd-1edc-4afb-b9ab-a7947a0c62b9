package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UpdateQuitStatusVo implements Serializable {
    private static final long serialVersionUID = 1448511948135380164L;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "新绑定代理人编码")
    private String newAgentCode;

    @ApiModelProperty(value = "离职日期")
    private String quitTime;

    @ApiModelProperty(value = "离职状态")
    private Integer quitStatus;

    @ApiModelProperty(value = "是否发送清退邮件0:不发送 1:发送")
    private Integer isSendMail;
}
