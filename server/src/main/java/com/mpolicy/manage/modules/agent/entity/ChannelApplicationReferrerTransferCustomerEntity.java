package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 推荐人客户移交记录
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
@TableName("channel_application_referrer_transfer_customer")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelApplicationReferrerTransferCustomerEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    @ApiModelProperty(value = "id 更新时必传")
    private Integer id;
    /**
     * 日志id
     */
    private Integer logId;

    /**
     * 编码
     */
    private String customerCode;

    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁 更新时必传")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

}
