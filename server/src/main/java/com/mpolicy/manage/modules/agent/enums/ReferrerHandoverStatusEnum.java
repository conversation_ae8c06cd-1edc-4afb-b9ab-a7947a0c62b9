package com.mpolicy.manage.modules.agent.enums;

import lombok.Getter;

/**
 * @Description 离职客户经理交接状态
 * @return
 * @Date 2023/10/11 17:10
 * <AUTHOR>
 **/
@Getter
public enum ReferrerHandoverStatusEnum {

    STATUS0(0,"未交接"),
    STATUS1(1,"已交接"),
    STATUS2(2,"自动交接失败"),
    STATUS3(3,"交接失败-无交接人"),
    STATUS4(4,"交接失败-无交接人-清空客户经理身份"),
            ;
    private Integer code;

    private String desc;

    ReferrerHandoverStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ReferrerHandoverStatusEnum matchSearchCode(Integer code) {
        for (ReferrerHandoverStatusEnum searchEnum : ReferrerHandoverStatusEnum.values()) {
            if (searchEnum.code.equals(code)) {
                return searchEnum;
            }
        }
        return null;
    }
}
