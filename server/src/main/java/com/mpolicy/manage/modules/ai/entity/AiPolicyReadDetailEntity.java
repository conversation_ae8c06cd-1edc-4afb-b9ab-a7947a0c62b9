package com.mpolicy.manage.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mpolicy.manage.modules.ai.validation.Review;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

@TableName("ai_policy_read_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiPolicyReadDetailEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 识别ID
     */
    @ApiModelProperty("主表 ID")
    private Long aiReadId;

    /**
     * 保单号
     */
    @ApiModelProperty("保单号")
    private String policyCode;

    /**
     * 保险公司编码
     */
    @ApiModelProperty("保险公司编码")
    private String insuranceCompanyCode;

    /**
     * 计划编码
     */
    @ApiModelProperty("计划编码")
    private String planCode;

    /**
     * 保费
     */
    @Pattern(regexp = "^-?\\d+(\\.\\d+)?$", message = "保费金额格式不对，请重新核对",groups = {Review.class})
    @NotBlank(message = "保费不能为空", groups = {Review.class})
    @ApiModelProperty("保费")
    private String premium;

    /**
     * 总保费
     */
    @Pattern(regexp = "^-?\\d+(\\.\\d+)?$", message = "总保费金额格式不对，请重新核对", groups = {Review.class})
    @NotBlank(message = "总保费不能为空", groups = {Review.class})
    @ApiModelProperty("总保费")
    private String totalPremium;

    /**
     * 代理人业务编码
     */
    @ApiModelProperty("代理人业务编码")
    private String agentBusinessCode;

    /**
     * 代理人姓名
     */
    @ApiModelProperty("代理人姓名")
    private String agentName;

    /**
     * 渠道推荐人工号
     */
    @ApiModelProperty("渠道推荐人工号D")
    private String manageWno;

    /**
     * 渠道推荐人姓名
     */
    @ApiModelProperty("渠道推荐人姓名")
    private String referrerName;

    /**
     * 销售渠道编码
     */
    @ApiModelProperty("主表 ID")
    private String salesChannelCode;

    /**
     * 承保日期
     */
    @ApiModelProperty("承保日期")
    private String underwritingDate;

    /**
     * 生效日期
     */
    @ApiModelProperty("生效日期")
    private String effectiveDate;

    /**
     * 终止日期
     */
    @ApiModelProperty("终止日期")
    private String invalidDate;

    /**
     * 单位姓名
     */
    @ApiModelProperty("单位姓名")
    private String holderName;

    /**
     * 证件类型
     */
    @ApiModelProperty("证件类型")
    private String holderCertiCode;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String holderCertiNo;

    /**
     * 单位地址
     */
    @ApiModelProperty("单位地址")
    private String holderAddress;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 被保人是投保人的
     */
    @ApiModelProperty("被保人是投保人的")
    private String insuredOwnerRela;

    /**
     * 被保人姓名
     */
    @ApiModelProperty("被保人姓名")
    private String insuredName;

    /**
     * 被保人证件类型
     */
    @ApiModelProperty("被保人证件类型")
    private String insuredCertiCode;

    /**
     * 被保人证件号码
     */
    @ApiModelProperty("被保人证件号码")
    private String insuredCertiNo;

    /**
     * 被保人性别
     */
    @ApiModelProperty("被保人性别")
    private String insuredGender;

    /**
     * 被保人出生日期
     */
    @ApiModelProperty("被保人出生日期")
    private String insuredBirthDay;

    /**
     * 被保人电话
     */
    @ApiModelProperty("被保人电话")
    private String insuredPhone;

    /**
     * 被保人详细地址
     */
    @ApiModelProperty("被保人详细地址")
    private String insuredAddress;

    /**
     * 销售平台
     */
    @ApiModelProperty("销售平台")
    private String salesPlatform;

    /**
     * 电子保单链接
     */
    @ApiModelProperty("电子保单链接")
    private String policyUrl;

    /**
     * 备注
     */
    @ApiModelProperty("主表 ID")
    private String remarks;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("操作时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}