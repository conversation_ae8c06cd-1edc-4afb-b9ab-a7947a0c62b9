package com.mpolicy.manage.modules.policy.service.group.export.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelBranchInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.policy.dao.PreservationTeamPeopleDao;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyInsuredInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyProductInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyProductInsuredMapEntity;
import com.mpolicy.manage.modules.policy.enums.group.export.InsuredOccupationalCategoryEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyProductInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyProductInsuredMapService;
import com.mpolicy.manage.modules.policy.service.group.export.GroupPolicyItemPageExportService;
import com.mpolicy.manage.modules.policy.service.impl.PolicyGroupInfoServiceImpl;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationTeamPeopleService;
import com.mpolicy.manage.modules.policy.vo.GroupPolicyItemPageExportVo;
import com.mpolicy.manage.modules.policy.vo.group.export.GroupPolicyItemExportAllItemVo;
import com.mpolicy.policy.common.enums.PolicyFamilyTypeEnum;
import com.mpolicy.policy.common.enums.PolicyIdCardTypeEnum;
import com.mpolicy.policy.common.enums.PolicyInsuredOptTypeEnum;
import com.mpolicy.policy.common.enums.PolicySalesTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yangdonglin
 * @create: 2023-08-15 09:46
 * @description: 团险页面导出
 */
@Service
@Slf4j
public class GroupPolicyPolicyItemPageExportServiceImpl implements GroupPolicyItemPageExportService {

    /**
     * 页面导出最大记录数
     */
    public static final int EXPORT_LARGE_RECORDS = 20000;
    /**
     * 最大in数量
     */
    public static final int MAXIMUM_IN = 200;
    /**
     * 导出时一个sheet大数量
     */
    public static final int SHEET_MAX = 1000000;
    /**
     * 每次查询的id数量【只包含id数据】
     */
    public static final int QUANTITY_PER_PROCESSING_ID = 100000;
    /**
     * 小鲸向海 分销渠道编码
     */
    public static final String XJXH_CHANNEL_DISTRIBUTION_CODE = "XJXH001";

    @Autowired
    private PolicyGroupInfoServiceImpl policyGroupInfoService;
    @Autowired
    private EpPolicyContractInfoService epPolicyContractInfoService;
    @Autowired
    private AgentUserInfoService agentUserInfoService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private ChannelInfoService channelInfoService;
    @Autowired
    private PreservationApplyService preservationApplyService;
    @Autowired
    private PreservationTeamPeopleService preservationTeamPeopleService;
    @Autowired
    private EpPolicyInsuredInfoService epPolicyInsuredInfoService;
    @Autowired
    private EpPolicyProductInsuredMapService epPolicyProductInsuredMapService;
    @Autowired
    private ChannelBranchInfoService channelBranchInfoService;
    @Autowired
    private PreservationTeamPeopleDao preservationTeamPeopleDao;
    @Autowired
    private EpPolicyProductInfoService epPolicyProductInfoService;


    /**
     * 页面查询导出
     *
     * @param paramMap
     * @param out
     * @throws IOException
     */
    @Override
    public void queryExportPage(final Map<String, Object> paramMap,
                                final OutputStream out) throws IOException {
        // Excel信息配置
        final ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
        int row = 0;
        int index = 1;
        Sheet sheet = new Sheet(index, 0, GroupPolicyItemPageExportVo.class);
        sheet.setSheetName("保单列表" + index);
        writer.write(new ArrayList<>(), sheet);
        //写入数据就
        setOutputStream(paramMap, writer, row, index, sheet);
        writer.finish();
        out.flush();
    }

    private void setOutputStream(final Map<String, Object> paramMap,
                                 final ExcelWriter writer,
                                 int row,
                                 int index,
                                 Sheet sheet) {
        //公用信息暂存
        final Map<String, AgentUserInfoEntity> agentMap = getAgentUserInfoEntityMap();
        final Map<String, ChannelApplicationReferrerEntity> referrerMap = getreferrerMap();
        final Map<String, String> branchInfoMap = getBranchInfo();
        final Map<String, String> channelInfoMap = getChannelInfoMap();
        final List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        final List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        final List<String> channelDistributionCodeList = PolicyPermissionHelper.getChannelDistributionCodeList();
        //无权限时直接返回，与列表查询保持一致
        if (orgCodeList != null && orgCodeList.size() == 0) {
            return;
        }
        if (channelBranchCodeList != null && channelBranchCodeList.size() == 0) {
            return;
        }
        //查询符合条件的保单id
        final List<Integer> ids = qryAllPolicyIds(paramMap,
                orgCodeList,
                channelBranchCodeList,
                channelDistributionCodeList);
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        //分组处理数据
        for (List<Integer> thisIds : ListUtils.partition(ids, MAXIMUM_IN)) {
            log.info("导出数据行数：{}", row);
            //查询保单信息
            final List<EpPolicyContractInfoEntity> entities = qryPolicys(thisIds);
            if (CollectionUtils.isEmpty(entities)) {
                continue;
            }
            final List<String> codes = entities.stream()
                    .filter(e -> Objects.nonNull(e) && StringUtils.isNotBlank(e.getContractCode()))
                    .map(EpPolicyContractInfoEntity::getContractCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(codes)) {
                continue;
            }
            //计划编码快照信息
            final Map<String, String> prouducMap = qryPlanMap(codes);
            //查询被保人信息
            final Map<String, List<EpPolicyInsuredInfoEntity>> insredMap = qryInsred(codes);
            //查询保全信息
            final Map<String, List<GroupPolicyItemExportAllItemVo>> allItemMap = qryAllItem(codes);
            //查询被保人信息 【合同编码，被保人，值】
            final Map<String, Map<String, List<EpPolicyProductInsuredMapEntity>>> productMaps = qryProductMap(codes);
            //丰富导出字段
            final List<GroupPolicyItemPageExportVo> groupPolicyItemPageExportVos = new ArrayList<>();
            for (EpPolicyContractInfoEntity entity : entities) {
                //设置被保人信息
                row = setInsreds(row,
                        agentMap,
                        referrerMap,
                        branchInfoMap,
                        channelInfoMap,
                        productMaps,
                        groupPolicyItemPageExportVos,
                        entity,
                        insredMap
                );
                //设置保全明细信息
                row = setAllItem(row,
                        agentMap,
                        referrerMap,
                        branchInfoMap,
                        channelInfoMap,
                        prouducMap,
                        groupPolicyItemPageExportVos,
                        entity,
                        allItemMap);
            }
            // 单个sheet数量限制
            if (row + groupPolicyItemPageExportVos.size() > SHEET_MAX) {
                row = 0;
                index++;
                sheet = new Sheet(index, 0, GroupPolicyItemPageExportVo.class);
                sheet.setSheetName("保单列表" + index);
            }
            writer.write(groupPolicyItemPageExportVos, sheet);
        }
    }

    /**
     * 查询保单信息
     *
     * @param thisIds
     * @return
     */
    private List<EpPolicyContractInfoEntity> qryPolicys(final List<Integer> thisIds) {
        return epPolicyContractInfoService.list(
                new LambdaQueryWrapper<EpPolicyContractInfoEntity>()
                        .select(
                                EpPolicyContractInfoEntity::getContractCode,
                                EpPolicyContractInfoEntity::getPolicyNo,
                                EpPolicyContractInfoEntity::getCompanyName,
                                EpPolicyContractInfoEntity::getApplicantName,
                                EpPolicyContractInfoEntity::getAgentCode,
                                EpPolicyContractInfoEntity::getReferrerCode,
                                EpPolicyContractInfoEntity::getEnforceTime)
                        .in(EpPolicyContractInfoEntity::getId, thisIds)
        );
    }

    /**
     * 查询计划编码
     *
     * @param codes
     * @return
     */
    private Map<String, String> qryPlanMap(List<String> codes) {
        return Optional
                .ofNullable(epPolicyProductInfoService
                        .list(new LambdaQueryWrapper<EpPolicyProductInfoEntity>()
                                .select(
                                        EpPolicyProductInfoEntity::getPlanCode,
                                        EpPolicyProductInfoEntity::getPlanName)
                                .in(EpPolicyProductInfoEntity::getContractCode, codes)
                                .groupBy(EpPolicyProductInfoEntity::getPlanCode)
                                .eq(EpPolicyProductInfoEntity::getDeleted, NumberUtils.INTEGER_ZERO)))
                .orElse(new ArrayList<>())
                .stream()
                .filter(e -> Objects.nonNull(e)
                        && StringUtils.isNotBlank(e.getPlanCode())
                        && StringUtils.isNotBlank(e.getPlanName()))
                .collect(Collectors.toMap(EpPolicyProductInfoEntity::getPlanCode,
                        EpPolicyProductInfoEntity::getPlanName,
                        (s, t) -> t));
    }

    /**
     * 查询被保人信息
     *
     * @param codes
     * @return
     */
    private Map<String, List<EpPolicyInsuredInfoEntity>> qryInsred(List<String> codes) {
        return Optional.ofNullable(epPolicyInsuredInfoService
                .list(new LambdaQueryWrapper<EpPolicyInsuredInfoEntity>()
                        .select(
                                EpPolicyInsuredInfoEntity::getContractCode,
                                EpPolicyInsuredInfoEntity::getInsuredCode,
                                EpPolicyInsuredInfoEntity::getInsuredName,
                                EpPolicyInsuredInfoEntity::getFirstInsuredRelation,
                                EpPolicyInsuredInfoEntity::getMainInsuredCode,
                                EpPolicyInsuredInfoEntity::getMainFlag,
                                EpPolicyInsuredInfoEntity::getInsuredGender,
                                EpPolicyInsuredInfoEntity::getInsuredBirthday,
                                EpPolicyInsuredInfoEntity::getInsuredIdType,
                                EpPolicyInsuredInfoEntity::getInsuredIdCard,
                                EpPolicyInsuredInfoEntity::getInsuredCareer,
                                EpPolicyInsuredInfoEntity::getInsuredOccupationalCategory,
                                EpPolicyInsuredInfoEntity::getReferrerCode,
                                EpPolicyInsuredInfoEntity::getChannelReferrerCode,
                                EpPolicyInsuredInfoEntity::getInsuredOptType,
                                EpPolicyInsuredInfoEntity::getInsuredEffectiveTime,
                                EpPolicyInsuredInfoEntity::getChannelCode,
                                EpPolicyInsuredInfoEntity::getSurrendered)
                        .in(EpPolicyInsuredInfoEntity::getContractCode, codes)
                        .in(EpPolicyInsuredInfoEntity::getInsuredOptType, Arrays
                                .asList(PolicyInsuredOptTypeEnum.DEL.getCode()
                                        , PolicyInsuredOptTypeEnum.IMPORT.getCode()))
                        .eq(EpPolicyInsuredInfoEntity::getDeleted, NumberUtils.INTEGER_ZERO)))
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(EpPolicyInsuredInfoEntity::getContractCode));
    }

    /**
     * 查询保全明细
     *
     * @param codes
     * @return
     */
    private Map<String, List<GroupPolicyItemExportAllItemVo>> qryAllItem(List<String> codes) {
        return Optional
                .ofNullable(preservationTeamPeopleDao
                        .qryGroupPolicyItemExportAllItemVo(codes))
                .orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(GroupPolicyItemExportAllItemVo::getPreservationEffectTime))
                .collect(Collectors
                        .groupingBy(GroupPolicyItemExportAllItemVo::getContractCode));
    }

    /**
     * 查询被保人险种
     *
     * @param codes
     * @return
     */
    private Map<String, Map<String, List<EpPolicyProductInsuredMapEntity>>> qryProductMap(List<String> codes) {
        return Optional.ofNullable(epPolicyProductInsuredMapService
                .list(new LambdaQueryWrapper<EpPolicyProductInsuredMapEntity>()
                        .select(
                                EpPolicyProductInsuredMapEntity::getContractCode,
                                EpPolicyProductInsuredMapEntity::getInsuredCode,
                                EpPolicyProductInsuredMapEntity::getPlanCode,
                                EpPolicyProductInsuredMapEntity::getPlanName,
                                EpPolicyProductInsuredMapEntity::getPremium)
                        .in(EpPolicyProductInsuredMapEntity::getContractCode, codes)
                        .eq(EpPolicyProductInsuredMapEntity::getDeleted, NumberUtils.INTEGER_ZERO)))
                .orElse(new ArrayList<>())
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getContractCode()))
                .collect(Collectors
                        .groupingBy(EpPolicyProductInsuredMapEntity::getContractCode, Collectors
                                .groupingBy(EpPolicyProductInsuredMapEntity::getInsuredCode)));
    }

    /**
     * 设置被保人信息
     *
     * @param row
     * @param agentMap
     * @param referrerMap
     * @param branchInfoMap
     * @param channelInfoMap
     * @param productMaps
     * @param groupPolicyItemPageExportVos
     * @param entity
     * @param insredMap
     * @return
     */
    private int setInsreds(int row,
                           Map<String, AgentUserInfoEntity> agentMap,
                           Map<String, ChannelApplicationReferrerEntity> referrerMap,
                           Map<String, String> branchInfoMap, Map<String, String> channelInfoMap,
                           Map<String, Map<String, List<EpPolicyProductInsuredMapEntity>>> productMaps,
                           List<GroupPolicyItemPageExportVo> groupPolicyItemPageExportVos,
                           EpPolicyContractInfoEntity entity,
                           final Map<String, List<EpPolicyInsuredInfoEntity>> insredMap) {
        //无被保人直接返回
        if (!insredMap.containsKey(entity.getContractCode())) {
            return row;
        }
        //被保人
        final List<EpPolicyInsuredInfoEntity> insreds = insredMap.get(entity.getContractCode());
        //被保人编码映射集
        final Map<String, String> insredCodeMap = getInsredsCodeMap(insreds);
        for (EpPolicyInsuredInfoEntity insred : insreds) {
            //被保人只要新单的
            if (Objects.isNull(entity.getEnforceTime())
                    || !entity.getEnforceTime().equals(insred.getInsuredEffectiveTime())) {
                continue;
            }
            //被保人险种信息
            final List<EpPolicyProductInsuredMapEntity> orDefault = productMaps
                    .getOrDefault(entity.getContractCode(), new HashMap<>())
                    .getOrDefault(insred.getInsuredCode(), new ArrayList<>());
            //获取第一条
            final EpPolicyProductInsuredMapEntity product = Optional
                    .ofNullable(orDefault)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(e -> e.get(0))
                    .orElse(new EpPolicyProductInsuredMapEntity());
            //计数保费
            final BigDecimal premium = Optional
                    .ofNullable(orDefault)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(e -> {
                        BigDecimal premiumSum = new BigDecimal("0");
                        for (EpPolicyProductInsuredMapEntity p : e) {
                            if (Objects.nonNull(p.getPremium())) {
                                premiumSum = premiumSum.add(p.getPremium());
                            }
                        }
                        return premiumSum;
                    }).orElse(new BigDecimal("0"));
            //渠道信息
            final ChannelApplicationReferrerEntity channel = Optional
                    .ofNullable(referrerMap.get(StringUtils.isNotBlank(insred.getChannelReferrerCode())
                            ? insred.getChannelReferrerCode()
                            : entity.getReferrerCode()))
                    .orElse(new ChannelApplicationReferrerEntity());
            //代理人
            final AgentUserInfoEntity agent = Optional
                    .ofNullable(agentMap.get(StringUtils.isNotBlank(insred.getReferrerCode())
                            ? insred.getReferrerCode()
                            : entity.getAgentCode()))
                    .orElse(new AgentUserInfoEntity());
            //字段丰富
            groupPolicyItemPageExportVos.add(GroupPolicyItemPageExportVo
                    .builder()
                    .sn(++row)
                    .policyNo(entity.getPolicyNo())
                    .companyName(entity.getCompanyName())
                    .applicantName(entity.getApplicantName())
                    .insuredName(insred.getInsuredName())
                    .firstInsuredRelation(Objects.nonNull(insred.getMainFlag()) && "0".equals(insred.getMainFlag().toString())
                            ? PolicyFamilyTypeEnum.ME.getFamilyDesc()
                            : Optional
                            .ofNullable(PolicyFamilyTypeEnum.decode(insred.getFirstInsuredRelation()))
                            .map(PolicyFamilyTypeEnum::getFamilyDesc)
                            .orElse(""))
                    .mainInsuredName(Optional
                            .ofNullable(insred)
                            .map(e -> {
                                if (Objects.nonNull(insred.getMainFlag()) && "0".equals(insred.getMainFlag().toString())) {
                                    return insred.getInsuredName();
                                }
                                if (Objects.nonNull(insred.getMainInsuredCode())
                                        && insredCodeMap.containsKey(insred.getMainInsuredCode())) {
                                    return insredCodeMap.get(insred.getMainInsuredCode());
                                }
                                return "";
                            })
                            .orElse(""))
                    .insuredGender(Optional
                            .ofNullable(insred.getInsuredGender())
                            .map(e -> 0 == e ? "女" : "男")
                            .orElse("未知"))
                    .insuredBirthday(DateUtil.formatDate(insred.getInsuredBirthday()))
                    .insuredIdType(Optional
                            .ofNullable(PolicyIdCardTypeEnum.getIdCardTypeEnumByCode(insred.getInsuredIdType()))
                            .map(PolicyIdCardTypeEnum::getName)
                            .orElse(""))
                    .insuredIdCard(insred.getInsuredIdCard())
                    .insuredCareer(insred.getInsuredCareer())
                    .insuredOccupationalCategory(Optional
                            .ofNullable(InsuredOccupationalCategoryEnum.findCode(insred.getInsuredOccupationalCategory()))
                            .map(InsuredOccupationalCategoryEnum::getDesc)
                            .orElse(insred.getInsuredOccupationalCategory()))
                    .planCode(product.getPlanCode())
                    .planName(product.getPlanName())
                    .premium(Optional
                            .ofNullable(premium)
                            .map(BigDecimal::toString)
                            .orElse(""))
                    .allPremium(null)
                    .referrerName(agent.getAgentName())
                    .referrerWno(agent.getBusinessCode())
                    .channelReferrerName(channel.getReferrerName())
                    .channelReferrerWno(channel.getReferrerWno())
                    .channelBranchName(Optional
                            .ofNullable(branchInfoMap
                                    .get(channel.getBranchCode()))
                            .orElse(""))
                    .channelName(Optional
                            .ofNullable(channelInfoMap.get(channel.getChannelCode()))
                            .filter(StringUtils::isNotBlank)
                            .orElse(channelInfoMap.get(insred.getChannelCode())))
                    .insuredOptType(PolicyInsuredOptTypeEnum.IMPORT.getDesc())
                    .insuredEffectiveTime(DateUtil.formatDate(insred.getInsuredEffectiveTime()))
                    .endorsementNo(null)
                    .build());
        }
        return row;
    }

    /**
     * 设置保全信息
     *
     * @param row
     * @param agentMap
     * @param referrerMap
     * @param branchInfoMap
     * @param channelInfoMap
     * @param prouducMap
     * @param groupPolicyItemPageExportVos
     * @param entity
     * @param allItemMap
     * @return
     */
    private int setAllItem(int row,
                           Map<String, AgentUserInfoEntity> agentMap,
                           Map<String, ChannelApplicationReferrerEntity> referrerMap,
                           Map<String, String> branchInfoMap, Map<String, String> channelInfoMap,
                           Map<String, String> prouducMap,
                           List<GroupPolicyItemPageExportVo> groupPolicyItemPageExportVos,
                           EpPolicyContractInfoEntity entity,
                           final Map<String, List<GroupPolicyItemExportAllItemVo>> allItemMap) {
        final List<GroupPolicyItemExportAllItemVo> allItemVos = allItemMap
                .getOrDefault(entity.getContractCode(), new ArrayList<>());
        for (GroupPolicyItemExportAllItemVo allItemVo : allItemVos) {
            //渠道信息
            final ChannelApplicationReferrerEntity channel = Optional
                    .ofNullable(referrerMap.get(StringUtils.isNotBlank(allItemVo.getChannelReferrerCode())
                            ? allItemVo.getChannelReferrerCode()
                            : entity.getReferrerCode()))
                    .orElse(new ChannelApplicationReferrerEntity());
            //代理人
            final AgentUserInfoEntity agent = Optional
                    .ofNullable(agentMap.get(StringUtils.isNotBlank(allItemVo.getReferrerCode())
                            ? allItemVo.getReferrerCode()
                            : entity.getAgentCode()))
                    .orElse(new AgentUserInfoEntity());
            groupPolicyItemPageExportVos.add(GroupPolicyItemPageExportVo
                    .builder()
                    .sn(++row)
                    .policyNo(entity.getPolicyNo())
                    .companyName(entity.getCompanyName())
                    .applicantName(entity.getApplicantName())
                    .insuredName(allItemVo.getInsuredName())
                    .firstInsuredRelation(allItemVo.getFirstInsuredRelation())
                    .mainInsuredName(allItemVo.getMainInsuredName())
                    .insuredGender(allItemVo.getInsuredGender())
                    .insuredBirthday(allItemVo.getInsuredBirthday())
                    .insuredIdType(allItemVo.getInsuredIdType())
                    .insuredIdCard(allItemVo.getInsuredIdCard())
                    .insuredCareer(allItemVo.getInsuredCareer())
                    .insuredOccupationalCategory(Optional
                            .ofNullable(InsuredOccupationalCategoryEnum.findCode(allItemVo.getInsuredOccupationalCategory()))
                            .map(InsuredOccupationalCategoryEnum::getDesc)
                            .orElse(allItemVo.getInsuredOccupationalCategory()))
                    .planCode(allItemVo.getPlanCode())
                    .planName(Optional
                            .ofNullable(allItemVo.getPlanCode())
                            .map(e -> prouducMap
                                    .getOrDefault(e, ""))
                            .orElse(""))
                    .premium(null)
                    .allPremium(Optional
                            .ofNullable(allItemVo.getSinglePremium())
                            .map(b -> "减人".equals(allItemVo.getPeopleType()) && b.compareTo(BigDecimal.ZERO) == 1 ? StrUtil
                                    .format("-{}", b.toString()) : b.toString())
                            .orElse(""))
                    .referrerName(agent.getAgentName())
                    .referrerWno(agent.getBusinessCode())
                    .channelReferrerName(channel.getReferrerName())
                    .channelReferrerWno(channel.getReferrerWno())
                    .channelBranchName(Optional
                            .ofNullable(branchInfoMap
                                    .get(channel.getBranchCode()))
                            .orElse(""))
                    .channelName(Optional
                            .ofNullable(channelInfoMap.get(channel.getChannelCode()))
                            .filter(StringUtils::isNotBlank)
                            .orElse(channelInfoMap.get(allItemVo.getChannelCode())))
                    .insuredOptType(allItemVo.getPeopleType())
                    .insuredEffectiveTime(DateUtil.formatDate(allItemVo.getPreservationEffectTime()))
                    .endorsementNo(allItemVo.getEndorsementNo())
                    .build());
        }
        return row;
    }

    /**
     * 设置险种映射关系
     *
     * @param insreds
     * @return
     */
    private Map<String, String> getInsredsCodeMap(List<EpPolicyInsuredInfoEntity> insreds) {
        return insreds.stream()
                .filter(e -> StringUtils
                        .isNotBlank(e.getInsuredCode())
                        && StringUtils.isNotBlank(e.getInsuredName()))
                .collect(Collectors.toMap(EpPolicyInsuredInfoEntity::getInsuredCode
                        , EpPolicyInsuredInfoEntity::getInsuredName,
                        (s, t) -> t));
    }

    /**
     * 查询保单id信息
     *
     * @param paramMap
     * @param orgCodeList
     * @param channelBranchCodeList
     * @param channelDistributionCodeList
     * @return
     */
    private List<Integer> qryAllPolicyIds(final Map<String, Object> paramMap,
                                          final List<String> orgCodeList,
                                          final List<String> channelBranchCodeList,
                                          final List<String> channelDistributionCodeList) {
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> queryById = policyGroupInfoService
                .getQueryWrapper(paramMap, orgCodeList, channelBranchCodeList, channelDistributionCodeList);
        queryById.select(EpPolicyContractInfoEntity::getId);
        queryById.orderByAsc(EpPolicyContractInfoEntity::getId);
        queryById.eq(EpPolicyContractInfoEntity::getDeleted, NumberUtils.INTEGER_ZERO);
        queryById.last(StrUtil.format("limit {}", EXPORT_LARGE_RECORDS));
        return Optional
                .ofNullable(epPolicyContractInfoService.list(queryById))
                .orElse(new ArrayList<>())
                .stream()
                .filter(e -> Objects.nonNull(e) && Objects.nonNull(e.getId()))
                .map(EpPolicyContractInfoEntity::getId)
                .collect(Collectors.toList());
    }

    /**
     * 获取代理人信息
     *
     * @return Map
     */
    public Map<String, AgentUserInfoEntity> getAgentUserInfoEntityMap() {
        final List<AgentUserInfoEntity> agentUsers = agentUserInfoService
                .list(new LambdaQueryWrapper<AgentUserInfoEntity>()
                        .select(
                                AgentUserInfoEntity::getAgentCode,
                                AgentUserInfoEntity::getAgentName,
                                AgentUserInfoEntity::getBusinessCode)
                        .last(StrUtil.format("limit {}", EXPORT_LARGE_RECORDS))
                );
        return Optional.ofNullable(agentUsers)
                .filter(CollectionUtils::isNotEmpty)
                .map(e -> e
                        .stream()
                        .filter(s -> StringUtils
                                .isNotBlank(s.getAgentCode())
                        ).collect(Collectors.toMap(AgentUserInfoEntity::getAgentCode, Function.identity(), (s, d) -> d))
                ).orElse(new HashMap<>());
    }

    /**
     * 获取推荐人信息
     *
     * @return Map
     */
    public Map<String, ChannelApplicationReferrerEntity> getreferrerMap() {
        final List<ChannelApplicationReferrerEntity> referrerUsers = channelApplicationReferrerService
                .list(new LambdaQueryWrapper<ChannelApplicationReferrerEntity>()
                        .select(
                                ChannelApplicationReferrerEntity::getReferrerCode,
                                ChannelApplicationReferrerEntity::getReferrerWno,
                                ChannelApplicationReferrerEntity::getReferrerName,
                                ChannelApplicationReferrerEntity::getChannelCode,
                                ChannelApplicationReferrerEntity::getBranchCode)
                        .last(StrUtil.format("limit {}", "50000"))
                );
        return Optional.ofNullable(referrerUsers)
                .filter(CollectionUtils::isNotEmpty)
                .map(e -> e
                        .stream()
                        .filter(s -> StringUtils
                                .isNotBlank(s.getReferrerCode())
                        ).collect(Collectors
                                .toMap(ChannelApplicationReferrerEntity::getReferrerCode, Function.identity(), (s, d) -> d))
                ).orElse(new HashMap<>());
    }

    /**
     * 获取推荐人信息
     *
     * @return Map
     */
    public Map<String, String> getChannelInfoMap() {
        final List<ChannelInfoEntity> channelInfos = channelInfoService
                .list(new LambdaQueryWrapper<ChannelInfoEntity>()
                        .select(
                                ChannelInfoEntity::getChannelCode,
                                ChannelInfoEntity::getChannelName)
                        .last(StrUtil.format("limit {}", EXPORT_LARGE_RECORDS))
                );
        return Optional.ofNullable(channelInfos)
                .filter(CollectionUtils::isNotEmpty)
                .map(e -> e
                        .stream()
                        .filter(s -> StringUtils.isNotBlank(s.getChannelCode())
                                && StringUtils.isNotBlank(s.getChannelName())
                        ).collect(Collectors
                                .toMap(ChannelInfoEntity::getChannelCode, ChannelInfoEntity::getChannelName, (s, d) -> d))
                ).orElse(new HashMap<>());
    }

    /**
     * 获取渠道分支信息
     *
     * @return Map
     */
    public Map<String, String> getBranchInfo() {
        final List<ChannelBranchInfoEntity> channelBranchInfo = channelBranchInfoService
                .list(new LambdaQueryWrapper<ChannelBranchInfoEntity>()
                        .select(
                                ChannelBranchInfoEntity::getBranchCode,
                                ChannelBranchInfoEntity::getBranchName)
                        .last(StrUtil.format("limit {}", EXPORT_LARGE_RECORDS))
                );
        return Optional.ofNullable(channelBranchInfo)
                .filter(CollectionUtils::isNotEmpty)
                .map(e -> e
                        .stream()
                        .filter(s -> StringUtils.isNotBlank(s.getBranchCode())
                                && StringUtils.isNotBlank(s.getBranchName())
                        ).collect(Collectors
                                .toMap(ChannelBranchInfoEntity::getBranchCode,
                                        ChannelBranchInfoEntity::getBranchName, (s, d) -> d))
                ).orElse(new HashMap<>());
    }

    /**
     * 判断是否是批减
     *
     * @param insred
     * @return
     */
    private boolean isSurrendered(final EpPolicyInsuredInfoEntity insred) {
        if (Objects.isNull(insred.getSurrendered())) {
            return false;
        }
        return "1".equals(insred.getSurrendered().toString());
    }

    /**
     * 判断是否是网销单
     *
     * @param entity
     * @return
     */
    public static boolean isOnlineSales(final EpPolicyContractInfoEntity entity) {
        return PolicySalesTypeEnum.ON_LINE.getCode().equals(entity.getSalesType()) ||
                (PolicySalesTypeEnum.OUT_LINE.getCode().equals(entity.getSalesType())
                        && !XJXH_CHANNEL_DISTRIBUTION_CODE.equals(entity.getChannelDistributionCode())
                );
    }
}
