package com.mpolicy.manage.modules.inside.controller;

import com.mpolicy.manage.modules.inside.service.IInsideCommonService;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.common.result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/inside")
@Slf4j
@Api(tags = "内部服务")
public class InsideController {

    @Autowired
    private IInsideCommonService insideCommonService;

    @GetMapping("/epPolicyContractInfo/field")
    @ApiOperation(value = "获取epPolicyContractInfo表所有字段名", notes = "获取epPolicyContractInfo所有字段名")
    public Result getEpPolicyContractInfoField() {
        List<String> fieldNames = Arrays.stream(EpPolicyContractInfoEntity.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
        return Result.success(fieldNames);
    }

    @GetMapping("/agentInfoByPolicyNo/{policyNo}")
    @ApiOperation(value = "根据保单号获取代理人信息", notes = "根据保单号获取代理人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", required = true, name = "policyNo", dataType = "String", value = "保单号", example = "ydl_cs20240109_06"),
    })
    @RequiresPermissions(value = {"dev:lostpolicy:all"})
    public Result<Map> getAgentInfoByPolicyNo(@PathVariable String policyNo) {
        return Result.success(insideCommonService.getAgentInfoByPolicyNo(policyNo));
    }
}
