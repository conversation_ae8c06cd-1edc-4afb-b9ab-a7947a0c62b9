package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireQuestionEntity;

import java.util.List;
import java.util.Map;

/**
 * 代理人问卷问题表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 19:21:44
 */
public interface AgentQuestionnaireQuestionService extends IService<AgentQuestionnaireQuestionEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    /**
     * 根据问卷id删除
     * @param questionnaireId
     */
    void deleteByQuestionnaireId(Integer questionnaireId);

    List<AgentQuestionnaireQuestionEntity> queryByQuestionnaireId(Integer questionnaireId);
}

