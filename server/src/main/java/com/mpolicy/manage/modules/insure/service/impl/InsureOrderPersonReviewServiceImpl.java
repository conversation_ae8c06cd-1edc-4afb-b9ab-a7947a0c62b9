package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.dao.InsureOrderPersonReviewDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderPersonReviewEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderPersonReviewService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("insureOrderPersonReviewService")
public class InsureOrderPersonReviewServiceImpl extends ServiceImpl<InsureOrderPersonReviewDao, InsureOrderPersonReviewEntity> implements InsureOrderPersonReviewService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<InsureOrderPersonReviewEntity> page = this.page(
                new Query<InsureOrderPersonReviewEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
