package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.agent.entity.AgentCompanyAccountEntity;
import com.mpolicy.manage.modules.protocol.vo.ImportInsuranceProductVo;

/**
 * <AUTHOR>
 */
public interface AgentCompanyAccountService extends IService<AgentCompanyAccountEntity> {

    /**
     * 导出保司账号
     * @param input 文件
     */
    void importCompanyAccount(ImportInsuranceProductVo input);
}
