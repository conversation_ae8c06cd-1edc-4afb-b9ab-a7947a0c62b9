package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 导入团险分单数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/12 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EpPolicyGroupSubImportVo extends BaseRowModel  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 被保人序号
     */
    @ApiModelProperty(value = "被保人序号")
    private String insuredId;
    /**
     * 被保人编码
     */
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;
    /**
     * 主被保人标识
     */
    @ApiModelProperty(value = "主被保人标识")
    private String mainInsuredFlag;
    /**
     * 对应主被保人
     */
    @ApiModelProperty(value = "对应主被保人")
    private String mainInsuredName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    @ExcelProperty(value = "被保人姓名",index = 0)
    private String insuredName;
    /**
     * 与主被保人关系
     */
    @ApiModelProperty(value = "与主被保人关系")
    @ExcelProperty(value = "与员工关系",index = 1)
    private String firstInsuredRelation;
    /**
     * 性别
     */
    @ApiModelProperty(value = "被保人性别 0：女 1：男")
    @ExcelProperty(value = "性别",index = 2)
    private String insuredGender;
    /**
     * 被保人出生日期
     */
    @ApiModelProperty(value = "被保人出生日期")
    @ExcelProperty(value = "出生日期",index = 3)
    private String insuredBirthday;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "被保人证件类型")
    @ExcelProperty(value = "证件类型",index = 4)
    private String insuredIdType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "被保人证件号码")
    @ExcelProperty(value = "证件号码",index = 5)
    private String insuredIdCard;
    /**
     * 证件有效期
     */
    @ApiModelProperty(value = "证件有效期")
    @ExcelProperty(value = "证件有效期",index = 6)
    private String insuredIdCardValidityEnd;
    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    @ExcelProperty(value = "国籍",index = 7)
    private String insuredNation;

    @ApiModelProperty(value = "地址")
    @ExcelProperty(value = "地址",index = 8)
    private String insuredAddress;


    @ApiModelProperty(value = "保险计划编码")
    @ExcelProperty(value = "保险计划编码",index = 9)
    private String planCode;

    @ApiModelProperty(value = "保费")
    @ExcelProperty(value = "保费",index = 10)
    private BigDecimal singlePremium;

    @ApiModelProperty(value = "职业代码")
    @ExcelProperty(value = "职业代码",index = 11)
    private String insuredCareer;

    @ApiModelProperty(value = "职业类别")
    @ExcelProperty(value = "职业类别",index = 12)
    private String insuredOccupationalCategory;

    @ApiModelProperty(value = "主被保人证件号码")
    @ExcelProperty(value = "主被保人证件号码",index = 13)
    private String mainInsuredIdCard;

    @ApiModelProperty(value = "联系方式")
    @ExcelProperty(value = "联系方式",index = 14)
    private String insuredMobile;

    @ApiModelProperty(value = "电子邮箱")
    @ExcelProperty(value = "电子邮箱",index = 15)
    private String insuredEmail;

    @ApiModelProperty(value = "工作单位")
    @ExcelProperty(value = "工作单位",index = 16)
    private String insuredCompany;

    @ExcelProperty(value = "推荐人工号",index = 17)
    private String referrerWno;

    @ApiModelProperty(value = "推荐人名字")
    private String referrerName;

    @ExcelProperty(value = "渠道推荐人工号",index = 18)
    private String channelReferrerWno;

    @ApiModelProperty(value = "渠道推荐人名字")
    private String channelReferrerName;

    @ApiModelProperty(value = "销售渠道")
    private String channelCode;

    @ApiModelProperty(value = "销售渠道")
    private String channelName;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchCode;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchName;

    @ExcelProperty(value = "备注",index = 19)
    private String remark;

}
