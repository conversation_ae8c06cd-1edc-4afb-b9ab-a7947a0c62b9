package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentStudioIpContentEntity;
import com.mpolicy.manage.modules.agent.vo.resp.AgentStudioIpContentRespVo;

import java.util.Map;

/**
 * 经纪人工作室IP内容
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 13:47:00
 */
public interface AgentStudioIpContentService extends IService<AgentStudioIpContentEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<AgentStudioIpContentRespVo> queryPage(Map<String,Object> paramMap);

    AgentStudioIpContentEntity info(String code);

    boolean saveOrUpdateEntity(AgentStudioIpContentEntity agentStudioIpContentEntity);

    boolean deleteEntity(String code);
}

