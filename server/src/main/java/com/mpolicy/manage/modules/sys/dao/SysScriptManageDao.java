package com.mpolicy.manage.modules.sys.dao;

import com.mpolicy.manage.modules.sys.entity.SysScriptManageEntity;
import com.mpolicy.manage.modules.sys.vo.SysScriptManageListInput;
import com.mpolicy.manage.modules.sys.vo.SysScriptManageListOut;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 脚本管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-20 10:38:46
 */
public interface SysScriptManageDao extends BaseMapper<SysScriptManageEntity> {


    /**
     * 获取脚本管理列表
     *
     * @param page
     * @param input
     * @return
     */
    IPage<SysScriptManageListOut> findSysScriptManageList(@Param("page") Page<SysScriptManageListOut> page, @Param("input") SysScriptManageListInput input);

}
