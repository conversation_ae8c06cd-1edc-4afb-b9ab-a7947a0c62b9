package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/2 17:56
 */
public interface ChannelBranchInfoService extends IService<ChannelBranchInfoEntity> {
    /**
     * 修改分支启用状态
     *
     * @param id       推荐人id
     * @param enabled  状态
     * @param revision 版本号
     * @return 修改结果
     */
    boolean changeEnable(Integer id, Integer enabled, long revision);

    /**
     * 获取渠道下的分支
     *
     * @return 分支列表
     */
    List<ChannelBranchInfoEntity> getChannelList();

    /**
     * 获取分支列表
     *
     * @param parentCode 上级节点编码
     * @param branchLevel 分支层级
     * @return 分支列表
     */
    List<ChannelBranchInfoEntity> getBranchList(String parentCode, Integer branchLevel);

    /**
     * 获取渠道分支
     * @return
     */
    List<ChannelBranchInfoEntity> getChannelBranchList(String channelCode);

    /**
     * 获取渠道树列表
     * @return
     */
    List<TreeListOut> findChannelTreeList();
}
