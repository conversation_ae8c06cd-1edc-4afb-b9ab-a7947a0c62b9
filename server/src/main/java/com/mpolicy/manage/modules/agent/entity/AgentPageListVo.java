package com.mpolicy.manage.modules.agent.entity;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: mpolicy-admin
 * @description: 代理人列表请求参数
 * @author: lsc
 * @created: 2022/03/30 14:21
 */
@Data
public class AgentPageListVo extends BasePage implements Serializable {
    private static final long serialVersionUID = -1437693509839152814L;

    @ApiModelProperty(value = "昵称")
    private String agentName;

    @ApiModelProperty(value = "执业证编码")
    private String certificateNum;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "业务编码")
    private String businessCode;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "代理人状态")
    private Integer agentStatus;

    @ApiModelProperty(value = "离职状态")
    private Integer quitStatus;

    private List<String> orgList;

    @ApiModelProperty(value = "展业区域")
    private String acquisitionArea;

    @ApiModelProperty(value = "展业区域")
    private List<String> acquisitionAreas;

    @ApiModelProperty(value = "区域编码")
    private String regionCode;

}
