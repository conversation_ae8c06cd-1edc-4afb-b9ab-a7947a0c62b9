package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * @Description 渠道分类枚举
 * @return
 * @Date 2024/3/5 9:28
 * <AUTHOR>
 **/
public enum ChannelInfoClassificationEnum {
    /**
     * 内勤
     */
    TYPE0(0, "普通渠道"),
    TYPE1(1, "高客渠道"),
    TYPE2(2, "农村业务"),
    ;

    private final Integer key;
    private final String value;

    ChannelInfoClassificationEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static ChannelInfoClassificationEnum getValueByKey(Integer key) {
        return Arrays.stream(values()).filter((x) -> x.key.equals(key)).findFirst().orElse(null);
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
