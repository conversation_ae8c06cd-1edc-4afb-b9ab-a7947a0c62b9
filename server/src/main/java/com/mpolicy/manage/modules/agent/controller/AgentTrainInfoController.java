package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.service.AgentTrainInfoService;
import com.mpolicy.manage.modules.agent.vo.train.*;
import com.mpolicy.manage.modules.common.model.SelectOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 代理人培训信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
@RestController
@RequestMapping("agent/train")
@Api(tags = "代理人培训")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTrainInfoController {

    private final AgentTrainInfoService agentTrainInfoService;


    @ApiOperation(value = "获取参会人员列表", notes = "获取参会人员列表",httpMethod = "GET")
    @GetMapping("findParticipants")
    public Result<List<SelectOut>> findParticipants() {
        List<SelectOut> list = agentTrainInfoService.findParticipants();
        return Result.success(list);
    }

    @ApiOperation(value = "获取培训人员列表", notes = "获取培训人员列表",httpMethod = "GET")
    @GetMapping("findTrainHostList")
    public Result<List<SelectOut>> findTrainHostList() {
        List<SelectOut> list = agentTrainInfoService.findTrainHostList();
        return Result.success(list);
    }


    @ApiOperation(value = "获取培训信息表列表", notes = "分页获取培训信息表列表",httpMethod = "GET")
    @GetMapping("findPageList")
    @RequiresPermissions(value = {"agent:train:all"})
    public Result<PageUtils<AgentTrainInfoListOut>> findPageList(AgentTrainInfoListVo params) {
        PageUtils<AgentTrainInfoListOut> page = agentTrainInfoService.findPageList(params);
        return Result.success(page);
    }


    @ApiOperation(value = "获取代理人培训时长", notes = "获取代理人培训时长",httpMethod = "GET")
    @GetMapping("findAgentTrainDuration/{agentCode}")
    public Result<AgentTrainDurationOut> findAgentTrainDuration(@PathVariable(value = "agentCode") String agentCode) {
        AgentTrainDurationOut page = agentTrainInfoService.findAgentTrainDuration(agentCode);
        return Result.success(page);
    }

    @GetMapping("info/{trainCode}")
    @RequiresPermissions(value = {"agent:train:all"})
    @ApiOperation(value = "获取代理人培训信息详情", notes = "获取代理人培训信息详情",httpMethod = "GET")
    public Result<AgentTrainInfoOut> info(@PathVariable("trainCode") String trainCode) {
        AgentTrainInfoOut agentTrainInfo = agentTrainInfoService.findAgentTrainInfo(trainCode);
        return Result.success(agentTrainInfo);
    }

    @PostMapping("save")
    @RequiresPermissions(value = {"agent:train:all"})
    @ApiOperation(value = "新增代理人培训信息", notes = "新增代理人培训信息", httpMethod = "POST")
    public Result save(@RequestBody SaveAgentTrainInfoVo save) {
        agentTrainInfoService.saveAgentTrainInfo(save);
        return Result.success();
    }


    @PostMapping("update")
    @RequiresPermissions(value = {"agent:train:all"})
    @ApiOperation(value = "修改代理人培训信息", notes = "修改代理人培训信息", httpMethod = "POST")
    public Result update(@RequestBody UpdateAgentTrainInfoVo update) {
        agentTrainInfoService.updateAgentTrainInfo(update);
        return Result.success();
    }


    @PostMapping("delete/{trainCode}")
    @RequiresPermissions(value = {"agent:train:all"})
    @ApiOperation(value = "删除代理人培训信息", notes = "删除代理人培训信息", httpMethod = "POST")
    public Result delete(@PathVariable("trainCode") String trainCode) {
        agentTrainInfoService.deleteAgentTrainInfo(trainCode);
        return Result.success();
    }

}
