package com.mpolicy.manage.modules.sell.entity;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ExportInsuranceProductListOut implements Serializable {
    private static final long serialVersionUID = 6339180201246343949L;
    @Alias("序号")
    private Integer id;

    @Alias("险种编码")
    private String productCode;

    @Alias("险种名称")
    private String productName;

    @Alias("保司编码")
    private String companyCode;

    @Alias("保司名称")
    private String companyName;

    /**
     * 主附险标记 1为主险 0为附加险
     */
    @Alias("主附险")
    private String mainProductFlag;
    /**
     * 条款是否上传 1为是 0为否
     */
    @Alias("条款是否上传")
    private String productTermsFileCode;
    /**
     * 费率表是否上传 1为是 0为否
     */
    @Alias("费率表是否上传")
    private String premTableFlag;
    /**
     * KV费率表是否上传 1为是 0为否
     */
    @Alias("KV费率表是否上传")
    private String kvPremTableFlag;
    /**
     * 责任定义是否上传 1为是 0为否
     */
    @Alias("责任定义是否上传")
    private String dutyFlag;

    @Alias("是否需要回执")
    private String receipt;

    @Alias("是否需要回访")
    private String callback;

    /**
     * 险种用途
     */
    @Alias("险种用途")
    private String protocolFlagName;

    /**
     * 险种渠道
     */
    @Alias("险种渠道")
    private String productChannelName;
}
