package com.mpolicy.manage.modules.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class SelectVo implements Serializable {


    private static final long serialVersionUID = -8423135465654506094L;
    @ApiModelProperty(value = "下拉框类型,参考字典")
    private Integer selectType;

    @ApiModelProperty(value = "种类")
    private String form;
}
