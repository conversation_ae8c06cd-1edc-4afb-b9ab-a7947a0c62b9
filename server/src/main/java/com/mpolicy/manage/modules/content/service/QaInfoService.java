package com.mpolicy.manage.modules.content.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.content.entity.QaInfoEntity;

import java.util.Map;

/**
 * 问答信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-21 16:26:44
 */
public interface QaInfoService extends IService<QaInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveEntity(QaInfoEntity qaInfoEntity);

    /**
     * 根据qaCode更新
     *
     * @param qaInfoEntity
     */
    void updateEntity(QaInfoEntity qaInfoEntity);

    /**
     * 获取某一问答对象
     *
     * @param qaCode
     * @return
     */
    QaInfoEntity getOneInfo(String qaCode);

    /**
     * 删除问答所有缓存
     */
    void invalidateQaCache();
}

