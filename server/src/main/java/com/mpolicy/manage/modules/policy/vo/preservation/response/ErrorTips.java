package com.mpolicy.manage.modules.policy.vo.preservation.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.google.common.base.Joiner;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 导入数据结果失败结果信息
 *
 * <AUTHOR>
 * @date 2023/1/9 19:22
 */
@Data
public class ErrorTips extends BaseRowModel implements Serializable {

    /**
     * 行号
     */
    @ExcelProperty(index = 0, value = "行号")
    private String seq;

    /**
     * 保单号
     */
    @ExcelProperty(index = 1, value = "保单号")
    private String policyCode;

    /**
     * 失败信息
     */
    @ExcelProperty(index = 2, value = "失败信息")
    private String message;

    public void addErrMsg(String errMsg){
        if(StringUtils.isBlank(message)){
            message = errMsg;
            return;
        }
        message += "|"+errMsg;
    }

    public static ErrorTips create(String seq,String policyCode,List<String> errMsgList){
        String errMsg = null;
        if(CollectionUtils.isNotEmpty(errMsgList)){
            errMsg = Joiner.on("；").join(errMsgList);
        }
        ErrorTips err = new ErrorTips();
        err.setSeq(seq);
        err.setPolicyCode(policyCode);
        err.setMessage(errMsg);
        return err;
    }

}
