package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 保全申请数据
 *
 * <AUTHOR>
 * @date 2022-03-21 10:39
 */
@ApiModel(value = "保单导出信息")
@Data
public class PreservationXls extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    //    @ExcelProperty(index = 0, value = "序号")
    private Integer no;

    /**
     * 保全流水号
     */
    @ExcelProperty(index = 1, value = "保全流水号")
    private String preservationCode;

    /**
     * 产品类型
     */
    @ExcelProperty(index = 3, value = "产品类型")
    private String policyType;

    /**
     * 保单号
     */
    @ExcelProperty(index = 4, value = "保单号")
    private String policyCode;

    /**
     * 保全类型名称
     */
    @ExcelProperty(index = 5, value = "保全类型")
    private String preservationTypeName;

    /**
     * 保全项目名称
     */
    @ExcelProperty(index = 6, value = "保全项目")
    private String preservationProjectName;

    /**
     * 保全生效日期
     */
    @ExcelProperty(index = 7, value = "保全生效日期")
    private String effectTime;

    /**
     * 保全批单号
     */
    @ExcelProperty(index = 8, value = "保全批单号")
    private String endorsementNo;

    /**
     * 保全变更原因名称
     */
    @ExcelProperty(index = 9, value = "变更原因")
    private String preservationWhyName;

    /**
     * 附加险解约类型
     */
    @ExcelProperty(index = 10, value = "附加险解约类型")
    private String terminationProductName;

    /**
     * 补退费金额
     */
    @ExcelProperty(index = 11, value = "补退费金额")
    private String surrenderCash;

    /**
     * 保司名称
     */
    @ExcelProperty(index = 12, value = "保险公司名称")
    private String companyName;

    /**
     * 产品名称
     */
    @ExcelProperty(index = 13, value = "产品名称")
    private String policyName;

    /**
     * 投保人姓名
     */
    @ExcelProperty(index = 14, value = "投保人/单位名称")
    private String holderName;

    /**
     * 销售渠道名称
     */
    @ExcelProperty(index = 15, value = "销售渠道")
    private String sellChannelName;

    /**
     * 代理人名称
     */
    @ExcelProperty(index = 16, value = "代理人")
    private String policyAgentName;

    /**
     * 推荐人名称
     */
    @ExcelProperty(index = 17, value = "推荐人")
    private String referrerName;

    @ExcelProperty(index = 18, value = "推荐人编码")
    private String referrerCode;
    /**
     * 渠道推荐人名称
     */
    @ExcelProperty(index = 19, value = "渠道推荐人")
    private String channelReferrerName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2019-01-01 15:12")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ExcelProperty(index = 20, value = "创建时间", format = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 创建人
     */
    @ExcelProperty(index = 21, value = "创建人")
    private String createUser;

    /**
     * 保全状态 1录入完成
     */
    @ExcelProperty(index = 22, value = "保全状态")
    private String preservationStatus;

    @ExcelProperty(index = 23, value = "图例")
    private String channelDistributionName;

    @ExcelProperty(index = 24, value = "协议险种名称")
    private String insuranceProductName;
    /**
     * 保单中心保单唯一号
     */
    private String contractCode;
}
