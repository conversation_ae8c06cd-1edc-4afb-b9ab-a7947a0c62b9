package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/7 14:04
 */
public enum AgentIpContentPlatformEnum {

    // 知乎
    ZHI_HU("AGENT_IP_CONTENT_PLATFORM:ZHI_HU", "知乎"),
    // 微博
    WEI_BO("AGENT_IP_CONTENT_PLATFORM:WEI_BO", "微博"),
    // 微信公众号
    WECHAT_PUBLIC_PLATFORM("AGENT_IP_CONTENT_PLATFORM:WECHAT_PUBLIC_PLATFORM", "微信公众号"),
    // 抖音
    DOU_YIN("AGENT_IP_CONTENT_PLATFORM:DOU_YIN", "抖音"),

    ;

    private final String key;
    private final String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    AgentIpContentPlatformEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static AgentIpContentPlatformEnum getValueByKey(String key) {
        return Arrays.stream(values()).filter((x) -> x.key.equals(key)).findFirst().orElse(null);
    }

    public static boolean isDouYin(AgentIpContentPlatformEnum platform){
        return AgentIpContentPlatformEnum.DOU_YIN.equals(platform);
    }

}
