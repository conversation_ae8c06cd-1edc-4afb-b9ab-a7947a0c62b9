package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.AgentReceptionDao;
import com.mpolicy.manage.modules.agent.entity.AgentReceptionEntity;
import com.mpolicy.manage.modules.agent.service.AgentReceptionService;
import org.springframework.stereotype.Service;

@Service("agentReceptionService")
public class AgentReceptionImpl extends ServiceImpl<AgentReceptionDao, AgentReceptionEntity> implements AgentReceptionService {
}
