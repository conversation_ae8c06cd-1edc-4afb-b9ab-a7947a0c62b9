package com.mpolicy.manage.modules.agent.dao;

import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 推荐人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
public interface ChannelApplicationReferrerDao extends BaseMapper<ChannelApplicationReferrerEntity> {

    /**
     * 查询渠道推荐人列表
     * @param referrerCodeList
     * @return
     */
    List<FastChannelApplicationReferrer> listFastEntity(@Param("referrerCodeList") List<String> referrerCodeList);

    /**
     * 查询渠道推荐人列表
     * @param referrerCode
     * @return
     */
    FastChannelApplicationReferrer queryOne(@Param("referrerCode") String referrerCode);

    List<FastChannelApplicationReferrer> listByJobNumber(@Param("jobNumberList")List<String> jobNumberList);
}
