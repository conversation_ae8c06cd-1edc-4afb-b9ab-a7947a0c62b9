package com.mpolicy.manage.modules.policy.vo.lost;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.policy.vo.PageInfo;
import com.mpolicy.manage.utils.LocalDateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;

/**
 * 丢单补偿列表查询
 *
 * <AUTHOR>
 */
@Data
public class PolicyLostRegisterQuery extends PageInfo {

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("提交开始时间")
    private String startDate;

    @ApiModelProperty("提交结束时间")
    private String endDate;

    @ApiModelProperty("处理状态 0:待处理 1:处理成功 2:处理失败")
    private Integer handleStatus;

    @ApiModelProperty("推荐人工号")
    private String recommendId;

    @ApiModelProperty("登记人工号")
    private String registerUserId;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人 1:人工 2:系统",example = "1:人工 2:系统")
    private Integer processUser;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;
    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果编码")
    private String handleResult;
    /**
     * 处理结果详情
     */
    @ApiModelProperty(value = "处理结果内容")
    private String handleResultDetail;

    /**
     * 参数校验
     */
    public void check() {
        if (page <= 0 || size < 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分页参数错误，页码不能为负数"));
        }
        if (size > PageInfo.MAX_PAGE_SIZE) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分页参数错误，查询条数超过限制"));
        }
    }

    public void initDate() {
        super.init();
        if (StringUtils.isNotBlank(startDate)) {
            if (!startDate.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("提交开始时间-查询开始时间：格式不正确"));
            }
        }
        if (StringUtils.isNotBlank(endDate)) {
            if (!endDate.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("提交结束时间-查询结束时间：格式不正确"));
            }
            LocalDate queryTime = LocalDateUtil.parseLocalDate(endDate);
            queryTime = queryTime.plusDays(1);
            endDate = LocalDateUtil.formatLocalDate(queryTime);
        }
    }
}
