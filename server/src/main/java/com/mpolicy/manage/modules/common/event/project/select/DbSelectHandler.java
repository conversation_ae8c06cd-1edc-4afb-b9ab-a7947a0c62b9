package com.mpolicy.manage.modules.common.event.project.select;

import com.mpolicy.manage.enums.SelectEnum;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.common.event.factory.SelectFactory;
import com.mpolicy.manage.modules.common.event.handler.SelectHandler;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.common.model.SelectVo;
import com.mpolicy.manage.modules.contract.service.ContractInnerSignatoryService;
import com.mpolicy.manage.modules.protocol.service.IEpProtocolInsuranceProductDetailService;
import com.mpolicy.manage.modules.sys.service.SysScriptManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DbSelectHandler extends SelectHandler {

    @Autowired
    private SysScriptManageService sysScriptManageService;
    @Autowired
    private ChannelInfoService channelInfoService;
    @Autowired
    private ContractInnerSignatoryService contractInnerSignatoryService;

    @Autowired
    private IEpProtocolInsuranceProductDetailService protocolInsuranceProductDetailService;

    @Override
    public List<SelectOut> findSelectList(SelectVo select) {
        switch (Objects.requireNonNull(SelectEnum.matchSearchCode(select.getSelectType()))) {
            case SCRIPT_LIST:
                // 脚本列表
                return sysScriptManageService.lambdaQuery()
                        .list().stream().map(m -> {
                            SelectOut out = new SelectOut();
                            out.setLabel(m.getScriptTitle());
                            out.setValue(m.getScriptCode());
                            return out;
                        }).collect(Collectors.toList());
            case POLICY_CHANNEL:
                // 保单销售渠道
                return channelInfoService.lambdaQuery()
                        .list().stream().map(m -> {
                            SelectOut out = new SelectOut();
                            out.setLabel(m.getChannelName());
                            out.setValue(m.getChannelCode());
                            return out;
                        }).collect(Collectors.toList());
            case CONTRACT_INNER_SIGNATORY:
                // 合约内部签署方
                return contractInnerSignatoryService.lambdaQuery()
                        .list().stream().map(m -> {
                            SelectOut out = new SelectOut();
                            out.setLabel(m.getInnerSignatoryName());
                            out.setValue(m.getInnerSignatoryCode());
                            return out;
                        }).collect(Collectors.toList());
            case PROTOCOL_INSURANCE_PRODUCT_COMPANY: {
                // 协议险种所属分公司
                return protocolInsuranceProductDetailService.findInsuranceProductCompanyList(0);
            }
            case CONTRACT_INSURANCE_PRODUCT_COMPANY: {
                // 合约险种所属分公司
                return protocolInsuranceProductDetailService.findInsuranceProductCompanyList(1);
            }
            default:
                return Collections.emptyList();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //脚本列表
        SelectFactory.register(SelectEnum.SCRIPT_LIST, this);
        SelectFactory.register(SelectEnum.POLICY_CHANNEL, this);
        SelectFactory.register(SelectEnum.CONTRACT_INNER_SIGNATORY, this);
        SelectFactory.register(SelectEnum.PROTOCOL_INSURANCE_PRODUCT_COMPANY, this);
        SelectFactory.register(SelectEnum.CONTRACT_INSURANCE_PRODUCT_COMPANY, this);
    }
}
