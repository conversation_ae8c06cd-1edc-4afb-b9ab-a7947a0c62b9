package com.mpolicy.manage.modules.customer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ClassName: CustomerLogOut
 * Description: 客户注销记录列表
 * date: 2023/8/16 14:11
 *
 * <AUTHOR>
 */
@Data
public class CustomerLogOut implements Serializable {
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "操作人姓名")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date createTime;
}
