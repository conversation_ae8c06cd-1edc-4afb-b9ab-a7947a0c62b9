package com.mpolicy.manage.modules.sell.entity;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ExportInsurancePortfolioListOut implements Serializable {
    private static final long serialVersionUID = 6339180201246343949L;
    @Alias("序号")
    private Integer id;
    @Alias("组合编码")
    private String portfolioCode;

    @Alias("组合名称")
    private String portfolioName;

    @Alias("保险公司")
    private String companyName;

    @Alias("类别")
    private String portfolioGroup;

    /**
     * 组合保障类型 字典
     */
    @Alias("类型")
    private String portfolioType;

    /**
     * 是否加入计划书 0否/1是
     */
    @Alias("是否加入计划书")
    private String joinPlan;

    /**
     * 是否加入产品对比 0否/1是
     */
    @Alias("是否加入产品对比")
    private String joinComparison;
    /**
     * 有无售卖规则 0否/1是
     */
    @Alias("有无售卖规则")
    private String rulesFlag;

    @Alias("该组合在系统中链接的险种编码")
    private String productCode;


}
