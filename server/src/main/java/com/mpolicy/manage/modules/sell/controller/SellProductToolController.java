package com.mpolicy.manage.modules.sell.controller;

import com.mpolicy.manage.modules.sell.service.ISellProductToolService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "销售产品配置")
@RestController
@RequestMapping("sell/product/tool")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SellProductToolController {

    private final ISellProductToolService sellProductToolService;



}
