package com.mpolicy.manage.modules.policy.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.policy.client.InsuranceBffClient;
import com.mpolicy.manage.modules.policy.service.AiConversationService;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsQueryVo;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * AI对话记录服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
@Slf4j
public class AiConversationServiceImpl implements AiConversationService {

    @Autowired
    private InsuranceBffClient insuranceBffClient;

    @Override
    public ConversationRecordsResponseVo getConversationRecords(ConversationRecordsQueryVo queryVo) {
        log.info("查询AI对话记录，参数：{}", JSON.toJSONString(queryVo));
        
        try {
            Result<ConversationRecordsResponseVo> result = insuranceBffClient.getConversationRecords(
                    queryVo.getPage(),
                    queryVo.getPageSize(),
                    queryVo.getKeyword(),
                    queryVo.getUserName(),
                    queryVo.getStartTime(),
                    queryVo.getEndTime()
            );
            
            log.info("调用Insurance BFF Project服务返回结果：{}", JSON.toJSONString(result));
            
            if (!result.isSuccess()) {
                log.error("调用Insurance BFF Project服务失败，错误信息：{}", result.getMsg());
                throw new GlobalException(result);
            }
            
            return result.getData();
            
        } catch (Exception e) {
            log.error("查询AI对话记录异常，参数：{}，异常信息：{}", JSON.toJSONString(queryVo), e.getMessage(), e);
            throw e;
        }
    }
}
