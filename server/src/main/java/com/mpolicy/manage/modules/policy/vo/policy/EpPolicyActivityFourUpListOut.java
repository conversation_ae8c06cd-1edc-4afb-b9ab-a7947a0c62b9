package com.mpolicy.manage.modules.policy.vo.policy;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EpPolicyActivityFourUpListOut  extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 4309726377512865817L;

    /**
     * 数据id
     */
    private Integer id;

    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyCode;

    @ExcelProperty(value = "被保人姓名")
    private String insuredsName;


    /**
     * 是否四级分销
     */
    @ExcelProperty(value = "是否四级分销")
    private String fourPolicyDesc;


    @ExcelProperty(value = "原推荐人姓名")
    private String sourceManageName;

    @ExcelProperty(value = "原推荐人工号")
    private String sourceManageWno;

    @ExcelProperty(value = "原村代姓名")
    private String sourceVillageRepresentativeName;

    /**
     * 渠道推荐人
     */
    @ExcelProperty(value = "变更后推荐人")
    private String referrerName;

    /**
     * 渠道推荐人
     */
    @ExcelProperty(value = "变更后推荐人工号")
    private String referrerWno;

    /**
     * 村代姓名
     */
    @ExcelProperty(value = "变更后村代姓名")
    private String villageRepresentativeName;

    /**
     * 渠道推荐人编码
     */
    private String referrerCode;
    /**
     * 创建人
     */
    @ExcelProperty(value = "修改人")
    private String createUser;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "修改时间")
    private Date createTime;

}
