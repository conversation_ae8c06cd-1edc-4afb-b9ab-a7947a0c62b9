package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveChannelApplicationReferrerLogVo implements Serializable {
    private static final long serialVersionUID = -2779854148990914054L;

    /**
     * 原来客户编码
     */
    private String sourceReferrerCode;

    /**
     * 目标客户编码
     */
    private String targetReferrerCode;

    @ApiModelProperty(value = "移交客户类型 1移交全部客户 2移交部分客户")
    @NotNull(message = "移交客户类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "交接类型 1自动完成交接 2不交接")
    @NotNull(message = "交接类型不能为空")
    private Integer handoverType;

    @ApiModelProperty(value = "移交部分客户的customerCodeList")
    private List<String> customerCodeList;
}
