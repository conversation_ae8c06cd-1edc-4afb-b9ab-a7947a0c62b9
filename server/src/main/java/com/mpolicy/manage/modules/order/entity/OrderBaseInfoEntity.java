package com.mpolicy.manage.modules.order.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户订单信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-16 10:21:13
 */
@TableName("order_base_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderBaseInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Integer id;
	/**
	 * 订单号
	 */
	private String orderCode;
	/**
	 * 保险平台预保单号
	 */
	private String insAdvancePolicyCode;
	/**
	 * 客户编码
	 */
	private String customerCode;
	/**
	 * 代理人编码
	 */
	private String agentCode;
	/**
	 * 商品名称
	 */
	private String commodityName;
	/**
	 * 商品编码
	 */
	private String commodityCode;
	/**
	 * 公司编码
	 */
	private String companyCode;
	/**
	 * 公司名称
	 */
	private String companyName;
	/**
	 * 保单编码
	 */
	private String policyCode;
	/**
	 * 订单状态信息
	 */
	private String orderStatusMsg;
	/**
	 * 订单类型 0 活动商品 1 网销商品
	 */
	private Integer orderType;
	/**
	 * 保单生效时间
	 */
	private String policyEffectiveTime;
	/**
	 * 保单终止时间
	 */
	private String policyEndTime;
	/**
	 * 订购金额
	 */
	private String orderCash;
	/**
	 * 保单快照url
	 */
	private String policySnapshotUrl;
	/**
	 * 设备类型
	 */
	private String deviceType;
	/**
	 * ip地址
	 */
	private String ip;
	/**
	 * 订单状态 0处理中  1订单成功  2 失败  3异常 4超时
	 */
	private String orderStatus;
	/**
	 * 合同流水号(保单中心)
	 */
	private String contractCode;
	/**
	 * 自定义字段
	 */
	private String customField;
	/**
	 * 应用名称
	 */
	private String appName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private long revision;
}
