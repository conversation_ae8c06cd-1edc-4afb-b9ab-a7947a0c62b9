package com.mpolicy.manage.modules.baichuan.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 百川渠道产品信息
 *
 * <AUTHOR>
 * @since 2023-08-27 00:26:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "百川渠道产品信息", description = "百川渠道产品信息")
public class BcChannelProductInfo implements Serializable {

    private static final long serialVersionUID = 1;


    /**
     * 渠道应用编码
     */
    @ApiModelProperty(value = "渠道应用编码", example = "BC4567890")
    private String appCode;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码", example = "P87654567890")
    private String commodityCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", example = "意外险")
    private String commodityName;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期", example = "2023-09-08")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;
    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期", example = "2023-09-08")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date invalidDate;
    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息", example = "备注信息")
    private String sellDesc;
    /**
     * 是否启用;0关闭，1启用
     */
    @ApiModelProperty(value = "是否启用;0关闭，1启用", example = "1")
    private Integer sellEnable;
    /**
     * 签约类型;1长期有效2指定日期
     */
    @ApiModelProperty(value = "签约类型;1长期有效2指定日期", example = "1")
    private Integer signType;
}
