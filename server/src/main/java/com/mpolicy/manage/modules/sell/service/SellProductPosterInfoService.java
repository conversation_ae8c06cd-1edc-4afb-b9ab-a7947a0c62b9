package com.mpolicy.manage.modules.sell.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.SellProductPosterInfoEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductPosterVo;

import java.util.List;
import java.util.Map;

/**
 * 产品海报信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-02 19:38:04
 */
public interface SellProductPosterInfoService extends IService<SellProductPosterInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 删除商品海报
     */
    void deleteConfine(Integer id);

    /**
     * 添加——修改商品海报
     */
    void saveUpdate(SellProductPosterVo sellProductPosterVo);

    /**
     * 获取所有的有海报产品
     *
     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/12/8 11:38
     */
    List<JSONObject> getProductList(String companyCode);
    /**
     * 获取所有的有海报保司
     *
     * <AUTHOR>
     * @date 2022/12/20 11:14

     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     */
    List<JSONObject> getCompanyList();

}

