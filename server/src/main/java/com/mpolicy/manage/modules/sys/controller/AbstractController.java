package com.mpolicy.manage.modules.sys.controller;

import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;

/**
 * Controller公共组件
 *
 * <AUTHOR>
 */
public abstract class AbstractController {

	/**
	 * 环境
	 */
	@Value("${spring.profiles.active:dev}")
	private String profilesActive;

	protected SysUserEntity getUser() {
//		if(StringUtils.equals(profilesActive,"dev")){
//			SysUserEntity user = new SysUserEntity();
//			user.setUsername("曾华光");
//			user.setMobile("18127065162");
//			return user;
//		}
		return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
	}

	protected Long getUserId() {
		return getUser().getUserId();
	}
}
