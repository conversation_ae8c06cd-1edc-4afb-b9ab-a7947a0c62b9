package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人账号表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-08 11:05:50
 */
@TableName("agent_user_account")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentUserAccountEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号
     */
    @ApiModelProperty(hidden = true)
    @TableId(value = "account", type = IdType.INPUT)
    private String account;
    /**
     * 账号类型0:手机号+密码登录
     */
    @ApiModelProperty(value = "账号类型0:手机号+密码登录", required = true)
    private Integer accountType;
    /**
     * 加密盐
     */
    @ApiModelProperty(value = "加密盐", required = true)
    private String salt;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true)
    private String password;
    /**
     * 经纪人编码
     */
    @ApiModelProperty(value = "经纪人编码", required = true)
    private String agentCode;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
