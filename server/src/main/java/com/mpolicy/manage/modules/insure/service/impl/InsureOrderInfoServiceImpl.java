package com.mpolicy.manage.modules.insure.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.insure.dao.InsureOrderInfoDao;
import com.mpolicy.manage.modules.insure.entity.InsureFlowHistoryEntity;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInfoEntity;
import com.mpolicy.manage.modules.insure.entity.InsureOrderPersonReviewEntity;
import com.mpolicy.manage.modules.insure.enums.InsureOrderStatusEnum;
import com.mpolicy.manage.modules.insure.enums.ProductGroupEnum;
import com.mpolicy.manage.modules.insure.service.InsureFlowHistoryService;
import com.mpolicy.manage.modules.insure.service.InsureOrderInfoService;
import com.mpolicy.manage.modules.insure.service.InsureOrderPersonReviewService;
import com.mpolicy.manage.modules.insure.vo.*;
import com.mpolicy.manage.modules.order.service.TraceSerialRecallDataService;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.sys.util.DicOperatingUtils;
import com.mpolicy.order.client.InsureOrderClient;
import com.mpolicy.order.common.enums.InsureOrderTypeEnum;
import com.mpolicy.order.common.order.PolicyGroupOrderDataOut;
import com.mpolicy.order.common.order.PolicyOrderDataOut;
import com.mpolicy.order.common.refund.InsureOrderRefundInput;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 投保订单信息表接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/29
 */
@Service("insureOrderInfoService")
@Slf4j
public class InsureOrderInfoServiceImpl extends ServiceImpl<InsureOrderInfoDao, InsureOrderInfoEntity> implements InsureOrderInfoService {

    @Autowired
    private InsureOrderClient insureOrderClient;

    @Autowired
    private TraceSerialRecallDataService traceSerialRecallDataService;

    @Autowired
    private InsureOrderInfoService insureOrderInfoService;

    @Autowired
    private InsureFlowHistoryService insureFlowHistoryService;

    @Autowired
    private InsureOrderPersonReviewService insureOrderPersonReviewService;


    private static final Pattern ID_CORD_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");

    @Override
    public PageUtils<InsureOrderList> queryInsureOrderListByPage(Map<String, Object> params) {

        String insureOrderCode = (String) params.get("insureOrderCode");
        String companyCode = (String) params.get("companyCode");
        String customerCode = (String) params.get("customerCode");

        String holderName = (String) params.get("holderName");
        String holderIdNo = (String) params.get("holderName");
        if (StringUtils.isNotBlank(holderName) && ID_CORD_PATTERN.matcher(holderName).matches()) {
            params.put("holderIdNo", params.remove("holderName"));
            holderName = null;
        } else {
            holderIdNo = null;
        }

        String insuredName = (String) params.get("insuredName");
        String insuredIdNo = (String) params.get("insuredName");
        if (StringUtils.isNotBlank(insuredName) && ID_CORD_PATTERN.matcher(insuredName).matches()) {
            params.put("insuredIdNo", params.remove("insuredName"));
            insuredName = null;
        } else {
            insuredIdNo = null;
        }

        String policyCode = (String) params.get("policyCode");
        String commodityCode = (String) params.get("commodityCode");
        String holderMobile = (String) params.get("holderMobile");
        String portfolioGroup = (String) params.get("portfolioGroup");
        //  报送状态0未报送 1报送
        Integer insureOrderStatus = RequestUtils.objectValueToInteger(params, "insureOrderStatus");
        Integer ruralProxyOrderType = RequestUtils.objectValueToInteger(params, "ruralProxyOrderType");
        params.put("insureOrderStatus", insureOrderStatus);
        params.put("ruralProxyOrderType", ruralProxyOrderType);

        String channelCode = (String) params.get("channelCode");
        String agentCode = (String) params.get("agentCode");
        String referrerCode = (String) params.get("referrerCode");

        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");

        // 获取当前操作用户对象权限
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        List<String> orgList = PolicyPermissionHelper.getOrgCodeList();
        // 如果部门或者机构任何一个为空，则没有数据返回
        if ((orgList != null && orgList.isEmpty()) || (channelBranchCodeList != null && channelBranchCodeList.isEmpty())) {
            return new PageUtils<InsureOrderList>(new ArrayList<>(), 0, 1, 1);
        }
        Optional.ofNullable(channelBranchCodeList).ifPresent(item -> {
            if (!CollectionUtils.isEmpty(item)) {
                params.put("channelBranchCodeList", item);
            }
        });
        Optional.ofNullable(orgList).ifPresent(item -> {
            if (!CollectionUtils.isEmpty(item)) {
                params.put("orgList", item);
            }
        });

        // mp 分页插件下标从1开始的，前端传入从1开始
//        params.put("page", (Long.parseLong(params.getOrDefault("page", "0") + "") + 1) + "");
        params.putIfAbsent("page", "1");
        params.putIfAbsent("limit", "20");
        IPage<InsureOrderInfoEntity> page = null;

        if (StringUtils.isBlank(policyCode)) {
            // 分页查询
            page = this.page(
                    new Query<InsureOrderInfoEntity>().getPage(params),
                    new LambdaQueryWrapper<InsureOrderInfoEntity>()
                            .ne(InsureOrderInfoEntity::getInsureOrderStatus, 11)
                            .eq(StringUtils.isNotBlank(insureOrderCode), InsureOrderInfoEntity::getInsureOrderCode, insureOrderCode)
                            .eq(StringUtils.isNotBlank(companyCode), InsureOrderInfoEntity::getCompanyCode, companyCode)
                            .eq(StringUtils.isNotBlank(customerCode), InsureOrderInfoEntity::getUserNo, customerCode)
                            .eq(StringUtils.isNotBlank(holderName), InsureOrderInfoEntity::getHolderName, holderName)
                            .eq(StringUtils.isNotBlank(agentCode), InsureOrderInfoEntity::getAgentCode, agentCode)
                            .eq(StringUtils.isNotBlank(referrerCode), InsureOrderInfoEntity::getReferrerCode, referrerCode)
                            .eq(StringUtils.isNotBlank(portfolioGroup), InsureOrderInfoEntity::getPortfolioGroup, portfolioGroup)
                            .eq(ruralProxyOrderType != null, InsureOrderInfoEntity::getRuralProxyOrderType, ruralProxyOrderType)
                            .apply(StringUtils.isNotBlank(holderMobile), "insure_order_code in (SELECT insure_order_code FROM insure_order_holder WHERE deleted = 0 and holder_mobile = {0})", holderMobile)
                            .apply(StringUtils.isNotBlank(holderIdNo), "insure_order_code in (SELECT insure_order_code FROM insure_order_holder WHERE deleted = 0 and holder_certi_no = {0})", holderIdNo)
                            .apply(StringUtils.isNotBlank(insuredName), "insure_order_code in (SELECT insure_order_code FROM insure_order_insured WHERE deleted = 0 and insured_name = {0})", insuredName)
                            .apply(StringUtils.isNotBlank(insuredIdNo), "insure_order_code in (SELECT insure_order_code FROM insure_order_insured WHERE deleted = 0 and insured_certi_no = {0})", insuredIdNo)
                            .eq(StringUtils.isNotBlank(channelCode), InsureOrderInfoEntity::getUserChannelCode, channelCode)
                            .eq(StringUtils.isNotBlank(commodityCode), InsureOrderInfoEntity::getCommodityCode, commodityCode)
                            .eq(insureOrderStatus != null, InsureOrderInfoEntity::getInsureOrderStatus, insureOrderStatus)
                            .in(orgList != null, InsureOrderInfoEntity::getOrgCode, orgList)
                            .in(channelBranchCodeList != null, InsureOrderInfoEntity::getChannelBranchCode, channelBranchCodeList)
                            .apply(StringUtils.isNotBlank(startDate), "date_format(create_time,'%Y-%m-%d') >= {0}", startDate)
                            .apply(StringUtils.isNotBlank(endDate), "date_format(create_time,'%Y-%m-%d') <= {0}", endDate)
                            .orderByDesc(InsureOrderInfoEntity::getId)
            );
        } else {
            page = baseMapper.queryInsureOrderInfoByPage(new Page(Long.parseLong(params.get("page").toString()), Long.parseLong(params.get("limit").toString())), params);
        }

        // 构建返回的vo list
        List<InsureOrderList> result = new ArrayList<>();
        page.getRecords().forEach(x -> {
            InsureOrderList bean = new InsureOrderList();
            BeanUtils.copyProperties(x, bean);

            ProductGroupEnum groupCodeEnum = ProductGroupEnum.getGroupCodeEnum(x.getPortfolioGroup());
            if (groupCodeEnum != null) {
                bean.setPortfolioGroupDesc(groupCodeEnum.getGroupName());
            }
            // 如果为财险类型，不需要显示 投保订单类型
            if (ProductGroupEnum.CX == groupCodeEnum) {
                bean.setInsureType("--");
            } else {
                bean.setInsureType(InsureOrderTypeEnum.decode(x.getInsureOrderType()).getName());
            }
            // 投保/核保时间
            if (x.getInsureTime() != null) {
                bean.setInsureTime(DateUtil.formatDateTime(x.getInsureTime()));
            }
            // 承保时间
            if (x.getUnderwriteTime() != null) {
                bean.setUnderwriteTime(DateUtil.formatDateTime(x.getUnderwriteTime()));
            }
            // 生效日期
            if (x.getEffectiveDate() != null) {
                bean.setEffectiveDate(DateUtil.formatDate(x.getEffectiveDate()));
            }
            // 支付时间
            if (x.getInsurePayTime() != null) {
                bean.setInsurePayTime(DateUtil.formatDateTime(x.getInsurePayTime()));
            }
            // 创建时间
            if (x.getCreateTime() != null) {
                bean.setCreateTime(DateUtil.formatDateTime(x.getCreateTime()));
            }

            // 投保状态描述
            bean.setInsureOrderStatusDesc(InsureOrderStatusEnum.decode(bean.getInsureOrderStatus()).getName());
            result.add(bean);
        });

        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }


    @Override
    public PolicyOrderDataOut queryInsureOrderDetail(String insureOrderCode) {
        // 调用投保中心服务的feign 获取投保订单详情
        Result<PolicyOrderDataOut> insureOrderDetail = insureOrderClient.getInsureOrderDetail(insureOrderCode, false);
        log.info("获取投保订单详情，请求订单唯一编号=[{}],响应结果=[{}]", insureOrderCode, JSON.toJSONString(insureOrderDetail));
        if (!insureOrderDetail.isSuccess()) {
            throw new GlobalException(insureOrderDetail);
        }
        return insureOrderDetail.getData();
    }

    @Override
    public PolicyGroupOrderDataOut groupDetail(String insureOrderCode) {
        // 调用投保中心服务的feign 获取投保订单详情
        Result<PolicyGroupOrderDataOut> insureOrderDetail = insureOrderClient.groupDetail(insureOrderCode, false);
        log.info("获取投保订单详情，请求订单唯一编号=[{}],响应结果=[{}]", insureOrderCode, JSON.toJSONString(insureOrderDetail));
        if (!insureOrderDetail.isSuccess()) {
            throw new GlobalException(insureOrderDetail);
        }
        return insureOrderDetail.getData();
    }

    @Override
    public InsureRecallOut queryInsureRecallData(String insureOrderCode) {
        // 1-1 【获取】投保订单数据不存在
        InsureOrderInfoEntity orderInfo = Optional.ofNullable(
                lambdaQuery()
                        .eq(InsureOrderInfoEntity::getInsureOrderCode, insureOrderCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("投保订单数据不存在")));
        InsureRecallOut result = new InsureRecallOut();
        BeanUtils.copyProperties(orderInfo, result);
        result.setInsureType(InsureOrderTypeEnum.decode(orderInfo.getInsureOrderType()).getName());
        // 投保/核保时间
        if (orderInfo.getInsureTime() != null) {
            result.setInsureTime(DateUtil.formatDateTime(orderInfo.getInsureTime()));
        }
        // 承保时间
        if (orderInfo.getUnderwriteTime() != null) {
            result.setUnderwriteTime(DateUtil.formatDateTime(orderInfo.getUnderwriteTime()));
        }
        // 生效日期
        if (orderInfo.getEffectiveDate() != null) {
            result.setEffectiveDate(DateUtil.formatDate(orderInfo.getEffectiveDate()));
        }
        // 支付时间
        if (orderInfo.getInsurePayTime() != null) {
            result.setInsurePayTime(DateUtil.formatDateTime(orderInfo.getInsurePayTime()));
        }
        // 投保状态描述
        result.setInsureOrderStatusDesc(InsureOrderStatusEnum.decode(result.getInsureOrderStatus()).getName());
        result.setRecallList(traceSerialRecallDataService.getPolicyTraceSerialRecallData(insureOrderCode));
        return result;
    }

    @Override
    public void changeRecallSts(String insureOrderCode, String stsFilePath) {
        InsureOrderInfoEntity orderInfo = Optional.ofNullable(
                lambdaQuery()
                        .eq(InsureOrderInfoEntity::getInsureOrderCode, insureOrderCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("投保订单数据不存在")));
        if (orderInfo.getIsSts().equals(1)) {

        }
        orderInfo.setIsSts(1);
        orderInfo.setStsFilePath(stsFilePath);
        this.updateById(orderInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void personReview(InsurePersonReviewInput input) {
        if (input.getReviewResult() == 0 && StringUtils.isBlank(input.getReviewRemark())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("审核不通过，请说明原因"));
        }
        // 获取订单
        InsureOrderInfoEntity orderInfoEntity = Optional.ofNullable(insureOrderInfoService.lambdaQuery()
                .eq(InsureOrderInfoEntity::getInsureOrderCode, input.getInsureOrderCode())
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("订单不存在")));
        if (orderInfoEntity.getInsureOrderStatus() != InsureOrderStatusEnum.PERSON_REVIEW.getCode()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该订单状态不允许进行审核操作"));
        }
        // 审核结果记录
        InsureOrderPersonReviewEntity personReview = new InsureOrderPersonReviewEntity();
        BeanUtils.copyProperties(input, personReview);
        insureOrderPersonReviewService.save(personReview);
        // 审核通过
        if (input.getReviewResult() == 1) {
            orderInfoEntity.setInsureOrderStatus(InsureOrderStatusEnum.STAY_SUBMIT.getCode());
            insureOrderInfoService.updateById(orderInfoEntity);
            InsureFlowHistoryEntity entity = Optional.ofNullable(insureFlowHistoryService.lambdaQuery()
                    .eq(InsureFlowHistoryEntity::getInsureOrderCode, input.getInsureOrderCode())
                    .eq(InsureFlowHistoryEntity::getOperateType, "customer_notice")
                    .eq(InsureFlowHistoryEntity::getDeleted, 0)
                    .orderByDesc(InsureFlowHistoryEntity::getId)
                    .last(" limit 1")
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("订单数据获取异常")));
            InsureFlowHistoryEntity historyEntity = new InsureFlowHistoryEntity();
            historyEntity.setInsureOrderCode(entity.getInsureOrderCode());
            historyEntity.setSourceInsureOrderCode(entity.getSourceInsureOrderCode());
            historyEntity.setUserNo(entity.getUserNo());
            historyEntity.setOperateType("ins_person_review");
            historyEntity.setOperateName("人工审核");
            historyEntity.setRequestData(entity.getRequestData());
            historyEntity.setResponseData(entity.getRequestData());
            historyEntity.setOperateRemark("后台人工审核");
            insureFlowHistoryService.save(historyEntity);
        } else {
            orderInfoEntity.setInsureOrderStatus(InsureOrderStatusEnum.INS_UPDATE.getCode());
            insureOrderInfoService.updateById(orderInfoEntity);
        }
    }

    @Override
    public void orderRefund(InsureOrderRefundInfo input) {
        InsureOrderRefundInput refundInput = new InsureOrderRefundInput();
        refundInput.setInsureOrderCode(input.getInsureOrderCode());
        refundInput.setRefundUser(ShiroUtils.getUserEntity().getUsername());
        refundInput.setRefundReason(input.getRefundReason());
        Result<String> result = insureOrderClient.orderRefund(refundInput);
        log.info("订单发起撤单，请求订单唯一编号=[{}],响应结果=[{}]", JSON.toJSONString(refundInput), JSON.toJSONString(result));
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
    }

    @Override
    public List<insureOrderInfoOut> queryInsureOrderList(Map<String, Object> params) {
        List<insureOrderInfoOut> list = new ArrayList<>();
        // 获取当前操作用户对象权限
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        List<String> orgList = PolicyPermissionHelper.getOrgCodeList();
        // 如果部门或者机构任何一个为空，则没有数据返回
        if ((orgList != null && orgList.isEmpty()) || (channelBranchCodeList != null && channelBranchCodeList.isEmpty())) {
            return list;
        }
        params.put("channelBranchCodeList", channelBranchCodeList);
        params.put("orgList", orgList);
        IPage<insureOrderInfoOut> obj = this.baseMapper.queryInsureOrderList(new Page(Long.valueOf(params.get("page").toString()), Long.valueOf(params.get("limit").toString())), params);
        list = obj.getRecords();
        JSONArray cacheDicList = JSON.parseArray(ConstantCacheHelper.getValue("sysDictionaryMapper_dict", "[]"));
        if (CollectionUtils.isEmpty(cacheDicList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("投保字典配置缓存缺失"));
        }
        list.forEach(x -> {
            x.setInsureOrderStatusDesc(InsureOrderStatusEnum.decode(x.getInsureOrderStatus()).getName());
            x.setHolderCertiCode(DicOperatingUtils.getInsureDictValue(cacheDicList, x.getHolderCertiCode()));
            x.setInsuredCertiCode(DicOperatingUtils.getInsureDictValue(cacheDicList, x.getInsuredCertiCode()));
            x.setInsuredOwnerRela(DicOperatingUtils.getInsureDictValue(cacheDicList, x.getInsuredOwnerRela()));
        });
        return list;
    }

    @Override
    public List<insureOrderInfoProOut> znQueryInsureOrderList(Map<String, Object> params) {
        IPage<insureOrderInfoProOut> obj = this.baseMapper.znQueryInsureOrderList(new Page(Long.valueOf(params.get("page").toString()), Long.valueOf(params.get("limit").toString())), params);
        List<insureOrderInfoProOut> list = obj.getRecords();
        list.forEach(x -> {
            x.setInsureOrderStatusDesc(InsureOrderStatusEnum.decode(x.getInsureOrderStatus()).getName());
        });
        return list;
    }
}
