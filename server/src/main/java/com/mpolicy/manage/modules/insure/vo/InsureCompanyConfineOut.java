package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 保司产品升级详情
 *
 * <AUTHOR>
 * @date 2022-10-09 13:42
 */
@Data
@ApiModel(value = "保司产品升级详情")
public class InsureCompanyConfineOut {

    @ApiModelProperty(value = "升级控制编码", example = "ICF1234567890")
    private String confineCode;

    @ApiModelProperty(value = "标题", example = "标题")
    private String title;

    @ApiModelProperty(value = "保司编码", example = "DCRS000000")
    private String companyCodes;

    @ApiModelProperty(value = "产品编码", example = "P20210331113718425004")
    private String productCodes;

    @ApiModelProperty(value = "客户端类型 1:小程序", example = "1")
    private Integer clientType;

    @ApiModelProperty(value = "客户端类型 1:小程序", example = "小程序")
    private String clientDesc;

    @ApiModelProperty(value = "产品名称", example = "妈咪保贝（新生版）少儿重大疾病保险")
    private String productNames;

    @ApiModelProperty(value = "保司名称", example = "鼎诚人寿保险有限责任公司")
    private String companyNames;

    @ApiModelProperty(value = "是否启用 0:停用;1:启用", example = "1")
    private Integer isStatus;

    @ApiModelProperty(value = "升级开始时间", example = "2022-10-13 12:38:23")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confineStartTime;

    @ApiModelProperty(value = "升级结束时间", example = "2022-10-13 12:38:23")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confineEndTime;

    @ApiModelProperty(value = "升级说明", example = "1")
    private String confineDesc;

    @ApiModelProperty(value = "配置时间", example = "2022-10-13 12:38:23")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "操作人", example = "张三")
    private String updateUser;
}
