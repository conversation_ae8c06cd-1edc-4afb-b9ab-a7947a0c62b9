package com.mpolicy.manage.modules.order.enums;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 投保订单客户风险等级枚举
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@AllArgsConstructor
@JSONType(serializeEnumAsJavaBean = true)
public enum RatingLevelEnum {
    rating_Level_R0(null,null,"R0","最低风险等级"),
    rating_Level_R1(null,900,"R1","区间风险等级"),
    rating_Level_R2(900,850,"R2","区间风险等级"),
    rating_Level_R3(850,800,"R3","区间风险等级"),
    rating_Level_R4(800,750,"R4","区间风险等级"),
    rating_Level_R5(750,700,"R5","区间风险等级"),
    rating_Level_R6(700,650,"R6","区间风险等级"),
    rating_Level_R7(650,620,"R7","区间风险等级"),
    rating_Level_R8(620,600,"R8","区间风险等级"),
    rating_Level_R9(600,null,"R9","区间风险等级"),
    rating_Level_RMAX(null,null,"Rmax","最高风险等级"),
    ;

    private final Integer creditScoreMax;
    private final Integer creditScoreMin;
    private final String ratingLevel;
    private final String description;

    public static RatingLevelEnum getByRatingLevel(String ratingLevel){
        for (RatingLevelEnum ratingLevelEnum : values()) {
            if (ratingLevelEnum.getRatingLevel().equals(ratingLevel)) {
                return ratingLevelEnum;
            }
        }
        return null;
    }

    @JSONField(name = "creditScoreMax")
    public Integer getCreditScoreMax() {
        return creditScoreMax;
    }
    @JSONField(name = "creditScoreMin")
    public Integer getCreditScoreMin() {
        return creditScoreMin;
    }
    @JSONField(name = "ratingLevel")
    public String getRatingLevel() {
        return ratingLevel;
    }

    @JSONField(name = "description")
    public String getDescription() {
        return description;
    }
}
