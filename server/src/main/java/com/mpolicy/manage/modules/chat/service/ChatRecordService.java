package com.mpolicy.manage.modules.chat.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.chat.entity.ChatRecordListOut;
import com.mpolicy.manage.modules.chat.entity.ChatRecordListVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ChatRecordService {

    /**
     * 获取聊天记录表列表
     *
     * @param vo
     * @return
     */
    PageUtils<List<ChatRecordListOut>> findChatRecordList(ChatRecordListVo vo);
}
