package com.mpolicy.manage.modules.policy.vo.preservation.check;


import com.google.common.base.Joiner;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationApplicantChangeForm;
import com.mpolicy.policy.common.channel.sync.vo.policy.preservation.channel.PreservationCheckResultVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "保全项检查：接口状态码正常的情况下，以下报文为警告项，前端提示给客户，但是不会阻断流程")
public class PreservationApplyCheckResult {

    private static final String WARNING_CODE = "1";
    private static final String SUCCESS_CODE = "0";
    private static final String ERR_CODE = "-1";

    @ApiModelProperty("校验结果:0=校验正常；1=告警弹框提示(不阻断业务)；-1=校验失败,需要阻断流程；")
    private String code = "0";

    @ApiModelProperty("错误提示信息")
    private String message;

    @ApiModelProperty("初始渠道推荐人变更保全校验结果")
    private CustomerManagerChangeCheckVo customerManagerChangeCheckVo;

    //遗留的坑
    @ApiModelProperty(value = "投保人基础信息变更")
    PreservationApplicantChangeForm preservationApplicantChangeForm;

    public static PreservationApplyCheckResult success(){
        return new PreservationApplyCheckResult();
    }

    public static PreservationApplyCheckResult err(String errMsg){
        PreservationApplyCheckResult result = new PreservationApplyCheckResult();
        result.setCode(ERR_CODE);
        result.setMessage(errMsg);
        return result;
    }
    public static PreservationApplyCheckResult err(List<String> errMsg){
        PreservationApplyCheckResult result = new PreservationApplyCheckResult();
        result.setCode(ERR_CODE);
        result.setMessage(Joiner.on("；").join(errMsg));
        return result;
    }

    public boolean isSuccess(){
        return Objects.equals(code, SUCCESS_CODE);
    }

    public boolean err(){
        return Objects.equals(code, ERR_CODE);
    }
}
