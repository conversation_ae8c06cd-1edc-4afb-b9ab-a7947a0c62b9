package com.mpolicy.manage.modules.sell.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.sell.service.SellProductUpdateStatusLogService;
import com.mpolicy.manage.modules.sell.vo.SellProductUpdateStatusLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 销售产品更变状态日志记录controller
 *
 *
 * @create 2024/11/21
 * @since 1.0.0
 */
@Slf4j
@Api(tags = "销售产品更变状态日志记录controller")
@RestController
@RequestMapping("sell/product/update/status/log")
public class SellProductUpdateStatusLogController {

    @Resource
    private SellProductUpdateStatusLogService sellProductUpdateStatusLogService;



    @ApiOperation(value = "获取商品所有的更变状态记录", notes = "获取商品所有的更变状态记录")
    @GetMapping("list/{productCode}")
    public Result<List<SellProductUpdateStatusLogVo>> list(@PathVariable String productCode) {
        List<SellProductUpdateStatusLogVo> sellProductUpdateStatusLogVos = sellProductUpdateStatusLogService.getListVOByProductCode(productCode);
        return Result.success(sellProductUpdateStatusLogVos);
    }
}
