package com.mpolicy.manage.modules.sell.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.dao.InsureCompanyConfineDao;
import com.mpolicy.manage.modules.sell.dao.SellProductPosterInfoDao;
import com.mpolicy.manage.modules.sell.entity.SellProductPosterInfoEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductPosterVo;
import com.mpolicy.manage.modules.sell.enums.InsureMarketingCategoryEnum;
import com.mpolicy.manage.modules.sell.enums.InsureSourceSystemEnum;
import com.mpolicy.manage.modules.sell.enums.MarketingAttachmentTypeEnum;
import com.mpolicy.manage.modules.sell.service.SellProductPosterInfoService;
import com.mpolicy.open.client.CfpamfOpenApiClient;
import com.mpolicy.open.common.cfpamf.operation.MarketingAttachment;
import com.mpolicy.open.common.cfpamf.operation.MarketingInfoInput;
import com.mpolicy.open.common.cfpamf.operation.PutDownMarketingInput;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("sellProductPosterInfoService")
public class SellProductPosterInfoServiceImpl extends ServiceImpl<SellProductPosterInfoDao, SellProductPosterInfoEntity> implements SellProductPosterInfoService {

    @Resource
    private InsureCompanyConfineDao insureCompanyConfineDao;

    @Autowired
    private CfpamfOpenApiClient cfpamfOpenApiClient;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String productCode = (String) params.get("productCode");
        String companyCode = (String) params.get("companyCode");
        IPage<SellProductPosterInfoEntity> page = this.page(
                new Query<SellProductPosterInfoEntity>().getPage(params),
                Wrappers.<SellProductPosterInfoEntity>lambdaQuery()
                        .eq(StringUtils.isNotBlank(companyCode), SellProductPosterInfoEntity::getCompanyCode, companyCode)
                        .eq(StringUtils.isNotBlank(productCode), SellProductPosterInfoEntity::getProductCode, productCode)

        );
        ArrayList<SellProductPosterVo> list = Lists.newArrayList();
        for (SellProductPosterInfoEntity entity : page.getRecords()) {
            SellProductPosterVo out = new SellProductPosterVo();
            BeanUtils.copyProperties(entity, out);
            list.add(out);
        }
        return new PageUtils<>(list, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public void deleteConfine(Integer id) {
        SellProductPosterInfoEntity entity = this.getById(id);
        if (entity == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("海报信息不存在"));
        }
        //素材圈下架操作
        putDownMaterial(String.valueOf(id));
        this.removeById(entity);
    }

    @Override
    public void saveUpdate(SellProductPosterVo sellProductPosterVo) {
        SellProductPosterInfoEntity entity = new SellProductPosterInfoEntity();
        if (sellProductPosterVo.getId() != null) {
            entity = this.getById(sellProductPosterVo.getId());
            if (entity == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("海报信息不存在"));
            }
        }
        if (!StringUtils.equals(sellProductPosterVo.getProductCode(), entity.getProductCode())) {
            List<SellProductPosterInfoEntity> list = this.lambdaQuery()
                    .eq(SellProductPosterInfoEntity::getProductCode, sellProductPosterVo.getProductCode())
                    .list();
            if (CollectionUtils.isNotEmpty(list)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该商品已经配置海报"));
            }
        }
        //获取update操作的Id,如果是新增则为null
        SellProductPosterInfoEntity oldEntity = null;
        if(Objects.nonNull(entity.getId())){
            oldEntity = new SellProductPosterInfoEntity();
            //复制原海报记录属性为后续判断是否需要下架素材做准备
            BeanUtils.copyProperties(entity,oldEntity);
        }
        BeanUtils.copyProperties(sellProductPosterVo, entity);
        this.saveOrUpdate(entity);
        //同步素材圈处理 materialHandle
        try{
            materialHandle(sellProductPosterVo,entity,oldEntity);
        }catch(Exception e){
            log.warn("同步素材圈异常 req= {}",JSON.toJSONString(sellProductPosterVo),e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("同步素材圈异常"));
        }
    }

    @Override
    public List<JSONObject> getProductList(String companyCode) {
        ArrayList<JSONObject> arrayList = Lists.newArrayList();
        List<SellProductPosterInfoEntity> list = this.lambdaQuery().list();
        if (CollectionUtils.isNotEmpty(list)) {
            JSONObject param = new JSONObject();
            param.put("productCodes", list.stream().map(SellProductPosterInfoEntity::getProductCode).collect(Collectors.toList()));
            if(StringUtils.isNotBlank(companyCode)){
                param.put("companyCodes", Arrays.asList(companyCode));
            }
            return insureCompanyConfineDao.getProductList(param);
        }
        return arrayList;
    }

    @Override
    public List<JSONObject> getCompanyList() {
        ArrayList<JSONObject> arrayList = Lists.newArrayList();
        List<SellProductPosterInfoEntity> list = this.lambdaQuery().list();
        Map<String, SellProductPosterInfoEntity> collect = list.stream().collect(Collectors.toMap(SellProductPosterInfoEntity::getCompanyCode, x -> x, (x, y) -> x));
        collect.forEach((k,y) -> {
            JSONObject object = new JSONObject();
            object.put("companyCode",k);
            object.put("companyName",y.getCompanyName());
            arrayList.add(object);
        });
        return arrayList;
    }

    /**
     * 同步素材圈添加素材
     * @param sellProductPosterVo
     * @param id
     */
    private void addMaterial(SellProductPosterVo sellProductPosterVo, Integer id){
        MarketingInfoInput request = new MarketingInfoInput();
        request.setProductCode(sellProductPosterVo.getProductCode());
        request.setProductName(sellProductPosterVo.getProductName());
        request.setCategoryCode(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode());
        request.setContent(sellProductPosterVo.getPosterText());
        request.setAttachmentType(MarketingAttachmentTypeEnum.IMAGE.getAttachmentType());
        request.setSourceSystemCode(InsureSourceSystemEnum.PRODUCT_CENTER.getSystemCode());
        request.setMaterialCode(String.valueOf(id));
        request.setLv1CategoryCode(sellProductPosterVo.getLv1CategoryCode());
        request.setLv2CategoryCode(sellProductPosterVo.getLv2CategoryCode());
        List<MarketingAttachment> marketingAttachmentAddDTOS = buildMarketingAttachmentList(sellProductPosterVo);
        request.setMarketingAttachmentAddDTOS(marketingAttachmentAddDTOS);
        log.info("addMaterial req= {}", JSON.toJSONString(request));
        cfpamfOpenApiClient.addMaterial("addMaterial",request);
    }

    /**
     * 同步素材圈处理
     * @param sellProductPosterVo
     * @param entity
     * @param oldEntity 更新前的对象
     */
    private void materialHandle(SellProductPosterVo sellProductPosterVo, SellProductPosterInfoEntity entity, SellProductPosterInfoEntity oldEntity){
        if(Objects.isNull(sellProductPosterVo.getId())){
            //新增操作创建素材
            addMaterial(sellProductPosterVo,entity.getId());
            return;
        }
        //商品、图片、文案任意项修改保存需触发下架素材圈对应素材操作
        if(
                !(Objects.equals(sellProductPosterVo.getProductCode(), oldEntity.getProductCode())
                        && Objects.equals(sellProductPosterVo.getPosterText(), oldEntity.getPosterText())
                        && Objects.equals(sellProductPosterVo.getFilePath(), oldEntity.getFilePath()))
        ){
            //素材圈下架操作
            putDownMaterial(String.valueOf(oldEntity.getId()));
            //新增操作创建素材
            addMaterial(sellProductPosterVo,entity.getId());
        }
    }

    /**
     * 素材圈下架操作
     * @param sourceSystemCode
     */
    private void putDownMaterial(String sourceSystemCode){
        PutDownMarketingInput request = new PutDownMarketingInput();
        request.setMaterialCode(sourceSystemCode);
        request.setCategoryCode(InsureMarketingCategoryEnum.PRODUCT.getCategoryCode());
        request.setSourceSystemCode(InsureSourceSystemEnum.PRODUCT_CENTER.getSystemCode());
        log.info("putDownMaterial req= {}", JSON.toJSONString(request));
        cfpamfOpenApiClient.putDownMaterial("putDownMaterial",request);
    }

    /**
     * 构建素材圈素材附件
     * @param sellProductPosterVo
     * @return
     */
    private List<MarketingAttachment> buildMarketingAttachmentList(SellProductPosterVo sellProductPosterVo){
        List<MarketingAttachment> marketingAttachmentAddDTOS = Lists.newArrayList();
        MarketingAttachment marketingAttachment = new MarketingAttachment();
        marketingAttachment.setFileUrl(sellProductPosterVo.getFilePath());
        String fileName = StringUtils.substring(sellProductPosterVo.getFilePath(), sellProductPosterVo.getFilePath().lastIndexOf("/") + 1);
        marketingAttachment.setOriginalFileName(fileName);
        marketingAttachment.setFolderName(InsureSourceSystemEnum.PRODUCT_CENTER.getSystemCode());
        marketingAttachmentAddDTOS.add(marketingAttachment);
        return marketingAttachmentAddDTOS;
    }
}
