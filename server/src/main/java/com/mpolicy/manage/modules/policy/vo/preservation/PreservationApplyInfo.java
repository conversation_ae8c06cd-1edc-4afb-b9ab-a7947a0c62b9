package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保全申请基本信息
 *
 * <AUTHOR>
 * @date 2022-03-21 10:39
 */
@ApiModel(value = "保全申请基本信息")
@Data
public class PreservationApplyInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 保全流水号
     */
    @ApiModelProperty(value = "保全流水号", example = "PR202102102020")
    private String preservationCode;

    /**
     * 保单中心保单唯一号
     */
    @ApiModelProperty(value = "保单中心保单唯一号", example = "C202102102020")
    private String contractCode;

    /**
     * 保全批单号
     */
    @ApiModelProperty(value = "保全批单号", example = "C202102102020")
    private String endorsementNo;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "P202102102020")
    private String policyCode;

    @ApiModelProperty(value = "保全续期期数", example = "1")
    private Integer renewalTermPeriod;

    /**
     * 保单名称
     */
    @ApiModelProperty(value = "保单名称", example = "小鲸向海尊享意外")
    private String policyName;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "保单名称", example = "人生险")
    private String policyType;

    /**
     * 保全类型编码
     */
    @ApiModelProperty(value = "保全类型编码", example = "POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE")
    private String preservationType;

    /**
     * 保全类型名称
     */
    @ApiModelProperty(value = "保全类型名称", example = "保单基本信息变更")
    private String preservationTypeName;

    /**
     * 保全项目编码
     */
    @ApiModelProperty(value = "保全项目编码", example = "POLICY:PRESERVATION:PROJECT:SURRENDER")
    private String preservationProject;

    /**
     * 保全项目名称
     */
    @ApiModelProperty(value = "保全项目名称", example = "退保")
    private String preservationProjectName;

    /**
     * 保全变更原因编码
     */
    @ApiModelProperty(value = "保全变更原因编码", example = "POLICY:PRESERVATION:WHY:CUSTOMER_IN_FORM")
    private String preservationWhy;

    /**
     * 保全变更原因名称
     */
    @ApiModelProperty(value = "保全变更原因名称", example = "客户主动告知")
    private String preservationWhyName;


    /**
     * 保全变更原因编码
     */
    @ApiModelProperty(value = "附加险解约类型编码", example = "POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:TERMINATION_PRODUCT:HESITATION_CANCEL")
    private String terminationProductType;

    /**
     * 保全变更原因名称
     */
    @ApiModelProperty(value = "附加险解约类型名称", example = "犹豫期内退保")
    private String terminationProductName;

    /**
     * 保司编码
     */
    @ApiModelProperty(value = "保司编码", example = "SC2050505")
    private String companyCode;

    /**
     * 保司名称
     */
    @ApiModelProperty(value = "保司名称", example = "中国平安")
    private String companyName;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名", example = "张三")
    private String holderName;

    /**
     * 投保人证据号码
     */
    @ApiModelProperty(value = "投保人证据号码", example = "3147650123456")
    private String holderIdNo;

    /**
     * 退保金额
     */
    @ApiModelProperty(value = "退保金额", example = "120.2")
    private BigDecimal surrenderCash;

    /**
     * 保全生效日期
     */
    @ApiModelProperty(value = "保全生效日期", example = "2019-01-01")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date preservationEffectTime;

    /**
     * 保全状态 1录入完成
     */
    @ApiModelProperty(value = "保全状态编码", example = "1")
    private Integer preservationStatus;

    /**
     * 保全状态 1录入完成
     */
    @ApiModelProperty(value = "保全状态描述录入完成", example = "录入完成")
    private String preservationStatusDesc;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2019-01-01 15:12")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "保全状态编码", example = "张三")
    private String createUser;
}
