package com.mpolicy.manage.modules.agent.vo.train;

import com.alibaba.fastjson.annotation.JSONField;
import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AgentTrainInfoListVo extends BasePage implements Serializable {
    private static final long serialVersionUID = -6347084574381297924L;


    @ApiModelProperty(value = "代理人编码")
    private String agentCode;


    @ApiModelProperty(value = "培训人员编码")
    private String trainHost;

    @ApiModelProperty(value = "培训主题")
    private String trainTopic;

    @ApiModelProperty(value = "是否暂存0:是 1:不是")
    private Integer isTemp;

    @ApiModelProperty(value = "开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
