package com.mpolicy.manage.modules.policy.vo.claim;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 理赔申请详情纪录
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔流水纪录")
@Data
public class ClaimApplyFlow {

    /**
     * 理赔服务单号
     */
    @ApiModelProperty(value = "理赔服务单号")
    private String claimNo;

    /**
     * 事件编码
     */
    @ApiModelProperty(value = "事件编码")
    private String opeCode;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    private String opeName;

    /**
     * 操作内容说明
     */
    @ApiModelProperty(value = "事件名称")
    private String opeContent;

    /**
     * 事件时间
     */
    @ApiModelProperty(value = "事件时间", example = "2019-01-01 15:12:20")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
