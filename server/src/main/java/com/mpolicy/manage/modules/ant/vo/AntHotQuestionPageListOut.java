package com.mpolicy.manage.modules.ant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 整村推进活动信息分页详情
 * @return
 * @Date 2023/6/12 18:26
 * <AUTHOR>
 **/
@Data
public class AntHotQuestionPageListOut implements Serializable {
    private static final long serialVersionUID = -121654351866168894L;
    /**
     * ID
     */
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 问题编码
     */
    @ApiModelProperty(value = "问题编码")
    private String code;
    /**
     * 问题名称
     */
    @ApiModelProperty(value = "问题名称")
    private String question;
    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型")
    private String type;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "回复内容")
    private String replyContent;
    /**
     * 提问量
     */
    @ApiModelProperty(value = "提问量")
    private Integer askNum;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(value = "逻辑删除 0:存在;-1:删除")
    private Integer deleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
