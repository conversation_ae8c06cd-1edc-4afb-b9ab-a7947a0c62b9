package com.mpolicy.manage.modules.policy.service.preservation.project;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.config.transaction.TxServiceManager;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationProductInsuredMapEntity;
import com.mpolicy.manage.modules.policy.enums.PreservationProjectEnum;
import com.mpolicy.manage.modules.policy.enums.PreservationTopicEnum;
import com.mpolicy.manage.modules.policy.enums.PreservationWhyEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.policy.service.common.EpPolicyBaseService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationInfoChangeService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationService;
import com.mpolicy.manage.modules.policy.strategy.PreservationStrategyFactory;
import com.mpolicy.manage.modules.policy.util.PreservationMapper;
import com.mpolicy.manage.modules.policy.vo.preservation.*;
import com.mpolicy.manage.modules.policy.vo.preservation.check.PreservationApplyCheckVo;
import com.mpolicy.manage.modules.request.PolicyClientRequest;
import com.mpolicy.manage.modules.settlement.common.SettlementReconcileBaseService;
import com.mpolicy.manage.modules.sys.util.LambdaUtils;
import com.mpolicy.manage.utils.CompareTools;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.common.enums.PolicyContractStatusEnum;
import com.mpolicy.policy.common.enums.PolicyProductTypeEnum;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.group.sub.EpGroupRelatedInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.preserve.MemberPremiumTrialVo;
import com.mpolicy.policy.common.ep.policy.preserve.MemberProductPremiumTrialVo;
import com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.validator.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保全项目基础类
 *
 * <AUTHOR>
 * @date 2022-04-28 10:33
 */
@Slf4j
public class PreservationProjectBase {

    @Autowired
    protected EpPolicyContractInfoService policyContractInfoService;
    @Autowired
    protected PolicyContractInfoService contractInfoService;
    @Autowired
    protected PreservationApplyService preservationApplyService;
    @Autowired
    protected PreservationInfoChangeService preservationInfoChangeService;
    @Autowired
    protected EpPolicyClient policyClient;
    @Autowired
    protected EpPolicyBaseService epPolicyBaseService;
    @Autowired
    private RabbitMQService rabbitMQService;
    @Autowired
    private PolicyClientRequest policyClientRequest;
    @Autowired
    protected TxServiceManager txServiceManager;
    @Autowired
    private PreservationStrategyFactory preservationStrategyFactory;
    @Autowired
    protected SettlementReconcileBaseService settlementReconcileBaseService;
    @Resource
    private PreservationMapper preservationMapper;
    @Resource
    protected ChannelApplicationReferrerService channelApplicationReferrerService;

    @Resource
    protected EpPolicyInsuredInfoService epPolicyInsuredInfoService;

    @Resource
    protected AgentUserInfoService agentUserInfoService;

    protected Map<String,Object> getPreservationExtendInfo(PreservationApplyInput param){
        return Maps.newHashMap();
    }

    /**
     * <p>
     * 保全mq发送触发：退保/协议解约/犹豫期退保
     * </p>
     *
     * @param preservationCode 保全申请号
     * <AUTHOR>
     * @since 2022/9/20
     */
    protected void sendPreservationMq(String preservationCode){
        // 发送mq进行保全操作
        MQMessage msg = new MQMessage();
        msg.setCode(preservationCode);
        msg.setOpeType(PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey());
        rabbitMQService.sendTopicMessage(PreservationTopicEnum.PRESERVATION_APPLY.getExchange(), PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey(), msg);
    }

    /**
     * <p>
     * 保单信息 转为 保全申请信息
     * </p>
     *
     * @param policy policy
     * @param bean   bean
     */
    protected void conversionPolicyToPreservationApply(PreservationProjectEnum projectEnum,PreservationApplyInput applyInput, EpContractBriefInfoVo policy, PreservationApplyEntity bean) {
        // 保单信息
        bean.setPolicyName(policy.getPortfolioName());
        bean.setPreservationProject(projectEnum.getCode());
        bean.setRenewalTermPeriod(policy.getRenewalTermPeriod());
        // 如果为增减员操作，判断为团单
        String policyProductType = policy.getPolicyProductType();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);
        if(policyProductTypeEnum!=null){
            bean.setPolicyType(policyProductTypeEnum.getPolicyProductType());
        }
        // 保司信息
        bean.setCompanyCode(policy.getCompanyCode());
        bean.setCompanyName(policy.getCompanyName());

        // 销售渠道
        bean.setSellChannelCode(policy.getChannelCode());
        bean.setSellChannelName(policy.getChannelName());

        // 管理机构
        bean.setManageOrgCode(policy.getOrgCode());
        bean.setManageOrgName(policy.getOrgName());

        // 代理人 + 推荐人
        bean.setPolicyAgentCode(policy.getAgentCode());
        bean.setPolicyAgentName(policy.getAgentName());
        bean.setReferrerCode(policy.getPolicyReferrerCode());
        bean.setReferrerName(policy.getPolicyReferrerName());
        // 渠道推荐人
        bean.setChannelReferrerCode(policy.getReferrerCode());
        bean.setChannelReferrerName(policy.getReferrerName());

        // 投保人
        bean.setHolderName(policy.getApplicantName());
        bean.setHolderIdNo(policy.getApplicantIdCard());
        // 被保人
        List<EpContractBriefInsuredInfoVo> insuredList = policy.getInsuredList();
        if(!CollectionUtils.isEmpty(insuredList)) {
            String insuredNames = insuredList.stream().map(EpContractBriefInsuredInfoVo::getInsuredName).collect(Collectors.joining(","));
            String insuredIdCards = insuredList.stream().map(EpContractBriefInsuredInfoVo::getInsuredIdCard).collect(Collectors.joining(","));

            bean.setInsuredNames(insuredNames);
            bean.setInsuredIdNos(insuredIdCards);
        }
        // 分支编码
        bean.setBranchCode(policy.getBranchCode());
        // 渠道分支权限信息
        bean.setChannelBranchCode(policy.getChannelBranchCode());
        bean.setChannelDistributionCode(policy.getChannelDistributionCode());
        setCustomerInfo(policy, bean);
        Map<String, Object> extendInfo = getPreservationExtendInfo(applyInput);
        if(extendInfo != null) {
            bean.setExtendInfo(JSON.toJSONString(extendInfo));
        }
    }

    /**
     * <p>
     * 保单信息 转为 保全申请信息
     * </p>
     *
     * @param policy policy
     * @param bean   bean
     */
    protected void conversionPolicyToPreservationApply(PreservationProjectEnum projectEnum, EpContractBriefInfoVo policy, PreservationApplyEntity bean) {
        // 保单信息
        bean.setPolicyCode(policy.getPolicyNo());
        bean.setPolicyName(policy.getPortfolioName());
        bean.setPreservationProject(projectEnum.getCode());
        bean.setRenewalTermPeriod(policy.getRenewalTermPeriod());
        // 如果为增减员操作，判断为团单
        String policyProductType = policy.getPolicyProductType();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);
        if(policyProductTypeEnum!=null){
            bean.setPolicyType(policyProductTypeEnum.getPolicyProductType());
        }
        // 保司信息
        bean.setCompanyCode(policy.getCompanyCode());
        bean.setCompanyName(policy.getCompanyName());

        // 销售渠道
        bean.setSellChannelCode(policy.getChannelCode());
        bean.setSellChannelName(policy.getChannelName());

        // 管理机构
        bean.setManageOrgCode(policy.getOrgCode());
        bean.setManageOrgName(policy.getOrgName());

        // 代理人 + 推荐人
        bean.setPolicyAgentCode(policy.getAgentCode());
        bean.setPolicyAgentName(policy.getAgentName());
        bean.setReferrerCode(policy.getPolicyReferrerCode());
        bean.setReferrerName(policy.getPolicyReferrerName());
        // 渠道推荐人
        bean.setChannelReferrerCode(policy.getReferrerCode());
        bean.setChannelReferrerName(policy.getReferrerName());

        // 投保人
        bean.setHolderName(policy.getApplicantName());
        bean.setHolderIdNo(policy.getApplicantIdCard());
        // 被保人
        List<EpContractBriefInsuredInfoVo> insuredList = policy.getInsuredList();
        if(!CollectionUtils.isEmpty(insuredList)) {
            String insuredNames = insuredList.stream().map(EpContractBriefInsuredInfoVo::getInsuredName).collect(Collectors.joining(","));
            String insuredIdCards = insuredList.stream().map(EpContractBriefInsuredInfoVo::getInsuredIdCard).collect(Collectors.joining(","));

            bean.setInsuredNames(insuredNames);
            bean.setInsuredIdNos(insuredIdCards);
        }
        // 分支编码
        bean.setBranchCode(policy.getBranchCode());
        // 渠道分支权限信息
        bean.setChannelBranchCode(policy.getChannelBranchCode());
        bean.setChannelDistributionCode(policy.getChannelDistributionCode());
        setCustomerInfo(policy, bean);
    }

    /**
     * <p>
     * 保单信息 转为 保全申请信息
     * </p>
     *
     * @param policy policy
     * @param bean   bean
     */
    protected void copyField(String project,EpPolicyContractInfoEntity policy, PreservationApplyEntity bean) {
        // 保单信息
        String policyName = policy.getPortfolioName();
        if(StringUtils.isBlank(policyName)){
            policyName = policy.getMainProductName();
        }
        bean.setPolicyName(policyName);

        bean.setPreservationProject(project);
        //该保全只针对首期，所以此处估计续期期数为1
        bean.setRenewalTermPeriod(1);
        // 如果为增减员操作，判断为团单
        String policyProductType = policy.getPolicyProductType();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyProductType);
        if(policyProductTypeEnum!=null){
            bean.setPolicyType(policyProductTypeEnum.getPolicyProductType());
        }
        // 保司信息
        bean.setCompanyCode(policy.getCompanyCode());
        bean.setCompanyName(policy.getCompanyName());

        // 销售渠道
        bean.setSellChannelCode(policy.getChannelCode());
        bean.setSellChannelName(policy.getChannelName());

        // 管理机构
        bean.setManageOrgCode(policy.getOrgCode());

        // 代理人 + 推荐人
        if(Objects.equals(policy.getSalesType(),0)) {
            bean.setReferrerCode(policy.getAgentCode());
        }else{
            bean.setPolicyAgentCode(policy.getAgentCode());
        }
        // 渠道推荐人
        bean.setChannelReferrerCode(policy.getReferrerCode());
        bean.setChannelReferrerName(policy.getReferrerName());

        // 投保人
        bean.setHolderName(policy.getApplicantName());
        // 分支编码
        bean.setBranchCode(policy.getChannelBranchCode());
        // 渠道分支权限信息
        bean.setChannelBranchCode(policy.getChannelBranchCode());
        bean.setChannelDistributionCode(policy.getChannelDistributionCode());
        setCustomerInfo(policy, bean);
    }

    /**
     * 保全申请条件
     * 2024-09-10:跟业务(程鹏)，产品(肖敏)确认，保全的初始推荐人都取保单层推荐人
     * @param policy 保单基础信息
     */
    protected void setCustomerInfo(EpContractBriefInfoVo policy, PreservationApplyEntity bean){
        bean.setCustomerManagerCode(policy.getCustomerManagerCode());
        bean.setCustomerManagerChannelCode(policy.getCustomerManagerChannelCode());
        bean.setCustomerManagerOrgCode(policy.getCustomerManagerOrgCode());
        bean.setCustomerManagerChannelOrgCode(policy.getCustomerManagerChannelOrgCode());
        bean.setCustomerManagerSupervisor(policy.getCustomerManagerSupervisor());
//            EpChannelManagerVo channelManagerVo = policyClientRequest.getNoGroupChannelCustomerInfo(policy.getContractCode(), policy.getRenewalTermPeriod());
//            if (Objects.nonNull(channelManagerVo)) {
//                bean.setCustomerManagerCode(channelManagerVo.getCustomerManagerCode());
//                bean.setCustomerManagerChannelCode(channelManagerVo.getCustomerManagerChannelCode());
//                bean.setCustomerManagerOrgCode(channelManagerVo.getCustomerManagerOrgCode());
//                bean.setCustomerManagerChannelOrgCode(channelManagerVo.getCustomerManagerChannelOrgCode());
//                bean.setCustomerManagerSupervisor(channelManagerVo.getCustomerManagerSupervisor());
//            }
    }

    /**
     * 保全申请条件
     * 2024-09-10:跟业务(程鹏)，产品(肖敏)确认，保全的初始推荐人都取保单层推荐人
     * @param policy 保单基础信息
     */
    protected void setCustomerInfo(EpPolicyContractInfoEntity policy, PreservationApplyEntity bean){
        bean.setCustomerManagerCode(policy.getCustomerManagerCode());
        bean.setCustomerManagerChannelCode(policy.getCustomerManagerChannelCode());
        bean.setCustomerManagerOrgCode(policy.getCustomerManagerOrgCode());
        bean.setCustomerManagerChannelOrgCode(policy.getCustomerManagerChannelOrgCode());
        bean.setCustomerManagerSupervisor(policy.getCustomerManagerSupervisor());
//            EpChannelManagerVo channelManagerVo = policyClientRequest.getNoGroupChannelCustomerInfo(policy.getContractCode(), policy.getRenewalTermPeriod());
//            if (Objects.nonNull(channelManagerVo)) {
//                bean.setCustomerManagerCode(channelManagerVo.getCustomerManagerCode());
//                bean.setCustomerManagerChannelCode(channelManagerVo.getCustomerManagerChannelCode());
//                bean.setCustomerManagerOrgCode(channelManagerVo.getCustomerManagerOrgCode());
//                bean.setCustomerManagerChannelOrgCode(channelManagerVo.getCustomerManagerChannelOrgCode());
//                bean.setCustomerManagerSupervisor(channelManagerVo.getCustomerManagerSupervisor());
//            }
    }

    /**
     * 需求规则：https://cfpamf.yuque.com/xg4y3f/ulb6rg/gmw642
     * 保单是否能做退保的规则
     * @param policy
     * @return
     */
    protected void policyCondition4Cancel(Date preservationEffectiveTime, EpContractBriefInfoVo policy){
        if(policy == null){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该保单不存在"));
        }

        if(preservationEffectiveTime == null){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保全生效日不能为空"));
        }

        if(!isLYBProduct(policy.getMainProductCode())) {
            Date policyEndTime = policy.getInsuredPeriodEndTime();
            if (policyEndTime != null) {
                if (preservationEffectiveTime.after(policyEndTime)) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保全生效日不能晚于保单失效时间"));
                }
            }
        }

        String policyStatus = policy.getAdminPolicyStatus();

        if(!canSurrender(policyStatus)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该保单不为承保状态，无法创建该保全"));
        }

        if(!typeCondition(policy.getPolicyProductType())){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该保单类型不支持当前保全项目"));
        }
    }

    /**
     * 更方亚确认：只有三个状态可以做退保：承保，生效中，终止  这三个状态可以退保
     * @param policyStatus
     * @return
     */
    protected boolean canSurrender(String policyStatus){
        return Objects.equals(PolicyContractStatusEnum.TERMINATION.getCode(), policyStatus)
                || Objects.equals(PolicyContractStatusEnum.ACTIVE.getCode(), policyStatus)
                || Objects.equals(PolicyContractStatusEnum.DISCONTINUE.getCode(), policyStatus)
                || Objects.equals(PolicyContractStatusEnum.SIGNED.getCode(), policyStatus);
    }



    /**
     * 需求规则：https://cfpamf.yuque.com/xg4y3f/ulb6rg/gmw642
     * @param productType
     * @return
     */
    private boolean typeCondition(String productType){
        return Objects.equals(PolicyProductTypeEnum.PERSONAL.getCode(), productType)
                || Objects.equals(PolicyProductTypeEnum.GROUP.getCode(), productType)
                || Objects.equals(PolicyProductTypeEnum.VEHICLE.getCode(), productType)
                || Objects.equals(PolicyProductTypeEnum.PROPERTY.getCode(), productType);
    }

    private static final Set<PreservationProjectEnum> SETS = Sets.immutableEnumSet(PreservationProjectEnum.SURRENDER,
                                                                                   PreservationProjectEnum.PROTOCOL_TERMINATION,
                                                                                   PreservationProjectEnum.HESITATION_SURRENDER,
                                                                                   PreservationProjectEnum.TERMINATION_PRODUCT);

    protected boolean someChannelCode(EpContractBriefInfoVo policy, List<EpGroupInsuredInfoVo> groupInsuredList, String someChannelCode) {
        String mainChannelCode = policy.getChannelCode();
        boolean r = false;
        for (EpGroupInsuredInfoVo insured : groupInsuredList) {
            String insuredChannelCode = insured.getChannelCode();
            insuredChannelCode = StringUtils.isBlank(insuredChannelCode) ? mainChannelCode : insuredChannelCode;

            if (Objects.equals(insuredChannelCode, someChannelCode)) {
                r = true;
                break;
            }
            List<EpGroupRelatedInsuredInfoVo> groupRelatedInsuredList = insured.getRelatedInsuredList();
            if (CollectionUtils.isEmpty(groupRelatedInsuredList)) {
                continue;
            }

            for (EpGroupRelatedInsuredInfoVo relatedInsured : groupRelatedInsuredList) {

                String relatedInsuredChannelCode = relatedInsured.getChannelCode();
                relatedInsuredChannelCode = StringUtils.isBlank(relatedInsuredChannelCode) ? mainChannelCode : relatedInsuredChannelCode;
                if (Objects.equals(relatedInsuredChannelCode, someChannelCode)) {
                    r = true;
                    break;
                }
            }

            if (r) {
                break;
            }
        }
        return r;
    }

    public static boolean isSurrender(PreservationProjectEnum projectEnum) {
        return SETS.contains(projectEnum);
    }

    /**
     * 自动保全全申请
     * @param applyInput
     */
    public void automaticApply(PreservationApplyInput applyInput){
        // 校验基础信息
        ValidatorUtils.validateEntity(applyInput);
        // 获取保全项目
        PreservationProjectEnum project = Optional.ofNullable(
                PreservationProjectEnum.decode(applyInput.getPreservationProject())
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保全项目不支持")));
        // 根据项目类型获取保全策略服务 + 执行保全申请

        // 续期期数默认为1，目前有些保全，即使当前续期期数不为1，但是保全信息中也要存首期，但是退保需要存当前续期期数
        Integer renewalTermPeriod = applyInput.getRenewalTermPeriod();
        if (renewalTermPeriod == null) {
            applyInput.setRenewalTermPeriod(1);
        }

        if (CollectionUtils.isEmpty(applyInput.getAddOrSubtractList())){
            this.checkTow(applyInput.getEndorsementNo(),null);
        }

        PreservationService<PreservationApplyInput> preservationService = preservationStrategyFactory.getPreservationService(project, PreservationApplyInput.class);
        String preservationCode = preservationService.apply(applyInput);

        if (!(Objects.equals(project.getCode(), PreservationProjectEnum.ADD_OR_SUBTRACT.getCode())
                && CollectionUtils.isEmpty(applyInput.getAddOrSubtractList()))) {
            // 保全申请完成后，发送mq
            MQMessage msg = new MQMessage();
            msg.setCode(preservationCode);
            msg.setOpeType(PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey());
            rabbitMQService.sendTopicMessage(PreservationTopicEnum.PRESERVATION_APPLY.getExchange(), PreservationTopicEnum.PRESERVATION_APPLY.getRouteKey(), msg);
            log.info("消息发送成功{}",msg);
        }
    }

    /**
     * 获取保全号
     * @param prInput
     * @return
     */
    public String getPreservationCode(PreservationApplyInput prInput) {
        return Optional
                .ofNullable(prInput.getPreservationCode())
                .filter(StringUtils::isNotBlank)
                .orElseGet(() -> CommonUtils.createCodeLastNumber("PR"));
    }

    /**
     * 更新保全状态到成功
     *
     * @param preservationCode
     * @return
     */
    public Boolean upPreservationResult(String preservationCode) {
        //更新保全
        return preservationApplyService.lambdaUpdate()
                .set(PreservationApplyEntity::getProcessingStatus, 1)
                .set(PreservationApplyEntity::getProcessingTime, new Date())
                .eq(PreservationApplyEntity::getPreservationCode, preservationCode)
                .update();
    }

    //校验保单+批单是否重复
    public void check(PreservationApplyInput applyInput) {
        this.checkTow(applyInput.getEndorsementNo(), applyInput.getPolicyCode());
    }

    /**
     * 系统默认生成明细数据
     * 只能针对在保的险种生成保费
     * @return
     */
    protected List<PreservationProductInsuredMapVo> genProductInsuredMapList(String contractCode,BigDecimal businessPremium,List<EpInsuredProductVo> productList) {
        List<MemberPremiumTrialVo> memberPremiumTrialVos = epPolicyBaseService.commonTrialPremium(contractCode, businessPremium,productList);
        List<PreservationProductInsuredMapVo> insuredProductList = Lists.newArrayList();

        Map<String, MemberProductPremiumTrialVo> policyProductTrialMap = new HashMap<>();
        for (MemberPremiumTrialVo memberPremiumTrialVo : memberPremiumTrialVos) {
            List<MemberProductPremiumTrialVo> productPremiumTrialList = memberPremiumTrialVo.getProductPremiumTrialList();
            for (MemberProductPremiumTrialVo entry : productPremiumTrialList) {
                String insuredCode = entry.getInsuredCode();
                String policyProductCode = entry.getPolicyProductCode();
                String productCode = entry.getProductCode();

                String key = StrUtil.format("{}-{}-{}", insuredCode, policyProductCode,productCode);
                policyProductTrialMap.put(key,entry);
            }
        }
        for(EpInsuredProductVo productVo:productList){
            PreservationProductInsuredMapVo vo = new PreservationProductInsuredMapVo();
            BeanUtils.copyProperties(productVo, vo);
            String insuredCode = productVo.getInsuredCode();
            String policyProductCode = productVo.getPolicyProductCode();
            String productCode = productVo.getProductCode();
            String key = StrUtil.format("{}-{}-{}", insuredCode, policyProductCode,productCode);
            MemberProductPremiumTrialVo trialVo = policyProductTrialMap.get(key);
            if(trialVo == null){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单险种保费试算失败:"+key));
            }
            BigDecimal productBusinessPremium = trialVo.getBusinessPremium();
            String productStatus = productVo.getProductStatus();
            if(PolicyContractStatusEnum.isSurrenderStatus(productStatus)){
                vo.setSurrenderPremium(productBusinessPremium);
            }
            else {
                vo.setPremium(productBusinessPremium);
            }
            vo.setBusinessPremium(productBusinessPremium);
            insuredProductList.add(vo);
        }
        return insuredProductList;
    }

//    protected List<PreservationProductInsuredMapEntity> trialPremium(String preservationCode,String contractCode, BigDecimal businessPremium){
//        log.info("开始调用保单中心Api试算保费 ={}",contractCode,businessPremium);
//        List<MemberPremiumTrialVo> memberPremiumTrialVos = epPolicyBaseService.commonTrialPremium(contractCode, businessPremium);
//        List<PreservationProductInsuredMapEntity> productInsuredMapEntityList = Lists.newArrayList();
//        boolean isRefund = CompareTools.safeCompare(businessPremium,BigDecimal.ZERO)<0;
//        for (MemberPremiumTrialVo memberPremiumTrialVo : memberPremiumTrialVos) {
//            List<MemberProductPremiumTrialVo> productPremiumTrialList = memberPremiumTrialVo.getProductPremiumTrialList();
//            for (MemberProductPremiumTrialVo memberProductPremiumTrialVo : productPremiumTrialList) {
//                PreservationProductInsuredMapEntity preservationProductInsuredMapEntity = preservationMapper.buildPreservationProductInsuredMapEntity(memberProductPremiumTrialVo,memberPremiumTrialVo);
//                preservationProductInsuredMapEntity.setPreservationCode(preservationCode);
//                preservationProductInsuredMapEntity.setContractCode(contractCode);
//                preservationProductInsuredMapEntity.setProductStatus(memberProductPremiumTrialVo.getProductStatus());
//                preservationProductInsuredMapEntity.setPremium(memberProductPremiumTrialVo.getProductPremium());
//                preservationProductInsuredMapEntity.setSurrenderPremium(memberProductPremiumTrialVo.getProductSurrenderPremium());
//                // 如果是退保的情况需要将正数改为负数
//                if (isRefund){
//                    preservationProductInsuredMapEntity.setBusinessPremium(memberProductPremiumTrialVo.getProductSurrenderPremium());
//                    preservationProductInsuredMapEntity.setBusinessType(2);
//                    preservationProductInsuredMapEntity.setProductStatus(PolicyContractStatusEnum.CANCELLATION.getCode());
//                } else {
//                    preservationProductInsuredMapEntity.setBusinessPremium(memberProductPremiumTrialVo.getProductPremium());
//                    preservationProductInsuredMapEntity.setBusinessType(1);
//                    preservationProductInsuredMapEntity.setProductStatus(PolicyContractStatusEnum.TERMINATION.getCode());
//                    preservationProductInsuredMapEntity.setPremium(memberProductPremiumTrialVo.getProductPremium());
//                }
//                productInsuredMapEntityList.add(preservationProductInsuredMapEntity);
//            }
//        }
//        return productInsuredMapEntityList;
//    }

    /**
     * 车险补退费
     * 系统拆分保费时，只从在保的险种上进行拆分，所以险种的状态都默认时承保
     * @param applyInput
     * @return
     */
    protected List<PreservationProductInsuredMapEntity> convertVehicleInfoDetail(String preservationCode,EpContractBriefInfoVo policy, PreservationApplyInput applyInput){
        List<PreservationProductInsuredMapEntity> productInsuredMapEntityList = Lists.newArrayList();
        String contractCode = applyInput.getContractCode();
        PreservationVehicleInfoChangeForm preservationVehicleInfoChangeForm = applyInput.getPreservationVehicleInfoChangeForm();
        boolean refundAmountFlag = preservationVehicleInfoChangeForm.isRefundAmountFlag();
        if (refundAmountFlag){
            BigDecimal refundAmount = preservationVehicleInfoChangeForm.getRefundAmount();
            boolean isRefund = refundAmount.compareTo(BigDecimal.ZERO)<0;
            List<EpInsuredProductVo> productList =currenttInsuredProductList(policy);
            List<MemberPremiumTrialVo> memberPremiumTrialVos = commonPremiumTrial(productList,applyInput);
            log.info("memberPremiumTrialVos ={}",JSON.toJSONString(memberPremiumTrialVos));
            for (MemberPremiumTrialVo memberPremiumTrialVo : memberPremiumTrialVos) {
                List<MemberProductPremiumTrialVo> productPremiumTrialList = memberPremiumTrialVo.getProductPremiumTrialList();
                for (MemberProductPremiumTrialVo memberProductPremiumTrialVo : productPremiumTrialList) {
                    PreservationProductInsuredMapEntity preservationProductInsuredMapEntity = preservationMapper.buildPreservationProductInsuredMapEntity(memberProductPremiumTrialVo,memberPremiumTrialVo);
                    preservationProductInsuredMapEntity.setPremium(memberProductPremiumTrialVo.getBusinessPremium());
                    preservationProductInsuredMapEntity.setProductStatus(PolicyContractStatusEnum.TERMINATION.getCode());
                    preservationProductInsuredMapEntity.setBusinessPremium(memberProductPremiumTrialVo.getBusinessPremium());
                    preservationProductInsuredMapEntity.setScalePremium(memberProductPremiumTrialVo.getBusinessPremium());
                    preservationProductInsuredMapEntity.setSurrenderPremium(null);
                    if (isRefund){
                        preservationProductInsuredMapEntity.setBusinessType(2);
                    }else{
                        preservationProductInsuredMapEntity.setBusinessType(1);
                    }
                    preservationProductInsuredMapEntity.setPreservationCode(preservationCode);
                    preservationProductInsuredMapEntity.setContractCode(contractCode);
                    productInsuredMapEntityList.add(preservationProductInsuredMapEntity);
                }
            }
        }
        return productInsuredMapEntityList;
    }

    protected List<EpInsuredProductVo> currenttInsuredProductList(EpContractBriefInfoVo policy) {
        if(PolicyContractStatusEnum.isSurrenderStatus(policy.getPolicyStatus())){
            return epPolicyBaseService.querySurrenderInsuredProductList(policy.getContractCode());
        }
        else{
            //2. 获取当前保单在保的被保人险种集合
            return epPolicyBaseService.queryActiveInsuredProductList(policy.getContractCode());
        }
    }

    protected List<PreservationProductInsuredMapEntity> convertInsuredProductList(String preservationCode,String contractCode, List<PreservationProductInsuredMapVo> insuredProductList){
        List<PreservationProductInsuredMapEntity> data = new ArrayList<>();

        for(PreservationProductInsuredMapVo entry:insuredProductList){
            initBusinessPremium(entry);
            PreservationProductInsuredMapEntity entity = new PreservationProductInsuredMapEntity();
            BeanUtils.copyProperties(entry,entity);
            BigDecimal businessPremium = entry.getBusinessPremium();
            Integer businessType = 1;
            if(CompareTools.safeCompare(businessPremium,BigDecimal.ZERO)<0){
                businessType = 2;
            }
            String mainInsurance = entry.getMainInsurance();
            entity.setMainInsurance(0);
            if(Objects.equals(mainInsurance,"1")||Objects.equals(mainInsurance,"是")){
                entity.setMainInsurance(1);
            }
            entity.setScalePremium(businessPremium);
            entity.setBusinessPremium(businessPremium);
            entity.setBusinessType(businessType);
            entity.setContractCode(contractCode);
            entity.setPreservationCode(preservationCode);
            data.add(entity);
        }
        return data;
    }

    protected void initBusinessPremium(PreservationProductInsuredMapVo vo){
        BigDecimal businessPremium = vo.getBusinessPremium();
        if(businessPremium == null){
            businessPremium = vo.getSurrenderPremium();
            if(businessPremium == null){
                businessPremium = vo.getPremium();
            }
        }
        vo.setBusinessPremium(businessPremium);
    }


    /**
     *
     * 获取费用更变的试算
     * @param applyInput
     * 试算驶入
     * @return
     * 试算结果
     */
    private List<MemberPremiumTrialVo> commonPremiumTrial(List<EpInsuredProductVo> productList,PreservationApplyInput applyInput) {
        String contractCode = applyInput.getContractCode();
        PreservationVehicleInfoChangeForm preservationVehicleInfoChangeForm = applyInput.getPreservationVehicleInfoChangeForm();
        BigDecimal refundAmount = preservationVehicleInfoChangeForm.getRefundAmount();
        return epPolicyBaseService.commonTrialPremium(contractCode, refundAmount,productList);
    }

    /**
     * 试算退保明细
     * [ep_preservation_product_insured_map] 数据规则：[surrender_premium]小于0
     * @param contractCode 保单合同编号
     * @param preservationCode 保全编号
     * @param applyInput 保全申请数据
     * @return 退保被保人险种明细
     */
    protected List<PreservationProductInsuredMapEntity> convertSurrenderDetail(String contractCode, final String preservationCode, PreservationApplyInput applyInput) {
        //如果业务人员未录入退保明细，则系统生成退保明细
        List<PreservationSurrenderDetailVo> surrenderDetailList = applyInput.getSurrenderDetailList();
        if(CollectionUtils.isEmpty(surrenderDetailList)){
            return trialSurrenderDetail(contractCode,preservationCode,applyInput);
        }
        String preservationProject = applyInput.getPreservationProject();
        PreservationProjectEnum projectEnum = PreservationProjectEnum.decode(preservationProject);
        // 试算退保保费
        List<MemberPremiumTrialVo> trialVo = trialSurrenderInfo(contractCode,preservationCode,applyInput);
        Map<String,MemberProductPremiumTrialVo> trialVoMap = new HashMap<>();
        for(MemberPremiumTrialVo vo:trialVo){
            List<MemberProductPremiumTrialVo> productPremiumTrialList = vo.getProductPremiumTrialList();
            if(CollectionUtils.isNotEmpty(productPremiumTrialList)){
                for(MemberProductPremiumTrialVo entry:productPremiumTrialList){
                    String policyProductCode = entry.getPolicyProductCode();
                    policyProductCode = StringUtils.isBlank(policyProductCode) ? "" : policyProductCode;
                    String insuredCode = entry.getInsuredCode();
                    String productCode = entry.getProductCode();
                    String key =  insuredCode + "-"+ productCode+ "-" + policyProductCode;
                    trialVoMap.put(key,entry);
                }
            }
        }
        BigDecimal one = new BigDecimal("-1");
        return surrenderDetailList.stream()
                .map(product -> {
                    PreservationProductInsuredMapEntity entity = new PreservationProductInsuredMapEntity();
                    entity.setPreservationCode(preservationCode);
                    entity.setContractCode(contractCode);

                    if(Objects.isNull(product.getSurrenderAmount())) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("险种退保保费不能为空:"+product.getProductCode()));
                    }
                    entity.setInsuredCode(product.getInsuredCode());
                    BigDecimal surrenderPremium = product.getSurrenderAmount().abs().multiply(one);
                    entity.setSurrenderPremium(surrenderPremium);

                    entity.setPolicyProductCode(product.getPolicyProductCode());
                    entity.setProductCode(product.getProductCode());
                    entity.setProductName(product.getProductName());
                    entity.setInsuredCode(product.getInsuredCode());
                    entity.setProductStatus(projectEnum.getPolicyStatus());

                    String policyProductCode = product.getPolicyProductCode();
                    policyProductCode = StringUtils.isBlank(policyProductCode) ? "" : policyProductCode;
                    String insuredCode = product.getInsuredCode();
                    String key = insuredCode + "-" + product.getProductCode()+ "-" + policyProductCode;
                    MemberProductPremiumTrialVo vo = trialVoMap.get(key);
                    if (vo == null) {
                        throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("险种保费试算失败:"+key));
                    }
                    BigDecimal scalePremium = vo.getScalePremium().abs().multiply(one);
                    entity.setScalePremium(scalePremium);
                    entity.setPlanCode(vo.getPlanCode());
                    entity.setPlanName(vo.getPlanName());
                    entity.setMainInsurance(vo.getMainInsurance());
                    entity.setProtocolProductName(vo.getProtocolProductName());
                    entity.setPremium(vo.getProductPremium());
                    entity.setBusinessPremium(entity.getSurrenderPremium());
                    entity.setBusinessType(2);
                    return entity;
                })
                .collect(Collectors.toList());
    }

    private List<MemberPremiumTrialVo> trialSurrenderInfo(String contractCode,String preservationCode,PreservationApplyInput applyInput){
        List<MemberPremiumTrialVo> surrenderTrialPremium = epPolicyBaseService.surrenderTrialPremium(contractCode,
                PreservationProjectEnum.decode(applyInput.getPreservationProject()),
                PreservationWhyEnum.decode(applyInput.getPreservationWhy()),
                applyInput.getSurrenderCash(),
                applyInput.getPreservationEffectTime(),
                preservationCode);
        if(CollectionUtils.isEmpty(surrenderTrialPremium)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保单退保保费试算失败"));
        }
        return surrenderTrialPremium;
    }

    /**
     * 业务人员未录入退保明细保费时，系统会试算出退保明细保费
     * @param contractCode
     * @param preservationCode
     * @param applyInput
     * @return
     */
    protected List<PreservationProductInsuredMapEntity> trialSurrenderDetail(String contractCode, String preservationCode,  PreservationApplyInput applyInput) {
        //1. 请求保单中心，试算退保保费明细
        List<MemberPremiumTrialVo> data = trialSurrenderInfo(contractCode,preservationCode,applyInput);
        String preservationProject = applyInput.getPreservationProject();
        PreservationProjectEnum projectEnum = PreservationProjectEnum.decode(preservationProject);
        return convertMemberProductMap(contractCode,preservationCode,projectEnum.getPolicyStatus(),data);
    }

    protected List<PreservationProductInsuredMapEntity> convertMemberProductMap(String contractCode, String preservationCode, String productStatus, List<MemberPremiumTrialVo> memberPremiumList ) {
        if(CollectionUtils.isEmpty(memberPremiumList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保全保费明细试算失败"));
        }
        List<PreservationProductInsuredMapEntity> data = new ArrayList<>();
        BigDecimal one = new BigDecimal("-1");
        for(MemberPremiumTrialVo member:memberPremiumList){
            List<MemberProductPremiumTrialVo> productPremiumTrialList = member.getProductPremiumTrialList();
            if(CollectionUtils.isEmpty(productPremiumTrialList)){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保全保费明细试算失败"));
            }
            for(MemberProductPremiumTrialVo memberProduct:productPremiumTrialList){
                PreservationProductInsuredMapEntity entity = new PreservationProductInsuredMapEntity();
                entity.setInsuredCode(member.getInsuredCode());
                entity.setContractCode(contractCode);
                entity.setPreservationCode(preservationCode);
                entity.setPremium(memberProduct.getProductPremium());
                entity.setScalePremium(memberProduct.getScalePremium().multiply(one));
                if(Objects.isNull(memberProduct.getProductSurrenderPremium())) {
                    throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("险种退保保费不能为空:"+memberProduct.getProductCode()));
                }
                entity.setSurrenderPremium(memberProduct.getProductSurrenderPremium().multiply(one));
                if(Objects.nonNull(member.getSurrenderPremium())) {
                    entity.setInsuredSurrenderPremium(member.getSurrenderPremium().multiply(one));
                }
                entity.setProductCode(memberProduct.getProductCode());
                entity.setPolicyProductCode(memberProduct.getPolicyProductCode());
                entity.setPlanCode(memberProduct.getPlanCode());
                entity.setPlanName(memberProduct.getPlanName());
                entity.setProductName(memberProduct.getProductName());
                entity.setProtocolProductName(memberProduct.getProtocolProductName());
                entity.setMainInsurance(memberProduct.getMainInsurance());
                entity.setProductStatus(productStatus);
                data.add(entity);
            }
        }
        return data;
    }

    /**
     * 公用方法，判断是否可以退保
     * @param policyStatus
     * @return
     */
    public boolean publicCanSurrender(String policyStatus){
        return canSurrender(policyStatus);
    }

    protected PreservationApplyCheckVo convertCheckVo(PreservationApplyInput dataInput) {
        PreservationApplyCheckVo checkVo = new PreservationApplyCheckVo();
        checkVo.setPolicyNo(dataInput.getPolicyCode());
        checkVo.setEndorsementNo(dataInput.getEndorsementNo());
        checkVo.setPreservationProject(dataInput.getPreservationProject());
        checkVo.setPreservationType(dataInput.getPreservationType());
        checkVo.setSurrenderCash(dataInput.getSurrenderCash());

        PreservationCustomerManagerChangeForm preservationCustomerManagerChangeForm = dataInput.getPreservationCustomerManagerChangeForm();
        String afterCode = preservationCustomerManagerChangeForm.getAfterCustomerManager();
        if(Objects.equals(afterCode,"无")){
            preservationCustomerManagerChangeForm.setAfterCustomerManagerName("");
            preservationCustomerManagerChangeForm.setAfterCustomerManager("");
            preservationCustomerManagerChangeForm.setAfterCustomerManagerChannelCode("");
            preservationCustomerManagerChangeForm.setAfterCustomerManagerChannel("");
        }

        checkVo.setPreservationCustomerManagerChangeForm(preservationCustomerManagerChangeForm);
        checkVo.setChannelDistributionChangeForm(dataInput.getChannelDistributionChangeForm());
        checkVo.setChannelReferrerChange(dataInput.getChannelReferrerChange());
        return checkVo;
    }

    /**
     * 检查是否存在相同的保全批单号。如果存在，并且满足特定条件，则不进行保全号的校验。
     *
     * @param endorsementNo 保全批单号
     * @param policyNo 保单号
     *
     * 注：该方法不返回任何值，但会在发现相同保全批单号时抛出异常或直接返回。
     */
    public void checkTow(String endorsementNo,String policyNo) {
        // 如果保全批单号为空，则直接返回，不进行后续校验
        if (StringUtils.isBlank(endorsementNo)) {
            return;
        }
        // 查询已存在的保全申请，使用保全批单号作为条件
        List<PreservationApplyEntity> existedList = preservationApplyService.list(
                new LambdaQueryWrapper<>(new PreservationApplyEntity()).select(PreservationApplyEntity::getId)
                        .eq(PreservationApplyEntity::getEndorsementNo, endorsementNo)
                        .eq(PreservationApplyEntity::getDeleted, 0)
        );
        // 如果查询结果不为空，表示已存在相同的保全批单号
        if (CollectionUtil.isNotEmpty(existedList)) {
            // 如果保单号不为空，则进行进一步的校验
            if(StringUtils.isNotBlank(policyNo)){
                // 根据保单号查询保单信息
                EpPolicyContractInfoEntity policyInfo = policyContractInfoService.lambdaQuery()
                        .eq(EpPolicyContractInfoEntity::getPolicyNo, policyNo)
                        .eq(EpPolicyContractInfoEntity::getDeleted, 0)
                        .one();
                String mainProductCode = policyInfo.getMainProductCode();
                // 获取不需要校验保全号的保险公司编码集合
                final Set<String> noEndorsementNoCode = Optional.ofNullable(DicCacheHelper
                                .getSons("POLICY:NOT_CHECK_ENDORSEMENT_NO"))
                        .map(e -> e.stream()
                                .map(DicCacheHelper.DicEntity::getValue)
                                .collect(Collectors.toSet()))
                        .orElse(new HashSet<>());
                // 记录日志，包含不校验保全号的保险公司代码和查询到的保单信息
                log.info("不校验配单号保司{}，{}",noEndorsementNoCode,policyInfo);
                // 如果查询到的保单信息不为空，并且属于不需要校验保全号的保险公司，则直接返回
                if(noEndorsementNoCode.contains(policyInfo.getCompanyCode())){
                    return;
                }
                if(isLYBProduct(mainProductCode)){
                    return;
                }
            }
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该保全批单号已存在"));
        }
    }

    /**
     * 蓝医保产品需要特殊处理
     * 1: 退保流程不校验批改单号重复
     * 2. 退保生效时间不校验合理性(是否大于保单失效时间)
     * @param productCode 保单主险编码
     * @return
     */
    protected boolean isLYBProduct(String productCode){
        // 获取不需要校验保全号的险种编码
        final Set<String> productCodeList = Optional.ofNullable(DicCacheHelper
                        .getSons("POLICY:PRESERVATION:PRODUCT_WHITE_LIST"))
                .map(e -> e.stream()
                        .map(DicCacheHelper.DicEntity::getValue)
                        .collect(Collectors.toSet()))
                .orElse(new HashSet<>());
        if(CollectionUtil.isNotEmpty(productCodeList)) {
            log.info("不校验重复批改单号的险种编码：{}，{}", productCodeList, productCode);
            return productCodeList.contains(productCode);
        }
        return false;
    }

}
