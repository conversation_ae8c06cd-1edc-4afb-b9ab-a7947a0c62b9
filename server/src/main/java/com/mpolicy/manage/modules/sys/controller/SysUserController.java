package com.mpolicy.manage.modules.sys.controller;

import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.common.AdminCommonKeys;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.entity.SysUserInfoOut;
import com.mpolicy.manage.modules.sys.form.PasswordForm;
import com.mpolicy.manage.modules.sys.service.SysUserRoleService;
import com.mpolicy.manage.modules.sys.service.SysUserService;
import com.mpolicy.manage.modules.sys.service.SysUserTokenService;
import com.mpolicy.web.common.validator.Assert;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.commons.lang.ArrayUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 系统用户
 *
 * @<NAME_EMAIL>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/sys/user")
public class SysUserController extends AbstractController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private IRedisService redisService;

    @Autowired
    private SysUserTokenService sysUserTokenService;

    private Integer UPDATE_FAIL_NUM = 3;

    /**
     * 所有用户列表
     */
    @ApiOperation(value = "所有用户列表", notes = "分页所有用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "user_id")
    })
    @GetMapping("/list")
    @RequiresPermissions("sys:user:list")
    public Result<PageUtils> list(@RequestParam(required = false) Map<String, Object> params) {
//        //只有超级管理员，才能查看所有管理员列表
//        if (getUserId() != FbmsConstant.SUPER_ADMIN) {
//            params.put("createUserId", getUserId()); //只看角色不考虑数据权限
//        }
        PageUtils page = sysUserService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 获取登录的用户信息
     */
    @ApiOperation(value = "获取登录的用户信息", notes = "获取登录的用户信息")
    @GetMapping("/info")
    public Result<SysUserEntity> info() {
        return Result.success(getUser());
    }

    /**
     * 修改登录用户密码
     */
    @ApiOperation(value = "修改登录用户密码", notes = "修改登录用户密码")
    @SysDbLog("修改密码")
    @PostMapping("/password")
    public Result password(@RequestBody @ApiParam(name = "form", value = "修改密码表单", required = true) PasswordForm form, HttpServletRequest request, HttpServletResponse response) {
        Assert.isBlank(form.getNewPassword(), "新密码不为能空");
        //sha256加密
        String password = new Sha256Hash(form.getPassword(), getUser().getSalt()).toHex();
        //sha256加密
        String newPassword = new Sha256Hash(form.getNewPassword(), getUser().getSalt()).toHex();
        Long userId = getUserId();
        //校验错误次数
        Integer updateFailNum = redisService.get(AdminCommonKeys.UPDATE_PASSWORD, userId + "", Integer.class);
        if (updateFailNum != null && updateFailNum >= UPDATE_FAIL_NUM) {
            String token = request.getParameter("token");
            Cookie[] cookies = request.getCookies();
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals("token")) {
                    token = cookie.getValue();
                }
            }
            sysUserTokenService.logoutByToken(token);
            Cookie cookie = new Cookie("token", null);
            cookie.setMaxAge(0);
            cookie.setPath("/");
            response.addCookie(cookie);
            redisService.set(AdminCommonKeys.LOGIN_FAIL_NUM, getUser().getUsername(), UPDATE_FAIL_NUM);
            redisService.delete(AdminCommonKeys.UPDATE_PASSWORD, userId + "");
            return Result.error(BasicCodeMsg.NEED_LOGIN.setMsg("密码输入错误次数大于3次,限制登录30分钟"));

        }
        //更新密码
        boolean flag = sysUserService.updatePassword(userId, password, newPassword);
        if (!flag) {
            redisService.set(AdminCommonKeys.UPDATE_PASSWORD, userId + "", (updateFailNum == null ? 0 : updateFailNum) + 1);
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("原密码不正确,输入错误3次将限制登录"));
        } else {
            redisService.delete(AdminCommonKeys.UPDATE_PASSWORD, userId + "");
        }
        return Result.success();
    }

    /**
     * 用户信息
     */
    @ApiOperation(value = "用户信息", notes = "根据用户id获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", example = "1", required = true, paramType = "path", dataType = "int")
    })
    @GetMapping("/info/{userId}")
    @RequiresPermissions("sys:user:info")
    public Result<SysUserInfoOut> info(@PathVariable("userId") Long userId) {
        SysUserInfoOut out = sysUserService.findSysUserInfoById(userId);
        return Result.success(out);
    }

    /**
     * 保存用户
     */
    @ApiOperation(value = "保存用户", notes = "保存用户")
    @SysDbLog("保存用户")
    @PostMapping("/save")
    @RequiresPermissions("sys:user:save")
    public Result save(@RequestBody SysUserEntity user) {
        ValidatorUtils.validateEntity(user, AddGroup.class);
        user.setCreateUserId(getUserId());
        sysUserService.saveUser(user);
        return Result.success();
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改用户", notes = "修改用户")
    @SysDbLog("修改用户")
    @PostMapping("/update")
    @RequiresPermissions("sys:user:update")
    public Result update(@RequestBody SysUserEntity user) {
        ValidatorUtils.validateEntity(user, UpdateGroup.class);

        user.setCreateUserId(getUserId());
        sysUserService.update(user);
        //若修改的是当前登陆用户 更新用户信息
        if (getUserId().longValue() == user.getUserId().longValue()) {
            ShiroUtils.reflushUser(user);
        }
        return Result.success();
    }

    /**
     * 删除用户
     */
    @ApiOperation(value = "删除用户", notes = "删除用户")
    @SysDbLog("删除用户")
    @PostMapping("/delete")
    @RequiresPermissions("sys:user:delete")
    public Result delete(@RequestBody @ApiParam(name = "userIds", value = "用户id集合", required = true) Long[] userIds) {
        if (ArrayUtils.contains(userIds, 1L)) {
            return Result.error(BasicCodeMsg.AUTHORIZATION_ERROR.setMsg("系统管理员不能删除"));
        }
        if (ArrayUtils.contains(userIds, getUserId())) {
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("当前用户不能删除"));
        }
        sysUserService.deleteBatch(userIds);
        return Result.success();
    }

    /**
     * 正则表达式验证密码 密码长度不小于6个字符，至少一个字母一个数字和一个特殊字符
     *
     * @param input 密码
     * @return
     */
    private boolean rexCheckPassword(String input) {
        String regex = "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[$@$.!%*#?&])[A-Za-z\\d$@$.!%*#?&]{6,}$";
        return input.matches(regex);
    }
}
