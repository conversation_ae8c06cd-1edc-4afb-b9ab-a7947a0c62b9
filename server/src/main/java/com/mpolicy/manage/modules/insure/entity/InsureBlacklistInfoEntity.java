package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保黑名单信息表
 * 
 * <AUTHOR>
 * @date 2022-11-08 13:58:37
 */
@TableName("insure_blacklist_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureBlacklistInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;

	/**
	 * 黑名单类型编码
	 */
	private String blacklistType;

	/**
	 * 证件类型
	 */
	private String identificationType;

	/**
	 * 姓名
	 */
	private String identificationName;

	/**
	 * 证件号码
	 */
	private String identificationNum;

	/**
	 * 黑名单说明
	 */
	private String blacklistDesc;

	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;

	/**
	 * 创建人
	 */
	@ApiModelProperty(hidden = true)
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(hidden = true)
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	@ApiModelProperty(hidden = true)
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(hidden = true)
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@ApiModelProperty(value = "乐观锁 更新时必须")
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
