package com.mpolicy.manage.modules.order.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 所有风险客户的字段，key值
 * 可通过他获取所有的该类型下的字典
 *
 *
 *
 * @create 2024/11/22
 * @since 1.0.0
 */
@Getter
public enum InsureOrderCustomerRiskDictionaryEnum {

    IDENTITY_TYPE("ORDER:ID_TYPE","证件类型"),
    CUSTOMER_TYPE("ORDER:CUSTOMER_TYPE","客户类型"),
    GENDER("ORDER:GENDER","性别"),
    MARITAL_STATUS("ORDER:MARITAL_STATUS","婚姻状况"),
    RISK_SOURCE("ORDER:CUSTOMER_RISK_SOURCE","风险来源"),
    CHANNEL_SOURCE("ORDER:CUSTOMER_SOURCE","渠道来源"),
    ;

    private final String parentKey;

    private final String description;

    InsureOrderCustomerRiskDictionaryEnum(String parentKey, String description) {
        this.parentKey = parentKey;
        this.description = description;
    }
}
