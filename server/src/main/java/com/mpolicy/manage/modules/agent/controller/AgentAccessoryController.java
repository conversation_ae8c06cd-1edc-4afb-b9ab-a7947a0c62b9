package com.mpolicy.manage.modules.agent.controller;

import java.util.List;

import com.mpolicy.manage.modules.agent.vo.resp.AgentAccessoryRespVo;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.service.AgentAccessoryService;
import com.mpolicy.common.result.Result;


/**
 * 经纪人人员附件
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:48
 */
@RestController
@RequestMapping("sys/agentaccessory")
@Api(tags = "经纪人人员附件")
public class AgentAccessoryController {

    @Autowired
    private AgentAccessoryService agentAccessoryService;

    /**
     * 获取打包后的文件
     */
    @ApiOperation(value = "获取打包后的文件", notes = "获取打包后的文件")
    @GetMapping("/downloadAsZip/{agentCode}/{type}")
    public Result<String> downloadAsZip(
            @ApiParam(value = "要下载的经纪人附件的类型", required = true)
            @PathVariable("type") String type,
            @ApiParam(value = "要下载的经纪人code", required = true)
            @PathVariable("agentCode") String agentCode) {
        String result = agentAccessoryService.downloadAsZip(type, agentCode);
        return Result.success(result);
    }

    /**
     * 获取附件信息列表
     */
    // @ApiOperation(value = "获取附件信息列表", notes = "获取附件信息列表")
    @GetMapping("/list/{agentCode}")
    public Result list(
            @ApiParam(value = "要获取的经纪人code", required = true)
            @PathVariable("agentCode") String agentCode) {
        List<AgentAccessoryRespVo> list = agentAccessoryService.list(agentCode);
        return Result.success(list);
    }
}
