package com.mpolicy.manage.modules.inte.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Description 智能推荐产品类型枚举
 * @Date 2023/4/10 15:03
 * <AUTHOR>
 **/
public enum InteRecommendProductTypeEnum {

    /**
     * 产品类型 1:意外 2:高风险职业 3:重疾 4:医疗 5:种植 6:农机 7:养殖 8:餐饮业综合 9:企财 10:家财 11:车险 12:防癌
     */
    TYPE_1(1, "意外险"),
    TYPE_2(2, "高风险职业险"),
    TYPE_3(3, "重疾险"),
    TYPE_4(4, "医疗险"),
    TYPE_5(5, "种植险"),
    TYPE_6(6, "农机险"),
    TYPE_7(7, "养殖险"),
    TYPE_8(8, "餐饮业综合保险"),
    TYPE_9(9, "企财险"),
    TYPE_10(10, "家财险"),
    TYPE_11(11, "车险"),
    TYPE_12(12, "防癌险");

    @Getter
    private Integer type;

    @Getter
    private String typeName;


    InteRecommendProductTypeEnum(Integer type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public static InteRecommendProductTypeEnum getEnumByType(Integer type) {
        return Arrays.stream(InteRecommendProductTypeEnum.values())
                .filter(x -> x.type.equals(type))
                .findFirst().orElse(null);
    }
}
