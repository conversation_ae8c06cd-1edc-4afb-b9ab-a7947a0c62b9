package com.mpolicy.manage.modules.policy.vo.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 理赔申请提交信息
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔申请提交信息")
@Data
public class ClaimApplyInput {

    /**
     * 所属理赔客户编号
     */
    @ApiModelProperty(value = "所属理赔客户编号", required = true)
    @NotBlank(message = "所属理赔客户编号不能为空")
    private String customerCode;

    /**
     * 所属理赔客户名称
     */
    @ApiModelProperty(value = "所属理赔客户名称", required = true)
    @NotBlank(message = "所属理赔客户名称不能为空")
    private String customerName;

    /**
     * 保单中心保单唯一编号
     */
    @ApiModelProperty(value = "保单中心保单唯一编号", required = true)
    @NotBlank(message = "保单中心保单唯一编号不能为空")
    private String contractCode;

    /**
     * 小程序客户证据号码
     */
    @ApiModelProperty(value = "小程序客户证据号码")
    private String certNo;

    /**
     * 理赔保单号
     */
    @ApiModelProperty(value = "理赔保单号", required = true)
    @NotBlank(message = "理赔保单号不能为空")
    private String policyCode;

    /**
     * 理赔保单名称
     */
    @ApiModelProperty(value = "理赔保单名称", required = true)
    @NotBlank(message = "理赔保单名称不能为空")
    private String policyName;

    /**
     * 被保人名称
     */
    @ApiModelProperty(value = "被保人名称", required = true)
    @NotBlank(message = "被保人名称不能为空")
    private String insuredName;

    /**
     * 投保人名称
     */
    @ApiModelProperty(value = "投保人名称", required = true)
    @NotBlank(message = "投保人名称不能为空")
    private String holderName;

    /**
     * 理赔联系手机号码
     */
    @ApiModelProperty(value = "理赔联系手机号码", required = true)
    @NotBlank(message = "理赔联系手机号码不能为空")
    private String claimMobile;

    /**
     * 理赔说明
     */
    @ApiModelProperty(value = "理赔说明")
    private String claimCommitSummary;

    /**
     * 创建来源 1客户申请2管理员
     */
    @ApiModelProperty(value = "创建来源 1客户申请2管理员", example = "2")
    @NotNull(message = "创建来源不能为空")
    private Integer createSource;
}
