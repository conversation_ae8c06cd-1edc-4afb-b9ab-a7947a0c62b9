package com.mpolicy.manage.modules.label.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LabelLibraryInfoSaveVo implements Serializable {
    private static final long serialVersionUID = 2862194243941390451L;

    @NotBlank(message = "标签库名称不能为空")
    @ApiModelProperty(value = "标签名称")
    private String libraryName;

    @ApiModelProperty(value = "标签库备注信息")
    private String remark;
}
