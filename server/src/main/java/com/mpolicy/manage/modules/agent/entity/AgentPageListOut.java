package com.mpolicy.manage.modules.agent.entity;

import com.mpolicy.manage.modules.agent.validator.group.AgentSaveOrUpdateGroup;
import com.mpolicy.manage.modules.agent.validator.group.AgentTransientGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @program: mpolicy-admin
 * @description: 代理人列表响应参数
 * @author: lsc
 * @created: 2022/03/30 14:22
 */
@Data
public class AgentPageListOut implements Serializable {
    private static final long serialVersionUID = -952327738476442317L;

    @ApiModelProperty(value = "经纪人工号")
    private String agentCode;

    @ApiModelProperty(value = "联系电话", required = true, example = "13452341234")
    private String mobile;

    @ApiModelProperty(value = "姓名", required = true, example = "测试姓名")
    @NotBlank(message = "姓名不能为空", groups = {AgentTransientGroup.class, AgentSaveOrUpdateGroup.class})
    private String agentName;

    @ApiModelProperty(value = "证件号", required = true, example = "130720198610081234")
    private String idCard;

    @ApiModelProperty(value = "所属机构或部门", required = true, example = "130720198610081234")
    private String orgName;

    @ApiModelProperty(value = "证件号类型", required = true, example = "130720198610081234")
    private String idType;

    @ApiModelProperty(value = "入职日期", required = true, example = "2021-02-17")
    private String entryDate;

    @ApiModelProperty(value = "入职方式")
    private String entryType;

    @ApiModelProperty(value = "职位", required = true, example = "AGENT_POSITION:1")
    private String position;

    @ApiModelProperty(value = "人员状态0:有效 1:无效 2:暂存", required = true, example = "0")
    private Integer agentStatus;

    @ApiModelProperty(value = "增员人姓名 仅作返回")
    private String recruitName;

    @ApiModelProperty(value = "业务编码", required = true, example = "U12346")
    private String businessCode;

    @ApiModelProperty("离职状态0:在职 1:离职")
    private Integer quitStatus;

    @ApiModelProperty("离职时间")
    private String quitTime;

    @ApiModelProperty("执业证编码")
    private String certificateNum;

    @ApiModelProperty("互联网营销告知书签署状态(0:未签约 2:已签约)")
    private Integer marketingSignStatus;

    @ApiModelProperty(value = "互联网告知书url")
    private String marketingUrl;

    @ApiModelProperty(value = "展业区域")
    private String acquisitionArea;
}
