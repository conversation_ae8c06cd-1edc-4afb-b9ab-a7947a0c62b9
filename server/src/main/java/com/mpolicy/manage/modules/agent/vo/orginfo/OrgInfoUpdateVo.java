package com.mpolicy.manage.modules.agent.vo.orginfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrgInfoUpdateVo extends OrgInfoSaveVo implements Serializable {
    private static final long serialVersionUID = -8062669448805615752L;

    private String orgCode;


    @ApiModelProperty(value = "版本号")
    private Integer revision;
}
