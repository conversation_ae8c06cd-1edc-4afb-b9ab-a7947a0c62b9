package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@TableName("agent_train_host")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentTrainHostEntity implements Serializable {
    private static final long serialVersionUID = -7399825016387083494L;

    /**
     *主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 培训编码
     */
    private String trainCode;

    /**
     * 代理人编码
     */
    private String agentCode;
}
