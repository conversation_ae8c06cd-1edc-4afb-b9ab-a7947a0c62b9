package com.mpolicy.manage.modules.insure.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.activity.vo.GirlEnrollPageListVo;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInfoEntity;
import com.mpolicy.manage.modules.insure.vo.InsureOrderList;
import com.mpolicy.manage.modules.insure.vo.insureOrderInfoOut;
import com.mpolicy.manage.modules.insure.vo.insureOrderInfoProOut;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 投保订单信息表
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
public interface InsureOrderInfoDao extends ImsBaseMapper<InsureOrderInfoEntity> {

    IPage<InsureOrderInfoEntity> queryInsureOrderInfoByPage(@Param("page") IPage page, @Param("input") Map<String, Object> input);

    IPage<insureOrderInfoOut> queryInsureOrderList(@Param("page") IPage page, @Param("input") Map<String, Object> input);
    IPage<insureOrderInfoProOut> znQueryInsureOrderList(@Param("page") IPage page, @Param("input") Map<String, Object> input);
}
