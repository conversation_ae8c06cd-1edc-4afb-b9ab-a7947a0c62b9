package com.mpolicy.manage.modules.insure.controller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.insure.service.InsureCustomerWhiteRosterService;
import com.mpolicy.manage.modules.insure.service.InsureUserWhiteRosterService;
import com.mpolicy.manage.modules.insure.vo.*;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 投保实名认证人脸识别白名单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-17 14:36:49
 */
@RestController
@RequestMapping("insure/whiteRoster")
@Api(tags = "投保实名认证人脸识别白名单")
public class InsureUserWhiteRosterController {

    @Autowired
    private InsureUserWhiteRosterService insureUesrWhiteRosterService;
    @Autowired
    private InsureCustomerWhiteRosterService insureCustomerWhiteRosterService;


    /**
     * 白名单列表
     */
    @ApiOperation(value = "获取投保实名认证人脸识别白名单列表", notes = "分页获取投保实名认证人脸识别白名单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "realName", dataType = "String", value = "姓名"),
            @ApiImplicitParam(paramType = "query", name = "cardNo", dataType = "String", value = "身份证号码"),
            @ApiImplicitParam(paramType = "query", name = "isStatus", dataType = "String", value = "是否启用"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "有效开始时间"),
            @ApiImplicitParam(paramType = "query", name = "rosterType", dataType = "String", value = "白名单类型 0：实名认证 1：人脸识别"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<PageUtils> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils page = insureUesrWhiteRosterService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 白名单详细信息
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "白名单详细信息", notes = "白名单详细信息")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<InsureUserWhiteRosterVO> info(@PathVariable("id") Integer id) {
        return Result.success(insureUesrWhiteRosterService.getWhiteRosterInfo(id));
    }

    /**
     * 白名单添加修改
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "白名单添加修改", notes = "白名单添加修改")
    @PostMapping("/saveOrUpdate")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<String> saveOrUpdate(@RequestBody @ApiParam(name = "insureUserWhiteRosterVO", value = "白名单信息", required = true) InsureUserWhiteRosterVO insureUserWhiteRosterVO) {
        ValidatorUtils.validateEntity(insureUserWhiteRosterVO);
        insureUesrWhiteRosterService.whiteRosterSaveOrUpdate(insureUserWhiteRosterVO);
        return Result.success();
    }

    /**
     * 白名单人脸识别导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "白名单人脸识别导入", notes = "白名单人脸识别导入")
    @PostMapping("/faceListUpload")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<List<String>> faceListUpload(@RequestParam(value = "fileCode") @ApiParam(name = "fileCode", required = true, value = "文件编码") String fileCode) {
        return Result.success(insureUesrWhiteRosterService.listUpload(fileCode));
    }

    /**
     * 白名单状态变更
     */
    @ApiOperation(value = "白名单状态变更", notes = "白名单状态变更")
    @PostMapping("/status/{id}")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<String> updateStatus(@PathVariable @ApiParam(name = "id", value = "编号", required = true) Integer id,
                                       @RequestParam(value = "isStatus") @ApiParam(name = "isStatus", value = "状态", required = true) Integer isStatus) {
        insureUesrWhiteRosterService.updateStatus(id, isStatus);
        return Result.success();
    }

    /**
     * 导出白名单模板
     */
    @GetMapping("/export")
    @RequiresPermissions(value = {"customer:white:all"})
    @ApiOperation(value = "导出白名单模板", notes = "导出白名单模板")
    public Result export(HttpServletResponse response) {
        ExcelUtil.writeExcel(response, Collections.emptyList(), URLUtil.encode("白名单模板", CharsetUtil.CHARSET_UTF_8), "sheet1", new WhiteRosterFaceOut());
        return Result.success();
    }

    /**
     * 白名单删除
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "白名单删除", notes = "白名单删除")
    @GetMapping("/delete/{id}")
    @RequiresPermissions(value = {"customer:white:all"})
    public Result<String> delete(@PathVariable("id") Integer id) {
        insureUesrWhiteRosterService.delete(id);
        return Result.success();
    }

    /***********************************************客户经理白名单************************************************************/
    /**
     * 客户经理白名单列表
     */
    @ApiOperation(value = "客户经理白名单列表", notes = "分页获取客户经理白名单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "realName", dataType = "String", value = "姓名"),
            @ApiImplicitParam(paramType = "query", name = "mobile", dataType = "String", value = "手机号"),
            @ApiImplicitParam(paramType = "query", name = "cardNo", dataType = "String", value = "身份证号码"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/customer/list")
    @RequiresPermissions(value = {"sales:white:all"})
    public Result<PageUtils> customerList(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils<InsureCustomerWhiteRosterOut> page = insureCustomerWhiteRosterService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 获取客户经理信息
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "获取客户经理信息", notes = "获取客户经理信息")
    @GetMapping("/customer/{mobile}")
    public Result<List<InsureCustomerInfoOut>> getCustomerInfo(@PathVariable("mobile") @ApiParam(name = "mobile", value = "手机号") String mobile) {
        return Result.success(insureCustomerWhiteRosterService.getCustomerInfo(mobile));
    }

    /**
     * 客户经理白名单添加修改
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "客户经理白名单添加修改", notes = "客户经理白名单添加修改")
    @PostMapping("/customer/saveOrUpdate")
    @RequiresPermissions(value = {"sales:white:all"})
    public Result<String> customerSaveOrUpdate(@RequestBody @ApiParam(name = "customerVo", value = "白名单信息", required = true) InsureCustomerWhiteRosterVo customerVo) {
        ValidatorUtils.validateEntity(customerVo);
        insureCustomerWhiteRosterService.customerSaveOrUpdate(customerVo);
        return Result.success();
    }

    /**
     * 客户经理导出白名单模板
     */
    @GetMapping("/customer/export")
    @ApiOperation(value = "客户经理导出白名单模板", notes = "客户经理导出白名单模板")
    public Result customerExport(HttpServletResponse response) {
        ArrayList<CustomerWhiteRosterFaceOut> list = Lists.newArrayList();
        CustomerWhiteRosterFaceOut out = new CustomerWhiteRosterFaceOut();
        out.setRealName("张三");
        out.setCardNo("11010119800101005X");
        out.setMobile("13299990000");
        out.setStartDate("2023/5/2");
        out.setEndDate("2023/5/23");
        out.setRemark("备注说明");
        list.add(out);
        ExcelUtil.writeExcel(response, list, URLUtil.encode("白名单模板", CharsetUtil.CHARSET_UTF_8), "sheet1", new CustomerWhiteRosterFaceOut());
        return Result.success();
    }

    /**
     * 客户经理白名单导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "客户经理白名单导入", notes = "客户经理白名单导入")
    @PostMapping("/customer/upload")
    @RequiresPermissions(value = {"sales:white:all"})
    public Result<List<String>> customerUpload(@RequestParam(value = "fileCode") @ApiParam(name = "fileCode", required = true, value = "文件编码") String fileCode) {
        return Result.success(insureCustomerWhiteRosterService.customerUpload(fileCode));
    }

    /**
     * 客户经理白名单导出
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "客户经理白名单导出", notes = "客户经理白名单导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "realName", dataType = "String", value = "姓名"),
            @ApiImplicitParam(paramType = "query", name = "mobile", dataType = "String", value = "手机号"),
            @ApiImplicitParam(paramType = "query", name = "cardNo", dataType = "String", value = "身份证号码"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/customer/exportList")
    public Result<String> customerListExport(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils<InsureCustomerWhiteRosterOut> page = insureCustomerWhiteRosterService.queryPage(params);
        ArrayList<CustomerWhiteRosterFaceOut> list = Lists.newArrayList();
        page.getList().forEach(x -> {
            CustomerWhiteRosterFaceOut vo = new CustomerWhiteRosterFaceOut();
            BeanUtils.copyProperties(x, vo);
            vo.setStartDate(DateUtils.convertDate2String(x.getStartDate() + " 00:00:00", "yyyy/MM/dd"));
            vo.setEndDate(DateUtils.convertDate2String(x.getEndDate() + " 00:00:00", "yyyy/MM/dd"));
            list.add(vo);
        });
        ExcelUtil.writeExcel(response, list, URLUtil.encode("白名单明细", StandardCharsets.UTF_8), "sheet1", new CustomerWhiteRosterFaceOut());
        return Result.success();
    }
}
