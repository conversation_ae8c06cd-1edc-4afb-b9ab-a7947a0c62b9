package com.mpolicy.manage.modules.sell.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.SellProductLabelMapEntity;
import com.mpolicy.manage.modules.sell.service.SellProductLabelMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * 售卖商品标签
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-22 12:02:26
 */
@RestController
@Api(tags = "售卖商品标签")
@RequestMapping("xjxh/sellproductlabelmap")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SellProductLabelMapController {

    private final SellProductLabelMapService sellProductLabelMapService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取售卖商品标签列表", notes = "分页获取售卖商品标签列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    public Result<PageUtils> list(@RequestParam Map<String, Object> params) {
        PageUtils page = sellProductLabelMapService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @GetMapping("/info/{id}")
    public Result<SellProductLabelMapEntity> info(@PathVariable("id") long id) {
        SellProductLabelMapEntity sellProductLabelMap = sellProductLabelMapService.getById(id);

        return Result.success(sellProductLabelMap);
    }
}
