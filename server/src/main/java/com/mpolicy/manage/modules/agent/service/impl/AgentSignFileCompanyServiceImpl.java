package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.authorize.client.AuthorizeCenterClient;
import com.mpolicy.authorize.common.sms.SendBatchSmsInput;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.CustomerKeys;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.enums.SmsCodeEnum;
import com.mpolicy.manage.modules.agent.dao.AgentSignFileCompanyDao;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.enums.AgentSignFileStatusEnum;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFileDetailPageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignNumOut;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignSubmitUploadInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 代理人签约文件公司信息service实现
 * @return
 * @Date 2023/5/29 18:05
 * <AUTHOR>
 **/
@Service("agentSignFileCompanyService")
public class AgentSignFileCompanyServiceImpl extends ServiceImpl<AgentSignFileCompanyDao, AgentSignFileCompanyEntity> implements AgentSignFileCompanyService {

    @Autowired
    private AgentSignFileService agentSignFileService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private AgentSignFileDetailService agentSignFileDetailService;
    @Autowired
    private AgentUserInfoService agentUserInfoService;
    @Autowired
    private AuthorizeCenterClient authorizeCenterClient;
    @Autowired
    private IRedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUpload(AgentSignSubmitUploadInput input) {
        //构建文件信息
        List<AgentSignFileEntity> fileList = buildFileList(input);
        //构建文件公司对应信息
        List<AgentSignFileCompanyEntity> fileCompanyList = buildFileCompanyList(input);
        //保存相关信息
        agentSignFileService.saveBatch(fileList);
        //发送短信提醒
        if(input.getEnabled() == StatusEnum.NORMAL.getCode()){
            //给符合条件的代理人发送短信提醒
//            List<String> orgCodeList = input.getFileList().stream().map(AgentSignFileInfo::getOrgCode).distinct().collect(Collectors.toList());
//            this.sendFileMsg(orgCodeList);
            //启用后触发清空弹窗操作

        }
        this.saveBatch(fileCompanyList);
        //构建签署文件信息
        List<AgentSignFileDetailEntity> detailList = buildAgentSignFileDetailList(fileCompanyList);
        agentSignFileDetailService.saveBatch(detailList);
        this.cleanConfirm();
    }

    @Override
    public void cleanConfirm() {
        List<AgentSignFileDetailEntity> list = this.agentSignFileDetailService.lambdaQuery().eq(AgentSignFileDetailEntity::getDeleted, StatusEnum.INVALID.getCode()).list();
        list.stream().map(AgentSignFileDetailEntity::getAgentCode).distinct().forEach(a->{
            redisService.delete(CustomerKeys.FILESTATUS, a);
        });
    }

    private List<AgentSignFileDetailEntity> buildAgentSignFileDetailList(List<AgentSignFileCompanyEntity> fileCompanyList){
        List<AgentSignFileDetailEntity> result = Lists.newArrayList();
        fileCompanyList.forEach(a->{
            List<String> childNode = orgInfoService.getChildNode(a.getOrgCode());
            //查询需要签署的人员信息
            List<AgentUserInfoEntity> list = agentUserInfoService.lambdaQuery().eq(AgentUserInfoEntity::getAgentType, a.getAgentType())
                    .eq(AgentUserInfoEntity::getQuitStatus,StatusEnum.INVALID.getCode())
                    .eq(AgentUserInfoEntity::getAgentStatus,StatusEnum.NORMAL.getCode())
                    .in(AgentUserInfoEntity::getOrgCode, childNode).list();
            if(CollectionUtil.isNotEmpty(list)){
                list.forEach(b->{
                    AgentSignFileDetailEntity bean = new AgentSignFileDetailEntity();
                    bean.setAgentCode(b.getAgentCode());
                    bean.setCompanyId(a.getId());
                    bean.setCompanyOrgCode(a.getOrgCode());
                    bean.setOrgCode(b.getOrgCode());
                    bean.setFileCode(a.getFileCode());
                    bean.setSignStatus(AgentSignFileStatusEnum.STATUS0.getCode());
                    result.add(bean);
                });
            }
        });
        return result;
    }

    @Override
    public void sendFileMsg(List<String> orgCodeList){
        //给指定机构下，未离职代理人发送短信信息
        List<String> allOrgCodeList = Lists.newArrayList();
        orgCodeList.forEach(a->{
            allOrgCodeList.add(a);
            List<OrgInfoVo> orgSonList = orgInfoService.getOrgSonList(a);
            List<String> orgSonCodeList = orgSonList.stream().map(OrgInfoVo::getOrgCode).collect(Collectors.toList());
            allOrgCodeList.addAll(orgSonCodeList);
        });
        List<AgentUserInfoEntity> list = agentUserInfoService.lambdaQuery().in(AgentUserInfoEntity::getOrgCode, allOrgCodeList)
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode()).list();
        if(CollectionUtil.isNotEmpty(list)){
            List<String> mobiles = list.stream().filter(a-> StringUtils.isNotBlank(a.getMobile())).map(AgentUserInfoEntity::getMobile).collect(Collectors.toList());
            // 进行短信发送
            SendBatchSmsInput sendBatchSmsInput = new SendBatchSmsInput();
            sendBatchSmsInput.setSmsCode(SmsCodeEnum.ADD_SCHEDULE.getCodeType());
            sendBatchSmsInput.setMobiles(mobiles);
//            authorizeCenterClient.sendBatchSms(AdminPublicConstant.SMS_CHANNEL_CODE, sendBatchSmsInput);
        }
    }

    private List<AgentSignFileEntity> buildFileList(AgentSignSubmitUploadInput input){
        List<AgentSignFileEntity> fileList = Lists.newArrayList();
        //构建文件信息
        input.getFileList().stream().forEach(a->{
            AgentSignFileEntity fileCodeBean = fileList.stream().filter(b -> a.getFileCode().equals(b.getFileCode())).findAny().orElse(null);
            if(Objects.nonNull(fileCodeBean)){
                return ;
            }
            AgentSignFileEntity bean = new AgentSignFileEntity();
            BeanUtils.copyProperties(a,bean);
            bean.setEnabled(input.getEnabled());
            if(input.getEnabled() == 1){
                Date now = new Date();
                bean.setEnableTime(now);
                bean.setExpireTime(DateUtils.addDateDays(now,3));
            }
            fileList.add(bean);
        });
        return fileList;
    }

    private List<AgentSignFileCompanyEntity> buildFileCompanyList(AgentSignSubmitUploadInput input){
        List<AgentSignFileCompanyEntity> fileCompanyList = Lists.newArrayList();
        List<String> orgCodeList = Lists.newArrayList();
        input.getFileList().stream().forEach(a->{
            AgentSignFileCompanyEntity bean = new AgentSignFileCompanyEntity();
            BeanUtils.copyProperties(a,bean);
            orgCodeList.add(a.getOrgCode());
            fileCompanyList.add(bean);
        });
        //匹配对应code的顶级组织机构信息
        Map<String, OrgInfoEntity> firstOrgInfo = orgInfoService.findFirstOrgInfo(orgCodeList);
        fileCompanyList.stream().forEach(a->{
            //获取对应的组织机构信息
            OrgInfoEntity orgInfoEntity = firstOrgInfo.get(a.getOrgCode());
            if(Objects.nonNull(orgInfoEntity)){
                a.setTopOrgCode(orgInfoEntity.getOrgCode());
            }
        });
        return fileCompanyList;
    }

    /**
     * 文件详细信息分页查询
     * @param fileCode 文件编码
     * @param params 分页参数
     */
    @Override
    public PageUtils<AgentSignFileDetailPageList> detailPageList(String fileCode, Map<String, Object> params) {
        params.put("fileCode",fileCode);
        IPage<AgentSignFileDetailPageList> iPage = this.baseMapper.detailPageList(new Page(Long.parseLong(params.get("page").toString()), Long.parseLong(params.get("limit").toString())), params);
        List<AgentSignFileDetailPageList> list = iPage.getRecords();
        list.stream().forEach(a->{
            String idList = a.getIdList();
            List<Integer> idLists = Arrays.stream(idList.split(",")).map(b -> Integer.parseInt(b)).collect(Collectors.toList());
            if(a.getEnabled().equals(StatusEnum.NORMAL.getCode())){
                AgentSignNumOut agentSignNumOut = agentSignFileDetailService.querySignNumByCodeAndCompanyId(fileCode, idLists);
                a.setShouldNum(agentSignNumOut.getShouldNum());
                a.setAlreadyNum(agentSignNumOut.getAlreadyNum());
                a.setDefaultFinishNum(agentSignNumOut.getDefaultFinishNum());
                a.setUnsignedNum(agentSignNumOut.getUnsignedNum());
            }else{
                a.setShouldNum(0);
                a.setAlreadyNum(0);
                a.setDefaultFinishNum(0);
                a.setUnsignedNum(0);
            }
        });
        return new PageUtils<>(list, (int)iPage.getTotal(), (int)iPage.getSize(), (int)iPage.getCurrent());
    }
}
