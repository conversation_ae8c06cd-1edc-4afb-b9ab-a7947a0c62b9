package com.mpolicy.manage.modules.tools.component;

import com.mpolicy.manage.modules.tools.service.ToolsModelChangeLogService;
import com.mpolicy.manage.modules.tools.vo.ToolsModelChangeLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @author: yangdonglin
 * @create: 2023-06-12 11:37
 * @description: 模型变更记录Help
 */
@Component
@Slf4j
public class ToolsModelChangeLogHelp {

    public static ToolsModelChangeLogHelp toolsModelChangeLogHelp;

    @Autowired
    @Lazy
    private ToolsModelChangeLogService toolsModelChangeLogService;


    @PostConstruct
    public void init() {
        toolsModelChangeLogHelp = this;
        toolsModelChangeLogHelp.toolsModelChangeLogService = this.toolsModelChangeLogService;
    }

    /**
     * 新增模型变更记录
     *
     * @param logVo
     */
    public static void save(ToolsModelChangeLogVo logVo) {
        toolsModelChangeLogHelp.toolsModelChangeLogService.save(logVo);
    }
}
