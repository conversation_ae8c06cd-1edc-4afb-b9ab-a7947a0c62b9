package com.mpolicy.manage.modules.policy.vo.policy;

import com.mpolicy.policy.common.ep.policy.EpProductInfoVo;
import com.mpolicy.policy.common.policy.vo.sync.PolicyProductInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("保障计划|虚拟计划")
public class PremiumPlanVo {

    @ApiModelProperty("虚拟计划｜保障计划编码")
    private String virtualPlanCode;

    @ApiModelProperty("虚拟计划｜保障计划名称")
    private String virtualPlanName;

    @ApiModelProperty("保单险种列表")
    private List<EpProductInfoVo> policyProductList;
}
