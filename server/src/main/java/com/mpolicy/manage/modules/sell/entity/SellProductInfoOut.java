package com.mpolicy.manage.modules.sell.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SellProductInfoOut extends SellProductListOut implements Serializable {
    private static final long serialVersionUID = 3773748830124297872L;

    @ApiModelProperty(value = "产品副标题")
    private String productSubtitle;

    @ApiModelProperty(value = "组合名称")
    private String portfolioName;

    @ApiModelProperty(value = "投保年龄")
    private String insuranceAge;

    @ApiModelProperty(value = "保障期间")
    private String guaranteePeriod;

    @ApiModelProperty(value = "展示保费")
    private String productPrice;

    @ApiModelProperty(value = "展示保费单位")
    private String productPriceUnit;

    @ApiModelProperty(value = "犹豫期")
    private String hesitationPeriod;

    @ApiModelProperty(value = "是否需要回执 0:不需要 1:需要")
    private Integer isReceipt;

    @ApiModelProperty(value = "是否需要回复 0:不需要 1:需要")
    private Integer isCallback;

    @ApiModelProperty(value = "选中的适用人群:CHILDREN;OLD_MAN;ADULT;TRAVEL;DOMESTIC_TRAVEL;OVERSEAS_TRAVEL;SELF_DRIVING_TOUR")
    private String applicableGroupList;

    @ApiModelProperty(value = "产品标签")
    private String productLabel;

    @ApiModelProperty(value = "小程序产品顺序")
    private BigDecimal sort;

    @ApiModelProperty(value = "经纪人产品顺序")
    private BigDecimal agentSort;

    @ApiModelProperty(value = "售卖方式0:网销 1:电销 2:线下")
    private Integer sellMode;

    @ApiModelProperty(value = "在售状态 0:不在售 1:在售")
    private Integer sellStatus;

    @ApiModelProperty(value = "投保链接类型 0:内部 1:小程序")
    private Integer insureUrlType;

    @ApiModelProperty(value = "投保链接")
    private String insureUrl;

    @ApiModelProperty(value = "续购链接")
    private String renewalUrl;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "产品缩封面")
    private String productCover;

    @ApiModelProperty(value = "产品缩略图")
    private String productThumbnail;

    @ApiModelProperty(value = "产品介绍文档编码")
    private String productDocumentCode;

    @ApiModelProperty(value = "产品介绍文档链接")
    private String productDocument;

    @ApiModelProperty(value = "产品介绍文档名称")
    private String productDocumentName;

    @ApiModelProperty(value = "产品视频")
    private String productVideo;

    @ApiModelProperty(value = "小程序产品视频")
    private String weChatMiniProductVideo;

    @ApiModelProperty(value = "小程序产品视频封面")
    private String weChatMiniProductVideoCover;

    @ApiModelProperty(value = "产品视频封面")
    private String productVideoPoster;

    @ApiModelProperty(value = "小程序产品类型")
    private String weChatMiniProductType ;

    @ApiModelProperty(value = "产品详情图")
    private String productDetails;

    @ApiModelProperty(value = "报备状态(字典)")
    private String reportStatus;

    @ApiModelProperty(value = "数据库锁")
    private Long revision;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "停售时间")
    private Date stopSellTime;

    @ApiModelProperty(value = "售卖地区")
    private List<SellAreaOut> sellAreaList;

    @ApiModelProperty(value = "农保地区")
    private List<SellAreaOut> ruralAreaList;

    @ApiModelProperty(value = "禁销分支")
    private List<SellAreaOut> prohibitBranchList;

    @ApiModelProperty(value = "售卖标签")
    private List<String> sellLabelList;


    @ApiModelProperty(value = "售卖产品关键词")
    private List<String> sellKeyword;

    @ApiModelProperty(value = "是否需要实名认证 0:不需要 1:需要")
    private Integer isRealAuth;


    @ApiModelProperty(value = "是否农村主推 0:不是 1:是")
    private Integer isRural;

    @ApiModelProperty(value = "是否为农村热销 0:不是 1:是")
    private Integer isRuralHotSale;

    @ApiModelProperty(value = "允许分享计划 0:允许 1:允许")
    private Integer isSharePlan;

    @ApiModelProperty(value = "是否需要绑定代理人 0:不需要 1:需要")
    private Integer isBindAgent;

    @ApiModelProperty(value = "投保状态 0：不允许投保 1：可以投保")
    private Integer insureStatus;

    @ApiModelProperty(value = "投保方式")
    private String insureMode;

    @ApiModelProperty(value = "是否人证码校验0:不是 1:是")
    private Integer isMobileVerify;

    @ApiModelProperty(value = "是否人工审核0:不是1:是")
    private Integer isPersonReview;

    @ApiModelProperty(value = "是否赠险0:不是1:是")
    private Integer isGive;

    @ApiModelProperty(value = "续保专用 0:不是1:是")
    private Integer isRenewal;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "是否四级分销0：不是，1：是")
    private Integer isLevelSale;

    @ApiModelProperty(value = "是否显示智能问答 0:否 1:是")
    private Integer isIntelligentSolution;

    @ApiModelProperty(value = "是否必须展示试算 0:否 1:是")
    private Integer isRecalculate;

    @ApiModelProperty(value = "发票状态0:未对接 1:启用 2:禁用")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "发票模版编码")
    private String invoiceTemplateCode;


    @ApiModelProperty(value = "可售渠道")
    private List<String> availableChannelCodeList;

    @ApiModelProperty(value = "陈列位置标记")
    private String productDisplayBlock;

    @ApiModelProperty(value = "商品分类：团意险，雇主险，公责险，安责险，其他...")
    private String displayCategory;

    @ApiModelProperty(value = "流程类型：在线投保，报价咨询...")
    private String operationModel;
}
