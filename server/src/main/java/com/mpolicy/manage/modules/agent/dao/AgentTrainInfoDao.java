package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.agent.entity.AgentTrainInfoEntity;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainDurationOut;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListVo;
import org.apache.ibatis.annotations.Param;

/**
 * 代理人培训信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
public interface AgentTrainInfoDao extends BaseMapper<AgentTrainInfoEntity> {

    /**
     * 获取培训信息表列表
     *
     * @param page
     * @param params
     * @return
     */
    IPage<AgentTrainInfoListOut> findPageList(@Param("page") Page<AgentTrainInfoListVo> page, @Param("params") AgentTrainInfoListVo params);

    /**
     * 获取代理人培训时长
     *
     * @param agentCode
     * @return
     */
    AgentTrainDurationOut findAgentTrainDuration(String agentCode);
}
