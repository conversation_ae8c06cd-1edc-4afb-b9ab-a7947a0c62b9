package com.mpolicy.manage.modules.agent.vo.questionnaire;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AgentQuestionnaireQuestionVo {

    private Integer id;
    /**
     * bl_agent_questionnaire表主键id
     */
    private Integer questionnaireId;
    /**
     * 题目
     */
    private String title;
    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    private Integer type;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 题型(1:单选题 2:多选题 3:判断题)
     */
    private String typeDesc;
    /**
     * 答案列表
     */
    private List<AgentQuestionnaireAnswerVo> answerList;
}
