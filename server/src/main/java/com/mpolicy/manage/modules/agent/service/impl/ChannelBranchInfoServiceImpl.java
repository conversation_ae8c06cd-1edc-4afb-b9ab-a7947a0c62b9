package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.manage.modules.agent.dao.ChannelBranchInfoDao;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelBranchInfoService;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/2 18:00
 */
@Service
public class ChannelBranchInfoServiceImpl extends ServiceImpl<ChannelBranchInfoDao, ChannelBranchInfoEntity> implements ChannelBranchInfoService {
    @Autowired
    ChannelInfoService channelInfoService;

    @Override
    public boolean changeEnable(Integer id, Integer enabled, long revision) {
        return this.updateById(ChannelBranchInfoEntity.builder().id(id).enabled(enabled).revision(revision).build());
    }

    @Override
    public List<ChannelBranchInfoEntity> getChannelList() {
        return channelInfoService.list()
                .stream().map(x ->
                        ChannelBranchInfoEntity.builder()
                                .id(-x.getId())
                                .branchCode(x.getChannelCode())
                                .branchName(x.getChannelName())
                                .branchLevel(0)
                                .createTime(x.getCreateTime())
                                .enabled(x.getEnabled())
                                .isNotEmpty(true)
                                .build()
                ).sorted(Comparator.comparing(ChannelBranchInfoEntity::getEnabled).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<ChannelBranchInfoEntity> getBranchList(String parentCode, Integer branchLevel) {
        return this.list(
                Wrappers.<ChannelBranchInfoEntity>lambdaQuery()
                        .eq(branchLevel == 0, ChannelBranchInfoEntity::getChannelCode, parentCode)
                        .eq(branchLevel > 0, ChannelBranchInfoEntity::getBranchParentCode, parentCode)
                        .eq(ChannelBranchInfoEntity::getBranchLevel, branchLevel + 1)
        );
    }

    @Override
    public List<ChannelBranchInfoEntity> getChannelBranchList(String channelCode) {
        List<ChannelBranchInfoEntity> list = this.lambdaQuery().eq(ChannelBranchInfoEntity::getChannelCode, channelCode).list();
        return list;
    }


    /**
     * 获取渠道树列表
     *
     * @return
     */
    @Override
    public List<TreeListOut> findChannelTreeList() {
        //获取渠道列表
        List<ChannelInfoEntity> channelList = channelInfoService.lambdaQuery()
                .eq(ChannelInfoEntity::getEnabled, StatusEnum.NORMAL.getCode())
                .orderByAsc(ChannelInfoEntity::getId)
                .list();
        List<TreeListOut> rootList = new ArrayList<>();
        List<TreeListOut> resultList = new ArrayList<>();
        channelList.forEach(action -> {
            TreeListOut out = new TreeListOut();
            out.setId(action.getChannelCode() + "-C");
            out.setLabel(action.getChannelName());
            rootList.add(out);
            resultList.add(out);
        });
        //获取分支列表
        List<ChannelBranchInfoEntity> branchInfoList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ChannelBranchInfoEntity::getEnabled, StatusEnum.NORMAL.getCode())
                .orderByAsc(ChannelBranchInfoEntity::getId)
                .list();

        branchInfoList.forEach(action -> {
            TreeListOut out = new TreeListOut();
            out.setId(action.getBranchCode() + "-B");
            out.setLabel(action.getBranchName());
            if (StrUtil.isNotBlank(action.getBranchParentCode())) {
                out.setParentId(action.getBranchParentCode() + "-B");
            } else {
                out.setParentId(action.getChannelCode() + "-C");
            }
            rootList.add(out);
        });
        //递归处理树形结构的数据
        resultList.forEach(action -> {
            action.setChildren(getChild(action.getId(), rootList));
        });
        return resultList;
    }

    /**
     * @param parentId
     * @param root
     * @return
     */
    private List<TreeListOut> getChild(String parentId, List<TreeListOut> root) {
        List<TreeListOut> childList = new ArrayList<>();
        root.forEach(action -> {
            if (parentId.equals(action.getParentId())) {
                childList.add(action);
            }
        });
        if (childList.isEmpty()) {
            return null;
        }
        // 把子菜单的子菜单再循环一遍
        childList.forEach(action -> {
            action.setChildren(getChild(action.getId(), root));
        });
        return childList;
    }
}
