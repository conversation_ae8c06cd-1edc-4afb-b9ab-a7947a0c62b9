package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agent.vo.questionnaire.AgentAnswerDetail;
import com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionAnswerDetail;
import com.mpolicy.manage.modules.agent.vo.questionnaire.AgentQuestionnaireOut;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 代理人问卷表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
public interface AgentQuestionnaireDao extends BaseMapper<AgentQuestionnaireEntity> {


    IPage<AgentQuestionnaireOut> queryQuestionnairePage(@Param("page") IPage page, @Param("params") Map<String, Object> params);

    List<AgentQuestionAnswerDetail> queryAgentAnswerPage(@Param("agentCode") String agentCode, @Param("questionnaireId") Integer questionnaireId);

    List<AgentQuestionAnswerDetail> details(Integer questionnaireId);

    IPage<AgentQuestionnaireOut> queryInvestigatePage(@Param("page") IPage page, @Param("params") Map<String, Object> params);
}
