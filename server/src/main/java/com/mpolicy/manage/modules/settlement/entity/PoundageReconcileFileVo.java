package com.mpolicy.manage.modules.settlement.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PoundageReconcileFileVo implements Serializable {
    private static final long serialVersionUID = 2776223282375834690L;

    @ApiModelProperty(value = "文件编码",example = "0001")
    private String fileCode;

    @ApiModelProperty(value = "文件名称",example = "111.xsl")
    private String fileName;

    @ApiModelProperty(value = "文件上传路径",example = "http://xxxx")
    private String filePath;
}
