package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.sell.entity.SellProductNoticeEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductNoticeListOut;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISellProductNoticeService extends IService<SellProductNoticeEntity> {

    /**
     * 获取产品告知书
     * @param productCode
     * @return
     */
    List<SellProductNoticeListOut> findSellProductNoticeList(String productCode);
}
