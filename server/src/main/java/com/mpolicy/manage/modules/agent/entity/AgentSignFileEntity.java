package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人签约文件信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
@TableName("agent_sign_file")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentSignFileEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 上传文件编码
     */
    private String fileCode;
    /**
     * 上传文件名称
     */
    private String fileName;
    /**
     * 上传文件路径
     */
    private String filePath;
    /**
     * 启用状态 0:未启用 1:启用
     */
    private Integer enabled;
    /**
     * 启用时间
     */
    private Date enableTime;
    private Date expireTime;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
