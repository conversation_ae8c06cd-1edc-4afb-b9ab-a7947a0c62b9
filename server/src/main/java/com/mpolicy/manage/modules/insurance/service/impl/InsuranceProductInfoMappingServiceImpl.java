package com.mpolicy.manage.modules.insurance.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.dao.InsuranceProductInfoMappingDao;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoMappingEntity;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoMappingMapEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoMappingMapService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoMappingService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.insurance.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by 陈杰生
 * 2024/1/17 10:46
 */
@Slf4j
@Service("InsuranceProductInfoMappingService")
public class InsuranceProductInfoMappingServiceImpl extends ServiceImpl<InsuranceProductInfoMappingDao, InsuranceProductInfoMappingEntity> implements InsuranceProductInfoMappingService {

    @Autowired
    private InsuranceProductInfoMappingMapService mapService;

    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;

    /**
     * @param productInfoMappingVO : 产品与险种映射信息
     * @Date: 2024/1/17 16:02
     * @Description: 保存产品与险种映射信息
     */
    @Override
    public void saveOrUpdateProductInfoMapping(ProductInfoMappingVO productInfoMappingVO) {
        log.info("产品与险种映射信息ProductInfoMappingVO={}", productInfoMappingVO);
        // 1. 数据校验
        checkParam(productInfoMappingVO);
        // 2. 根据Id删除产品与险种映射信息
        this.remove(Wrappers.<InsuranceProductInfoMappingEntity>lambdaQuery().eq(InsuranceProductInfoMappingEntity::getProductCode, productInfoMappingVO.getProductCode()));
        InsuranceProductInfoMappingEntity productInfoMappingEntity = new InsuranceProductInfoMappingEntity();
        // 3. vo -> entity
        buildProductInfoMappingEntity(productInfoMappingEntity, productInfoMappingVO);
        // 4. 保存产品与险种映射信息
        this.save(productInfoMappingEntity);
    }

    /**
     * @param productCode : 产品编码
     * @Date: 2024/1/17 16:59
     * @Description: 根据产品编码删除产品与险种映射信息
     */
    @Override
    public void deleteProductInfoMapping(String productCode) {
        log.info("根据产品编码删除产品与险种映射信息productCode={}", productCode);
        this.remove(Wrappers.<InsuranceProductInfoMappingEntity>lambdaQuery().eq(InsuranceProductInfoMappingEntity::getProductCode, productCode));
    }

    /**
     * @param input : 分页查询Input
     * @Date: 2024/1/17 17:02
     * @Description: 分页查询
     */
    @Override
    public PageUtils<ProductInfoMappingOut> queryPage(ProductInfoMappingInput input) {
        log.info("分页查询ProductInfoMappingInput={}", input);
        // 1. 获取分页数据
        IPage<ProductInfoMappingOut> page = baseMapper.getProductInfoMappingOutList(new Page(input.getPage(), input.getLimit()), input);
        return new PageUtils<>(page);
    }

    /**
     * @param productCode : 产品编码
     * @return : com.mpolicy.manage.modules.insurance.vo.ProductInfoMappingVO
     * @Date: 2024/1/18 9:26
     * @Description: 单查
     */
    @Override
    public ProductInfoMappingVO queryProductInfoMappingDetail(String productCode) {
        log.info("单查productCode={}", productCode);
        ProductInfoMappingVO productInfoMappingVO = new ProductInfoMappingVO();
        // 1. 查询产品信息映射表
        InsuranceProductInfoMappingEntity infoMappingEntity = this.lambdaQuery().eq(InsuranceProductInfoMappingEntity::getProductCode, productCode).one();
        BeanUtils.copyProperties(infoMappingEntity, productInfoMappingVO);
        // 2. 查询产品信息映射map表
        List<ProductInfoMappingMapVO> productInfoMappingMapVOS = mapService.lambdaQuery().eq(InsuranceProductInfoMappingMapEntity::getProductCode, productCode).list().stream()
                .map(x -> ProductInfoMappingMapVO
                        .builder()
                        .insuranceTypeCode(x.getInsuranceTypeCode())
                        .insuranceTypeName(x.getInsuranceTypeName())
                        .build())
                .collect(Collectors.toList());
        productInfoMappingVO.setProductInfoMappingMapVOList(productInfoMappingMapVOS);
        // 3. 返回构建好的productInfoMappingVO
        return productInfoMappingVO;
    }

    /**
     * @param input    :
     * @param response :
     * @Date: 2024/1/18 9:43
     * @Description: 导出 产品与险种映射配置
     */
    @Override
    public void exportProductInfoMappingDetail(ProductInfoMappingInput input, HttpServletResponse response) {
        log.info("导出产品与险种映射配置ProductInfoMappingVO={}", input);
        // 1.  查询产品信息映射表
        List<ExportProductInfoMappingExcel> excelList = baseMapper.getProductInfoMappingOutExcelList(input)
                .stream().map(x -> ExportProductInfoMappingExcel
                        .builder()
                        .productCode(x.getProductCode())
                        .productName(x.getProductName())
                        .companyName(x.getCompanyName())
                        .productMappingInfo(x.getProductMappingInfo())
                        .build()
                ).collect(Collectors.toList());
        // 2. 设置excel表格
        try (ServletOutputStream out = response.getOutputStream()) {
            String fileName = "产品与险种映射配置.xlsx";
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode(fileName));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, ExportProductInfoMappingExcel.class);
            sheet.setSheetName("产品与险种映射配置");
            writer.write(excelList, sheet);
            writer.finish();
            out.flush();
        } catch (Exception e) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("导出产品与险种映射配置失败"));
        }
    }

    /**
     * @param productCode : 险种编码
     * @return : java.lang.String
     * @Date: 2024/1/18 15:02
     * @Description: 根据险种编码查险种名称
     */
    @Override
    public String queryInsuranceTypeName(String productCode, String companyCode) {
        InsuranceProductInfoEntity productInfoEntity = Optional.ofNullable(insuranceProductInfoService.lambdaQuery().eq(InsuranceProductInfoEntity::getProductCode, productCode).one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该险种在系统中不存在")));
        if (companyCode == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("保险公司编码不能为空"));
        }
        if (!StringUtils.equals(productInfoEntity.getCompanyCode(), companyCode)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该险种不属于该保险公司"));
        }
        InsuranceProductInfoMappingMapEntity mappingMapEntity = mapService.lambdaQuery().eq(InsuranceProductInfoMappingMapEntity::getInsuranceTypeCode, productCode).one();
        if (!Objects.isNull(mappingMapEntity)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该产品已经关联了险种:" + productCode));
        }

        boolean present = Optional.ofNullable(mapService.lambdaQuery().eq(InsuranceProductInfoMappingMapEntity::getProductCode, productCode).one())
                .isPresent();
        if (present) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("该险种已经关联了其他产品，不允许重复关联"));
        }
        return productInfoEntity.getProductName();
    }

    /**
     * @param productInfoMappingEntity : 产品与险种映射配置entity
     * @param productInfoMappingVO     : 产品与险种映射配置Vo
     * @Date: 2024/1/17 16:16
     * @Description: vo -> entity
     */
    private void buildProductInfoMappingEntity(InsuranceProductInfoMappingEntity productInfoMappingEntity, ProductInfoMappingVO productInfoMappingVO) {
        productInfoMappingEntity.setProductCode(productInfoMappingVO.getProductCode());
        productInfoMappingEntity.setProductName(productInfoMappingVO.getProductName());
        productInfoMappingEntity.setCompanyCode(productInfoMappingVO.getCompanyCode());
        productInfoMappingEntity.setCompanyName(productInfoMappingVO.getCompanyName());
        productInfoMappingEntity.setProductMappingInfo(productInfoMappingVO.getProductInfoMappingMapVOList().stream().map(ProductInfoMappingMapVO::getInsuranceTypeName).collect(Collectors.joining(", ")));
    }

    /**
     * @param productInfoMappingVO : 产品与险种映射信息
     * @Date: 2024/1/18 15:39
     * @Description: 校验
     */
    private void checkParam(ProductInfoMappingVO productInfoMappingVO) {
        if (productInfoMappingVO.getProductInfoMappingMapVOList().stream().map(ProductInfoMappingMapVO::getInsuranceTypeCode).distinct().count() != productInfoMappingVO.getProductInfoMappingMapVOList().size()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("存在重复的险种编码"));
        }
    }


}
