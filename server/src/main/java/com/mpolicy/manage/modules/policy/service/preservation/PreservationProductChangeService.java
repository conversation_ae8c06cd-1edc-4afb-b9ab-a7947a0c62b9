package com.mpolicy.manage.modules.policy.service.preservation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.policy.entity.PreservationFlowEntity;
import com.mpolicy.manage.modules.policy.entity.PreservationProductChangeEntity;

/**
 * 险种变更保全明细
 *
 * <AUTHOR>
 */
public interface PreservationProductChangeService extends IService<PreservationProductChangeEntity> {
}
