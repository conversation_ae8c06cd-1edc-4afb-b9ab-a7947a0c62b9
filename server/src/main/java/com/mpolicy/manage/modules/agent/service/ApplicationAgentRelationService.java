package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ApplicationAgentRelationEntity;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 渠道代理人关系表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 19:32:39
 */
public interface ApplicationAgentRelationService extends IService<ApplicationAgentRelationEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveOrUpdateAgentRelation(String applicationCode, List<AgentInfoVo> agentInfoVoList);
}

