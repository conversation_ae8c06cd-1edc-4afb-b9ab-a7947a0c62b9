package com.mpolicy.manage.modules.policy.service;

import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsQueryVo;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsResponseVo;

/**
 * AI对话记录服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface AiConversationService {

    /**
     * 查询AI聊天对话记录
     *
     * @param queryVo 查询参数
     * @return 对话记录响应结果
     */
    ConversationRecordsResponseVo getConversationRecords(ConversationRecordsQueryVo queryVo);
}
