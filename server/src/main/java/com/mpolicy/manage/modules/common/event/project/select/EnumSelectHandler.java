package com.mpolicy.manage.modules.common.event.project.select;

import com.google.common.collect.Lists;
import com.mpolicy.agent.common.enums.SellProductAvailableChannelEnum;
import com.mpolicy.manage.enums.SelectEnum;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.baichuan.enums.ChannelGroupEnum;
import com.mpolicy.manage.modules.baichuan.service.BcChannelBasicInfoService;
import com.mpolicy.manage.modules.common.event.factory.SelectFactory;
import com.mpolicy.manage.modules.common.event.handler.SelectHandler;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.common.model.SelectVo;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.insure.enums.InsureOrderStatusEnum;
import com.mpolicy.manage.modules.sell.entity.SellProductInfoEntity;
import com.mpolicy.manage.modules.sell.enums.RenewalTypeEnum;
import com.mpolicy.manage.modules.sell.service.ISellProductService;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileDiffTypeEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileDiffWhyEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EnumSelectHandler extends SelectHandler {

    private final InsuranceCompanyService insuranceCompanyService;

    private final ISellProductService iSellProductService;

    private final ChannelInfoService channelInfoService;

    private final BcChannelBasicInfoService bcChannelBasicInfoService;

    private final InsuranceProductInfoService insuranceProductInfoService;


    @Override
    public List<SelectOut> findSelectList(SelectVo select) {
        switch (SelectEnum.matchSearchCode(select.getSelectType())) {
            case RECONCILE_SUBJECT: {
                return Arrays.stream(ReconcileSubjectOnlineEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getName())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case RECONCILE_MONTH: {
                return Arrays.stream(ReconcileMonthEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getDesc())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case PRODUCT_GROUP: {
                Map<String, String> map = DicCacheHelper.getSons("PRODUCT:PRODUCTGROUP").stream().collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
                return map.entrySet().stream().map(m ->
                        SelectOut.builder()
                                .label(m.getValue())
                                .value(m.getKey())
                                .disabled(false)
                                .build()
                ).collect(Collectors.toList());
            }
            case COMPANY_ALL_ARRAY: {
                return insuranceCompanyService.lambdaQuery().list().stream().map(x -> SelectOut.builder()
                        .label(x.getCompanyName())
                        .value(x.getCompanyCode())
                        .disabled("0".equals(x.getCompanyStatus()))
                        .build()
                ).collect(Collectors.toList());
            }
            case PRODUCT_ALL_ARRAY: {
                return iSellProductService.lambdaQuery()
                        .eq(StringUtils.isNotBlank(select.getForm()), SellProductInfoEntity::getProductStatus, 1)
                        .list()
                        .stream().map(x -> SelectOut.builder()
                                .label(x.getProductName())
                                .value(x.getProductCode())
                                .disabled(false)
                                .build()
                        ).collect(Collectors.toList());
            }
            case POLICY_STATUS: {
                Map<String, String> map = DicCacheHelper.getSons("POLICY_STATUS").stream().collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
                return map.entrySet().stream().map(m ->
                        SelectOut.builder()
                                .label(m.getValue())
                                .value(m.getKey())
                                .disabled(false)
                                .build()
                ).collect(Collectors.toList());
            }
            case CHANNEL_ALL_ARRAY: {
                return channelInfoService.lambdaQuery().list().stream().map(x -> SelectOut.builder()
                        .label(x.getChannelName())
                        .value(x.getChannelCode())
                        .disabled(false)
                        .build()
                ).collect(Collectors.toList());
            }
            case RECONCILE_DIFF_TYPE: {
                return Arrays.stream(ReconcileDiffTypeEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getName())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case RECONCILE_DIFF_WHY: {
                List<SelectOut> collect;
                if (StringUtils.isBlank(select.getForm())) {
                    collect = Arrays.stream(ReconcileDiffWhyEnum.values())
                            .map(m -> SelectOut.builder()
                                    .label(m.getName())
                                    .value(m.getCode())
                                    .disabled(false)
                                    .build())
                            .collect(Collectors.toList());
                } else {
                    collect = this.getReconcileDiffWhy(select.getForm());
                }
                return collect;
            }
            case SELL_PRODUCT_AVAILABLE_CHANNEL: {
                return Arrays.stream(SellProductAvailableChannelEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getDesc())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case CHANNEL_TYPE: {
                return Arrays.stream(ChannelGroupEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getName())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case BAICHUAN_XJ_CHANNEL: {
                return channelInfoService.lambdaQuery()
                        .eq(ChannelInfoEntity::getChannelClassification, 4)
                        .eq(ChannelInfoEntity::getEnabled, 1)
                        .list().stream().map(x -> SelectOut.builder()
                                .label(x.getChannelName())
                                .value(x.getChannelCode())
                                .disabled(false)
                                .build()
                        ).collect(Collectors.toList());
            }
            case BAICHUAN_CHANNEL: {
                return bcChannelBasicInfoService.lambdaQuery()
                        .list().stream().map(x -> SelectOut.builder()
                                .label(x.getAppName())
                                .value(x.getAppCode())
                                .disabled(false)
                                .build()
                        ).collect(Collectors.toList());
            }
            case INSURANCE_PRODUCT: {
                return insuranceProductInfoService.lambdaQuery()
                        .list().stream().map(x -> SelectOut.builder()
                                .label(x.getProductName())
                                .value(x.getProductCode())
                                .disabled(false)
                                .build()
                        ).collect(Collectors.toList());
            }
            case RENEWAL_TYPE: {
                return Arrays.stream(RenewalTypeEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getName())
                                .value(m.getCode())
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            case ORDER_STATUS: {
                return Arrays.stream(InsureOrderStatusEnum.values())
                        .map(m -> SelectOut.builder()
                                .label(m.getName())
                                .value(String.valueOf(m.getCode()))
                                .disabled(false)
                                .build())
                        .collect(Collectors.toList());
            }
            default: {

            }
        }
        return null;
    }

    /**
     * 根据类型获取【差异原因类型】
     *
     * @param form:
     * @return : java.util.List<com.mpolicy.manage.modules.common.model.SelectOut>
     * <AUTHOR>
     * @date 2023/6/1 10:45
     */
    private List<SelectOut> getReconcileDiffWhy(String form) {
        List<SelectOut> collect = Lists.newArrayList();
        ReconcileDiffWhyEnum monthDiff = ReconcileDiffWhyEnum.MONTH_DIFF;
        ReconcileDiffWhyEnum xiaowhaleMissed = ReconcileDiffWhyEnum.XIAOWHALE_MISSED;
        ReconcileDiffWhyEnum policyCodeError = ReconcileDiffWhyEnum.POLICY_CODE_ERROR;
        ReconcileDiffWhyEnum preservationNoSync = ReconcileDiffWhyEnum.PRESERVATION_NO_SYNC;
        ReconcileDiffWhyEnum amountDiff = ReconcileDiffWhyEnum.AMOUNT_DIFF;
        ReconcileDiffWhyEnum settlementRateDiff = ReconcileDiffWhyEnum.SETTLEMENT_RATE_DIFF;
        ReconcileDiffWhyEnum reconcileAmountAccuracy = ReconcileDiffWhyEnum.RECONCILE_AMOUNT_ACCURACY;
        ReconcileDiffWhyEnum premiumAccuracy = ReconcileDiffWhyEnum.PREMIUM_ACCURACY;
        ReconcileDiffWhyEnum other = ReconcileDiffWhyEnum.OTHER;


        switch (ReconcileDiffTypeEnum.getReconcileDiffType(form)) {
            case AMOUNT_ACCURACY: {
                collect.add(SelectOut.builder().label(reconcileAmountAccuracy.getName()).value(reconcileAmountAccuracy.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(premiumAccuracy.getName()).value(premiumAccuracy.getCode()).disabled(false).build());
                return collect;
            }
            case AMOUNT_DIFF: {
                collect.add(SelectOut.builder().label(preservationNoSync.getName()).value(preservationNoSync.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(amountDiff.getName()).value(amountDiff.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(settlementRateDiff.getName()).value(settlementRateDiff.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(other.getName()).value(other.getCode()).disabled(false).build());
                return collect;
            }
            case XIAOWHALE_MISSED: {
                collect.add(SelectOut.builder().label(monthDiff.getName()).value(monthDiff.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(xiaowhaleMissed.getName()).value(xiaowhaleMissed.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(policyCodeError.getName()).value(policyCodeError.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(other.getName()).value(other.getCode()).disabled(false).build());
                return collect;
            }
            case COMPANY_MISSED: {
                collect.add(SelectOut.builder().label(monthDiff.getName()).value(monthDiff.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(policyCodeError.getName()).value(policyCodeError.getCode()).disabled(false).build());
                collect.add(SelectOut.builder().label(other.getName()).value(other.getCode()).disabled(false).build());
                return collect;
            }
            default: {

            }
        }
        return collect;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        SelectFactory.register(SelectEnum.RECONCILE_SUBJECT, this);
        SelectFactory.register(SelectEnum.RECONCILE_MONTH, this);
        SelectFactory.register(SelectEnum.PRODUCT_GROUP, this);
        SelectFactory.register(SelectEnum.COMPANY_ALL_ARRAY, this);
        SelectFactory.register(SelectEnum.PRODUCT_ALL_ARRAY, this);
        SelectFactory.register(SelectEnum.POLICY_STATUS, this);
        SelectFactory.register(SelectEnum.CHANNEL_ALL_ARRAY, this);
        SelectFactory.register(SelectEnum.RECONCILE_DIFF_TYPE, this);
        SelectFactory.register(SelectEnum.RECONCILE_DIFF_WHY, this);
        SelectFactory.register(SelectEnum.SELL_PRODUCT_AVAILABLE_CHANNEL, this);
        SelectFactory.register(SelectEnum.CHANNEL_TYPE, this);
        SelectFactory.register(SelectEnum.BAICHUAN_XJ_CHANNEL, this);
        SelectFactory.register(SelectEnum.BAICHUAN_CHANNEL, this);
        SelectFactory.register(SelectEnum.INSURANCE_PRODUCT, this);
        SelectFactory.register(SelectEnum.RENEWAL_TYPE, this);
        SelectFactory.register(SelectEnum.ORDER_STATUS, this);
    }
}
