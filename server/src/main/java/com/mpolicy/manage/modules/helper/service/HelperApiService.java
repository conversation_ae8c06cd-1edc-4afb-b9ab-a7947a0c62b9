package com.mpolicy.manage.modules.helper.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.export.common.ExportBasicService;
import com.mpolicy.manage.modules.helper.entity.TmpPolicySuperviseGroupEntity;
import com.mpolicy.manage.modules.helper.vo.PolicyComplianceExportVo;
import com.mpolicy.manage.modules.policy.entity.PolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.vo.DingTalkMessage;
import com.mpolicy.manage.modules.policy.vo.EpV2PolicyExportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导出service
 *
 * <AUTHOR>
 * @date 2022-01-28 13:20
 */
@Slf4j
@Component
public class HelperApiService extends ExportBasicService {

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 页面导出最大记录数
     */
    public static final int EXPORT_LARGE_RECORDS = 20000;

    /**
     * 导出时一个sheet大数量
     */
    public static final int SHEET_MAX = 1000000;


    private static final String DING_TALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=";
    /**
     * 保单钉钉推送 测试
     */
    private static final String DING_TALK_POLICY_GROUP_URL_TEST = "9a5bc92a41d1879e0507cc7c455349ddfd9b02e0d1bc37117a45cc0c2d700279";
    /**
     * 保单钉钉推送 生产
     */
    private static final String DING_TALK_POLICY_GROUP_URL = "7fe1eb5478a6a23119022b975d1f10269f1e52fa9043736b2c8c5e842f77f0e3";

    @Autowired
    private PolicyContractInfoService policyContractInfoService;

    @Autowired
    private TmpPolicySuperviseGroupService tmpPolicySuperviseGroupService;

    /**
     * 辅助推送钉钉
     *
     * @param ossBaseOut oss文件信息
     * <AUTHOR>
     * @since 2023/9/3
     */
    public void send2DingTalk(String token,OssBaseOut ossBaseOut) {
        String text = String.format("【%s】辅助交单数据已经完成部分，点击下载保单数据吧！",isPro()?"生产环境":"测试环境");
        DingTalkMessage dingTalkMessage =
                new DingTalkMessage(ossBaseOut.getAccessDomainPath(), StrUtil.subAfter(ossBaseOut.getFilePath(), "/", true), text);
        String body = JSON.toJSONString(dingTalkMessage);

        String pushToken =token;
        if(StringUtils.isBlank(pushToken)){
            pushToken = isPro() ? DING_TALK_POLICY_GROUP_URL : DING_TALK_POLICY_GROUP_URL_TEST;
            log.info("获取默认推送地址:{}",pushToken);
        }
        String pushUrl= DING_TALK_URL.concat(pushToken);
        HttpResponse response = HttpRequest.post(pushUrl)
                .body(body)
                .timeout(10000).execute();
        log.info("辅助钉钉消息发送完成，body={},response={}", body,response==null?null:response.body());
    }

    private boolean isPro(){
        return active.contains("pro")||active.contains("PRO");
    }

    /**
     * 导出excel + 上传oss
     *
     * @param prefixName 文件前缀
     * @param params     查询条件
     * <AUTHOR>
     * @since 2022/1/28
     */
    public void exportPolicy(String prefixName, Map<String, Object> params,String token) {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        OutputStream out = null;
        try {
            Object e1=params.get("orderTimeBegin");
            Object e2=params.get("orderTimeEnd");

            if(e1==null){
                e1 = params.get("enforceTimeBegin");
                e2 = params.get("enforceTimeEnd");
            }

            // 生成保单文件
            String fileName = prefixName.concat(StrUtil.format("{}年{}月-{}年{}月",
                            DateUtil.parse(String.valueOf(e1), "yyyy-MM-dd").year(),
                    DateUtil.parse(String.valueOf(e1), "yyyy-MM-dd").month() + 1,
                            DateUtil.parse(String.valueOf(e2), "yyyy-MM-dd").year(),
                    DateUtil.parse(String.valueOf(e2), "yyyy-MM-dd").month() + 1))
                    .concat("交单数据.xlsx");
            out = new FileOutputStream(StrUtil.format("logs/{}", fileName));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            int sheetNo = 1;
            Sheet sheet = new Sheet(sheetNo, 0, PolicyComplianceExportVo.class);
            int page = 0;
            int limitSize = 5000;
            int sheetCount = 0;
            int maxId = policyContractInfoService.getExportPolicyMaxEpcidCompliance(params);
            log.info("辅助导出的maxId = {}",maxId);
            // 执行遍历导出
            while (true) {
                // 分批查询
                int startSize = page > 0 ? page * limitSize + 1 : 0;
                params.put("betweenSize", String.valueOf(startSize));
                params.put("limitSize", page == 0 ? String.valueOf(startSize + limitSize) : String.valueOf(startSize + limitSize - 1));
                // 构建需要导出的保单
                List<EpV2PolicyExportVo> expData = policyContractInfoService.exportPolicyByCompliance(params, true);
                if (startSize < maxId) {
                    // 构建新的导入vo，需要精简属性
                    log.info("保单导出中，startSize={},limitSize={},dataSize = {}", params.get("betweenSize"), params.get("limitSize"), expData.size());
                    sheetCount = sheetCount + expData.size();
                    if (sheetCount > SHEET_MAX) {
                        sheetNo = sheetNo + 1;
                        sheet = new Sheet(sheetNo, 0, PolicyComplianceExportVo.class);
                        sheet.setSheetName(StrUtil.format("保单列表-{}", sheetNo));
                        sheetCount = 0;
                    }
                    if (!expData.isEmpty()) {
                        writer.write(expData, sheet);
                    }
                    page++;
                } else {
                    log.info("保单构建完成，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("构建完成.....耗时={}", millis);
            writer.finish();
            out.flush();

            // 上传到oss
            OssBaseOut policy = uploadExportReport(new File(StrUtil.format("logs/{}", fileName)), FileModelEnum.EXPORT, "policy", millis);
            log.info("文件上传oss成功, 导出条件={}, 地址为：{}", params, policy);
            send2DingTalk(token,policy);
        } catch (IOException e) {
            log.warn("台账数据导出失败",e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void policyCashDiff() {
        Map<String, Object> params = new HashMap<>();
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        int page = 0;
        int limitSize = 500;
        // 执行遍历导出
        while (true) {
            int startSize = page > 0 ? page * limitSize + 1 : 0;
            params.put("betweenSize", String.valueOf(startSize));
            params.put("limitSize", page == 0 ? String.valueOf(startSize + limitSize) : String.valueOf(startSize + limitSize - 1));
            // 构建需要导出的保单
            List<TmpPolicySuperviseGroupEntity> expData = tmpPolicySuperviseGroupService.policyGroupList(params);
            if (expData.isEmpty()) {
                break;
            }
            // 执行差异对比
            List<String> policyCodeList = expData.stream().map(TmpPolicySuperviseGroupEntity::getPolicyCode).collect(Collectors.toList());
            List<PolicyContractInfoEntity> xjPolicyList = policyContractInfoService.lambdaQuery()
                    .select(PolicyContractInfoEntity::getPolicyNo, PolicyContractInfoEntity::getPremiumTotal, PolicyContractInfoEntity::getPolicyStatus)
                    .in(PolicyContractInfoEntity::getPolicyNo, policyCodeList)
                    .list();
            Map<String, PolicyContractInfoEntity> xjPolicyMaps = xjPolicyList.stream().collect(Collectors.toMap(PolicyContractInfoEntity::getPolicyNo, x -> x, (x, y) -> x));

            if (!xjPolicyList.isEmpty()) {
                for (TmpPolicySuperviseGroupEntity bean : expData) {
                    if (xjPolicyMaps.containsKey(bean.getPolicyCode())) {
                        PolicyContractInfoEntity policyInfo = xjPolicyMaps.get(bean.getPolicyCode());
                        log.info("确认单保单=【{}】，小鲸保单=【{}】", JSON.toJSONString(bean), JSON.toJSONString(policyInfo));
                        // 1 确认单保费 = 小鲸保费的 【通过】

                        if (bean.getPolicyCash().compareTo(policyInfo.getPremiumTotal()) == 0) {
                            bean.setDataStatus(1);
                            break;
                        }

                        // 2 确认单保费为0 小鲸状态是退保的，【通过】
                        if (bean.getPolicyCash().compareTo(BigDecimal.ZERO) == 0) {
                            bean.setDataStatus(1);
                            break;
                        }

                        // TODO: 2023/9/4 其他金额差异处理

                        // 3 设置差异状态及差异金额
                        bean.setPolicyDiffCash(bean.getPolicyCash().subtract(policyInfo.getPremiumTotal()));
                        bean.setDataStatus(2);
                    } else {
                        bean.setDataStatus(9);
                    }
                }
                ;
            } else {
                log.info("保单未能匹配到小鲸保单");
                expData.forEach(x -> {
                    x.setDataStatus(9);
                });
            }
            tmpPolicySuperviseGroupService.updateBatchById(expData);
            page++;
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        log.info("构建完成.....耗时={}", millis);
    }
}
