package com.mpolicy.manage.modules.helper.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保险月度明细金额临时表 
 * 
 * <AUTHOR>
 * @date 2023-09-04 00:02:08
 */
@TableName("tmp_policy_supervise_group")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TmpPolicySuperviseGroupEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 保单保额
	 */
	private BigDecimal policyCash;
	/**
	 * 保单业务差额
	 */
	private BigDecimal policyDiffCash;
	/**
	 * 数据业务状态 0 待处理 1：匹配正常，2：存在差异
	 */
	private Integer dataStatus;
	/**
	 * 备注
	 */
	private String policyDesc;
}
