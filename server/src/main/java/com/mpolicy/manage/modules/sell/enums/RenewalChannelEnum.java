package com.mpolicy.manage.modules.sell.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <p>
 * 续购渠道枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/9
 */
public enum RenewalChannelEnum {

    /**
     * 续购渠道枚举
     */
    XIAOWHALE("XI<PERSON><PERSON><PERSON><PERSON>", "小鲸"),
    ZHNX("ZHNX", "农保");

    @Getter
    private String code;

    @Getter
    private String name;


    RenewalChannelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RenewalChannelEnum decode(String code) {
        return Arrays.stream(RenewalChannelEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }
}
