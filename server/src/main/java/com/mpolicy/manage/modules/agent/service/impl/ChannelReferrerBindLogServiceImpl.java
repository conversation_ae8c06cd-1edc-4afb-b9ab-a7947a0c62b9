package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.ChannelReferrerBindLogDao;
import com.mpolicy.manage.modules.agent.entity.ChannelReferrerBindLogEntity;
import com.mpolicy.manage.modules.agent.service.ChannelReferrerBindLogService;
import org.springframework.stereotype.Service;


@Service("channelReferrerBindLogService")
public class ChannelReferrerBindLogServiceImpl extends ServiceImpl<ChannelReferrerBindLogDao, ChannelReferrerBindLogEntity> implements ChannelReferrerBindLogService {

}
