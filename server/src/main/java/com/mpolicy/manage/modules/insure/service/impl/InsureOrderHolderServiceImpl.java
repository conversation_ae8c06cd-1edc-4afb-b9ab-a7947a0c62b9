package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureOrderHolderDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderHolderEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderHolderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投保订单投保人信息接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Service("insureOrderHolderService")
public class InsureOrderHolderServiceImpl extends ServiceImpl<InsureOrderHolderDao, InsureOrderHolderEntity> implements InsureOrderHolderService {

}
