package com.mpolicy.manage.modules.policy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: yangdonglin
 * @create: 2023-08-15 10:55
 * @description: 团险导出Vo
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupPolicyPageExportVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;

    /**
     * 保单状态
     */
    @ExcelProperty(value = "保单状态")
    private String policyStatus;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String portfolioName;

    /**
     * 保费
     */
    @ExcelProperty(value = "保费")
    private String premiumTotal;

    /**
     * 投保时间
     */
    @ExcelProperty(value = "投保时间")
    private String applicantTime;

    /**
     * 生效日期
     */
    @ExcelProperty(value = "生效日期")
    private String enforceTime;

    /**
     * 保险公司
     */
    @ExcelProperty(value = "保险公司")
    private String companyName;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String applicantName;

    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道")
    private String channelName;

    /**
     * 代理人
     */
    @ExcelProperty(value = "代理人")
    private String agentName;

    /**
     * 推荐人
     */
    @ExcelProperty(value = "推荐人")
    private String policyReferrerName;

    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String policyReferrerBusinessCode;
}
