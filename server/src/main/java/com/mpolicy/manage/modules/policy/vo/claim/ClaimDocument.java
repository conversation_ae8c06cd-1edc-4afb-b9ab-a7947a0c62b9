package com.mpolicy.manage.modules.policy.vo.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 理赔申请提交信息
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔申请申请材料信息")
@Data
public class ClaimDocument {

    /**
     * 理赔材料类型
     */
    @ApiModelProperty(value = "理赔材料类型")
    @NotBlank(message = "理赔材料类型不能为空")
    private String claimDocumentCode;

    /**
     * 理赔材料类型文件集合
     */
    @ApiModelProperty(value = "理赔材料类型文件集合")
    @Size(min = 1, message = "理赔材料类型文件集合缺失")
    @Valid
    private List<ClaimDocFileInfo> fileList;
}
