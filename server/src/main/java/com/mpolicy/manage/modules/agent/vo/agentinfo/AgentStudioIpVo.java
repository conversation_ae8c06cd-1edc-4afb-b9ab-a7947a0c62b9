package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AgentStudioIpVo implements Serializable {
    private static final long serialVersionUID = 5831937502805534094L;

    @ApiModelProperty(value = "工作室勋章")
    private String studioTag;

    @ApiModelProperty(value = "知乎名称")
    private String zhihuName;

    @ApiModelProperty(value = "知乎粉丝数")
    private String zhihuFans;

    @ApiModelProperty(value = "知乎认证")
    private String zhihuVerified;

    @ApiModelProperty(value = "知乎标签")
    private String zhihuTag;

    @ApiModelProperty(value = "知乎文章数")
    private Integer zhihuCount;

    @ApiModelProperty(value = "微博名")
    private String weiboName;

    @ApiModelProperty(value = "微博粉丝数")
    private String weiboFans;

    @ApiModelProperty(value = "微博认证")
    private String weiboVerified;

    @ApiModelProperty(value = "微博介绍")
    private String weiboIntro;

    @ApiModelProperty(value = "微博文章数")
    private Integer weiboCount;

    @ApiModelProperty(value = "公众号账号")
    private String wechatPublicPlatformName;

    @ApiModelProperty(value = "公众号账号介绍")
    private String wechatPublicPlatformIntro;

    @ApiModelProperty(value = "公众号账号文章数")
    private Integer wechatPublicPlatformCount;

    @ApiModelProperty(value = "抖音名")
    private String douyinName;

    @ApiModelProperty(value = "抖音粉丝数")
    private String douyinFans;

    @ApiModelProperty(value = "抖音点赞数")
    private String douyinLikeNum;

    @ApiModelProperty(value = "抖音认证")
    private String douyinVerified;

    @ApiModelProperty(value = "抖音介绍")
    private String douyinIntro;

    @ApiModelProperty(value = "抖音文章数")
    private Integer douyinCount;
}
