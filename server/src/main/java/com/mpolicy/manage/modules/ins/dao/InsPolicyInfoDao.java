package com.mpolicy.manage.modules.ins.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.ins.entity.InsPolicyInfoEntity;

import java.util.Date;
import java.util.List;

/**
 * 保险业务平台投保对象表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-18 14:13:03
 */
public interface InsPolicyInfoDao extends BaseMapper<InsPolicyInfoEntity> {

    List<String> queryAcceptMasterProposalNoList(Date startTime, String organizationCode);
}