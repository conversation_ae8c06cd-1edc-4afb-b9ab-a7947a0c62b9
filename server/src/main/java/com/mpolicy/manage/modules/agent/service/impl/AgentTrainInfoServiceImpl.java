package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.dao.AgentTrainHostDao;
import com.mpolicy.manage.modules.agent.dao.AgentTrainInfoDao;
import com.mpolicy.manage.modules.agent.dao.AgentTrainSignDao;
import com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao;
import com.mpolicy.manage.modules.agent.entity.AgentTrainHostEntity;
import com.mpolicy.manage.modules.agent.entity.AgentTrainInfoEntity;
import com.mpolicy.manage.modules.agent.entity.AgentTrainSignEntity;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentTrainInfoService;
import com.mpolicy.manage.modules.agent.vo.train.*;
import com.mpolicy.manage.modules.common.model.SelectOut;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service("agentTrainInfoService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTrainInfoServiceImpl extends ServiceImpl<AgentTrainInfoDao, AgentTrainInfoEntity> implements AgentTrainInfoService {

    private final AgentTrainSignDao agentTrainSignDao;

    private final AgentTrainHostDao agentTrainHostDao;

    private final AgentUserInfoDao agentUserInfoDao;

    /**
     * 获取代理人培训信息表列表
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<AgentTrainInfoListOut> findPageList(AgentTrainInfoListVo params) {
        IPage<AgentTrainInfoListOut> page = baseMapper.findPageList(new Page<AgentTrainInfoListVo>(params.getPage(), params.getLimit()), params);
        page.getRecords().forEach(action -> {
            action.setTrainDuration(new BigDecimal(action.getTrainDuration()).divide(new BigDecimal(3600), 0, BigDecimal.ROUND_HALF_UP).longValue());
            if (StrUtil.isNotBlank(action.getTrainHostName())) {
                String[] split = action.getTrainHostName().split("\\,");
                if (split.length > 2) {
                    action.setTrainHostName(StrUtil.format("{}、{}等{}人", split[0], split[1], split.length));
                }
            }
        });
        return new PageUtils(page);
    }

    /**
     * 获取代理人培训信息详情
     *
     * @param trainCode
     * @return
     */
    @Override
    public AgentTrainInfoOut findAgentTrainInfo(String trainCode) {
        AgentTrainInfoEntity trainInfo = baseMapper.selectById(trainCode);
        if (trainInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("培训记录不存在"));
        }
        AgentTrainInfoOut out = new AgentTrainInfoOut();
        BeanUtil.copyProperties(trainInfo, out);
        //获取签到人员列表
        List<String> participants = new LambdaQueryChainWrapper<>(agentTrainSignDao)
                .eq(AgentTrainSignEntity::getTrainCode, trainCode)
                .list().stream().map(AgentTrainSignEntity::getAgentCode)
                .collect(Collectors.toList());
        out.setParticipants(participants);
        //获取主持列表
        List<String> trainHostList = new LambdaQueryChainWrapper<>(agentTrainHostDao)
                .eq(AgentTrainHostEntity::getTrainCode, trainCode)
                .list().stream().map(AgentTrainHostEntity::getAgentCode)
                .collect(Collectors.toList());
        out.setTrainHostList(trainHostList);
        return out;
    }

    /**
     * 新增代理人培训信息
     *
     * @param save
     */
    @Override
    public void saveAgentTrainInfo(SaveAgentTrainInfoVo save) {
        AgentTrainInfoEntity trainInfo = new AgentTrainInfoEntity();
        BeanUtil.copyProperties(save, trainInfo);
        trainInfo.setTrainCode(CommonUtils.createCode("AT"));
        trainInfo.setTrainDuration(DateUtil.between(save.getBeginTime(), save.getEndTime(), DateUnit.SECOND));
        baseMapper.insert(trainInfo);
        //新增签到列表
        if (CollUtil.isNotEmpty(save.getParticipants())) {
            agentTrainSignDao.beachInsertTrainSign(trainInfo.getTrainCode(), save.getParticipants());
        }
        //新增培训人员
        if (CollUtil.isNotEmpty(save.getTrainHostList())) {
            agentTrainHostDao.beachInsertTrainHost(trainInfo.getTrainCode(), save.getTrainHostList());
        }
    }

    /**
     * 修改代理人培训信息
     *
     * @param update
     */
    @Override
    public void updateAgentTrainInfo(UpdateAgentTrainInfoVo update) {
        AgentTrainInfoEntity trainInfo = baseMapper.selectById(update.getTrainCode());
        if (trainInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("培训记录不存在"));
        }
        AgentTrainInfoEntity updateTrainInfo = new AgentTrainInfoEntity();
        BeanUtil.copyProperties(update, updateTrainInfo);
        updateTrainInfo.setTrainDuration(DateUtil.between(update.getBeginTime(), update.getEndTime(), DateUnit.SECOND));
        baseMapper.updateById(updateTrainInfo);
        if (CollUtil.isEmpty(update.getParticipants())) {
            agentTrainSignDao.delete(
                    Wrappers.<AgentTrainSignEntity>lambdaQuery()
                            .eq(AgentTrainSignEntity::getTrainCode, update.getTrainCode())
            );
        } else {
            //获取签到人员列表
            List<String> participants = new LambdaQueryChainWrapper<>(agentTrainSignDao)
                    .eq(AgentTrainSignEntity::getTrainCode, update.getTrainCode())
                    .list().stream().map(AgentTrainSignEntity::getAgentCode)
                    .collect(Collectors.toList());
            //获取新增的签到人员列表
            List<String> saveList = update.getParticipants().stream().filter(x -> !participants.contains(x))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                agentTrainSignDao.beachInsertTrainSign(update.getTrainCode(), saveList);
            }
            //获取删除的签到人员列表
            List<String> deleteList = participants.stream().filter(x -> !update.getParticipants().contains(x))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteList)) {
                agentTrainSignDao.delete(
                        Wrappers.<AgentTrainSignEntity>lambdaQuery()
                                .eq(AgentTrainSignEntity::getTrainCode, update.getTrainCode())
                                .in(AgentTrainSignEntity::getAgentCode, deleteList)
                );
            }

        }

        if (CollUtil.isEmpty(update.getTrainHostList())) {
            agentTrainHostDao.delete(
                    Wrappers.<AgentTrainHostEntity>lambdaQuery()
                            .eq(AgentTrainHostEntity::getTrainCode, update.getTrainCode())
            );
        } else {
            //获取签到人员列表
            List<String> trainHostList = new LambdaQueryChainWrapper<>(agentTrainHostDao)
                    .eq(AgentTrainHostEntity::getTrainCode, update.getTrainCode())
                    .list().stream().map(AgentTrainHostEntity::getAgentCode)
                    .collect(Collectors.toList());
            //获取新增的签到人员列表
            List<String> saveList = update.getTrainHostList().stream().filter(x -> !trainHostList.contains(x))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                agentTrainHostDao.beachInsertTrainHost(update.getTrainCode(), saveList);
            }
            //获取删除的签到人员列表
            List<String> deleteList = trainHostList.stream().filter(x -> !update.getTrainHostList().contains(x))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteList)) {
                agentTrainHostDao.delete(
                        Wrappers.<AgentTrainHostEntity>lambdaQuery()
                                .eq(AgentTrainHostEntity::getTrainCode, update.getTrainCode())
                                .in(AgentTrainHostEntity::getAgentCode, deleteList)
                );
            }

        }
    }

    /**
     * 删除代理人培训信息
     *
     * @param trainCode
     */
    @Override
    public void deleteAgentTrainInfo(String trainCode) {
        baseMapper.deleteById(trainCode);
    }

    /**
     * 获取代理人培训时长
     *
     * @param agentCode
     * @return
     */
    @Override
    public AgentTrainDurationOut findAgentTrainDuration(String agentCode) {
        AgentTrainDurationOut out = baseMapper.findAgentTrainDuration(agentCode);
        if (out == null) {
            out = new AgentTrainDurationOut();
            out.setSumTrainDuration(0L);
        } else {
            out.setSumTrainDuration(out.getSumTrainDuration() / 3600);
        }
        return out;
    }

    @Override
    public List<SelectOut> findParticipants() {
        List<SelectOut> resultList = new LambdaQueryChainWrapper<>(agentUserInfoDao)
                .eq(AgentUserInfoEntity::getAgentStatus, StatusEnum.NORMAL.getCode())
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                .list().stream().map(x -> {
                    SelectOut out = new SelectOut();
                    out.setLabel(x.getAgentName());
                    out.setValue(x.getAgentCode());
                    return out;
                }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<SelectOut> findTrainHostList() {
        List<SelectOut> resultList = new LambdaQueryChainWrapper<>(agentUserInfoDao)
                .eq(AgentUserInfoEntity::getAgentStatus, StatusEnum.NORMAL.getCode())
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                .list().stream().map(x -> {
                    SelectOut out = new SelectOut();
                    out.setLabel(x.getAgentName());
                    out.setValue(x.getAgentCode());
                    return out;
                }).collect(Collectors.toList());
        return resultList;
    }
}
