package com.mpolicy.manage.modules.policy.vo.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * AI聊天对话记录查询请求参数
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ApiModel("AI聊天对话记录查询请求参数")
public class ConversationRecordsQueryVo {

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page;

    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize;

    @ApiModelProperty(value = "关键词", example = "意外险")
    private String keyword;

    @ApiModelProperty(value = "用户名", example = "唐钰惠")
    private String userName;

    @ApiModelProperty(value = "开始时间", example = "2025-07-10")
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-07-19")
    private String endTime;
}
