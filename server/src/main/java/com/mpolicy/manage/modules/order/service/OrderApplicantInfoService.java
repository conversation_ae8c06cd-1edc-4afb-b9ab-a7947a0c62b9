package com.mpolicy.manage.modules.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.order.entity.OrderApplicantInfoEntity;

import java.util.Map;

/**
 * 订单商品信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-16 10:21:13
 */
public interface OrderApplicantInfoService extends IService<OrderApplicantInfoEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

