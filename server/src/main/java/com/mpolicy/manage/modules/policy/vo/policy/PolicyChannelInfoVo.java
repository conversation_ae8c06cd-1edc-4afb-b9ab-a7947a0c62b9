package com.mpolicy.manage.modules.policy.vo.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("保单渠道信息")
public class PolicyChannelInfoVo {
    @ApiModelProperty(
            value = "渠道推荐人类型 0:推荐人 1:代理人",
            example = "1"
    )
    private Integer referrerType;
    @ApiModelProperty("渠道推荐人工号")
    private String referrerWno;
    @ApiModelProperty(
            value = "推荐人编码(代理人)",
            example = "ag20210712172633ePz9wj"
    )
    private String policyReferrerCode;
    @ApiModelProperty(
            value = "推荐人编码(代理人)-手机号",
            example = "18890981111"
    )
    private String policyReferrerMobile;
    @ApiModelProperty(
            value = "保单推荐人(代理人)机构编码",
            example = "OR20210222103029tIBLey"
    )
    private String policyReferrerOrgCode;
    @ApiModelProperty(
            value = "推荐人姓名(代理人)",
            example = "张三"
    )
    private String policyReferrerName;
    @ApiModelProperty(
            value = "推荐人工号(代理人)",
            example = "XJ88888"
    )
    private String policyReferrerBusinessCode;
    @ApiModelProperty(
            value = "渠道推荐人编码",
            example = "ag20210712172633ePz9wj"
    )
    private String referrerCode;
    @ApiModelProperty("渠道推荐人姓名")
    private String referrerName;
    @ApiModelProperty(
            value = "渠道推荐人分支编码",
            example = "OR20210222103029tIBLey"
    )
    private String channelBranchCode;
    @ApiModelProperty("渠道分支名字")
    private String branchName;
    @ApiModelProperty(
            value = "渠道编码",
            example = "free"
    )
    private String channelCode;
    @ApiModelProperty("渠道名字")
    private String channelName;
    @ApiModelProperty("站长姓名")
    private String propagandistName;
    @ApiModelProperty("站长身份证号")
    private String propagandistIdCard;
    @ApiModelProperty(
            value = "三方平台编码",
            example = "xiangzhu"
    )
    private String thirdPartyPlatformCode;
    @ApiModelProperty(
            value = "三方客户编码",
            example = "xz001"
    )
    private String thirdPartyCustomerCode;
    @ApiModelProperty(
            value = "小程序客户编码",
            example = "C20211231111111"
    )
    private String miniAppCustomerCode;
    @ApiModelProperty(
            value = "佣金发放系数,0-1以内",
            example = "0.55"
    )
    private BigDecimal commissionFactor;

    @ApiModelProperty("保单初始推荐人名字")
    private String customerManagerName;

    @ApiModelProperty("保单初始推荐人渠道机构编码")
    private String customerManagerChannelOrgCode;

    @ApiModelProperty("保单初始推荐人编码")
    private String customerManagerCode;

    @ApiModelProperty("保单初始推荐人-工号")
    private String customerManagerChannelCode;

    @ApiModelProperty("保单初始推荐人机构")
    private String customerManagerOrgCode;

    @ApiModelProperty("客户经理督导")
    private String customerManagerSupervisor;
}
