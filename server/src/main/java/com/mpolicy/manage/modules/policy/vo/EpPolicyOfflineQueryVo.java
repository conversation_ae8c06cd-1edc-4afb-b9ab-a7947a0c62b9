package com.mpolicy.manage.modules.policy.vo;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: EpPolicyOfflineQueryVo
 * Description: 投保告知书分页查询参数
 * date: 2023/2/8 15:59
 *
 * <AUTHOR>
 */
@Data
public class EpPolicyOfflineQueryVo extends BasePage {
    /**
     * 保单号/预保单号
     */
    @ApiModelProperty(value = "保单号/预保单号")
    private String businessCode;
    /**
     * 签署时间
     */
    @ApiModelProperty(value = "签署时间")
    private String finishSignTime;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
}
