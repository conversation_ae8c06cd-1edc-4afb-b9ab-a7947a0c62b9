package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户经理信息
 *
 * <AUTHOR>
 * @date 2023-8-2
 */
@Data
@ApiModel(value = "客户经理信息")
public class InsureCustomerInfoOut {


    @ApiModelProperty(value = "姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "身份证号", example = "123456789098765")
    private String identificationNum;

    @ApiModelProperty(value = "手机号", example = "12345678121")
    private String mobile;

    @ApiModelProperty(value = "客户编码", example = "C415267393r2934872389")
    private String customerCode;

    @ApiModelProperty(value = "所属渠道", example = "地区")
    private String referrerChannelName;

}
