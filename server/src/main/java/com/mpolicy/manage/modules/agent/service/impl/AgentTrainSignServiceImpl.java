package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.dao.AgentTrainInfoDao;
import com.mpolicy.manage.modules.agent.dao.AgentTrainSignDao;
import com.mpolicy.manage.modules.agent.entity.AgentTrainInfoEntity;
import com.mpolicy.manage.modules.agent.entity.AgentTrainSignEntity;
import com.mpolicy.manage.modules.agent.service.AgentTrainSignService;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListVo;
import com.mpolicy.manage.modules.agent.vo.train.TrainSignListOut;
import com.mpolicy.manage.modules.agent.vo.train.UpdateTrainSignVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service("agentTrainSignService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTrainSignServiceImpl extends ServiceImpl<AgentTrainSignDao, AgentTrainSignEntity> implements AgentTrainSignService {

    private final AgentTrainInfoDao agentTrainInfoDao;

    /**
     * 获取培训签到列表
     *
     * @param trainCode
     * @return
     */
    @Override
    public List<TrainSignListOut> findTrainSignList(String trainCode) {
        //获取培训记录
        AgentTrainInfoEntity agentTrainInfo = agentTrainInfoDao.selectById(trainCode);
        if (agentTrainInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("培训记录不存在"));
        }
        //获取签到列表
        List<TrainSignListOut> resultList = baseMapper.findTrainSignList(trainCode);
        if (StatusEnum.INVALID.getCode().equals(agentTrainInfo.getSignStatus())) {
            resultList.forEach(action -> {
                action.setAssessmentResult(StatusEnum.NORMAL.getCode());
                action.setSignStatus(StatusEnum.NORMAL.getCode());
            });
        }
        return resultList;
    }

    /**
     * 更新培训签到数据
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTrainSignList(UpdateTrainSignVo vo) {
        //判断培训记录状态
        AgentTrainInfoEntity trainInfo = agentTrainInfoDao.selectById(vo.getTrainCode());
        if (trainInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("培训记录不存在"));
        }
        if (StatusEnum.NORMAL.getCode().equals(trainInfo.getSignStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("培训记录已提交,请勿重复提交"));
        }
        //更新培训记录状态
        AgentTrainInfoEntity update = new AgentTrainInfoEntity();
        update.setTrainCode(vo.getTrainCode());
        update.setSignStatus(StatusEnum.NORMAL.getCode());
        agentTrainInfoDao.updateById(update);
        //更新签到列表
        List<AgentTrainSignEntity> updateList = new ArrayList<>();
        vo.getTrainSignList().forEach(action -> {
            AgentTrainSignEntity updateTrainSign = new AgentTrainSignEntity();
            updateTrainSign.setId(action.getId());
            updateTrainSign.setSignStatus(action.getSignStatus());
            updateTrainSign.setAssessmentResult(action.getAssessmentResult());
            updateList.add(updateTrainSign);
        });
        baseMapper.updateBatch(updateList);
    }

    /**
     * 获取代理人培训信息表列表
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<AgentTrainInfoListOut> findPageList(AgentTrainInfoListVo params) {
        IPage<AgentTrainInfoListOut> page = baseMapper.findPageList(new Page<AgentTrainInfoListVo>(params.getPage(), params.getLimit()), params);
        page.getRecords().forEach(action -> {
            action.setTrainDuration(new BigDecimal(action.getTrainDuration()).divide(new BigDecimal(3600), 0, BigDecimal.ROUND_HALF_UP).longValue());
        });
        return new PageUtils(page);
    }
}
