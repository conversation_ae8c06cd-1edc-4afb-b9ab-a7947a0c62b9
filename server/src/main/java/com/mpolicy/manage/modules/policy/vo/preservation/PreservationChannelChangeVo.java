package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PreservationChannelChangeVo {
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    @ApiModelProperty(value = "合同号")
    private String contractCode;
    @ApiModelProperty(value = "保全编号")
    private String preservationCode;
    @ApiModelProperty(value = "当前保单渠道")
    private String currentChannelCode;
    @ApiModelProperty(value = "变更后保单渠道")
    private String afterChannelCode;
}
