package com.mpolicy.manage.modules.insure.controller;

import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.modules.insure.service.InsureCompanyConfineService;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineOut;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineVO;
import com.mpolicy.manage.modules.insure.vo.InsureConfineStatusVO;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 保司升级控制
 *
 * <AUTHOR>
 * @date 2022-10-09 10:49:19
 */
@RestController
@RequestMapping("confine/company")
@Api(tags = "保司升级控制")
public class InsureCompanyConfineController {

    @Autowired
    private InsureCompanyConfineService insureCompanyConfineService;


    /**
     * 获取保司升级控制列表
     */
    @ApiOperation(value = "获取保司升级控制列表", notes = "分页获取保司升级控制列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "title", dataType = "String", value = "标题"),
            @ApiImplicitParam(paramType = "query", name = "companyCodes", dataType = "String", value = "保司编码"),
            @ApiImplicitParam(paramType = "query", name = "confineStartTime", dataType = "String", value = "升级开始时间开始"),
            @ApiImplicitParam(paramType = "query", name = "confineEndTime", dataType = "String", value = "升级开始时间结束"),
            @ApiImplicitParam(paramType = "query", name = "startTime", dataType = "String", value = "创建开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endTime", dataType = "String", value = "创建结束时间"),
            @ApiImplicitParam(paramType = "query", name = "isStatus", dataType = "int", value = "启用状态"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"sales:company:all"})
    public Result<PageUtils<InsureCompanyConfineOut>> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils<InsureCompanyConfineOut> page = insureCompanyConfineService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 获取有产品的保司
     */
    @ApiOperation(value = "获取有产品的保司", notes = "获取有产品的保司")
    @GetMapping("/getCompanyList")
    public Result<List<JSONObject>> getCompanyList() {
        List<JSONObject> list = insureCompanyConfineService.getCompanyList();
        return Result.success(list);
    }

    /**
     * 获取保司下的产品
     */
    @ApiOperation(value = "获取保司下的产品", notes = "获取保司下的产品")
    @PostMapping("/getProductList")
    @RequiresPermissions(value = {"sales:company:all"})
    public Result<List<JSONObject>> getProductList(@RequestParam(value = "companyCodes") @ApiParam(name = "companyCodes",required = true, value = "保司编码")List<String> companyCodes) {
        List<JSONObject> list = insureCompanyConfineService.getProductList(companyCodes);
        return Result.success(list);
    }

    /**
     * 删除保司升级控制
     */
    @ApiOperation(value = "删除保司升级控制", notes = "删除保司升级控制")
    @GetMapping("/delete/{confineCode}")
    @RequiresPermissions(value = {"sales:company:all"})
    @SysDbLog("删除保司升级控制")
    public Result deleteConfine(@PathVariable(value = "confineCode") String confineCode) {
        insureCompanyConfineService.deleteConfine(confineCode);
        return Result.success();
    }

    /**
     * 修改保司升级控制控制状态
     */
    @ApiOperation(value = "修改保司升级控制控制状态", notes = "修改保司升级控制控制状态")
    @PostMapping("/status")
    @RequiresPermissions(value = {"sales:company:all"})
    @SysDbLog("修改保司升级控制控制状态")
    public Result updateStatus(@RequestBody @ApiParam(name = "insureConfineStatusVO", value = "保司升级状态", required = true) InsureConfineStatusVO insureConfineStatusVO) {
        ValidatorUtils.validateEntity(insureConfineStatusVO);
        insureCompanyConfineService.updateStatus(insureConfineStatusVO);
        return Result.success();
    }

    /**
     * 添加——修改保司升级控制
     */
    @ApiOperation(value = "添加——修改保司升级控制", notes = "添加——修改保司升级控制")
    @PostMapping("/save_update")
    @RequiresPermissions(value = {"sales:company:all"})
    @SysDbLog("添加——修改保司升级控制")
    public Result saveUpdate(@RequestBody @ApiParam(name = "insureCompanyConfineVO", value = "保司升级信息", required = true) InsureCompanyConfineVO insureCompanyConfineVO) {
        ValidatorUtils.validateEntity(insureCompanyConfineVO);
        insureCompanyConfineService.saveUpdate(insureCompanyConfineVO);
        return Result.success();
    }
}
