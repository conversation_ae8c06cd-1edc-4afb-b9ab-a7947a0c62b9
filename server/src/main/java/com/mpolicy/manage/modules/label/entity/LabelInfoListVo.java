package com.mpolicy.manage.modules.label.entity;

import com.mpolicy.manage.common.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class LabelInfoListVo extends BasePage implements Serializable {
    private static final long serialVersionUID = -2746360112881262997L;


    @NotBlank(message = "标签分组不能为空")
    @ApiModelProperty(value = "标签分组(字典)")
    private String labelGroup;

    @ApiModelProperty(value = "标签库编码")
    private String libraryCode;

    @ApiModelProperty(value = "标签名")
    private String labelName;

    @ApiModelProperty(value = "保障类型(字典)")
    private String portfolioType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;


}
