package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 渠道信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 16:37:12
 */
public interface ChannelInfoService extends IService<ChannelInfoEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<ChannelInfoEntity> queryPage(Map<String, Object> paramMap);

    /**
     * 修改渠道启用状态
     */
    boolean changeEnable(String code, Integer enabled, long revision);

    /**
     * 获取渠道信息
     */
    ChannelInfoEntity info(String code);

    /**
     * 更新或修改
     *
     * @param channelInfoEntity 渠道实体
     * @return 操作状态
     */
    boolean saveOrUpdateEntity(ChannelInfoEntity channelInfoEntity);



    /**
     * 获取销售渠道(核心业务-保单中心)
     *
     * @param isNeedPermission 是否需要权限
     * @param showChannelFlag 显示所有渠道标识（1表示显示所有渠道，其他显示有效渠道）
     * @return
     */
    List<ChannelInfoVo> getChannelList(boolean isNeedPermission, String showChannelFlag);


}

