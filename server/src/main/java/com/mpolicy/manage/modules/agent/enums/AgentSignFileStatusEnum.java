package com.mpolicy.manage.modules.agent.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * ClassName: AgentSignFileStatusEnum
 * Description:
 * date: 2023/6/1 17:16
 *
 * <AUTHOR>
 */
@Getter
public enum AgentSignFileStatusEnum {
    STATUS0(0,"未签署"),
    // 主动签署
    STATUS1(1, "主动签署"),
    // 自动确认
    STATUS2(2, "自动确认"),

    ;

    private Integer code;
    private String name;

    AgentSignFileStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AgentSignFileStatusEnum getValueByKey(Integer code) {
        return Arrays.stream(values()).filter((x) -> Objects.equals(x.code,code)).findFirst().orElse(null);
    }
}
