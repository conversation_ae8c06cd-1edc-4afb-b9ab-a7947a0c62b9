package com.mpolicy.manage.modules.common.service;


import com.mpolicy.manage.enums.InvalidCacheEnum;

/**
 * <AUTHOR>
 */
public interface IInvalidCacheService {


    /**
     * 设置缓存失效
     *
     * @param invalidCache
     * @param key
     */
    void invalidCache(InvalidCacheEnum invalidCache, String key);

    /**
     * 设置缓存失效
     *
     * @param invalidCache
     */
    void invalidCache(InvalidCacheEnum invalidCache);
}
