package com.mpolicy.manage.modules.insure.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.insure.dao.InsureCustomerWhiteRosterDao;
import com.mpolicy.manage.modules.insure.entity.InsureCustomerWhiteRosterEntity;
import com.mpolicy.manage.modules.insure.entity.InsureUserWhiteRosterEntity;
import com.mpolicy.manage.modules.insure.service.InsureCustomerWhiteRosterService;
import com.mpolicy.manage.modules.insure.vo.CustomerWhiteRosterFaceVo;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerInfoOut;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerWhiteRosterOut;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerWhiteRosterVo;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service("insureCustomerWhiteRosterService")
@Slf4j
public class InsureCustomerWhiteRosterServiceImpl extends ServiceImpl<InsureCustomerWhiteRosterDao, InsureCustomerWhiteRosterEntity> implements InsureCustomerWhiteRosterService {

    @Resource
    private CustomerBasicInfoService customerBasicInfoService;
    @Resource
    private SysDocumentService sysDocumentService;
    @Resource
    private StorageService storageService;
    @Resource
    private ChannelApplicationReferrerService channelApplicationReferrerService;

    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public PageUtils<InsureCustomerWhiteRosterOut> queryPage(Map<String, Object> params) {
        String realName = RequestUtils.objectValueToString(params, "realName");
        String mobile = RequestUtils.objectValueToString(params, "mobile");
        String cardNo = RequestUtils.objectValueToString(params, "cardNo");
        String startDate = RequestUtils.objectValueToString(params, "startDate");
        String endDate = RequestUtils.objectValueToString(params, "endDate");
        IPage<InsureCustomerWhiteRosterEntity> page = this.page(
                new Query<InsureCustomerWhiteRosterEntity>().getPage(params),
                new LambdaQueryWrapper<InsureCustomerWhiteRosterEntity>()
                        .eq(StringUtils.isNotBlank(mobile), InsureCustomerWhiteRosterEntity::getMobile, mobile)
                        .like(StringUtils.isNotBlank(realName), InsureCustomerWhiteRosterEntity::getRealName, realName)
                        .eq(StringUtils.isNotBlank(cardNo), InsureCustomerWhiteRosterEntity::getCardNo, cardNo)
                        .eq(StringUtils.isNotBlank(cardNo), InsureCustomerWhiteRosterEntity::getCardNo, cardNo)
                        .apply(StringUtils.isNotBlank(startDate), "(date_format(start_date,'%Y-%m-%d') >= {0} or is_long_term = 1)", startDate)
                        .apply(StringUtils.isNotBlank(endDate), "(date_format(end_date,'%Y-%m-%d') <= {0} or is_long_term = 1)", endDate)
                        .orderByDesc(InsureCustomerWhiteRosterEntity::getStartDate)

        );
        List<InsureCustomerWhiteRosterOut> voList = new ArrayList<>();
        for (InsureCustomerWhiteRosterEntity entity : page.getRecords()) {
            InsureCustomerWhiteRosterOut out = new InsureCustomerWhiteRosterOut();
            out.setStartDate(DateUtils.convertDate2String(entity.getStartDate(), "yyyy-MM-dd"));
            out.setEndDate(DateUtils.convertDate2String(entity.getEndDate(), "yyyy-MM-dd"));
            out.setValidDate(entity.getIsLongTerm() == 1 ? "始终有效" : out.getStartDate() + "至" + out.getEndDate());
            BeanUtils.copyProperties(entity, out);
            voList.add(out);

        }
        return new PageUtils<>(voList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public ArrayList<InsureCustomerInfoOut> getCustomerInfo(String mobile) {
        List<CustomerBasicInfoEntity> customerList = customerBasicInfoService.lambdaQuery()
                .eq(CustomerBasicInfoEntity::getMobile, mobile)
                .eq(CustomerBasicInfoEntity::getCancelStatus, 0)
                .eq(CustomerBasicInfoEntity::getCertificationStatus, 1)
                .list();
        if(CollectionUtils.isEmpty(customerList)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该手机号不存在或未实名"));
        }
        ArrayList<InsureCustomerInfoOut> list = Lists.newArrayList();
        customerList.forEach(x -> {
            InsureCustomerInfoOut out = new InsureCustomerInfoOut();
            if (StringUtils.isNotBlank(x.getInnerReferrerCode())) {
                Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getReferrerCode, x.getInnerReferrerCode())
                        .last("limit 1")
                        .one()).ifPresent(o -> out.setReferrerChannelName(o.getReferrerChannelName()));
            }
            BeanUtils.copyProperties(x, out);
            list.add(out);
        });
        return list;
    }

    @Override
    public void customerSaveOrUpdate(InsureCustomerWhiteRosterVo vo) {
        InsureCustomerWhiteRosterEntity entity = new InsureCustomerWhiteRosterEntity();
        if (vo.getId() != null) {
            entity = Optional.ofNullable(this.getById(vo.getId()))
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("白名单信息获取失败")));
        }else{
            InsureCustomerWhiteRosterEntity one = this.lambdaQuery().eq(InsureCustomerWhiteRosterEntity::getMobile, vo.getMobile()).last("limit 1").one();
            if(one != null){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前用户已添加，请勿重复添加"));
            }
        }
        BeanUtils.copyProperties(vo, entity);
        if (vo.getIsLongTerm() == 1) {
            entity.setStartDate(new Date());
            entity.setEndDate(DateUtils.convertString2Date("2199-12-31", "yyyy-MM-dd"));
        } else {
            entity.setStartDate(vo.getStartDate());
            entity.setEndDate(vo.getEndDate());
        }
        entity.setCardNo(vo.getCardNo().toUpperCase());
        this.saveOrUpdate(entity);
        log.info("客户经理白名单操作id={}，操作类型={}，操作人={}，变更结果={}", entity.getId(), vo.getId() == null ? "新增" : "编辑", ShiroUtils.getUserEntity().getUsername(), JSON.toJSONString(entity));
    }

    @Override
    public List<String> customerUpload(String fileCode) {
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传失败")));

        // 生成到本地
        String localFilePath = tempPath.concat(document.getFileName());
        log.info("ossPath={}, localFilePath={}", document.getFilePath(), localFilePath);
        storageService.downloadFileToLocal(document.getFilePath(), localFilePath);
        List<Object> list = ExcelUtil.readExcel(localFilePath, new CustomerWhiteRosterFaceVo());
        if (CollectionUtils.isEmpty(list)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件内容为空"));
        }
        ArrayList<InsureCustomerWhiteRosterEntity> rosterList = Lists.newArrayList();
        List<String> errorList = Lists.newArrayList();
        for (int i = 0; i < list.size(); i++) {
            CustomerWhiteRosterFaceVo vo = new CustomerWhiteRosterFaceVo();
            BeanUtils.copyProperties(list.get(i), vo);
            if (StringUtils.isBlank(vo.getCardNo())) {
                errorList.add("第" + (i + 2) + "行证件号码为空");
            }
            String zx = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
            if (StringUtils.isNotBlank(vo.getCardNo()) && !Pattern.matches(zx, vo.getCardNo().trim())) {
                errorList.add("第" + (i + 2) + "行证件号码不是正确的身份证格式");
            }
            if (StringUtils.isBlank(vo.getMobile())) {
                errorList.add("第" + (i + 2) + "行手机号为空");
            }
            if (StringUtils.isBlank(vo.getStartDate())) {
                errorList.add("第" + (i + 2) + "行生效日期为空");
            }
            if (StringUtils.isBlank(vo.getEndDate())) {
                errorList.add("第" + (i + 2) + "行失效日期为空");
            }
            if (StringUtils.isBlank(vo.getRemark())) {
                errorList.add("第" + (i + 2) + "行备注说明为空");
            }
            Date startDate = vo.convertStartDate();
            Date endDate = vo.convertEndDate();
            if (startDate == null || endDate == null || endDate.compareTo(startDate) < 0) {
                errorList.add("第" + (i + 2) + "行生效日期要在失效日期之前");
            }
            InsureCustomerWhiteRosterEntity entity = new InsureCustomerWhiteRosterEntity();
            if (StringUtils.isNotBlank(vo.getCardNo()) && StringUtils.isNotBlank(vo.getMobile())) {
                CustomerBasicInfoEntity customerInfo = customerBasicInfoService.lambdaQuery()
                        .eq(CustomerBasicInfoEntity::getMobile, vo.getMobile())
                        .eq(CustomerBasicInfoEntity::getIdentificationNum, vo.getCardNo())
                        .eq(CustomerBasicInfoEntity::getCancelStatus, 0)
                        .eq(CustomerBasicInfoEntity::getCertificationStatus, 1)
                        .last("limit 1")
                        .one();
                if (customerInfo == null) {
                    errorList.add("第" + (i + 2) + "行该手机号不存在或未实名");
                } else if (StringUtils.isNotBlank(customerInfo.getCustomerCode())) {
                    entity.setCustomerCode(customerInfo.getCustomerCode());
                    entity.setRealName(customerInfo.getRealName());
                    if(StringUtils.isNotBlank(customerInfo.getInnerReferrerCode())){
                        Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                                .eq(ChannelApplicationReferrerEntity::getReferrerCode, customerInfo.getInnerReferrerCode())
                                .last("limit 1")
                                .one()).ifPresent(x -> entity.setReferrerChannelName(x.getReferrerChannelName()));
                    }
                }
                InsureCustomerWhiteRosterEntity one = this.lambdaQuery().eq(InsureCustomerWhiteRosterEntity::getMobile, vo.getMobile()).last("limit 1").one();
                if(one != null){
                    errorList.add("第" + (i + 2) + "行当前用户已添加，请勿重复添加");
                }
            }
            entity.setCardNo(StringUtils.isNotBlank(vo.getCardNo()) ? vo.getCardNo().toUpperCase().trim() : null);
            entity.setMobile(StringUtils.isNotBlank(vo.getMobile()) ? vo.getMobile().trim() : null);
            entity.setStartDate(startDate);
            entity.setEndDate(endDate);
            entity.setIsLongTerm(0);
            entity.setRemark(vo.getRemark());
            rosterList.add(entity);
        }
        if (CollectionUtils.isEmpty(errorList)) {
            this.saveBatch(rosterList);
            log.info("客户经理白名单操作id={}，操作类型={}，操作人={}，变更结果={}", fileCode, "导入", ShiroUtils.getUserEntity().getUsername(), JSON.toJSONString(rosterList));
        }
        return errorList;
    }
}
