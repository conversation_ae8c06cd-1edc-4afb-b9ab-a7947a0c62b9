package com.mpolicy.manage.modules.sys.util.excel;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;

/**
 * @author: yangdonglin
 * @create: 2023/12/27 12:52
 * @description: 中和农信-团险
 * 根据当前字段所处成位置，更新index,从0开始。
 */
@Slf4j
public class ExcelDateUtil {

    /**
     * 时间校验
     *
     * @param dateStr
     */
    public static boolean isDate(String dateStr) {
        final String dateRegex = "^[\\d| ]*$";
        if (dateStr.matches(dateRegex)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将给定的日期字符串转换为Excel日期格式的字符串。
     * Excel日期格式是以1900年1月1日为起始日，以天为单位的序列号。
     *
     * @param date 待转换的日期字符串。支持多种格式，具体取决于DateUtil的解析能力。
     * @return 如果输入为null或非法日期，或者日期字符串长度小于6，则原样返回。
     * 否则，返回表示相应日期的Excel日期格式字符串。
     */
    public static String toExcelDate(String date) {
        // 检查输入日期是否为null
        if (Objects.isNull(date)) {
            return null;
        }
        // 如果日期字符串是合法的且长度小于6（比如只包含日期部分），则直接返回
        if (isDate(date) && date.length() < 6) {
            return date;
        }
        // 尝试解析日期字符串
        final DateTime dateTime = DateUtil.parse(date);
        // 如果解析失败，返回空字符串
        if (Objects.isNull(dateTime)) {
            return "";
        }
        // 成功解析后，转换为Excel日期格式字符串并返回
        return getExcelDaysStr(dateTime);
    }

    /**
     * 将给定的日期字符串转换为特定的日期字符串格式。
     *
     * @param date 输入的日期字符串，可以是多种格式，但首先会尝试将其转换为Excel日期格式。
     * @return 如果转换成功，则返回转换后的日期字符串；如果输入为空、转换失败或发生异常，则返回空字符串或null。
     */
    public static String toDateStrOne(String date) {
        try {
            // 检查输入日期字符串是否为空
            if (StringUtils.isBlank(date)) {
                return "";
            }
            // 尝试将输入日期转换为Excel日期格式
            final String excelDate = toExcelDate(date);
            // 检查转换后的Excel日期字符串是否为空
            if (StringUtils.isBlank(excelDate)) {
                return "";
            }
            // 将Excel日期字符串转换为DateTime对象
            final DateTime dateTime = excelDateTuDate(excelDate);
            // 检查DateTime对象是否为空
            if (Objects.isNull(dateTime)) {
                return "";
            }
            // 将DateTime对象转换为指定格式的日期字符串并返回
            return DateUtil.date(dateTime).toDateStr();
        } catch (Exception e) {
            // 捕获并记录转换过程中的异常
            log.info("日期转换异常", e);
        }
        // 如果发生异常则返回null
        return null;
    }

    /**
     * 将Excel日期格式转换为日期对象。
     * Excel中的日期是从1900年1月1日开始计算的，这个方法会将Excel中的日期天数转换为对应的日期对象。
     *
     * @param day 表示Excel中的日期，为一个字符串，需要转换为日期对象。
     * @return 返回转换后的日期对象，对应于Excel日期所代表的实际日期。
     */
    public static DateTime excelDateTuDate(String day) {
        // 将字符串表示的日期转换为整数
        Integer integer = Integer.valueOf(day);
        // 创建一个日历对象，设置为1900年1月1日的前一天，以配合Excel日期系统的计算方式
        Calendar calendar = new GregorianCalendar(1900, 0, -1);
        // 获取设置后的日期对象
        Date gregorianDate = calendar.getTime();
        // 根据Excel日期天数，计算出实际日期并返回
        return DateUtil.offsetDay(gregorianDate, integer);
    }

    /**
     * 获取excel天数
     *
     * @param date
     * @return
     */
    public static String getExcelDaysStr(Date date) {
        if (Objects.isNull(date)) {
            return "";
        }
        final LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return getExcelDaysStr(localDate);
    }

    /**
     * 获取excel天数
     *
     * @param newDate
     * @return
     */
    public static String getExcelDaysStr(LocalDate newDate) {
        final LocalDate excelStartDate = LocalDate.of(1899, 12, 30);
        return Long.toString(ChronoUnit.DAYS.between(excelStartDate, newDate));
    }

    /**
     * 主函数，用于演示如何使用不同的日期格式生成随机日期，并将其与Excel日期格式进行转换。
     * 该函数不接受参数，不返回任何值。
     */
    public static void main(String[] args) {
        // 以下行演示了使用各种不同格式生成随机日期
        generateRandomDate("yyyy-MM-dd HH:mm:ss");
        generateRandomDate("yyyy/MM/dd HH:mm:ss");
        generateRandomDate("yyyy.MM.dd HH:mm:ss");
        generateRandomDate("yyyy年MM月dd日 HH时mm分ss秒");
        generateRandomDate("yyyy-MM-dd");
        generateRandomDate("yyyy/MM/dd");
        generateRandomDate("yyyy.MM.dd");
        generateRandomDate("yyyy-MM-dd HH:mm");
        generateRandomDate("yyyy-MM-dd HH:mm:ss.SSS");
        generateRandomDate("yyyyMMddHHmmss");
        generateRandomDate("yyyyMMddHHmmssSSS");
        generateRandomDate("yyyyMMdd");
        generateRandomDate("yyyy-MM-dd'T'HH:mm:ss'Z'");
        generateRandomDate("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        generateRandomDate("yyyy-MM-dd'T'HH:mm:ssZ");
        generateRandomDate("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    }

    /**
     * 生成指定格式的随机日期，并输出其字符串形式以及对应的Excel日期格式。
     *
     * @param format 日期格式字符串，用于指定生成日期的格式。
     */
    public static void generateRandomDate(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        final Date randomDate = new Date(1837305029000L);
        String formattedDate = sdf.format(randomDate);
        // 输出格式化后的日期字符串及其对应的Excel日期格式
        System.out.println(formattedDate + ":" + ExcelDateUtil.toExcelDate(formattedDate));
    }
}
