package com.mpolicy.manage.modules.agent.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.utils.DesensitizedUtil;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.entity.AgentPageListOut;
import com.mpolicy.manage.modules.agent.entity.AgentPageListVo;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.manage.modules.agent.vo.*;
import com.mpolicy.manage.modules.agent.vo.agentinfo.*;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.sys.controller.AbstractController;
import com.mpolicy.manage.modules.sys.service.ShiroService;
import com.mpolicy.service.common.service.DicCacheHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 经纪人用户信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
@RestController
@RequestMapping("sys/agentuserinfo")
@Api(tags = "经纪人信息")
public class AgentUserInfoController extends AbstractController {

    @Autowired
    private AgentUserInfoService agentUserInfoService;
    @Autowired
    private ShiroService shiroService;


    @GetMapping("list")
    @RequiresPermissions(value = {"agent:list:info"})
    @ApiOperation(value = "分页获取经纪人用户信息表列表", notes = "分页获取经纪人用户信息表列表")
    public Result<PageUtils<AgentPageListOut>> findAgentPageList(AgentPageListVo input) {
        PageUtils<AgentPageListOut> page = agentUserInfoService.findAgentPageList(input);
        Set<String> userPermissions = shiroService.getUserPermissions(ShiroUtils.getUserId());
        if (!userPermissions.contains("agent:list:add")) {
            page.getList().forEach(x -> {
                x.setAgentName(DesensitizedUtil.chineseName(x.getAgentName()));
                x.setIdCard(DesensitizedUtil.idCardNum(x.getIdCard(), 1, 2));
                x.setMobile(DesensitizedUtil.mobilePhone(x.getMobile()));
                x.setRecruitName(DesensitizedUtil.chineseName(x.getRecruitName()));
            });
        }
        return Result.success(page);
    }

    @GetMapping("/export")
    @RequiresPermissions(value = {"agent:list:info"})
    @ApiOperation(value = "导出经纪人用户信息表列表", notes = "导出经纪人用户信息表列表")
    public Result<?> export(AgentPageListVo params, HttpServletResponse response) {
        List<AgentUserInfoExportVo> export = agentUserInfoService.export(params);
        ExcelUtil.writeExcel(response, export, URLUtil.encode("代理人列表", StandardCharsets.UTF_8), "sheet1", new AgentUserInfoExportVo());
        return Result.success();
    }


    @ApiOperation(value = "根据code获取经纪人信息")
    @GetMapping("/info/{agentCode}")
    @RequiresPermissions(value = {"agent:list:info"})
    public Result<com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo> info(@ApiParam(value = "代理人编码", required = true)
                                                                                  @PathVariable("agentCode") String agentCode) {
        com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo agent = agentUserInfoService.findAgentInfo(agentCode);
        return Result.success(agent);
    }

    /**
     * 获取区域经理可选区域
     */
    @ApiOperation(value = "获取区域经理可选区域")
    @GetMapping("/areaManagerRegionList")
    public Result<List<DicCacheHelper.DicEntity>> areaManagerRegionList(@RequestParam(value = "agentCode", required = false) String agentCode) {
        List<String> regionList = new ArrayList<>();
        agentUserInfoService.list(
                Wrappers.<AgentUserInfoEntity>lambdaQuery()
                        .ne(StrUtil.isNotBlank(agentCode), AgentUserInfoEntity::getAgentCode, agentCode)
                        .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
        ).forEach(action -> {
            if (StrUtil.isNotBlank(action.getAreaManagerRegion())) {
                regionList.addAll(Arrays.asList(action.getAreaManagerRegion().split(",")));
            }

        });

        List<DicCacheHelper.DicEntity> referrerRegionList = DicCacheHelper.getSons("REFERRER_REGION");
        List<DicCacheHelper.DicEntity> result = referrerRegionList.stream()
                .peek(x -> {
                    if (regionList.contains(x.getKey())) {
                        x.setRemark("true");
                    }
                }).collect(Collectors.toList());
        return Result.success(result);
    }

    /**
     * 获取经纪人基本信息
     */
    @ApiOperation(value = "获取经纪人基本信息")
    @GetMapping("/getBaseInfo")
    public Result<List<AgentInfoVo>> getBaseInfo() {
        List<AgentInfoVo> agentList = agentUserInfoService.getBaseInfo(null);

        return Result.success(agentList);
    }


    /**
     * 获取代理人信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取代理人信息(核心业务-保单中心)")
    @PostMapping("/getAgentBaseInfo")
    public Result<List<AgentBaseInfoVo>> getAgentBaseInfo(@ApiParam(name = "orgCode", value = "机构编码") @RequestParam(required = false) String orgCode,
                                                          @ApiParam(name = "agentNameOrBusinessCode", value = "代理人姓名/工号") @RequestParam(required = false) String agentNameOrBusinessCode,
                                                          @ApiParam(name = "isOnLine", value = "业务分类 1:网销 0:线下") @RequestParam(required = false) String isOnLine
    ) {
        //当业务分类为网销的时候，返回固定的信息
        if ("1".equals(isOnLine)) {
            return Result.success();
        }
        List<AgentBaseInfoVo> agentList = agentUserInfoService.getAgentBaseInfo(orgCode, agentNameOrBusinessCode);

        return Result.success(agentList);
    }

    /**
     * 获取代理人信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取代理人信息(核心业务-保单中心) 权限")
    @PostMapping("/getAgentBaseInfoPermission")
    public Result<List<AgentBaseInfoVo>> getAgentBaseInfoPermission(@ApiParam(name = "orgCode", value = "机构编码") @RequestParam(required = false) String orgCode,
                                                                    @ApiParam(name = "agentNameOrBusinessCode", value = "代理人姓名/工号") @RequestParam(required = false) String agentNameOrBusinessCode,
                                                                    @ApiParam(name = "isOnLine", value = "业务分类 1:网销 0:线下") @RequestParam(required = false) String isOnLine
    ) {
        //当业务分类为网销的时候，返回固定的信息
        if ("1".equals(isOnLine)) {
            return Result.success();
        }
        List<AgentBaseInfoVo> agentList = agentUserInfoService.getAgentBaseInfo(orgCode, agentNameOrBusinessCode, true);

        return Result.success(agentList);
    }

    /**
     * 获取保单推荐人信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取保单推荐人信息(核心业务-保单中心) 权限")
    @PostMapping("/getPolicyReferrerInfoPermission")
    public Result<List<ReferrerInfoVo>> getPolicyReferrerInfoPermission(@ApiParam(name = "referrerCodeOrName", value = "推荐人姓名/工号")
                                                                        @RequestParam(required = false) String referrerCodeOrName) {
        List<AgentBaseInfoVo> agentList = agentUserInfoService.getAgentBaseInfo(null, referrerCodeOrName, true);

        List<ReferrerInfoVo> policyReferrerInfoVoList = agentList.stream().map(x ->
                ReferrerInfoVo.builder().referrerCode(x.getAgentCode()).referrerName(x.getAgentName())
                        .referrerWno(x.getBusinessCode()).quitStatus(x.getQuitStatus())
                        .build()).collect(Collectors.toList());
        return Result.success(policyReferrerInfoVoList);
    }

    /**
     * 根据组织编码获取经纪人列表
     */
    @ApiOperation(value = "根据组织编码获取经纪人列表")
    @GetMapping("/getBaseInfoByOrgCode/{orgCode}")
    public Result<List<AgentInfoVo>> getBaseInfoByOrgCode(@PathVariable("orgCode") String orgCode) {
        List<AgentInfoVo> agentList = agentUserInfoService.getBaseInfo(orgCode);

        return Result.success(agentList);
    }


    @ApiOperation(value = "新增代理人信息", notes = "新增代理人信息")
    @PostMapping("save")
    @RequiresPermissions(value = {"agent:list:add"})
    public Result saveAgentInfo(@ApiParam(value = "暂存经纪人信息的对象", required = true)
                                @RequestBody com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo save) {
        agentUserInfoService.saveAgentInfo(save);
        return Result.success();
    }

    @ApiOperation(value = "修改经纪人信息", notes = "修改经纪人信息")
    @PostMapping("update")
    @RequiresPermissions(value = {"agent:list:add"})
    public Result updateAgentInfo(@ApiParam(value = "修改经纪人信息的对象", required = true)
                                  @RequestBody com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo update) {
        agentUserInfoService.updateAgentInfo(update);
        return Result.success();
    }


    /**
     * 删除经纪人信息
     */
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"agent:list:add"})
    public Result delete(
            @ApiParam(value = "删除经纪人的code", required = true)
            @PathVariable("code") String agentCode) {
        agentUserInfoService.deleteEntity(agentCode);

        return Result.success();
    }

    /**
     * 完全保存经纪人信息
     */
    @ApiOperation(value = "完全保存或修改经纪人信息", notes = "完全保存或修改经纪人信息")
    @PostMapping("/saveAllInfo")
    public Result saveAllInfo(
            @ApiParam(value = "保存经纪人的信息", required = true)
            @RequestBody @Validated AgentVo agentReqVo) {
        agentUserInfoService.saveOrUpdateEntity(agentReqVo, false);
        return Result.success();
    }

    @ApiOperation(value = "设置员工离职", notes = "设置员工离职")
    @PostMapping("/updateQuitStatus")
    @RequiresPermissions(value = {"agent:list:quit"})
    public Result updateQuitStatus(
            @ApiParam(value = "设置员工离职", required = true)
            @RequestBody @Validated UpdateQuitStatusVo update) {
        agentUserInfoService.updateQuitStatus(update);
        return Result.success();
    }

    @ApiOperation(value = "获取代理人是否绑定客户", notes = "获取代理人是否绑定客户", httpMethod = "GET")
    @GetMapping("findAgentBindCustomerInfo/{agentCode}")
    public Result findAgentBindCustomerInfo(
            @ApiParam(value = "代理人编码", required = true)
            @PathVariable("agentCode") String agentCode) {
        AgentBindCustomerInfoOut out = agentUserInfoService.findAgentBindCustomerInfo(agentCode);
        return Result.success(out);
    }

    @ApiOperation(value = "获取交接代理人列表", notes = "获取交接代理人列表", httpMethod = "GET")
    @GetMapping("findHandoverPersonList")
    public Result findHandoverPersonList(@RequestParam(value = "agentCode", required = false)
                                         @NotBlank(message = "代理人编码不能为空") String agentCode,
                                         @RequestParam(value = "agentName") String agentName) {
        List<HandoverPersonListOut> list = agentUserInfoService.findHandoverPersonList(agentCode, agentName);
        return Result.success(list);
    }


    @ApiOperation(value = "变更代理人区域信息", notes = "变更代理人区域信息")
    @PostMapping("updateAreaManagerRegion")
    @RequiresPermissions(value = {"agent:agent-info:replace"})
    public Result updateAreaManagerRegion(@RequestBody @Validated UpdateAreaManagerRegionVo update) {
        agentUserInfoService.updateAreaManagerRegion(update);
        return Result.success();
    }

    @ApiOperation(value = "获取区域代理人列表", notes = "获取区域代理人列表", httpMethod = "GET")
    @GetMapping("findAreaManagerRegionAgentList")
    public Result findAreaManagerRegionAgentList() {
        List<AreaManagerRegionAgentOut> list = agentUserInfoService.findAreaManagerRegionAgentList();
        return Result.success(list);
    }

    @ApiOperation(value = "获取区域列表", notes = "获取区域下片区列表", httpMethod = "GET")
    @GetMapping("findRegionList")
    public Result findRegionList(@RequestParam(value = "agentCode", required = false) String agentCode) {
        List<SubregionListOut> list = agentUserInfoService.findRegionList(agentCode);
        return Result.success(list);
    }

    @ApiOperation(value = "获取区域下片区列表", notes = "获取区域下片区列表", httpMethod = "GET")
    @PostMapping("findSubregionList")
    public Result findSubregionList(@RequestBody SubregionListVo vo) {
        List<SubregionListOut> list = agentUserInfoService.findSubregionList(vo);
        return Result.success(list);
    }

    @ApiOperation(value = "获取当前代理人的片区列表", notes = "获取当前代理人的片区列表", httpMethod = "GET")
    @PostMapping("findSubregionListByAgentCode")
    public Result findSubregionListByAgentCode(@RequestBody SubregionListByAgentCodeVo vo) {
        List<SubregionListOut> list = agentUserInfoService.findSubregionListByAgentCode(vo);
        return Result.success(list);
    }

    @ApiOperation(value = "获取当前代理人区域列表", notes = "获取当前代理人区域列表", httpMethod = "GET")
    @GetMapping("findAreaManagerRegionAgentListByAgentCode/{agentCode}")
    public Result findAreaManagerRegionAgentListByAgentCode(@PathVariable String agentCode) {
        List<SubregionListOut> list = agentUserInfoService.findAreaManagerRegionAgentListByAgentCode(agentCode);
        return Result.success(list);
    }

    @ApiOperation(value = "获取增员人列表", notes = "获取增员人列表", httpMethod = "GET")
    @GetMapping("findRecruitList")
    public Result findRecruitList() {
        List<SelectOut> list = agentUserInfoService.lambdaQuery()
                .eq(AgentUserInfoEntity::getAgentStatus, 0)
                .or()
                .eq(AgentUserInfoEntity::getAgentStatus, 1)
                .list().stream().map(m -> {
                    SelectOut result = new SelectOut();
                    result.setLabel(m.getAgentName() + m.getBusinessCode());
                    result.setValue(m.getAgentCode());
                    result.setDisabled(m.getAgentStatus() != 1);
                    return result;
                }).sorted(Comparator.comparing(SelectOut::getDisabled)).collect(Collectors.toList());
        return Result.success(list);
    }

}
