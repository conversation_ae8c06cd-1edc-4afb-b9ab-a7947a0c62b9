package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureOrderInfoEntity;
import com.mpolicy.manage.modules.insure.vo.*;
import com.mpolicy.order.common.order.PolicyGroupOrderDataOut;
import com.mpolicy.order.common.order.PolicyOrderDataOut;

import java.util.List;
import java.util.Map;

/**
 * 投保订单信息表
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
public interface InsureOrderInfoService extends IService<InsureOrderInfoEntity> {

    /**
     * 投保订单列表管理
     *
     * @param paramMap: 查询条件
     * @return {@link com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.insure.vo.InsureOrderList> }
     * <AUTHOR>
     * @since 2022/5/29
     */
    PageUtils<InsureOrderList> queryInsureOrderListByPage(Map<String, Object> paramMap);

    /**
     * 获取投保订单详情
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.order.common.order.PolicyOrderDataOut }
     * <AUTHOR>
     * @since 2022/5/29
     */
    PolicyOrderDataOut queryInsureOrderDetail(String insureOrderCode);
    /**
     * 获取投保订单详情
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.order.common.order.PolicyOrderDataOut }
     * <AUTHOR>
     * @since 2022/5/29
     */
    PolicyGroupOrderDataOut groupDetail(String insureOrderCode);


    /**
     * 投保回溯详情获取
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.manage.modules.insure.vo.InsureRecallOut }
     * <AUTHOR>
     * @since 2022/5/29
     */
    InsureRecallOut queryInsureRecallData(String insureOrderCode);

    /**
     * 添加STS文件地址
     *
     * @param insureOrderCode:
     * @param stsFilePath:
     * @return : void
     * <AUTHOR>
     * @date 2022/11/7 16:47
     */
    void changeRecallSts(String insureOrderCode, String stsFilePath);

    /**
     * 订单人工审核
     *
     * @param input:
     * @return : void
     * <AUTHOR>
     * @date 2023/4/14 18:39
     */
    void personReview(InsurePersonReviewInput input);

    /**
     * 订单发起撤单
     *
     * @return : void
     * <AUTHOR>
     * @date 2023/7/5 11:07
     */
    void orderRefund(InsureOrderRefundInfo input);
    /**
     * 订单导出
     *
     * <AUTHOR>
     * @date 2023/7/5 18:10
     * @param params:
     * @return : java.util.List<com.mpolicy.manage.modules.insure.vo.insureOrderInfoOut>
     */
    List<insureOrderInfoOut> queryInsureOrderList(Map<String, Object> params);
    /**
     * 订单导出
     *
     * <AUTHOR>
     * @date 2023/7/5 18:10
     * @param params:
     * @return : java.util.List<com.mpolicy.manage.modules.insure.vo.insureOrderInfoOut>
     */
    List<insureOrderInfoProOut> znQueryInsureOrderList(Map<String, Object> params);
}

