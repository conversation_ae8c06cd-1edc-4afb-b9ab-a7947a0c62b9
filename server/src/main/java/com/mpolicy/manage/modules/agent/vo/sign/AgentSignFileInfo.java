package com.mpolicy.manage.modules.agent.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * ClassName: AgentSignFileInfo
 * Description: 代理人文件签署上传文件信息
 * date: 2023/5/30 11:43
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "代理人文件签署上传文件信息", description = "代理人文件签署上传文件信息")
public class AgentSignFileInfo {

    @ApiModelProperty(value = "上传文件公司组织编码")
    @NotBlank(message = "组织编码不能为空")
    private String orgCode;

    @ApiModelProperty(value = "人员类型")
    @NotBlank(message = "人员类型不能为空")
    private String agentType;

    @ApiModelProperty(value = "上传文件编码")
    @NotBlank(message = "上传文件编码不能为空")
    private String fileCode;

    @ApiModelProperty(value = "上传文件名称")
    @NotBlank(message = "上传文件名称不能为空")
    private String fileName;

    @ApiModelProperty(value = "上传文件路径")
    @NotBlank(message = "上传文件路径不能为空")
    private String filePath;
}
