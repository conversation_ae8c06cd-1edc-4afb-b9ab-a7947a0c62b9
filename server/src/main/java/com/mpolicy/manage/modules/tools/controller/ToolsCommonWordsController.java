package com.mpolicy.manage.modules.tools.controller;

import java.util.Map;

import com.mpolicy.common.result.BasicCodeMsg;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.tools.entity.ToolsCommonWordsEntity;
import com.mpolicy.manage.modules.tools.service.ToolsCommonWordsService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;

import javax.validation.Valid;


/**
 * 测试报告常用语
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-23 16:50:03
 */
@RestController
@RequestMapping("tools/toolscommonwords")
@Api(tags = "测试报告常用语")
public class ToolsCommonWordsController {

    @Autowired
    private ToolsCommonWordsService toolsCommonWordsService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取测试报告常用语列表", notes = "分页获取测试报告常用语列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"cms:commonwords:all"})
    public Result<PageUtils> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils page = toolsCommonWordsService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation(value = "根据id获取信息")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"cms:commonwords:all"})
    public Result<ToolsCommonWordsEntity> info(@PathVariable("id") Integer id) {
        ToolsCommonWordsEntity toolsCommonWords = toolsCommonWordsService.getById(id);

        return Result.success(toolsCommonWords);
    }

    /**
     * 保存
     */
    @ApiOperation(value = "保存常用语")
    @PostMapping("/save")
    @RequiresPermissions(value = {"cms:commonwords:all"})
    public Result save(@Valid @RequestBody ToolsCommonWordsEntity toolsCommonWordsEntity) {
        boolean result = toolsCommonWordsService.saveEntity(toolsCommonWordsEntity);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 更新
     */
    @ApiOperation(value = "更新常用语")
    @PostMapping("/update")
    @RequiresPermissions(value = {"cms:commonwords:all"})
    public Result update(@Valid @RequestBody ToolsCommonWordsEntity toolsCommonWordsEntity) {
        boolean result = toolsCommonWordsService.updateById(toolsCommonWordsEntity);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }

    /**
     * 删除
     */
    @ApiOperation(value = "根据id删除")
    @GetMapping("/delete/{id}")
    @RequiresPermissions(value = {"cms:commonwords:all"})
    public Result delete(@PathVariable("id") String id) {
        boolean result = toolsCommonWordsService.removeById(id);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
    }

}
