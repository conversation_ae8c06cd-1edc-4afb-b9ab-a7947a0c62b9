package com.mpolicy.manage.modules.policy.vo.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * AI聊天对话记录信息
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ApiModel("AI聊天对话记录信息")
public class ConversationRecordVo {

    @ApiModelProperty(value = "记录ID")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户问题")
    private String userQuestion;

    @ApiModelProperty(value = "AI回答")
    private String aiAnswer;

    @ApiModelProperty(value = "对话时间")
    private Date conversationTime;

    @ApiModelProperty(value = "会话ID")
    private String sessionId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "问题类型")
    private String questionType;

    @ApiModelProperty(value = "满意度评分")
    private Integer satisfactionScore;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
