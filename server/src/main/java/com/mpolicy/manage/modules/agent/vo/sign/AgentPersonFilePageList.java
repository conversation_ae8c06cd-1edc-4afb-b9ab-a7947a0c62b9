package com.mpolicy.manage.modules.agent.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description  代理人人员文件签署返回信息
 * @return
 * @Date 2023/6/9 14:07
 * <AUTHOR>
 **/
@Data
@ApiModel(value = "代理人人员文件签署返回信息", description = "代理人人员文件签署返回信息")
public class AgentPersonFilePageList implements Serializable {
    private static final long serialVersionUID = 6996541243589026219L;

    /**
     * 代理人编码
     */
    @ApiModelProperty("代理人编码")
    private String agentCode;

    /**
     * 人员类型
     */
    @ApiModelProperty("人员类型")
    private String agentType;
    @ApiModelProperty("人员类型信息")
    private String agentTypeDesc;
    @ApiModelProperty("代理人所在组织机构编码")
    private String orgCode;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String agentName;

    /**
     * 代理人昵称
     */
    @ApiModelProperty("代理人昵称")
    private String agentNickName;

    /**
     * 离职状态0:在职 1:离职
     */
    @ApiModelProperty("离职状态0:在职 1:离职")
    private Integer quitStatus;
    @ApiModelProperty("离职状态信息")
    private String quitStatusDesc;

    /**
     * 执业证编码
     */
    @ApiModelProperty(value = "执业证编码", required = true)
    private String certificateNum;

    /**
     * 应签署人数
     */
    @ApiModelProperty(value = "应签署文件数")
    private Integer shouldNum;

    /**
     * 已签署人数
     */
    @ApiModelProperty(value = "已签署文件数")
    private Integer alreadyNum;

    /**
     * 未签署人数
     */
    @ApiModelProperty(value = "未签署文件数")
    private Integer unsignedNum;

    /**
     * 系统默认完成人数
     */
    @ApiModelProperty(value = "系统默认完成文件数")
    private Integer defaultFinishNum;
}
