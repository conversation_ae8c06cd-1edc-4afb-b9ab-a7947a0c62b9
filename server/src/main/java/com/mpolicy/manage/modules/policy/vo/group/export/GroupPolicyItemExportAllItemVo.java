package com.mpolicy.manage.modules.policy.vo.group.export;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: yang<PERSON>lin
 * @create: 2023-08-21 11:23
 * @description: 团险分单导出保全明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupPolicyItemExportAllItemVo {
    /**
     * 合同编码
     */
    private String contractCode;
    /**
     * 补退费
     */
    private BigDecimal singlePremium;
    /**
     * 关系
     */
    private String firstInsuredRelation;
    /**
     * 被保人姓名
     */
    private String insuredName;
    /**
     * 主被保人姓名
     */
    private String mainInsuredName;
    /**
     * 性别
     */
    private String insuredGender;
    /**
     * 生日
     */
    private String insuredBirthday;
    /**
     * 证件类型
     */
    private String insuredIdType;
    /**
     * 证件号
     */
    private String insuredIdCard;
    /**
     * 计划编码
     */
    private String planCode;
    /**
     * 计划编码
     */
    private String planName;
    /**
     * 职业代码
     */
    private String insuredCareer;
    /**
     * 职务类别
     */
    private String insuredOccupationalCategory;
    /**
     * 推荐人编码
     */
    private String referrerCode;
    /**
     * 渠道推荐人编码
     */
    private String channelReferrerCode;
    /**
     * 渠道
     */
    private String channelCode;
    /**
     * 增加员类型
     */
    private String peopleType;
    /**
     * 批改单号
     */
    private String endorsementNo;
    /**
     * 保全生效时间
     */
    private Date preservationEffectTime;
}
