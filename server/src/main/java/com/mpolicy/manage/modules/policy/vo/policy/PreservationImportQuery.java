package com.mpolicy.manage.modules.policy.vo.policy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.policy.common.policy.vo.group.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("保全导入查询")
public class PreservationImportQuery extends PageQuery implements Serializable {
    private static final long serialVersionUID = -5144307260738757386L;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间-开始时间",example = "2024-01-01")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-截止时间",example = "2024-01-02")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTimeEnd;
}
