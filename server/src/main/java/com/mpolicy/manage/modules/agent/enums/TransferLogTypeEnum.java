package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * @Description 推荐人转译客户类型枚举
 * @return
 * @Date 2023/8/11 13:57
 * <AUTHOR>
 **/
public enum TransferLogTypeEnum {
    /**
     * 移交客户类型
     */
    TYPE1(1, "移交全部客户"),
    TYPE2(2, "移交部分客户"),
    ;

    private final Integer type;
    private final String name;

    TransferLogTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static TransferLogTypeEnum getNameByCode(Integer type) {
        return Arrays.stream(values()).filter((x) -> x.type.equals(type)).findFirst().orElse(null);
    }

    public Integer getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }
}
