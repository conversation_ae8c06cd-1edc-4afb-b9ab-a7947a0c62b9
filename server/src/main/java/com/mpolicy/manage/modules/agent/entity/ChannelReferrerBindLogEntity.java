package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户经理绑定记录表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-10-14 15:23:05
 */
@TableName("channel_referrer_bind_log")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelReferrerBindLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户经理编码
     */
    private String referrerCode;
    /**
     * 客户经理名称
     */
    private String referrerName;
    /**
     * 客户经理工号
     */
    private String referrerWno;
    /**
     * 1:绑定 2解绑
     */
    private Integer type;
    /**
     * 1:注册 2扫码绑定 3任务 4后台
     */
    private Integer model;
    /**
     * 来源
     */
    private String source;
    /**
     * 创建用户
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
}
