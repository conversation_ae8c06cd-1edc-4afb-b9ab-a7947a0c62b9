package com.mpolicy.manage.modules.sell.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("sell_product_tool_map")
public class SellProductToolMapEntity implements Serializable {
    private static final long serialVersionUID = 1379643647160037892L;

    private Long id;

    private String productCode;

    private Integer toolId;

    private Integer clickStatus;

    private Integer isLogin;
}
