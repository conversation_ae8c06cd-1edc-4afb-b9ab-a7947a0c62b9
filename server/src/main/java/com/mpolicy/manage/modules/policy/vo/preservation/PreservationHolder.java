package com.mpolicy.manage.modules.policy.vo.preservation;

import com.mpolicy.manage.modules.policy.enums.PreservationWhyEnum;
import com.mpolicy.policy.common.enums.PreservationProjectEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
@Data
public class PreservationHolder {

    private String contractCode;

    private String preservationCode;

    private PreservationProjectEnum preservationProjectEnum;

    private PreservationWhyEnum preservationWhyEnum;

    private BigDecimal preservationAmount;

    private Date effectiveTime;





}
