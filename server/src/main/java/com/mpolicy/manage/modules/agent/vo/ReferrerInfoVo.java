package com.mpolicy.manage.modules.agent.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/17 11:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReferrerInfoVo {
    /**
     * 渠道推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名")
    private String referrerName;
    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人编码")
    private String referrerCode;
    /**
     * 推荐人工号
     */
    @ApiModelProperty(value = "推荐人工号")
    private String referrerWno;

    /**
     * 离职状态0:在职 1:离职
     */
    @ApiModelProperty(value = "离职状态0:在职 1:离职")
    private Integer quitStatus;

}
