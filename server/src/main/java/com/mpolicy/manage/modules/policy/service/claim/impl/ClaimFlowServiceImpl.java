package com.mpolicy.manage.modules.policy.service.claim.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.policy.dao.ClaimFlowDao;
import com.mpolicy.manage.modules.policy.entity.ClaimFlowEntity;
import com.mpolicy.manage.modules.policy.service.claim.ClaimFlowService;
import org.springframework.stereotype.Service;


@Service("claimFlowService")
public class ClaimFlowServiceImpl extends ServiceImpl<ClaimFlowDao, ClaimFlowEntity> implements ClaimFlowService {


}
