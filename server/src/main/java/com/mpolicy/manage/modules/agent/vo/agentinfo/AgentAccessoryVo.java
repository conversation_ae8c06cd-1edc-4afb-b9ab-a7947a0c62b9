package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AgentAccessoryVo implements Serializable {
    private static final long serialVersionUID = -4051013835901841705L;

    @ApiModelProperty("文件的id")
    private Integer id;

    @ApiModelProperty(value = "附件类型")
    private String identificationType;

    @ApiModelProperty("附件格式")
    private String suffix;

    @ApiModelProperty("文件地址")
    private String filePath;
}
