package com.mpolicy.manage.modules.sell.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.agent.common.enums.InsureModeEnum;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.enums.*;
import com.mpolicy.manage.modules.common.service.AsyncService;
import com.mpolicy.manage.modules.common.service.IInvalidCacheService;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.sell.dao.*;
import com.mpolicy.manage.modules.sell.entity.*;
import com.mpolicy.manage.modules.sell.enums.OperationTypeEnum;
import com.mpolicy.manage.modules.sell.service.*;
import com.mpolicy.manage.modules.sell.utils.SellMapper;
import com.mpolicy.manage.modules.sell.vo.PolicyCommodityVo;
import com.mpolicy.manage.modules.sell.vo.SellProductVO;
import com.mpolicy.manage.modules.sys.dao.SysDocumentDao;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.product.client.ProductClient;
import com.mpolicy.product.common.portfolio.PortfolioPlanOut;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SellProductServiceImpl extends ServiceImpl<SellProductInfoDao, SellProductInfoEntity> implements ISellProductService {

    private static final Integer SELL_PRODUCT_STATUS_DOWN_CODE = 0;

    @Autowired
    private SellProductInfoDao sellProductInfoDao;
    @Autowired
    private SellProductConfigDao sellProductConfigDao;
    @Autowired
    private SysDocumentDao sysDocumentDao;
    @Autowired
    private ProductClient productClient;
    @Autowired
    private SellProductLabelMapDao sellProductLabelMapDao;
    @Autowired
    private SellProductLabelMapService sellProductLabelMapService;
    @Autowired
    @Lazy
    private AsyncService asyncService;
    @Autowired
    private SellProductToolMapDao sellProductToolMapDao;
    @Autowired
    private SellProductToolDao sellProductToolDao;
    @Autowired
    private IInvalidCacheService invalidCacheService;
    @Autowired
    private SellProductAvailableChannelService sellProductAvailableChannelService;
    @Autowired
    private PolicyCommoditySignService policyCommoditySignService;
    @Autowired
    private AdminChangeHistoryService adminChangeHistoryService;
    @Resource
    private RabbitMQService rabbitMQService;

    @Resource
    private InsuranceProductInfoService insuranceProductInfoService;

    @Resource
    private SellProductUpdateStatusLogService sellProductUpdateStatusLogService;

    @Resource
    private SellMapper sellMapper;


    /**
     * 获取销售商品列表
     *
     * @param vo
     * @return
     */
    @Override
    public PageUtils<SellProductListOut> findPageList(SellProductListVo vo) {
        IPage<SellProductListOut> page = sellProductInfoDao
                .findPageList(new Page<SellProductListVo>(vo.getPage(), vo.getLimit()), vo);
        return new PageUtils(page);
    }

    /**
     * 获取导出销售商品列表
     *
     * @param vo
     * @return
     */
    @Override
    public List<ExportProductListOut> findExportList(SellProductListVo vo) {
        HashMap<String, String> portfolioTypeMap = new HashMap<>();
        portfolioTypeMap.put("1", "小程序");
        portfolioTypeMap.put("2", "APP");
        portfolioTypeMap.put("3", "小程序+APP");
        Map<String, String> productType = DicCacheHelper.getSons("PRODUCT:PROTECTION_TYPE").stream().collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
        List<ExportProductListOut> exportList = baseMapper.findExportList(vo);
        AtomicInteger i = new AtomicInteger(1);
        exportList.forEach(x -> {
            x.setId(i.getAndAdd(1));
            x.setProductType(productType.get(x.getProductType()));
            x.setClientType(portfolioTypeMap.get(x.getClientType()));
            x.setProductStatus(x.getProductStatus().equals("1") ? "已发布" : "未发布");
        });

        return exportList;
    }

    @Override
    public SellProductInfoOut info(String productCode) {
        SellProductInfoOut sellProductInfo = sellProductInfoDao.findSellProductInfoByCode(productCode);
        if (sellProductInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在"));
        }
        sellProductInfo.setProductCover(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getProductCover()));
        sellProductInfo.setProductThumbnail(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getProductThumbnail()));
        sellProductInfo.setProductDetails(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getProductDetails()));
        sellProductInfo.setProductVideoPoster(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getProductVideoPoster()));
        sellProductInfo.setProductVideo(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getProductVideo()));
        sellProductInfo.setWeChatMiniProductVideo(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getWeChatMiniProductVideo()));
        sellProductInfo.setWeChatMiniProductVideoCover(DomainUtil.addOssDomainIfNotExist(sellProductInfo.getWeChatMiniProductVideoCover()));
        sellProductInfo.setProductDisplayBlock(sellProductInfo.getProductDisplayBlock());
        if (StrUtil.isNotBlank(sellProductInfo.getProductDocumentCode())) {
            //获取文档详情
            SysDocumentEntity document = new LambdaQueryChainWrapper<>(sysDocumentDao)
                    .eq(SysDocumentEntity::getFileCode, sellProductInfo.getProductDocumentCode())
                    .last("limit 1").one();
            if (document != null) {
                sellProductInfo.setProductDocument(DomainUtil.addOssDomainIfNotExist(document.getFilePath()));
                sellProductInfo.setProductDocumentName(document.getFileName());
            } else {
                sellProductInfo.setProductDocumentCode("");
            }
        }
        //售卖地区
        List<SellProductConfigEntity> collect = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType())
                .list();

        List<SellAreaOut> sellAreaList = new ArrayList<>();
        if (collect.isEmpty()) {
            sellAreaList.add(SellAreaOut.builder()
                    .label("全国")
                    .value("")
                    .build());
        } else {
            collect.forEach(action -> {
                sellAreaList.add(SellAreaOut.builder()
                        .label(action.getLabel())
                        .value(action.getValue())
                        .build());
            });
        }
        sellProductInfo.setSellAreaList(sellAreaList);
        List<SellAreaOut> ruralAreaList = new ArrayList<>();
        // 农保显示区域
        List<SellProductConfigEntity> ruralShowAreaList = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.RURAL_SHOW_AREA.getConfigType())
                .list();
        if (ruralShowAreaList.isEmpty()) {
            ruralAreaList.add(SellAreaOut.builder()
                    .label("所有区域")
                    .value("")
                    .build());
        } else {
            ruralShowAreaList.forEach(action -> {
                ruralAreaList.add(SellAreaOut.builder()
                        .label(action.getLabel())
                        .value(action.getValue())
                        .build());
            });
        }
        sellProductInfo.setRuralAreaList(ruralAreaList);
        //商品禁销分支
        List<SellProductConfigEntity> branchConfigList = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.PRODUCT_PROHIBIT_BRANCH.getConfigType())
                .list();
        List<SellAreaOut> branchList = new ArrayList<>();
        branchConfigList.forEach(action -> {
            branchList.add(SellAreaOut.builder()
                    .label(action.getLabel())
                    .value(action.getValue())
                    .build());
        });
        sellProductInfo.setProhibitBranchList(branchList);
        //商品标签列表
        List<String> sellLabelList = new LambdaQueryChainWrapper<>(sellProductLabelMapDao)
                .eq(SellProductLabelMapEntity::getProductCode, productCode)
                .list().stream().map(SellProductLabelMapEntity::getLabelCode)
                .collect(Collectors.toList());
        sellProductInfo.setSellLabelList(sellLabelList);
        //产品售卖关键词
        List<String> sellKeyword = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                .list().stream().map(SellProductConfigEntity::getValue)
                .collect(Collectors.toList());
        sellProductInfo.setSellKeyword(sellKeyword);
        //可售渠道
        List<String> availableChannelCodeList = sellProductAvailableChannelService.lambdaQuery()
                .eq(SellProductAvailableChannelEntity::getProductCode, productCode)
                .list().stream().map(SellProductAvailableChannelEntity::getChannelCode)
                .collect(Collectors.toList());
        sellProductInfo.setAvailableChannelCodeList(availableChannelCodeList);
        return sellProductInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SellProductUpdateVo vo) {
        //判断产品信息是否存在
        SellProductInfoOut info = info(vo.getProductCode());
        if (info == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在"));
        }
        //更新产品基础信息
        updateProductBasics(vo);

        //处理适用人群
        handleApplicableGroup(vo.getApplicableGroupList(), info.getApplicableGroupList(), vo.getProductCode());

        //处理售区域
        handleSellArea(vo.getSellAreaList(), vo.getProductCode());

        //处理农保显示区域
        handleRuralArea(vo.getRuralAreaList(), vo.getProductCode());

        //处理禁销分支
        handleProhibitBranch(vo.getProhibitBranchList(), vo.getProductCode());

        //处理善品标签
        handleSellLabel(vo.getSellLabelList(), vo.getProductCode());

        //处理售卖关键词
        handleSellKeyword(vo.getSellKeyword(), vo.getProductCode());

        //处理停售商品
        handleStopSell(vo.getStopSellTime(), info.getStopSellTime(), vo.getProductCode(), vo.getProductAbbreviation());
        //删除
        sellProductAvailableChannelService.lambdaUpdate()
                .eq(SellProductAvailableChannelEntity::getProductCode, vo.getProductCode())
                .remove();
        if (CollUtil.isNotEmpty(vo.getAvailableChannelCodeList())) {
            List<SellProductAvailableChannelEntity> saveBatch = vo.getAvailableChannelCodeList().stream().map(m -> {
                SellProductAvailableChannelEntity result = new SellProductAvailableChannelEntity();
                result.setProductCode(vo.getProductCode());
                result.setChannelCode(m);
                result.setProductName(vo.getProductName());
                result.setChannelName(DicCacheHelper.getValue(m));
                return result;
            }).collect(Collectors.toList());
            sellProductAvailableChannelService.saveBatch(saveBatch);
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.SELL_PRODUCT_INFO, vo.getProductCode());
    }


    /**
     * 更新产品基础信息
     *
     * @param vo 更新的内容实体
     */
    private void updateProductBasics(SellProductUpdateVo vo) {
        SellProductInfoEntity build = SellProductInfoEntity.builder()
                .build();
        BeanUtil.copyProperties(vo, build);
        build.setInsureStatus(build.getSellStatus());
        //非网销或网销转线下 不可售
        if (!SellModeEnum.NETWORK_SALES.getCode().equals(build.getSellMode())
                && !SellModeEnum.NETWORK_TO_OFFLINE.getCode().equals(build.getSellMode())) {
            build.setInsureStatus(StatusEnum.INVALID.getCode());
        }
        //未报备完成的不允许投保
        if (!"REPORT_STATUS:2".equals(build.getReportStatus())) {
            build.setInsureStatus(StatusEnum.INVALID.getCode());
        }
        build.setProductCover(DomainUtil.removeDomain(vo.getProductCover()));
        build.setProductThumbnail(DomainUtil.removeDomain(vo.getProductThumbnail()));
        build.setProductDetails(DomainUtil.removeDomain(vo.getProductDetails()));
        build.setProductDocumentCode(DomainUtil.removeDomain(vo.getProductDocumentCode()));
        build.setProductVideo(DomainUtil.removeDomain(vo.getProductVideo()));
        build.setWeChatMiniProductType(DomainUtil.removeDomain(vo.getWeChatMiniProductType()));
        build.setWeChatMiniProductVideo(DomainUtil.removeDomain(vo.getWeChatMiniProductVideo()));
        //上传了视频 没有上传封面的时候取视频第一帧作为视频封面
        if (StrUtil.isNotBlank(vo.getProductVideo()) && StrUtil.isBlank(vo.getProductVideoPoster())) {
            vo.setProductVideoPoster(DomainUtil.addOssDomainIfNotExist(vo.getProductVideo()) + "?x-oss-process=video/snapshot,t_0,f_jpg,w_642,h_0");
        }
        build.setProductVideoPoster(DomainUtil.removeDomain(vo.getProductVideoPoster()));

        //以下这些需要配置了组合才可以处理,如果没有配置组合恢复默认值
        if (StrUtil.isNotBlank(build.getPortfolioCode())) {
            //允许分享计划
            if (StatusEnum.NORMAL.getCode().equals(build.getIsSharePlan())) {
                //当前商品为对接方式不是API，不支持指定分享计划
                if (!StrUtil.equals(build.getInsureMode(), InsureModeEnum.API.getCode())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前商品为对接方式不是API，不支持指定分享计划"));
                }
                Result<PortfolioPlanOut> result = productClient.queryPlanInfo(build.getPortfolioCode());
                if (!result.isSuccess()) {
                    throw new GlobalException(result);
                }
                if (StatusEnum.NORMAL.getCode().equals(result.getData().getPlanPrefixCondition())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该产品存在计划被其他要素影响，不支持指定分享计划"));
                }
                if (CollUtil.isEmpty(result.getData().getPlanList())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前商品不存在多个计划，无需指定分享计划"));
                }

            }
        } else {
            //计划不允许分享
            build.setIsSharePlan(StatusEnum.INVALID.getCode());
        }

        sellProductInfoDao.updateById(build);
    }

    /**
     * 处理停售商品
     *
     * @param stopSellTime        停售时间
     * @param oldStopSellTime     原来设置的停售时间
     * @param productCode         产品编码
     * @param productAbbreviation 产品简称
     */
    private void handleStopSell(Date stopSellTime, Date oldStopSellTime, String productCode, String productAbbreviation) {
        //判断修改的下架时间是否变更,如果变更推送消息
        if (stopSellTime != null) {
            if (oldStopSellTime == null || (oldStopSellTime.getTime() != stopSellTime.getTime())) {
                asyncService.sendSellStopTips(productCode, productAbbreviation, DateUtil.date(stopSellTime));
            }
        }
    }

    /**
     * 处理售卖商品的关键词
     *
     * @param sellKeyword 售卖关键词
     * @param productCode 产品编码
     */
    private void handleSellKeyword(List<String> sellKeyword, String productCode) {
        if (CollUtil.isNotEmpty(sellKeyword)) {
            List<SellProductConfigEntity> sellKeywordList = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                    .in(SellProductConfigEntity::getValue, sellKeyword)
                    .list();
            //判断是否存在已经被占用的标签名称
            List<String> repeatSellKeyword = sellKeywordList.stream()
                    .filter(a -> !a.getProductCode().equals(productCode))
                    .map(SellProductConfigEntity::getValue).collect(Collectors.toList());
            if (repeatSellKeyword != null && !repeatSellKeyword.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("关键词[{}]已被占用", CollUtil.join(repeatSellKeyword, ","))));
            }
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType()));
            //新增售卖区域
            sellKeyword.forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                        .label(SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getLabel())
                        .productCode(productCode)
                        .value(action)
                        .build());
            });
        } else {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType()));
        }

    }


    /**
     * 处理是售卖标签
     *
     * @param labelList   标签列表
     * @param productCode 产品编码
     */
    private void handleSellLabel(List<String> labelList, String productCode) {
        //商品标签
        if (CollUtil.isNotEmpty(labelList)) {
            List<String> sellLabelList = new LambdaQueryChainWrapper<>(sellProductLabelMapDao)
                    .eq(SellProductLabelMapEntity::getProductCode, productCode)
                    .list().stream().map(SellProductLabelMapEntity::getLabelCode)
                    .collect(Collectors.toList());
            if (!CollUtil.disjunction(labelList, sellLabelList).isEmpty()) {
                //删除数据
                sellProductLabelMapDao.delete(new QueryWrapper<SellProductLabelMapEntity>()
                        .lambda().eq(SellProductLabelMapEntity::getProductCode, productCode));
                //新增数据
                List<SellProductLabelMapEntity> saveBatch = new ArrayList<>();
                labelList.forEach(action -> {
                    saveBatch.add(SellProductLabelMapEntity.builder()
                            .labelCode(action)
                            .productCode(productCode)
                            .build());
                });
                sellProductLabelMapService.saveBatch(saveBatch);
            }
        } else {
            sellProductLabelMapDao.delete(new QueryWrapper<SellProductLabelMapEntity>()
                    .lambda().eq(SellProductLabelMapEntity::getProductCode, productCode));
        }
    }


    /**
     * 处理适用人群
     *
     * @param applicableGroupList    新的适用人群
     * @param oldApplicableGroupList 原来的适用人群
     * @param productCode            产品编码
     */
    private void handleApplicableGroup(String applicableGroupList, String oldApplicableGroupList, String productCode) {
        if (StrUtil.isBlank(applicableGroupList)) {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.FOR_THE_CROWD.getConfigType()));
        } else if (!applicableGroupList.equals(oldApplicableGroupList)) {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.FOR_THE_CROWD.getConfigType()));
            List<String> split = StrUtil.split(applicableGroupList, ';');
            split.forEach(action -> {
                if (StrUtil.isNotBlank(action)) {
                    sellProductConfigDao.insert(SellProductConfigEntity.builder()
                            .configType(SellProductConfigEnum.FOR_THE_CROWD.getConfigType())
                            .label(SellProductConfigEnum.FOR_THE_CROWD.getLabel())
                            .productCode(productCode)
                            .value(action)
                            .build());
                }
            });
        }
    }

    /**
     * 处理售卖地区
     *
     * @param sellAreaList 售卖地区
     * @param productCode  产品编码
     */
    private void handleSellArea(List<SellAreaOut> sellAreaList, String productCode) {
        //售卖地区
        List<String> collect = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType())
                .list().stream().map(SellProductConfigEntity::getValue).collect(Collectors.toList());
        //判断当前变更的区域和原来的储存的区域是否存在差异,如果存在差异直接删除变更
        if (!CollUtil.disjunction(sellAreaList.stream().map(SellAreaOut::getValue).collect(Collectors.toList()), collect).isEmpty()) {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType()));
            //新增售卖区域
            sellAreaList.forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType())
                        .label(action.getLabel())
                        .productCode(productCode)
                        .value(action.getValue())
                        .build());
            });
        }
    }
    /**
     * 处理禁销分支
     *
     * @param sellAreaList 禁销分支
     * @param productCode  产品编码
     */
    private void handleProhibitBranch(List<SellAreaOut> sellAreaList, String productCode) {
        //禁销分支
        List<String> collect = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.PRODUCT_PROHIBIT_BRANCH.getConfigType())
                .list().stream().map(SellProductConfigEntity::getValue).collect(Collectors.toList());
        //判断当前变更的区域和原来的储存的区域是否存在差异,如果存在差异直接删除变更
        if (!CollUtil.disjunction(sellAreaList.stream().map(SellAreaOut::getValue).collect(Collectors.toList()), collect).isEmpty()) {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.PRODUCT_PROHIBIT_BRANCH.getConfigType()));
            //新增禁销分支
            sellAreaList.forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.PRODUCT_PROHIBIT_BRANCH.getConfigType())
                        .label(action.getLabel())
                        .productCode(productCode)
                        .value(action.getValue())
                        .build());
            });
        }
    }

    /**
     * 处理农保地区
     *
     * @param ruralAreaList 农保显示的区域
     * @param productCode   产品编码
     */
    private void handleRuralArea(List<SellAreaOut> ruralAreaList, String productCode) {
        List<String> collect = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getProductCode, productCode)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.RURAL_SHOW_AREA.getConfigType())
                .list().stream().map(SellProductConfigEntity::getValue).collect(Collectors.toList());
        //判断当前变更的区域和原来的储存的区域是否存在差异,如果存在差异直接删除变更
        if (!CollUtil.disjunction(ruralAreaList.stream().map(SellAreaOut::getValue).collect(Collectors.toList()), collect).isEmpty()) {
            sellProductConfigDao.delete(new LambdaQueryWrapper<SellProductConfigEntity>()
                    .eq(SellProductConfigEntity::getProductCode, productCode)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.RURAL_SHOW_AREA.getConfigType()));
            //新增售卖区域
            ruralAreaList.forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.RURAL_SHOW_AREA.getConfigType())
                        .label(action.getLabel())
                        .productCode(productCode)
                        .value(action.getValue())
                        .build());
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SellProductSaveVo vo) {
        SellProductInfoEntity build = SellProductInfoEntity.builder()
                .productCode(CommonUtils.createCodeLastNumber("P"))
                .build();
        BeanUtil.copyProperties(vo, build);
        build.setInsureStatus(build.getSellStatus());
        //非网销或网销转线下 不可售
        if (!SellModeEnum.NETWORK_SALES.getCode().equals(build.getSellMode())
                && !SellModeEnum.NETWORK_TO_OFFLINE.getCode().equals(build.getSellMode())) {
            build.setInsureStatus(StatusEnum.INVALID.getCode());
        }
        //未报备完成的不允许投保
        if (!"REPORT_STATUS:2".equals(build.getReportStatus())) {
            build.setInsureStatus(StatusEnum.INVALID.getCode());
        }
        build.setProductCover(DomainUtil.removeDomain(vo.getProductCover()));
        build.setProductThumbnail(DomainUtil.removeDomain(vo.getProductThumbnail()));
        build.setProductDetails(DomainUtil.removeDomain(vo.getProductDetails()));
        build.setProductDocumentCode(DomainUtil.removeDomain(vo.getProductDocumentCode()));
        build.setProductVideo(DomainUtil.removeDomain(vo.getProductVideo()));
        build.setWeChatMiniProductVideo(DomainUtil.removeDomain(vo.getWeChatMiniProductVideo()));
        build.setWeChatMiniProductVideoCover(DomainUtil.removeDomain(vo.getWeChatMiniProductVideoCover()));
        //上传了视频 没有上传封面的时候取视频第一帧作为视频封面
        if (StrUtil.isNotBlank(vo.getProductVideo()) && StrUtil.isBlank(vo.getProductVideoPoster())) {
            vo.setProductVideoPoster(DomainUtil.addOssDomainIfNotExist(vo.getProductVideo()) + "?x-oss-process=video/snapshot,t_0,f_jpg,w_642,h_0");
        }
        build.setProductVideoPoster(DomainUtil.removeDomain(vo.getProductVideoPoster()));
        //以下这些需要配置了组合才可以处理,如果没有配置组合恢复默认值
        if (StrUtil.isNotBlank(build.getPortfolioCode())) {
            //允许分享计划
            if (StatusEnum.NORMAL.getCode().equals(build.getIsSharePlan())) {
                Result<PortfolioPlanOut> result = productClient.queryPlanInfo(build.getPortfolioCode());
                if (!result.isSuccess()) {
                    throw new GlobalException(result);
                }
                if (StatusEnum.NORMAL.getCode().equals(result.getData().getPlanPrefixCondition())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该产品存在计划被其他要素影响，不支持指定分享计划"));
                }
                if (CollUtil.isEmpty(result.getData().getPlanList())) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前商品不存在多个计划，无需指定分享计划"));
                }
            }
        } else {
            //计划不允许分享
            build.setIsSharePlan(StatusEnum.INVALID.getCode());
        }
        sellProductInfoDao.insert(build);
        //新增适用人群
        if (StrUtil.isNotBlank(vo.getApplicableGroupList())) {
            List<String> split = StrUtil.split(vo.getApplicableGroupList(), ';');
            split.forEach(action -> {
                if (StrUtil.isNotBlank(action)) {
                    SellProductConfigEntity insert = SellProductConfigEntity.builder()
                            .configType(SellProductConfigEnum.FOR_THE_CROWD.getConfigType())
                            .label(SellProductConfigEnum.FOR_THE_CROWD.getLabel())
                            .productCode(build.getProductCode())
                            .value(action)
                            .build();
                    sellProductConfigDao.insert(insert);
                }
            });
        }
        //新增售卖区域
        vo.getSellAreaList().forEach(action -> {
            sellProductConfigDao.insert(SellProductConfigEntity.builder()
                    .configType(SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType())
                    .label(action.getLabel())
                    .productCode(build.getProductCode())
                    .value(action.getValue())
                    .build());
        });
        //农保区域处理
        if (CollUtil.isNotEmpty(vo.getRuralAreaList())) {
            vo.getRuralAreaList().forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.RURAL_SHOW_AREA.getConfigType())
                        .label(action.getLabel())
                        .productCode(build.getProductCode())
                        .value(action.getValue())
                        .build());
            });
        }
        //禁销分支出路
        if (CollUtil.isNotEmpty(vo.getProhibitBranchList())) {
            vo.getProhibitBranchList().forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.PRODUCT_PROHIBIT_BRANCH.getConfigType())
                        .label(action.getLabel())
                        .productCode(build.getProductCode())
                        .value(action.getValue())
                        .build());
            });
        }
        //商品标签
        if (vo.getSellLabelList() != null && !vo.getSellLabelList().isEmpty()) {
            List<SellProductLabelMapEntity> saveBatch = new ArrayList<>();
            vo.getSellLabelList().forEach(action -> {
                saveBatch.add(SellProductLabelMapEntity.builder()
                        .labelCode(action)
                        .productCode(build.getProductCode())
                        .build());
            });
            sellProductLabelMapService.saveBatch(saveBatch);
        }
        //售卖关键词
        List<String> sellKeyword = vo.getSellKeyword();
        if (sellKeyword != null && !sellKeyword.isEmpty()) {
            List<SellProductConfigEntity> repeatSellKeyword = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                    .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                    .in(SellProductConfigEntity::getValue, sellKeyword)
                    .list();
            if (repeatSellKeyword != null && !repeatSellKeyword.isEmpty()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("关键词[{}]已被占用",
                        CollUtil.join(repeatSellKeyword.stream().map(SellProductConfigEntity::getValue)
                                .collect(Collectors.toList()), ","))));
            }
            sellKeyword.forEach(action -> {
                sellProductConfigDao.insert(SellProductConfigEntity.builder()
                        .configType(SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                        .label(SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getLabel())
                        .productCode(build.getProductCode())
                        .value(action)
                        .build());
            });
        }
        //停售时间处理
        if (vo.getStopSellTime() != null) {
            asyncService.sendSellStopTips(build.getProductCode(), build.getProductAbbreviation(), DateUtil.date(build.getStopSellTime()));
        }

        if (CollUtil.isNotEmpty(vo.getAvailableChannelCodeList())) {
            List<SellProductAvailableChannelEntity> saveBatch = vo.getAvailableChannelCodeList().stream().map(m -> {
                SellProductAvailableChannelEntity result = new SellProductAvailableChannelEntity();
                result.setProductCode(build.getProductCode());
                result.setChannelCode(m);
                result.setProductName(vo.getProductName());
                result.setChannelName(DicCacheHelper.getValue(m));
                return result;
            }).collect(Collectors.toList());
            sellProductAvailableChannelService.saveBatch(saveBatch);
        }

        invalidCacheService.invalidCache(InvalidCacheEnum.SELL_PRODUCT_INFO, build.getProductCode());
    }

    @Override
    public void delete(String productCode) {

    }

    /**
     * 修改状态
     *
     * @param vo
     */
    @Override
    public void updateStatus(SellProductStatusVo vo) {
        SellProductInfoEntity productInfo = sellProductInfoDao.selectById(vo.getProductCode());
        if (productInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("商品信息不存在"));
        }
        SellProductInfoEntity.SellProductInfoEntityBuilder builder = SellProductInfoEntity
                .builder()
                .stopSellTime(null) //只要更变了状态定时任务就失效
                .productCode(vo.getProductCode())
                .productStatus(vo.getProductStatus());


        if (StatusEnum.INVALID.getCode().equals(vo.getProductStatus())){
            builder.isRuralHotSale(StatusEnum.INVALID.getCode());
        }

        sellProductInfoDao.updateById(
                builder.build());

        //上架 ;售卖方式0:网销 ;报备完成
        if (StatusEnum.NORMAL.getCode().equals(vo.getProductStatus()) &&
                ((StatusEnum.INVALID.getCode().toString().equals(productInfo.getSellMode())
                        && "REPORT_STATUS:2".equals(productInfo.getReportStatus()))
                        || !StatusEnum.INVALID.getCode().toString().equals(productInfo.getSellMode()))) {
            asyncService.sendSellStartTips(productInfo.getProductCode(), productInfo.getProductAbbreviation());

        }
        invalidCacheService.invalidCache(InvalidCacheEnum.SELL_PRODUCT_INFO, vo.getProductCode());
    }

    @Override
    public void timeUpdateStatus(TimeSellProductStatusVo timeSellProductStatusVo) {
        SellProductInfoEntity productInfo = sellProductInfoDao.selectById(timeSellProductStatusVo.getProductCode());
        Date stopSellTime = productInfo.getStopSellTime();
        Date changeTime = timeSellProductStatusVo.getChangeTime();
        if (changeTime.equals(stopSellTime)) {
            this.updateStatus(timeSellProductStatusVo);
            sellProductInfoDao.updateById(SellProductInfoEntity.builder()
                    .productCode(timeSellProductStatusVo.getProductCode())
                    .stopSellTime(null)
                    .build());
            if (SELL_PRODUCT_STATUS_DOWN_CODE.equals(timeSellProductStatusVo.getProductStatus())) {
                asyncService.sendSellDownTips(productInfo.getProductCode(),productInfo.getProductAbbreviation());
            }
            log.info("定时更变产品状态执行完毕：timeSellProductStatusVo={}",JSON.toJSONString(timeSellProductStatusVo));
        }else {
            log.info("定时更变产品状态执行失败，该任务不是最新任务，跳过处理：timeSellProductStatusVo={}，productInfo={}",JSON.toJSONString(timeSellProductStatusVo),JSON.toJSONString(productInfo));
        }
    }

    @Override
    public void updateStatusV2(TimeSellProductStatusVo timeSellProductStatusVo) {
        SellProductInfoEntity productInfo = sellProductInfoDao.selectById(timeSellProductStatusVo.getProductCode());
        if (timeSellProductStatusVo.getProductStatus().equals(productInfo.getProductStatus())){
            log.warn("不能上架已上架的商品或者不能下架已下架的商品 timeSellProductStatusVo={},productInfo={}",JSON.toJSONString(timeSellProductStatusVo),JSON.toJSONString(productInfo));
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("不能上架已上架的商品或者不能下架已下架的商品"));
        }
        Integer changeType = timeSellProductStatusVo.getChangeType();
        ProductStatusChangeType productStatusChangeType = ProductStatusChangeType.deCode(changeType);

        switch (productStatusChangeType){
            case UPDATE_NOW:
                this.updateStatus(timeSellProductStatusVo);
                break;
            case UPDATE_TIME:
                productInfo.setStopSellTime(timeSellProductStatusVo.getChangeTime());
                sellProductInfoDao.updateById(productInfo);
                asyncService.sendSellStopTips(productInfo.getProductCode(),productInfo.getProductAbbreviation(),DateUtil.date(timeSellProductStatusVo.getChangeTime()));
                break;
        }
        productStatusChangelog(DateUtil.date(),productStatusChangeType,timeSellProductStatusVo);
    }

    /**
     *
     *
     * 针对商品状态更变进行日志记录
     * 内部使用异步的形式进行记录
     * 如果更变原因参数为空，将跳过记录
     *
     *
     * @param operationTime
     *
     * 操作时间
     *
     * @param productStatusChangeType
     *
     * 操作更变类型
     *
     * @param timeSellProductStatusVo
     *
     * 定时操作的vo参数
     *
     */
    @Async
    private void productStatusChangelog(Date operationTime,ProductStatusChangeType productStatusChangeType, TimeSellProductStatusVo timeSellProductStatusVo) {
        log.info("进行商品上下架记录执行开始 operationTime={}，productStatusChangeType={}，timeSellProductStatusVo={}",operationTime,productStatusChangeType,JSON.toJSONString(timeSellProductStatusVo));
        String operationReason = timeSellProductStatusVo.getOperationReason();
        if (StringUtils.isBlank(operationReason)){
            log.info("进行商品上下架记录无原因跳过");
            return;
        }

        SellProductUpdateStatusLogEntity sellProductUpdateStatusLogEntity = sellMapper.timeSellProductStatusVo2SellProductUpdateStatusLogEntity(timeSellProductStatusVo);
        sellProductUpdateStatusLogEntity.setOperationTime(operationTime);
        Integer productStatus = timeSellProductStatusVo.getProductStatus();
        if (StatusEnum.INVALID.getCode().equals(productStatus)) {
            sellProductUpdateStatusLogEntity.setOperationType(OperationTypeEnum.NOW_DOWN.getCode());
        }else {
            sellProductUpdateStatusLogEntity.setOperationType(OperationTypeEnum.NOW_UP.getCode());
        }

        switch (productStatusChangeType){
            case UPDATE_NOW:
                // 如果是立即上下架，操作时间就是状态更变时间
                sellProductUpdateStatusLogEntity.setStatusChangeTime(operationTime);
                break;
            case UPDATE_TIME:
                // 如果是定时上下架，操作时间就是指定的上下架的时间
                Date changeTime = timeSellProductStatusVo.getChangeTime();
                sellProductUpdateStatusLogEntity.setStatusChangeTime(changeTime);
                break;
            default:
                log.warn("未匹配到产品状态更变类型 productStatusChangeType={}",JSON.toJSONString(productStatusChangeType));
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("未匹配到产品状态更变类型"));
        }
        sellProductUpdateStatusLogService.save(sellProductUpdateStatusLogEntity);
    }

    private MQMessage buildMQMessage(TimeSellProductStatusVo timeSellProductStatusVo,QueueEnum queueEnum) {
        Date changeDate = timeSellProductStatusVo.getChangeTime();
        long changeTime = DateUtil.between(DateUtil.date(), changeDate, DateUnit.MS);
        MQMessage mqMessage = new MQMessage();
        mqMessage.setCode(timeSellProductStatusVo.getProductCode());
        mqMessage.setData(JSON.parseObject(JSON.toJSONString(timeSellProductStatusVo)));
        mqMessage.setOpeType(queueEnum.getRouteKey());
        mqMessage.setTtl(changeTime);
        return mqMessage;
    }

    @Override
    public String getProductName(String productCode) {
        SellProductInfoEntity sellProductInfoEntity = baseMapper.selectOne(Wrappers.<SellProductInfoEntity>lambdaQuery().eq(SellProductInfoEntity::getProductCode, productCode));
        if (sellProductInfoEntity == null) {
            return null;
        }
        return sellProductInfoEntity.getProductName();
    }

    /**
     * 获取售卖区域列表
     *
     * @return
     */
    @Override
    public List<SellAreaOut> findSellAreaList() {
        List<SellAreaOut> result = CollUtil.newArrayList(SellAreaOut.builder()
                .label("全国")
                .value("")
                .build());
        List<DicCacheHelper.DicEntity> list = DicCacheHelper.getSons(SellProductConfigEnum.SELL_PRODUCT_AREA.getConfigType());
        list.forEach(action -> {
            result.add(SellAreaOut.builder()
                    .label(action.getValue())
                    .value(action.getKey())
                    .build());
        });
        return result;
    }

    @Override
    public List<SellAreaOut> findRuralAreaList() {
        List<SellAreaOut> result = CollUtil.newArrayList(SellAreaOut.builder()
                .label("所有区域")
                .value("")
                .build());
        List<DicCacheHelper.DicEntity> list = DicCacheHelper.getSons("REFERRER_REGION");
        list.forEach(action -> {
            result.add(SellAreaOut.builder()
                    .label(action.getValue())
                    .value(action.getKey())
                    .build());
        });
        return result;
    }

    /**
     * 获取产品关键词是否存在
     *
     * @param sellKeyword
     * @return
     */
    @Override
    public SellKeywordOut findIsSellKeyword(String sellKeyword) {
        Integer count = new LambdaQueryChainWrapper<>(sellProductConfigDao)
                .eq(SellProductConfigEntity::getConfigType, SellProductConfigEnum.SELL_PRODUCT_KEYWORD.getConfigType())
                .in(SellProductConfigEntity::getValue, sellKeyword)
                .count();
        return SellKeywordOut.builder()
                .isSellKeyword(count)
                .build();
    }

    /**
     * 获取售卖商品下拉列表
     *
     * @param vo
     * @return
     */
    @Override
    public List<SellSelectListOut> findSellSelectList(SellSelectListVo vo) {
        return sellProductInfoDao.findSellSelectList(vo);
    }

    @Override
    public List<SellToolListOut> findSellToolList(String productCode) {
        Map<Integer, SellProductToolMapEntity> toolMap = new LambdaQueryChainWrapper<>(sellProductToolMapDao)
                .eq(SellProductToolMapEntity::getProductCode, productCode)
                .list().stream()
                .collect(Collectors.toMap(SellProductToolMapEntity::getToolId, key -> key, (k1, k2) -> k1));
        List<SellProductToolEntity> toolList = new LambdaQueryChainWrapper<>(sellProductToolDao)
                .orderByAsc(SellProductToolEntity::getToolType)
                .orderByDesc(SellProductToolEntity::getToolIndex)
                .list();
        List<SellToolListOut> resultList = new ArrayList<>();
        toolList.forEach(action -> {
            SellToolListOut out = new SellToolListOut();
            if (toolMap.containsKey(action.getId())) {
                SellProductToolMapEntity toolMapEntity = toolMap.get(action.getId());
                out.setClickStatus(toolMapEntity.getClickStatus());
                out.setIsLogin(toolMapEntity.getIsLogin());
            } else {
                out.setClickStatus(StatusEnum.INVALID.getCode());
                out.setIsLogin(StatusEnum.INVALID.getCode());
            }
            out.setProductCode(productCode);
            out.setToolId(action.getId());
            out.setJumpName(action.getJumpName());
            resultList.add(out);
        });
        return resultList;
    }

    @Override
    public void updateSellTool(SellToolVo vo) {
        SellProductToolMapEntity sellProductToolMap = new LambdaQueryChainWrapper<>(sellProductToolMapDao)
                .eq(SellProductToolMapEntity::getToolId, vo.getToolId())
                .eq(SellProductToolMapEntity::getProductCode, vo.getProductCode())
                .one();
        if (sellProductToolMap == null) {
            SellProductToolMapEntity save = new SellProductToolMapEntity();
            save.setIsLogin(vo.getIsLogin());
            save.setClickStatus(vo.getClickStatus());
            save.setProductCode(vo.getProductCode());
            save.setToolId(vo.getToolId());
            sellProductToolMapDao.insert(save);
        } else {
            SellProductToolMapEntity update = new SellProductToolMapEntity();
            update.setIsLogin(vo.getIsLogin());
            update.setClickStatus(vo.getClickStatus());
            update.setId(sellProductToolMap.getId());
            sellProductToolMapDao.updateById(update);
        }

    }


    /**
     * 判断是否可以勾选分享计划
     *
     * @param portfolioCode 组合编码
     * @return
     */
    @Override
    public PortfolioPlanOut findIsSharePlan(String portfolioCode) {
        Result<PortfolioPlanOut> result = productClient.queryPlanInfo(portfolioCode);
        if (!result.isSuccess()) {
            throw new GlobalException(result);
        }
        PortfolioPlanOut portfolioPlan = result.getData();
        return portfolioPlan;
    }

    @Override
    public void policyCommodity(PolicyCommodityVo vo) {
        String commodityCode = vo.getCommodityCode();
        PolicyCommoditySignEntity entity = Optional.ofNullable(policyCommoditySignService.lambdaQuery()
                .eq(PolicyCommoditySignEntity::getCommodityCode, commodityCode)
                .one()).orElse(new PolicyCommoditySignEntity());
        BeanUtils.copyProperties(vo, entity);
        entity.setProblemContent(CollectionUtils.isEmpty(vo.getProblemContent()) ? null : vo.getProblemContent().toJSONString());
        policyCommoditySignService.saveOrUpdate(entity);
        AdminChangeHistoryEntity historyEntity = new AdminChangeHistoryEntity();
        historyEntity.setChangeType(AdminChangeEnum.BROKERS_COMPANY.getCode());
        historyEntity.setChangeName(AdminChangeEnum.BROKERS_COMPANY.getName());
        historyEntity.setChangeCode(commodityCode);
        historyEntity.setChangeContent(JSON.toJSONString(vo));
        adminChangeHistoryService.save(historyEntity);
    }

    @Override
    public PolicyCommodityVo getPolicyCommodity(String productCode) {
        PolicyCommodityVo result = new PolicyCommodityVo();
        PolicyCommoditySignEntity entity = policyCommoditySignService.lambdaQuery()
                .eq(PolicyCommoditySignEntity::getCommodityCode, productCode)
                .one();
        if(entity != null){
            BeanUtils.copyProperties(entity, result);
            result.setProblemContent(StringUtils.isEmpty(entity.getProblemContent()) ? null : JSONArray.parseArray(entity.getProblemContent()));
            return result;
        }
        SellProductInfoOut info = Optional.ofNullable(
                sellProductInfoDao.findSellProductInfoByCode(productCode)
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("产品编码不存在")));
        result.setCommodityCode(info.getProductCode());
        result.setCommodityName(info.getProductName());
        result.setCompanyCode(info.getSupplierCode());
        result.setCompanyName(info.getSupplierName());
        return result;
    }

    @Override
    public List<SellProductListOut> findIntelligentSolutionList() {
        List<SellProductInfoEntity> list = this.lambdaQuery().eq(SellProductInfoEntity::getIsIntelligentSolution, StatusEnum.NORMAL.getCode())
                .list();
        List<SellProductListOut> collect = list.stream().map(a -> {
            SellProductListOut bean = new SellProductListOut();
            BeanUtil.copyProperties(a, bean);
            return bean;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<String> timeUpdateStatusV2(Date startDate,Date endDate) {
        // 获取所有需要符合条件的，需要处理的商品
        List<SellProductInfoEntity> needHandlerSellProductList = lambdaQuery()
                .isNotNull(SellProductInfoEntity::getStopSellTime)
                .between(SellProductInfoEntity::getStopSellTime, startDate, endDate)
                .list();

        for (SellProductInfoEntity sellProductInfoEntity : needHandlerSellProductList) {
            SellProductStatusVo sellProductStatusVo = getSellProductStatusVo(sellProductInfoEntity);
            this.updateStatus(sellProductStatusVo);
            log.info("商品态更变 sellProductStatusVo ={}",JSON.toJSONString(sellProductStatusVo));
        }

        return needHandlerSellProductList
                .stream()
                .map(SellProductInfoEntity::getProductCode)
                .collect(Collectors.toList());
    }

    /**
     *
     *
     * 获取销售状态的vo
     * 如果当前的是上架状态，就将他处理为下架
     * 如果是下架状态，就视为是上架处理
     *
     *
     * @param sellProductInfoEntity
     * @return
     */
    private SellProductStatusVo getSellProductStatusVo(SellProductInfoEntity sellProductInfoEntity) {
        SellProductStatusVo sellProductStatusVo = new SellProductStatusVo();
        sellProductStatusVo.setProductCode(sellProductInfoEntity.getProductCode());
        Integer productStatus = sellProductInfoEntity.getProductStatus();
        if (StatusEnum.INVALID.getCode().equals(productStatus)) {
            sellProductStatusVo.setProductStatus(StatusEnum.NORMAL.getCode());
        }else {
            sellProductStatusVo.setProductStatus(StatusEnum.INVALID.getCode());
        }
        return sellProductStatusVo;
    }

    @Override
    public SellProductVO getByProductCode(String productCode) {
        List<SellProductInfoEntity> sellProductInfoEntityList = this.lambdaQuery()
                .eq(SellProductInfoEntity::getProductCode, productCode)
                .list();

        // 一般产品编码查询的唯一的商品，如果不是找到最新的一条
        if (CollectionUtils.isNotEmpty(sellProductInfoEntityList)) {
            SellProductInfoEntity sellProductInfoEntity = sellProductInfoEntityList.get(0);
            SellProductVO sellProductVO = new SellProductVO();
            BeanUtils.copyProperties(sellProductInfoEntity,sellProductVO);
            List<InsuranceProductInfoEntity> insuranceProductInfoEntityList = insuranceProductInfoService.getListByPortfolio(sellProductInfoEntity.getPortfolioCode());
            sellProductVO.setInsuranceProductInfoEntityList(insuranceProductInfoEntityList);
            return sellProductVO;
        }

        return null;
    }
}
