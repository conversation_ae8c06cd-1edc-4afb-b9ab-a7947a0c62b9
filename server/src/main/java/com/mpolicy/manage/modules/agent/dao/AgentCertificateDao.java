package com.mpolicy.manage.modules.agent.dao;

import com.mpolicy.manage.modules.agent.entity.AgentCertificateEntity;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentCertificateVo;
import com.mpolicy.service.common.mapper.ImsBaseMapper;

import java.util.List;

/**
 * 经纪人人员证件信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentCertificateDao extends ImsBaseMapper<AgentCertificateEntity> {


    /**
     * 代理人证件列表
     * @param agentCode
     * @return
     */
    List<AgentCertificateVo> findAgentCertificateList(String agentCode);
}
