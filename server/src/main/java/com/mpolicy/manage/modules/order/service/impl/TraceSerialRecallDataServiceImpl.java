package com.mpolicy.manage.modules.order.service.impl;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.modules.insure.vo.InsureRecallList;
import com.mpolicy.manage.modules.order.dao.TraceSerialRecallDataDao;
import com.mpolicy.manage.modules.order.entity.TraceSerialRecallDataEntity;
import com.mpolicy.manage.modules.order.service.TraceSerialRecallDataService;
import com.mpolicy.order.common.enums.TraceRecallTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


@Service("traceSerialRecallDataService")
public class TraceSerialRecallDataServiceImpl extends ServiceImpl<TraceSerialRecallDataDao, TraceSerialRecallDataEntity> implements TraceSerialRecallDataService {

    @Override
    public List<InsureRecallList> getPolicyTraceSerialRecallData(String insureOrderCode) {
        // 1 获取投保回溯集合
        List<TraceSerialRecallDataEntity> list = lambdaQuery().eq(TraceSerialRecallDataEntity::getTraceSerialId, insureOrderCode).list();

        // 2 构建返回vo
        List<InsureRecallList> result = list.stream().map(x -> {
            InsureRecallList bean = new InsureRecallList();
            BeanUtils.copyProperties(x, bean);
            bean.setInsureOrderCode(x.getTraceSerialId());
            bean.setEnterPageTime(DateUtil.formatDateTime(x.getEnterPageTime()));
            bean.setQuitPageTime(DateUtil.formatDateTime(x.getQuitPageTime()));
            if(StringUtils.equals(TraceRecallTypeEnum.IMAGE.getRecallCode(),x.getRecallType())){
                bean.setShotImgPath(DomainUtil.addOssDomainIfNotExist(x.getShotImgPath()));
            }else{
                bean.setShotImgPath(x.getShotImgPath());
            }
            return bean;
        }).collect(Collectors.toList());
        return result;
    }
}
