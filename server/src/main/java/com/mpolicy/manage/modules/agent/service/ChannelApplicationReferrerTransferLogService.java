package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerTransferLogEntity;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerLogVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerTransferLogOut;
import com.mpolicy.manage.modules.agent.vo.agentinfo.SaveChannelApplicationReferrerLogVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ChannelApplicationReferrerTransferLogService  extends IService<ChannelApplicationReferrerTransferLogEntity> {

    /**
     * 获取推荐人客户转移记录
     *
     * @param input 查询请求参数
     * @return 转移记录
     */
    List<ChannelApplicationReferrerTransferLogOut> findTransferLogList(ChannelApplicationReferrerLogVo input);


    /**
     * 添加记录户转移记录
     * @param input 请求参数
     * @return 添加结果
     */
    void saveChannelApplicationReferrerLog(SaveChannelApplicationReferrerLogVo input);
}
