package com.mpolicy.manage.modules.agent.vo.questionnaire;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AgentQuestionnaireAnswerVo {

    /**
     * bl_agent_questionnaire_question表主键id
     */
    @ApiModelProperty(value = "问卷id(前端无需赋值)")
    private Integer questionId;
    /**
     * 答案
     */
    private String answer;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "代理人姓名(前端无需赋值)")
    private Date createTime;

    @ApiModelProperty(value = "是否选中")
    private Integer isSelected;
}
