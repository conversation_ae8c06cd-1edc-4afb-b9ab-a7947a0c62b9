package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.BiGroupAgentEntrustPolicyDao;
import com.mpolicy.manage.modules.agent.entity.BiGroupAgentEntrustPolicyEntity;
import com.mpolicy.manage.modules.agent.service.BiGroupAgentEntrustPolicyService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("biGroupAgentEntrustPolicyService")
public class BiGroupAgentEntrustPolicyImpl extends ServiceImpl<BiGroupAgentEntrustPolicyDao, BiGroupAgentEntrustPolicyEntity> implements BiGroupAgentEntrustPolicyService {
}
