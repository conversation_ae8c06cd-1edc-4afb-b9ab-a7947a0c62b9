package com.mpolicy.manage.modules.policy.controller;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.validator.ValidatorUtils;
import com.mpolicy.manage.modules.policy.service.AiConversationService;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsQueryVo;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * AI对话记录管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("sys/policy/ai-conversation")
@Api(tags = "AI对话记录管理")
@Slf4j
public class AiConversationController {

    @Autowired
    private AiConversationService aiConversationService;

    /**
     * 查询AI聊天对话记录
     */
    @ApiOperation(value = "查询AI聊天对话记录", notes = "查询AI聊天对话记录")
    @PostMapping("/records")
    public Result<ConversationRecordsResponseVo> getConversationRecords(@RequestBody ConversationRecordsQueryVo queryVo) {
        log.info("查询AI对话记录，查询条件={}", JSON.toJSONString(queryVo));
        

        
        ConversationRecordsResponseVo result = aiConversationService.getConversationRecords(queryVo);
        
        return Result.success(result);
    }

    /**
     * 查询AI聊天对话记录 - GET方式（兼容URL参数调用）
     */
    @ApiOperation(value = "查询AI聊天对话记录(GET)", notes = "查询AI聊天对话记录(GET)")
    @GetMapping("/records")
    @RequiresPermissions(value = {"policy:ai:conversation:query"})
    public Result<ConversationRecordsResponseVo> getConversationRecordsGet(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime) {
        
        ConversationRecordsQueryVo queryVo = new ConversationRecordsQueryVo();
        queryVo.setPage(page);
        queryVo.setPageSize(pageSize);
        queryVo.setKeyword(keyword);
        queryVo.setUserName(userName);
        queryVo.setStartTime(startTime);
        queryVo.setEndTime(endTime);
        
        log.info("查询AI对话记录(GET)，查询条件={}", JSON.toJSONString(queryVo));
        
        
        
        ConversationRecordsResponseVo result = aiConversationService.getConversationRecords(queryVo);
        
        return Result.success(result);
    }
}
