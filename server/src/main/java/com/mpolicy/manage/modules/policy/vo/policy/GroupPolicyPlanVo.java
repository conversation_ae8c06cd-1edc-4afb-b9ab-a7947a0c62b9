package com.mpolicy.manage.modules.policy.vo.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("团险保单-计划信息")
public class GroupPolicyPlanVo {

    @ApiModelProperty("产品计划编码:关联佣金时通过该编码获取")
    private String planCode;

    @ApiModelProperty("产品计划名称")
    private String planName;

    @ApiModelProperty("投保计划人数(新契约投保人数)")
    private int planInsuredNum;

    @ApiModelProperty("保费计划列表")
    private List<PremiumPlanVo> premiumPlanList;
}
