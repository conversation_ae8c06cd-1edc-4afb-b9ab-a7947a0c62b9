package com.mpolicy.manage.modules.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.web.common.utils.Query;
import java.util.Map;

import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.order.dao.OrderBaseInfoDao;
import com.mpolicy.manage.modules.order.entity.OrderBaseInfoEntity;
import com.mpolicy.manage.modules.order.service.OrderBaseInfoService;


@Service("orderBaseInfoService")
public class OrderBaseInfoServiceImpl extends ServiceImpl<OrderBaseInfoDao, OrderBaseInfoEntity> implements OrderBaseInfoService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<OrderBaseInfoEntity> page = this.page(
                new Query<OrderBaseInfoEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
