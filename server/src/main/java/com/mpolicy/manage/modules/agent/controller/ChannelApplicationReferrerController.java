package com.mpolicy.manage.modules.agent.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelReferrerBindLogEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelReferrerBindLogService;
import com.mpolicy.manage.modules.agent.vo.ChannelApplicationReferrerRowVo;
import com.mpolicy.manage.modules.agent.vo.ChannelReferrerBindLogVo;
import com.mpolicy.manage.modules.agent.vo.customer.QueryReferrerCustomerVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.customer.vo.CustomerBasicInfoVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 推荐人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
@RestController
@RequestMapping("agent/channelapplicationreferrer")
@Api(tags = "推荐人信息")
public class ChannelApplicationReferrerController {

    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;
    @Autowired
    private ChannelReferrerBindLogService channelReferrerBindLogService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取推荐人信息列表", notes = "分页获取推荐人信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerWno", dataType = "String", value = "工号", example = "ZHNX0001"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerName", dataType = "String", value = "姓名", example = "张翠萍"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerMobile", dataType = "String", value = "手机号", example = "1234567890"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerRegion", dataType = "String", value = "所属区域", example = "referrerRegion:1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerServiceStatus", dataType = "String", value = "入离职状态", example = "0：在职 1:离职"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerCode", dataType = "String", value = "人员编码", example = "321321"),
            @ApiImplicitParam(paramType = "query", required = true, name = "handoverStatus", dataType = "String", value = "交接状态", example = "1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list/{applicationCode}")
    @RequiresPermissions(value = {"channel:zcjd:all","channel:zhnx:referrer:all"},logical = Logical.OR)
    public Result<PageUtils<ChannelApplicationReferrerEntity>> list(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode,
            @RequestParam Map<String, Object> params) {
        PageUtils<ChannelApplicationReferrerEntity> page = channelApplicationReferrerService.queryPage(applicationCode, params);
        return Result.success(page);
    }

    /**
     * 列表
     */
    @ApiOperation(value = "获取推荐人信息列表", notes = "分页获取推荐人信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerWno", dataType = "String", value = "工号", example = "ZHNX0001"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerName", dataType = "String", value = "姓名", example = "张翠萍"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerMobile", dataType = "String", value = "手机号", example = "1234567890"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerRegion", dataType = "String", value = "所属区域", example = "referrerRegion:1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export/{applicationCode}")
    @RequiresPermissions(value = {"channel:zcjd:all","channel:zhnx:referrer:all"},logical = Logical.OR)
    public Result export(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode,
            HttpServletResponse response,
            @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils<ChannelApplicationReferrerEntity> page = channelApplicationReferrerService.queryPage(applicationCode, params);
        AtomicInteger i = new AtomicInteger(1);
        List<ChannelApplicationReferrerRowVo> list = page.getList().stream().map(x -> {
            ChannelApplicationReferrerRowVo target = new ChannelApplicationReferrerRowVo();
            BeanUtils.copyProperties(x, target);
            target.setNo(i.getAndIncrement());
            target.setEnabled(StatusEnum.getNameByCode(x.getEnabled()).getName());
            return target;
        }).collect(Collectors.toList());

        ExcelUtil.writeExcel(response, list, "推荐人信息", "sheet1", new ChannelApplicationReferrerRowVo());
        return Result.success();
    }

    /**
     * 根据id查看详情
     */
    @ApiOperation(value = "根据id查看详情", notes = "根据id查看详情")
    @GetMapping("/info/{id}")
    public Result<ChannelApplicationReferrerEntity> info(@PathVariable("id") Integer id) {
        ChannelApplicationReferrerEntity channelApplicationReferrer = channelApplicationReferrerService.info(id);

        return Result.success(channelApplicationReferrer);
    }

    /**
     * 根据id查看详情
     */
    @ApiOperation(value = "根据id查看详情", notes = "根据id查看详情")
    @GetMapping("/referrerWno/{referrerWno}")
    public Result<JSONObject> referrerWno(@PathVariable("referrerWno") String referrerWno) {
        ChannelApplicationReferrerEntity channelApplicationReferrer = channelApplicationReferrerService
                .lambdaQuery()
                .eq(ChannelApplicationReferrerEntity::getReferrerWno, referrerWno).one();
        if (channelApplicationReferrer == null) {
            return Result.success();
        } else {
            JSONObject res = new JSONObject();
            StringBuffer tips = new StringBuffer(channelApplicationReferrer.getReferrerName())
                    .append(" ")
                    .append(channelApplicationReferrer.getReferrerWno());
            //区域
            if (StrUtil.isNotBlank(channelApplicationReferrer.getReferrerRegion())){
                tips.append(" ").append(DicCacheHelper.getValue(channelApplicationReferrer.getReferrerRegion()));
            }
            //片区
            if (StrUtil.isNotBlank(channelApplicationReferrer.getReferrerSubregion())){
                tips.append(" ").append(DicCacheHelper.getValue(channelApplicationReferrer.getReferrerSubregion()));
            }
            //分支
            if (StrUtil.isNotBlank(channelApplicationReferrer.getReferrerChannelName())){
                tips.append(" ").append(channelApplicationReferrer.getReferrerChannelName());
            }
            res.put("tips", tips.toString());
            res.put("referrerCode", channelApplicationReferrer.getReferrerCode());
            return Result.success(res);
        }

    }

    /**
     * 查看总数和生效数
     */
    @ApiOperation(value = "查看总数和生效数", notes = "查看总数和生效数")
    @GetMapping("/totalAndEffective/{applicationCode}")
    public Result<Map<String, Integer>> totalAndEffective(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode) {
        Map<String, Integer> total = channelApplicationReferrerService.getTotalAndEffective(applicationCode);

        return Result.success(total);
    }

    /**
     * 获取推荐人基础信息列表
     */
    @ApiOperation(value = "获取推荐人基础信息列表", notes = "获取推荐人基础信息列表")
    @GetMapping("/referrerList/{applicationCode}")
    public Result<List<ChannelApplicationReferrerVo>> referrerList(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode) {
        List<ChannelApplicationReferrerVo> list = channelApplicationReferrerService.getReferrerList(applicationCode);

        return Result.success(list);
    }

    /**
     * 获取推荐人基础信息根据分支编码
     */
    @ApiOperation(value = "获取推荐人基础信息根据分支编码", notes = "获取推荐人基础信息根据分支编码")
    @GetMapping("/getReferrerByBranchCodeList")
    public Result<List<ChannelApplicationReferrerVo>> getReferrerByBranchCodeList(
            @RequestParam(required = false) @ApiParam(name = "branchCode")
                    String branchCode) {
        List<ChannelApplicationReferrerVo> list = channelApplicationReferrerService.getReferrerByBranchCodeList(branchCode);

        return Result.success(list);
    }

    /**
     * 新增或修改
     */
    @NoRepeatSubmit(keyName = "token")
    @ApiOperation(value = "新增或修改推荐人信息", notes = "新增或修改推荐人信息")
    @PostMapping("/saveOrUpdate/{applicationCode}")
    public Result<ChannelApplicationReferrerEntity> saveOrUpdate(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode,
            @ApiParam(value = "渠道推荐人对象", required = true)
            @Validated @RequestBody ChannelApplicationReferrerEntity channelApplicationReferrerEntity) throws InterruptedException {
        // 设置应用编码
        channelApplicationReferrerEntity.setApplicationCode(applicationCode);

        boolean result = channelApplicationReferrerService.saveOrUpdateEntity(channelApplicationReferrerEntity);

        if (result) {
            channelApplicationReferrerEntity.setFilePath(DomainUtil.addOssDomainIfNotExist(channelApplicationReferrerEntity.getFilePath()));
            return Result.success(channelApplicationReferrerEntity);
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("操作失败"));
        }
    }

    /**
     * 修改推荐人启用状态
     *
     * @param id       推荐人id
     * @param enabled  状态
     * @param revision 版本号
     * @return 修改结果
     */
    @ApiOperation(value = "修改推荐人启用状态", notes = "修改推荐人启用状态")
    @PostMapping("/changeEnable/{id}")
    public Result changeEnable(
            @ApiParam(name = "id", value = "推荐人id", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(name = "enabled", value = "启用状态 0:禁用 1:启用 ", required = true)
            @RequestParam("enabled") Integer enabled,
            @ApiParam(name = "revision", value = "版本号", required = true)
            @RequestParam("revision") long revision) {
        boolean result = channelApplicationReferrerService.changeEnable(id, enabled, revision);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }

    /**
     * 发短信
     */
    @ApiOperation(value = "发短信", notes = "发短信")
    @GetMapping("/sendMessage/{id}")
    public Result sendMessage(@PathVariable("id") Integer id) {
        boolean result = channelApplicationReferrerService.sendMessage(id);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("短信发送失败"));
        }
    }

    /**
     * 查询推荐人名下客户
     * @param vo 查询条件
     */
    @ApiOperation(value = "查询推荐人名下客户", notes = "查询推荐人名下客户")
    @PostMapping("/queryCustomerByReferrerCode")
    public Result<List<CustomerBasicInfoVo>> queryCustomerByReferrerCode(@RequestBody @Valid QueryReferrerCustomerVo vo){
        ChannelApplicationReferrerEntity referrer = this.channelApplicationReferrerService.lambdaQuery().eq(ChannelApplicationReferrerEntity::getReferrerCode, vo.getReferrerCode()).one();
        if(Objects.isNull(referrer)){
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("推荐人信息不存在"));
        }
        List<CustomerBasicInfoEntity> list = this.customerBasicInfoService.lambdaQuery().eq(CustomerBasicInfoEntity::getCancelStatus, StatusEnum.INVALID.getCode())
                .eq(CustomerBasicInfoEntity::getReferrerCode, vo.getReferrerCode())
                .apply("COALESCE(inner_referrer_code, '') != {0}", vo.getReferrerCode())
                .and(StringUtils.isNotBlank(vo.getKey()),x->x.like(CustomerBasicInfoEntity::getMobile, vo.getKey())
                    .or()
                    .like(CustomerBasicInfoEntity::getIdentificationNum, vo.getKey()))
                .list();
        List<CustomerBasicInfoVo> collect = list.stream().map(a -> {
            CustomerBasicInfoVo bean = new CustomerBasicInfoVo();
            BeanUtils.copyProperties(a, bean);
            return bean;
        }).collect(Collectors.toList());
        return Result.success(collect);
    }

    /**
     * 客户经理绑定记录
     * @param customerCode 客户编码
     * @return
     */
    @ApiOperation(value = "查询客户客户经理绑定记录", notes = "查询客户客户经理绑定记录")
    @GetMapping("/bindList/{customerCode}")
    public Result<List<ChannelReferrerBindLogVo>> bindList(@PathVariable("customerCode") String customerCode) {
        List<ChannelReferrerBindLogEntity> list = channelReferrerBindLogService.lambdaQuery().eq(ChannelReferrerBindLogEntity::getCustomerCode, customerCode).list();
        List<ChannelReferrerBindLogVo> collect = list.stream().map(a -> {
            ChannelReferrerBindLogVo bean = new ChannelReferrerBindLogVo();
            BeanUtils.copyProperties(a, bean);
            return bean;
        }).collect(Collectors.toList());
        return Result.success(collect);
    }
}
