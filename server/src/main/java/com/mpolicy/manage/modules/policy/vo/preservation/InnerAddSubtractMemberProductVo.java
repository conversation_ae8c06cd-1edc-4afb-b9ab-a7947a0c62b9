package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("增减员-险种信息")
public class InnerAddSubtractMemberProductVo implements Serializable {

    @ApiModelProperty("产品中心-险种编码")
    private String productCode;

    @ApiModelProperty("产品中心-险种名称")
    private String productName;

    @ApiModelProperty("是否主险:0=否，1=是")
    private Integer mainInsurance;

    @ApiModelProperty("协议险种名称")
    private String protocolProductName;

    @ApiModelProperty("保单中心-险种编码")
    private String policyProductCode;

    @ApiModelProperty("小鲸-计划编码")
    private String planCode;

    @ApiModelProperty("小鲸-计划编码")
    private String planName;

    @ApiModelProperty("虚拟计划编码")
    private String virtualPlanCode;

    @ApiModelProperty("虚拟计划编码")
    private String virtualPlanName;

    @ApiModelProperty("承保保费")
    private BigDecimal premium;

    @ApiModelProperty("退保保费")
    private BigDecimal surrenderPremium;

    @ApiModelProperty("险种状态")
    private String productStatus;
}
