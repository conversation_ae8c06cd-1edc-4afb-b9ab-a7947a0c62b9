package com.mpolicy.manage.modules.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.alarm.dao.EpBusinessMonitorDao;
import com.mpolicy.manage.modules.alarm.entity.EpBusinessMonitorEntity;
import com.mpolicy.manage.modules.alarm.enums.EnumAlarm;
import com.mpolicy.manage.modules.alarm.service.EpBusinessMonitorService;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;

/**
 * 业务告警
 *
 * <AUTHOR>
 */
@Service
public class EpBusinessMonitorServiceImpl extends ServiceImpl<EpBusinessMonitorDao, EpBusinessMonitorEntity> implements EpBusinessMonitorService {

    public int saveOne(EnumAlarm alarm, String message) {
        EpBusinessMonitorEntity entity = new EpBusinessMonitorEntity();
        entity.setAlarmInfo(message);
        entity.setType(alarm.getType());
        return baseMapper.insert(entity);
    }

    @Override
    public int saveOne(EnumAlarm alarm, String pattern, Object... param) {
        String message = MessageFormat.format(pattern,param);
        EpBusinessMonitorEntity entity = new EpBusinessMonitorEntity();
        entity.setAlarmInfo(message);
        entity.setType(alarm.getType());
        return baseMapper.insert(entity);
    }
}
