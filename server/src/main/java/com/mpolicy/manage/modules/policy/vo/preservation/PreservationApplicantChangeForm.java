package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023/12/13 16:23
 * @description: 投保人保全变更
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PreservationApplicantChangeForm {

    @ApiModelProperty(value = "变更前投保人信息")
    EpPreservationApplicantInfoChangeVo startApplicantInfo;

    @ApiModelProperty(value = "变更后投保人信息")
    EpPreservationApplicantInfoChangeVo endApplicantInfo;
}
