package com.mpolicy.manage.modules.policy.vo.policy;

import lombok.Data;

import java.io.Serializable;

@Data
public class PolicyActivityChangeInfoOut implements Serializable {
    private static final long serialVersionUID = -4954298264758697785L;

    /**
     * 是否四级分销单
     */
    private Boolean fourPolicy;
    /**
     * 渠道推荐人姓名+工号
     */
    private String managerName;
    /**
     * 渠道推荐人证件号
     */
    private String managerIdNumber;
    /**
     * 渠道推荐人来源
     */
    private String managerSource;
    /**
     * 村代姓名
     */
    private String villageRepresentativeName;
    /**
     * 村代证件号
     */
    private String villageRepresentativeIdNumber;

    /**
     * 渠道分支编码
     */
    private String branchCode;

    /**
     * 渠道分支名称
     */
    private String branchName;

    /**
     * 销售渠道编码
     */
    private String channelCode;

    /**
     * 销售渠道名称
     */
    private String channelName;
}
