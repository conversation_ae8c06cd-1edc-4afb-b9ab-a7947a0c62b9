package com.mpolicy.manage.modules.agent.vo.agentinfo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class ImportCompanyAccountExcel implements Serializable {
    private static final long serialVersionUID = -362314652623766023L;


    @Alias("保司编码")
    private String companyCode;

    @Alias("代理人业务编码")
    private String businessCode;

    @<PERSON>as("保司账号")
    private String companyAccount;
}
