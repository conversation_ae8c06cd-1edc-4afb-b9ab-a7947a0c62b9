package com.mpolicy.manage.modules.label.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.label.entity.LabelInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelInfoListOut;
import com.mpolicy.manage.modules.label.entity.LabelInfoListVo;

/**
 * 标签信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
public interface LabelInfoService extends IService<LabelInfoEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<LabelInfoListOut> findPageList(LabelInfoListVo paramMap);
}

