package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.authorize.client.AuthorizeCenterClient;
import com.mpolicy.authorize.common.sms.SendMsgInput;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.enums.SmsCodeEnum;
import com.mpolicy.manage.modules.agent.dao.AgentUserAccountDao;
import com.mpolicy.manage.modules.agent.entity.AgentUserAccountEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserAccountService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;


@Service("agentUserAccountService")
public class AgentUserAccountServiceImpl extends ServiceImpl<AgentUserAccountDao, AgentUserAccountEntity> implements AgentUserAccountService {

    @Autowired
    AuthorizeCenterClient authorizeCenterClient;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentUserAccountEntity> page = this.page(
                new Query<AgentUserAccountEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void createOrUpdateAccount(String mobile, String password, String agentCode, String agentName, Integer type) {
        AgentUserAccountEntity entity = this.getOne(
                Wrappers.<AgentUserAccountEntity>lambdaQuery()
                        .eq(AgentUserAccountEntity::getAgentCode, agentCode)
                        .eq(AgentUserAccountEntity::getAccountType, type)
        );

        String salt = RandomUtil.randomString(4);
        if (entity == null) {
            // 账号不存在，创建账号
            entity = new AgentUserAccountEntity(mobile, type, salt, SecureUtil.md5("xj" + password + salt), agentCode, null, null);
            if (!this.save(entity)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("账号创建失败"));
            }

            // 进行短信发送
            SendMsgInput sendMsgInput = new SendMsgInput();
            sendMsgInput.setMobile(mobile);
            sendMsgInput.setSmsCode(SmsCodeEnum.NEW_AGENT.getCodeType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", agentName);
            sendMsgInput.setData(jsonObject);
            authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, sendMsgInput);

        } else if (!Objects.equals(entity.getAccount(), mobile)) {
            // 账号存在，手机号改变，修改手机号
            entity.setAccount(mobile);
            if (!this.update(entity,
                    Wrappers.<AgentUserAccountEntity>lambdaQuery()
                            .eq(AgentUserAccountEntity::getAgentCode, agentCode)
                            .eq(AgentUserAccountEntity::getAccountType, type))) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("账号修改失败"));
            }
        }

    }

}
