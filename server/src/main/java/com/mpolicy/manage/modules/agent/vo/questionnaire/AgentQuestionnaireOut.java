package com.mpolicy.manage.modules.agent.vo.questionnaire;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AgentQuestionnaireOut {

    //文件名称
    @ApiModelProperty(value = "代理人编号")
    private String title;

    @ApiModelProperty(value = "代理人编号")
    private Integer id;

    /**
     * 开始采集时间
     */
    @ApiModelProperty(value = "开始采集时间")
    private String investigateStartTime;
    /**
     * 结束采集时间
     */
    @ApiModelProperty(value = "结束采集时间")
    private String investigateEndTime;

    /**
     * 开始采集时间
     */
    @ApiModelProperty(value = "发布时间")
    private String publishTime;

    @ApiModelProperty(value = "应提交人数")
    private Integer needInvestigateCnt;

    @ApiModelProperty(value = "已提交人数")
    private Integer submitCnt;
}
