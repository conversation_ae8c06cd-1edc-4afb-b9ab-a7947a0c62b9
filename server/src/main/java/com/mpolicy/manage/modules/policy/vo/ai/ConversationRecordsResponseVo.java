package com.mpolicy.manage.modules.policy.vo.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * AI聊天对话记录响应结果
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ApiModel("AI聊天对话记录响应结果")
public class ConversationRecordsResponseVo {

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "当前页码")
    private Integer page;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "总页数")
    private Integer totalPages;

    @ApiModelProperty(value = "对话记录列表")
    private List<ConversationRecordVo> records;
}
