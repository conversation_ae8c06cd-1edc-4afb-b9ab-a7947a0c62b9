package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.dao.ChannelApplicationReferrerTransferLogDao;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerTransferCustomerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerTransferLogEntity;
import com.mpolicy.manage.modules.agent.enums.TransferLogTypeEnum;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerTransferCustomerService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerTransferLogService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerLogVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.ChannelApplicationReferrerTransferLogOut;
import com.mpolicy.manage.modules.agent.vo.agentinfo.SaveChannelApplicationReferrerLogVo;
import com.mpolicy.manage.modules.customer.entity.CustomerBasicInfoEntity;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("channelApplicationReferrerTransferLogService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelApplicationReferrerTransferLogServiceImpl extends ServiceImpl<ChannelApplicationReferrerTransferLogDao, ChannelApplicationReferrerTransferLogEntity> implements ChannelApplicationReferrerTransferLogService {


    private final CustomerBasicInfoService customerBasicInfoService;
    private final ChannelApplicationReferrerService channelApplicationReferrerService;
    private final ChannelApplicationReferrerTransferCustomerService channelApplicationReferrerTransferCustomerService;

    /**
     * 获取推荐人客户转移记录
     *
     * @param input 查询请求参数
     * @return 转移记录
     */
    @Override
    public List<ChannelApplicationReferrerTransferLogOut> findTransferLogList(ChannelApplicationReferrerLogVo input) {
        return lambdaQuery().eq(ChannelApplicationReferrerTransferLogEntity::getSourceReferrerCode, input.getReferrerCode())
                .or().eq(ChannelApplicationReferrerTransferLogEntity::getTargetReferrerCode, input.getReferrerCode())
                .groupBy(ChannelApplicationReferrerTransferLogEntity::getId)
                .orderByDesc(ChannelApplicationReferrerTransferLogEntity::getCreateTime)
                .list().stream().map(m -> {
                    ChannelApplicationReferrerTransferLogOut result = new ChannelApplicationReferrerTransferLogOut();
                    if (input.getReferrerCode().equals(m.getSourceReferrerCode())) {
                        result.setReferrerName(m.getTargetReferrerName());
                        result.setReferrerWno(m.getTargetReferrerWno());
                        result.setTransferDesc("转移给");
                    } else {
                        result.setReferrerWno(m.getSourceReferrerWno());
                        result.setReferrerName(m.getSourceReferrerName());
                        result.setTransferDesc("接收自");
                    }
                    result.setTransferTime(DateUtil.date(m.getCreateTime()).toDateStr());
                    result.setCustomerNum(m.getCustomerNum());
                    return result;
                }).collect(Collectors.toList());
    }


    /**
     * 添加记录户转移记录
     *
     * @param input 请求参数
     * @return 添加结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChannelApplicationReferrerLog(SaveChannelApplicationReferrerLogVo input) {

        ChannelApplicationReferrerEntity sourceReferrer = Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                .eq(ChannelApplicationReferrerEntity::getReferrerCode, input.getSourceReferrerCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("原推荐人信息不存在")));

        ChannelApplicationReferrerEntity targetReferrer = Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                .eq(ChannelApplicationReferrerEntity::getReferrerCode, input.getTargetReferrerCode())
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("目标推荐人信息不存在")));

        if(TransferLogTypeEnum.TYPE2.getType().equals(input.getType())&& CollectionUtils.isEmpty(input.getCustomerCodeList())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("移交客户不能为空"));
        }
        //获取推荐人客户数量
        List<CustomerBasicInfoEntity> list;
        if(TransferLogTypeEnum.TYPE1.getType().equals(input.getType())){
            list = customerBasicInfoService.lambdaQuery()
                    .eq(CustomerBasicInfoEntity::getReferrerCode, input.getSourceReferrerCode())
                    .eq(CustomerBasicInfoEntity::getCancelStatus, StatusEnum.INVALID.getCode())
                    .apply("COALESCE(inner_referrer_code, '') != {0}", input.getSourceReferrerCode())
                    .list();
        }else{
            list = customerBasicInfoService.lambdaQuery()
                    .in(CustomerBasicInfoEntity::getCustomerCode,input.getCustomerCodeList()).list();
        }

        ChannelApplicationReferrerTransferLogEntity channelApplicationReferrerTransferLog = new ChannelApplicationReferrerTransferLogEntity();
        BeanUtils.copyProperties(input, channelApplicationReferrerTransferLog);
        channelApplicationReferrerTransferLog.setCustomerNum(list.size());
        channelApplicationReferrerTransferLog.setSourceReferrerName(sourceReferrer.getReferrerName());
        channelApplicationReferrerTransferLog.setSourceReferrerWno(sourceReferrer.getReferrerWno());
        channelApplicationReferrerTransferLog.setTargetReferrerName(targetReferrer.getReferrerName());
        channelApplicationReferrerTransferLog.setTargetReferrerWno(targetReferrer.getReferrerWno());
        //添加记录数据
        save(channelApplicationReferrerTransferLog);
        //
        if (!list.isEmpty()) {
            List<ChannelApplicationReferrerTransferCustomerEntity> collect = list.stream().map(action -> {
                ChannelApplicationReferrerTransferCustomerEntity channelApplicationReferrerTransferCustomer = new ChannelApplicationReferrerTransferCustomerEntity();
                channelApplicationReferrerTransferCustomer.setCustomerCode(action.getCustomerCode());
                channelApplicationReferrerTransferCustomer.setLogId(channelApplicationReferrerTransferLog.getId());
                return channelApplicationReferrerTransferCustomer;
            }).collect(Collectors.toList());
            //批量插入数据
            channelApplicationReferrerTransferCustomerService.saveBatch(collect, 5000);
            // 更新客户推荐人
            customerBasicInfoService.lambdaUpdate()
                    .in(CustomerBasicInfoEntity::getCustomerCode, list.stream().map(m -> m.getCustomerCode()).distinct().collect(Collectors.toList()))
                    .set(CustomerBasicInfoEntity::getReferrerCode, input.getTargetReferrerCode())
                    .update();
        }
        if(input.getHandoverType().equals(StatusEnum.NORMAL.getCode())){
            this.customerBasicInfoService.lambdaUpdate().eq(CustomerBasicInfoEntity::getCancelStatus,StatusEnum.INVALID.getCode())
                    .eq(CustomerBasicInfoEntity::getInnerReferrerCode,sourceReferrer.getReferrerCode())
                    .set(CustomerBasicInfoEntity::getInnerReferrerFlag,StatusEnum.INVALID.getCode())
                    .set(CustomerBasicInfoEntity::getReferrerBindingTime, new Date())
                    .set(CustomerBasicInfoEntity::getInnerReferrerCode,null)
                    .set(CustomerBasicInfoEntity::getReferrerCode,targetReferrer.getReferrerCode()).update();
            this.channelApplicationReferrerService.lambdaUpdate().eq(ChannelApplicationReferrerEntity::getReferrerCode,sourceReferrer.getReferrerCode())
                    .set(ChannelApplicationReferrerEntity::getHandoverStatus,StatusEnum.NORMAL.getCode())
                    .set(ChannelApplicationReferrerEntity::getHandoverTime,new Date())
                    .set(ChannelApplicationReferrerEntity::getHandoverReferrerWno,targetReferrer.getReferrerWno()).update();
        }
    }
}
