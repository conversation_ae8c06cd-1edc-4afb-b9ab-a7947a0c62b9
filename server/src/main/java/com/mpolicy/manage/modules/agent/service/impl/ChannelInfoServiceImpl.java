package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.AdminCommonKeys;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.dao.ChannelInfoDao;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationService;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelApplicationVo;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.ChannelVo;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.web.common.utils.Query;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service("channelInfoService")
public class ChannelInfoServiceImpl extends ServiceImpl<ChannelInfoDao, ChannelInfoEntity> implements ChannelInfoService {
    @Autowired
    private ChannelApplicationService channelApplicationService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private IRedisService redisService;

    public static final String SHOW_CHANNEL_FLAG = "1";
    @Override
    public PageUtils<ChannelInfoEntity> queryPage(Map<String, Object> params) {
        Integer channelType = RequestUtils.objectValueToInteger(params, "channelType");
        Integer channelClassification = RequestUtils.objectValueToInteger(params, "channelClassification");
        String createTime = RequestUtils.objectValueToString(params, "createTime");
        String channelCode = RequestUtils.objectValueToString(params, "channelCode");
        String channelName = RequestUtils.objectValueToString(params, "channelName");

        // 分页查询
        IPage<ChannelInfoEntity> page = this.page(
                new Query<ChannelInfoEntity>().getPage(params),
                new QueryWrapper<ChannelInfoEntity>().lambda()
                        .eq(channelType != null, ChannelInfoEntity::getChannelType, channelType)
                        .apply(createTime != null, "date_format(create_time,'%Y-%m-%d') = {0}", createTime)
                        .eq(channelCode != null, ChannelInfoEntity::getChannelCode, channelCode)
                        .eq(channelClassification != null, ChannelInfoEntity::getChannelClassification, channelClassification)
                        .like(channelName != null, ChannelInfoEntity::getChannelName, channelName)
        );
        return new PageUtils<>(page);
    }

    @Override
    public boolean changeEnable(String code, Integer enabled, long revision) {
        if (StatusEnum.getNameByCode(enabled) == null) {
            return false;
        }
        ChannelInfoEntity channelInfoEntity = new ChannelInfoEntity();
        channelInfoEntity.setEnabled(enabled);
        channelInfoEntity.setRevision(revision);
        boolean update = this.update(channelInfoEntity, new QueryWrapper<ChannelInfoEntity>().lambda().eq(ChannelInfoEntity::getChannelCode, code));
        updateChannelAndApplicationCache(this.info(code));
        return update;
    }

    @Override
    public ChannelInfoEntity info(String code) {
        return this.getOne(Wrappers.<ChannelInfoEntity>lambdaQuery().eq(ChannelInfoEntity::getChannelCode, code));
    }

    @Override
    public boolean saveOrUpdateEntity(ChannelInfoEntity channelInfoEntity) {
        channelInfoEntity.setEnabled(null);
        ChannelInfoEntity one = this.info(channelInfoEntity.getChannelCode());
        List<ChannelInfoEntity> sameChannelNameList = list(Wrappers.<ChannelInfoEntity>lambdaQuery()
                .eq(ChannelInfoEntity::getChannelName, channelInfoEntity.getChannelName()));
        if (one != null && (sameChannelNameList.isEmpty() || (sameChannelNameList.size() == 1 && StringUtil.equals(channelInfoEntity.getChannelCode(), sameChannelNameList.get(0).getChannelCode())))) {
            // 修改渠道信息
            channelInfoEntity.setId(one.getId());
            if (!one.getChannelClassification().equals(channelInfoEntity.getChannelClassification())) {
                channelApplicationReferrerService.update(
                        new UpdateWrapper<ChannelApplicationReferrerEntity>().lambda()
                                .eq(ChannelApplicationReferrerEntity::getChannelCode, one.getChannelCode())
                                .set(ChannelApplicationReferrerEntity::getChannelClassification, channelInfoEntity.getChannelClassification())
                );
            }
            updateChannelAndApplicationCache(channelInfoEntity);
        } else if (one == null && sameChannelNameList.isEmpty()) {
            // 新增渠道信息
        } else {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("渠道名称已存在"));
        }

        return this.saveOrUpdate(channelInfoEntity);
    }


    @Override
    public List<ChannelInfoVo> getChannelList(boolean isNeedPermission, String showChannelFlag) {
        SysUserEntity userEntity = ShiroUtils.getUserEntity();

        List<String> channelCodeList = null;
        // 如果是需要权限并且不是全部的情况下才处理
        if (isNeedPermission && StatusEnum.NORMAL.getCode().equals(userEntity.getIsAllChannel())) {
            List<String> channelBranchPermissions = StrUtil.split(userEntity.getChannelCode(), ',');
            channelCodeList = channelBranchPermissions.stream().distinct().collect(Collectors.toList());
            if (channelCodeList.isEmpty()) {
                return new ArrayList<>();
            }
        }
        return this.lambdaQuery()
                .eq(!Objects.equals(showChannelFlag, SHOW_CHANNEL_FLAG), ChannelInfoEntity::getEnabled, 1)
                .in(channelCodeList != null, ChannelInfoEntity::getChannelCode, channelCodeList)
                .list().stream().map(item -> {
                    ChannelInfoVo vo = new ChannelInfoVo();
                    BeanUtils.copyProperties(item, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 更新缓存数据
     *
     * @param channelInfoEntity 渠道实体
     */
    private void updateChannelAndApplicationCache(ChannelInfoEntity channelInfoEntity) {
        // 处理渠道
        ChannelVo channelVo = new ChannelVo();

        BeanUtils.copyProperties(channelInfoEntity, channelVo);
        channelVo.setChannelEnabled(channelInfoEntity.getEnabled());

        redisService.hset(AdminCommonKeys.CHANNEL,
                Constant.CHANNEL_INFO,
                channelInfoEntity.getChannelCode(),
                channelVo);

        // 处理渠道应用
        channelApplicationService.list(
                Wrappers.<ChannelApplicationEntity>lambdaQuery()
                        .eq(ChannelApplicationEntity::getChannelCode, channelInfoEntity.getChannelCode())
        ).forEach(channelApplicationEntity -> {
            ChannelApplicationVo channelApplicationVo = new ChannelApplicationVo();

            // 1、处理渠道
            BeanUtils.copyProperties(channelInfoEntity, channelApplicationVo);
            channelApplicationVo.setChannelEnabled(channelInfoEntity.getEnabled());

            // 2、处理渠道应用
            channelApplicationVo.setApplicationCode(channelApplicationEntity.getApplicationCode());
            channelApplicationVo.setApplicationEnabled(channelApplicationEntity.getEnabled());
            channelApplicationVo.setAgentSelectType(channelApplicationEntity.getAgentSelectType());
            channelApplicationVo.setAgentInfoVoList(channelApplicationEntity.getAgentInfoVoList());

            // 3、写入缓存
            redisService.hset(AdminCommonKeys.CHANNEL_APPLICATION,
                    Constant.APPLICATION_INFO,
                    channelApplicationEntity.getApplicationCode(),
                    channelApplicationVo);
        });

    }

}
