package com.mpolicy.manage.modules.policy.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保全团队增减员信息
 * 
 * <AUTHOR>
 * @date 2022-04-06 14:06:32
 */
@TableName("ep_preservation_team_people")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreservationTeamPeopleEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;

	@TableField("insured_code")
	private String insuredCode;
	/**
	 * 保全流水号
	 */
	private String preservationCode;
	/**
	 * 保单中心保单唯一号
	 */
	private String contractCode;
	/**
	 * 保单号
	 */
	private String policyCode;
	/**
	 * 序号
	 */
	private String peopleNumber;
	/**
	 * 增减员类型 加人/减人
	 */
	private String peopleType;

	/**
	 * 主被保人标识
	 */
	private String mainInsuredFlag;

	/**
	 * 对应主被保人
	 */
	private String mainInsuredName;

	/**
	 * 被保人姓名
	 */
	private String insuredName;
	/**
	 * 与主被保人关系
	 */
	private String firstInsuredRelation;
	/**
	 * 被保人性别
	 */
	private String insuredGender;
	/**
	 * 被保人出生日期
	 */
	private String insuredBirthday;

	@ApiModelProperty(value = "被保人保单生效时年龄")
	private Integer insuredPolicyAge;

	/**
	 * 被保人证件类型
	 */
	private String insuredIdType;
	/**
	 * 被保人证件号码
	 */
	private String insuredIdCard;
	/**
	 * 证件有效期
	 */
	private String insuredIdCardValidityEnd;
	/**
	 * 国籍
	 */
	private String insuredNation;

	private String insuredRelation;
	/**
	 * 地址
	 */
	private String insuredAddress;
	/**
	 * 保险险种编码（废弃）
	 */
	private String productCode;
	/**
	 * 小鲸-计划编码
	 */
	private String planCode;

	/**
	 * 小鲸-计划编码
	 */
	private String planName;

	@ApiModelProperty("虚拟计划编码")
	private String virtualPlanCode;

	@ApiModelProperty("虚拟计划")
	private String virtualPlanName;
	/**
	 * 保费
	 */
	private BigDecimal singlePremium;
	/**
	 * 职业代码
	 */
	private String insuredCareer;
	/**
	 * 职业类别
	 */
	private String insuredOccupationalCategory;
	/**
	 * 主被保人证件号码
	 */
	private String mainInsuredIdCard;
	/**
	 * 联系方式
	 */
	private String insuredMobile;
	/**
	 * 电子邮箱
	 */
	private String insuredEmail;
	/**
	 * 工作单位
	 */
	private String insuredCompany;

	@TableField("referrer_code")
	private String referrerCode;

	@TableField("channel_referrer_code")
	private String channelReferrerCode;

	@TableField("channel_branch_code")
	private String channelBranchCode;

	@TableField("channel_code")
	private String channelCode;

	@TableField("org_code")
	private String orgCode;

	@TableField("product_info")
	private String productInfo;

	/**
	 * 客户经理
	 */
	private String customerManagerCode;

	/**
	 * 客户经理渠道编码
	 */
	private String customerManagerChannelCode;
	/**
	 * 客户经理所属分支机构
	 */
	private String customerManagerOrgCode;
	/**
	 * 客户经理渠道机构编码
	 */
	private String customerManagerChannelOrgCode;
	/**
	 * 客户经理督导
	 */
	private String customerManagerSupervisor;


	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
