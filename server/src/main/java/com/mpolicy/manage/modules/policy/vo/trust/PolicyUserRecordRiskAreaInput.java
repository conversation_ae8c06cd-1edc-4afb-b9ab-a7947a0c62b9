package com.mpolicy.manage.modules.policy.vo.trust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * ClassName: PolicyUserRecordRiskAreaInput
 * Description: 险种配置地区信息
 * date: 2023/11/10 14:38
 *
 * <AUTHOR>
 */
@ApiModel(value = "险种配置地区信息")
@Data
public class PolicyUserRecordRiskAreaInput {

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码", example = "110000", required = true)
    @NotBlank(message = "省份编码不能为空")
    private String provincesCode;
    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称", example = "北京市", required = true)
    @NotBlank(message = "省份名称不能为空")
    private String provincesName;
    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码", example = "110100", required = true)
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", example = "市辖区", required = true)
    @NotBlank(message = "城市名称不能为空")
    private String cityName;
    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码", example = "110101", required = true)
    @NotBlank(message = "区编码不能为空")
    private String areaCode;
    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称", example = "东城区", required = true)
    @NotBlank(message = "区名称不能为空")
    private String areaName;
}
