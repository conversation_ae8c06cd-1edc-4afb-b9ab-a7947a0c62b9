package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 险种--险种大类枚举类
 */
public enum ProductGroupEnum {

    /**
     * 险种大类
     */
    GRX("PRODUCT:PRODUCTGROUP:1", "个人险"),
    CX("PRODUCT:PRODUCTGROUP:2", "财险"),
    TX("PRODUCT:PRODUCTGROUP:4", "团险");

    @Getter
    private String groupCode;

    @Getter
    private String groupName;


    ProductGroupEnum(String groupCode, String groupName) {
        this.groupCode = groupCode;
        this.groupName = groupName;
    }

    public static ProductGroupEnum getGroupCodeEnum(String groupCode) {
        return Arrays.stream(ProductGroupEnum.values())
                .filter(x -> x.groupCode.equals(groupCode))
                .findFirst().orElse(null);
    }
}
