package com.mpolicy.manage.modules.label.controller;

import cn.hutool.core.bean.BeanUtil;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoListOut;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoSaveVo;
import com.mpolicy.manage.modules.label.entity.LabelLibraryInfoUpdateVo;
import com.mpolicy.manage.modules.label.service.LabelLibraryInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 标签库信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
@Api(tags = "标签库信息")
@RestController
@RequestMapping("label/library/info")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LabelLibraryInfoController {


    private final LabelLibraryInfoService labelLibraryInfoService;


    @ApiOperation(value = "获取标签库信息列表", notes = "获取标签库信息列表不分页")
    @GetMapping("list")
    public Result<List<LabelLibraryInfoListOut>> list() {
        List<LabelLibraryInfoListOut> list = labelLibraryInfoService.findLabelLibraryInfoList();
        return Result.success(list);
    }

    @ApiOperation(value = "新增标签库信息", notes = "新增标签库信息")
    @PostMapping("save")
    @RequiresPermissions("label:library:save")
    public Result<LabelLibraryInfoEntity> save(@RequestBody @Valid LabelLibraryInfoSaveVo vo) {
        LabelLibraryInfoEntity labelInfo = LabelLibraryInfoEntity.builder()
                .libraryCode(CommonUtils.createCodeLastNumber("LBB"))
                .build();
        BeanUtil.copyProperties(vo, labelInfo);
        labelLibraryInfoService.save(labelInfo);
        return Result.success(labelInfo);
    }

    @ApiOperation(value = "修改标签库信息", notes = "修改标签库信息")
    @PostMapping("update")
    @RequiresPermissions("label:library:update")
    public Result<LabelLibraryInfoEntity> update(@RequestBody @Valid LabelLibraryInfoUpdateVo vo) {
        LabelLibraryInfoEntity labelInfo = new LabelLibraryInfoEntity();
        BeanUtil.copyProperties(vo, labelInfo);
        labelLibraryInfoService.updateById(labelInfo);
        return Result.success(labelInfo);
    }
}
