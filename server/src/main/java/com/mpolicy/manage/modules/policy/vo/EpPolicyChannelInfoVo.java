package com.mpolicy.manage.modules.policy.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保单渠道信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/14 16:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "保单渠道信息", description = "保单渠道信息")
public class EpPolicyChannelInfoVo {
    /**
     * 渠道推荐人类型 0:推荐人 1:代理人
     */
    @ApiModelProperty(value = "渠道推荐人类型 0:推荐人 1:代理人", example = "1")
    private Integer referrerType;
    /**
     * 渠道推荐人工号
     */
    @ApiModelProperty(value = "渠道推荐人工号")
    private String referrerWno;
    /**
     * 保单推荐人编码(代理人)
     */
    @ApiModelProperty(value = "推荐人编码(代理人)", example = "ag20210712172633ePz9wj")
    private String policyReferrerCode;
    /**
     * 保单推荐人姓名(代理人)
     */
    @ApiModelProperty(value = "推荐人姓名(代理人)", example = "张三")
    private String policyReferrerName;
    /**
     * 保单推荐人工号(代理人)
     */
    @ApiModelProperty(value = "推荐人工号(代理人)", example = "XJ88888")
    private String policyReferrerBusinessCode;
    /**
     * 渠道推荐人编码
     */
    @ApiModelProperty(value = "渠道推荐人编码", example = "ag20210712172633ePz9wj")
    private String referrerCode;
    /**
     * 渠道推荐人姓名
     */
    @ApiModelProperty(value = "渠道推荐人姓名")
    private String referrerName;
    /**
     * 渠道推荐人分支编码
     */
    @ApiModelProperty(value = "渠道推荐人分支编码", example = "OR20210222103029tIBLey")
    private String channelBranchCode;
    /**
     * 渠道分支名字
     */
    @ApiModelProperty(value = "渠道分支名字")
    private String branchName;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码", example = "free")
    private String channelCode;
    /**
     * 渠道名字
     */
    @ApiModelProperty(value = "渠道名字")
    private String channelName;
    /**
     * 离职状态0:在职 1:离职
     */
    @ApiModelProperty(value = "离职状态0:在职 1:离职")
    private Integer quitStatus;

}
