package com.mpolicy.manage.modules.sell.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 续购配置推荐产品类型
 *
 * <AUTHOR>
 * @date 2023-9-20
 */
@Data
@ApiModel(value = "续购配置推荐产品类型")
public class RenewalProductRecommendType implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 推荐险种类型
     */
    @ApiModelProperty(value = "推荐险种类型", example = "PRODUCT:PROTECTION_TYPE:LQ")
    private String productType;

    /**
     * 推荐险种类型名称
     */
    @ApiModelProperty(value = "推荐险种类型名称", example = "两全")
    private String productTypeName;

    /**
     * 推荐险种列表
     */
    @ApiModelProperty(value = "推荐险种列表")
    private List<RenewalProductRecommendInfo> recommendInfos;

}
