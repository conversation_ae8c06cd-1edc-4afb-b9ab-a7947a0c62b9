package com.mpolicy.manage.modules.policy.vo.policy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInsuredInfoVo;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefProductInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 保单合同
 */
@Data
@ApiModel("保单合同信息")
public class FastPolicyContractInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "保单合同编码", example = "保单合同编码")
    private String contractCode;

    @ApiModelProperty(value = "保单号", example = "CS0001")
    private String policyNo;

    @ApiModelProperty(value = "第三方流水号(订单号)", example = "CS0001")
    private String serialNumber;

    @ApiModelProperty(value = "当前续期期数", example = "1")
    private Integer renewalTermPeriod;

    @ApiModelProperty(value = "保单主险类型", example = "PRODUCT:PRODUCTGROUP:1")
    private String policyProductType;

    @ApiModelProperty(value = "保单类型 0:预收件；1:承保件", example = "0")
    private Integer policyType;

    @ApiModelProperty(value = "组合名称", example = "众安财险尊享e生全家桶（年缴版）")
    private String portfolioName;

    @ApiModelProperty(value = "产品编码", example = "XJ900001")
    private String commodityCode;

    @ApiModelProperty(value = "保险公司编码")
    private String companyCode;

    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    @ApiModelProperty(value = "首期保费")
    private BigDecimal premiumTotal;

    @ApiModelProperty(value = "保单状态", example = "POLICY:ADVANCE_STATUS:1")
    private String policyStatus;

    @ApiModelProperty(value = "保单状态(中文)", example = "承保")
    private String policyStatusStr;

    @ApiModelProperty(value = "管理后台保单状态", example = "POLICY:ADVANCE_STATUS:1")
    private String adminPolicyStatus;

    @ApiModelProperty(value = "管理后台保单状态(中文)", example = "承保")
    private String adminPolicyStatusStr;

    @ApiModelProperty(value = "生效时间", example = "2021-01-01")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enforceTime;

    @ApiModelProperty(value = "失效时间", example = "2021-01-01")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date insuredPeriodEndTime;

    @ApiModelProperty(value = "销售渠道编码")
    private String channelCode;
    @ApiModelProperty(value = "销售渠道名称")
    private String channelName;

    @ApiModelProperty(value = "分支编码")
    private String branchCode;

    @ApiModelProperty(value = "渠道分支编码 权限")
    private String channelBranchCode;

    @ApiModelProperty(value = "分销渠道编码 权限",example = "XJXH001")
    private String channelDistributionCode;

    @ApiModelProperty(value = "管理机构编码")
    private String orgCode;

    @ApiModelProperty(value = "管理机构名称")
    private String orgName;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "保单推荐人编码(或代理人)")
    private String policyReferrerCode;

    @ApiModelProperty(value = "保单推荐人姓名")
    private String policyReferrerName;

    @ApiModelProperty(value = "渠道推荐人编码")
    private String referrerCode;

    @ApiModelProperty(value = "渠道推荐人姓名")
    private String referrerName;
    /**
     * 投保主体名称
     */
    @ApiModelProperty(value = "投保主体名称", example = "张三")
    private String applicantName;
    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "投保人证件号")
    private String applicantIdCard;

    @ApiModelProperty(value = "投保人证件类型")
    private String applicantIdType;

    @ApiModelProperty(value = "投保人类型")
    private Integer applicantType;

    @ApiModelProperty(value = "投保人手机号")
    private String applicantMobile;

    @ApiModelProperty(value = "展示模式 0:托管单;1:普通单")
    private Integer showModel;

    @ApiModelProperty(value = "被保人列表")
    private List<EpContractBriefInsuredInfoVo> insuredList;

    @ApiModelProperty(value = "险种列表")
    private List<EpContractBriefProductInfoVo> productList;

    @ApiModelProperty(value = "续保保单号")
    private String sourcePolicyNo;

    @ApiModelProperty(value = "保司保单号")
    private String thirdPolicyNo;

    @ApiModelProperty(value = "续保期数")
    private Integer renewalPeriod;

    @ApiModelProperty(value = "是否暂存 0,1:已提交;2:暂存")
    private Integer intact;

    @ApiModelProperty(value = "客户经理(初始渠道推荐人编码)")
    private String customerManagerCode;

    @ApiModelProperty(value = "客户经理客户经理(初始渠道推荐人名字")
    private String customerManagerName;

    @ApiModelProperty(value = "客户经理渠道工号(初始渠道推荐人工号)")
    private String customerManagerChannelCode;

    @ApiModelProperty(value = "客户经理所属分支机构(初始渠道所属机构编码)")
    private String customerManagerOrgCode;

    @ApiModelProperty(value = "客户经理所属分支机构(初始渠道所属机构名称)")
    private String customerManagerOrgName;

    @ApiModelProperty(value = "客户经理渠道机构编码(初始渠道所属机构编码(农保))")
    private String customerManagerChannelOrgCode;

    @ApiModelProperty(value = "客户经理督导")
    private String customerManagerSupervisor;

    @ApiModelProperty(value = "交单时间")
    private Date orderTime;

}
