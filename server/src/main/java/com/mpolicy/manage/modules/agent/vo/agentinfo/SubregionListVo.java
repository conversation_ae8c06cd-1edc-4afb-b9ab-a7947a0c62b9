package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SubregionListVo implements Serializable {
    private static final long serialVersionUID = -1167911447691450394L;

    @ApiModelProperty(value = "代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "区域编码")
    private List<String> regionCodeList;
}
