package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.web.common.utils.Query;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.agent.dao.OrgInfoAccessoryDao;
import com.mpolicy.manage.modules.agent.entity.OrgInfoAccessoryEntity;
import com.mpolicy.manage.modules.agent.service.OrgInfoAccessoryService;
import org.springframework.transaction.annotation.Transactional;


@Service("orgInfoAccessoryService")
public class OrgInfoAccessoryServiceImpl extends ServiceImpl<OrgInfoAccessoryDao, OrgInfoAccessoryEntity> implements OrgInfoAccessoryService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<OrgInfoAccessoryEntity> page = this.page(
                new Query<OrgInfoAccessoryEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveEntity(String orgCode, List<OrgInfoAccessoryEntity> orgInfoAccessoryEntityList) {
        if (orgInfoAccessoryEntityList != null && orgInfoAccessoryEntityList.size() > 0) {
            List<OrgInfoAccessoryEntity> collect = orgInfoAccessoryEntityList.stream().peek(accessory -> {
                accessory.setOrgCode(orgCode);
                accessory.setFilePath(DomainUtil.removeDomain(accessory.getFilePath()));
            }).collect(Collectors.toList());
            baseMapper.insertBatchSomeColumn(collect);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEntity(String orgCode, List<OrgInfoAccessoryEntity> orgInfoAccessoryEntityList) {
        this.remove(
                Wrappers.<OrgInfoAccessoryEntity>lambdaQuery().eq(OrgInfoAccessoryEntity::getOrgCode, orgCode)
        );
        saveEntity(orgCode, orgInfoAccessoryEntityList);
    }
}
