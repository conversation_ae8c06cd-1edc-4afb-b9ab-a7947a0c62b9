package com.mpolicy.manage.modules.insurance.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EpProtocolInsuranceProductDetail implements Serializable {
    private static final long serialVersionUID = 5776195975357324359L;

    private Integer insuranceProductId;
    private String insuranceProductCode;
    private String insuranceProductName;
    private String companyCode;
    private List<String> companyCodeArr;
    private String companyName;
    private String orgCode;
    private List<String> orgCodeArr;
    private String orgName;
    private Date beginTime;
    private Date endTime;
    private Integer isNationwide;
}
