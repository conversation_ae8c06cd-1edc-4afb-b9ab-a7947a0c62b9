package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 分支机构信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-02 17:48:12
 */
@TableName("channel_branch_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelBranchInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Integer id;
    /**
     * 分支编码
     */
    @ApiModelProperty(value = "分支编码")
    private String branchCode;
    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称")
    @NotBlank(message = "分支名称不能为空")
    private String branchName;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    private String channelCode;
    /**
     * 上级分支编码
     */
    @ApiModelProperty(value = "上级分支编码")
    private String branchParentCode;
    /**
     * 分支地址
     */
    @ApiModelProperty(value = "分支地址")
    private String branchLocation;
    /**
     * 分支层级
     */
    @ApiModelProperty(value = "分支层级", hidden = true)
    private Integer branchLevel;
    /**
     * 是否为空父节点
     */
    @ApiModelProperty(value = "是否为非空父节点")
    private Boolean isNotEmpty;
    /**
     * 启用状态 1:启用;0:关闭
     */
    @ApiModelProperty(value = "启用状态 1:启用;0:关闭")
    private Integer enabled;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁 更新时必传")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

}
