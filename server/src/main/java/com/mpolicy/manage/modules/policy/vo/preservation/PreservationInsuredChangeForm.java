package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保单保费变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单保费变更-被保人变更明细")
public class PreservationInsuredChangeForm implements Serializable {

    @NotBlank(message = "被保人编号不能为空")
    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("变更前信息")
    private PreservationInsuredBaseInfo before;

    @ApiModelProperty("变更后信息")
    private PreservationInsuredBaseInfo after;
}
