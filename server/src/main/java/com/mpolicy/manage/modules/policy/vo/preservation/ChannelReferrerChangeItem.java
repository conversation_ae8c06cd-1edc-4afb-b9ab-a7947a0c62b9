package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ChannelReferrerChangeItem {

    @ApiModelProperty(value = "被保人编号", required = true)
    private String insuredCode;

    @ApiModelProperty("被保人编号")
    private String insuredName;

    @ApiModelProperty(value = "被保人证件号", required = true)
    private String insuredIdCard;

    @ApiModelProperty(value = "被保人证件类型", required = true)
    private String insuredIdType;

    @ApiModelProperty("生效时间")
    private Date insuredEffectiveTime;

    @ApiModelProperty("计划编码")
    private String productCode;

    @ApiModelProperty("计划名称")
    private String productName;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty(value = "渠道推荐人编码",required = true)
    private String channelReferrerCode;

    @ApiModelProperty("渠道推荐人名字(可选)")
    private String channelReferrerName;

    @ApiModelProperty(value = "渠道分支编码",required = true)
    private String channelBranchCode;

    @ApiModelProperty(value = "初始推荐人编码",required = true)
    private String customerManagerCode;

    @ApiModelProperty(value = "初始推荐人名字",required = true)
    private String customerManagerName;

    @ApiModelProperty(value = "初始推荐人机构编码",required = true)
    private String customerManagerOrgCode;

    @ApiModelProperty(value = "初始推荐人机构编码",required = true)
    private String customerManagerOrgName;

    @ApiModelProperty("分支名称(可选)")
    private String branchName;

    @ApiModelProperty(value = "销售渠道",required = true)
    private String channelCode;

    @ApiModelProperty("销售渠道名称")
    private String channelName;
}
