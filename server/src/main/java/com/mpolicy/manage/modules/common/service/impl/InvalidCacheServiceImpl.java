package com.mpolicy.manage.modules.common.service.impl;

import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.manage.common.AgentKeys;
import com.mpolicy.manage.common.SellProductKeys;
import com.mpolicy.manage.enums.InvalidCacheEnum;
import com.mpolicy.manage.modules.common.service.IInvalidCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 缓存失效处理
 *
 * @program: ims-agent
 * @description:
 * @author: lsc
 * @created: 2022/06/23 11:01
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvalidCacheServiceImpl implements IInvalidCacheService {

    private final IRedisService redisService;


    /**
     * 设置缓存失效
     *
     * @param invalidCache
     * @param key
     */
    @Override
    public void invalidCache(InvalidCacheEnum invalidCache, String key) {
        switch (invalidCache) {
            case AGENT_INFO: {
                // 代理人信息
                redisService.delete(AgentKeys.AGENT_BASICS_INFO, key);
                redisService.delete(AgentKeys.AGENT_DETAILS_INFO, key);
                break;
            }
            case SELL_PRODUCT_INFO: {
                // 清空售卖商品信息
                redisService.delete(SellProductKeys.SELL_PRODUCT_BASIC, key);
                redisService.delete(SellProductKeys.SELL_PRODUCT_DETAILS, key);
                redisService.delete(SellProductKeys.SELL_PRODUCT_INFO, key);
                break;
            }
            default: {
                log.warn("未配置缓存刷新模块，modelEnum={}", invalidCache.getCacheCode());
            }
        }
    }

    @Override
    public void invalidCache(InvalidCacheEnum invalidCache) {

    }
}
