package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.sell.entity.SellProductUpdateStatusLogEntity;
import com.mpolicy.manage.modules.sell.vo.SellProductUpdateStatusLogVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 商品上下级日志记录表
 *
 *
 * @create 2024/11/20
 * @since 1.0.0
 */
public interface SellProductUpdateStatusLogService extends IService<SellProductUpdateStatusLogEntity> {

    /**
     *
     *
     * 根据商品编码获取商品状态更变记录的VO
     *
     *
     * @param productCode
     *
     * 商品编码
     *
     * @return
     */
    List<SellProductUpdateStatusLogVo> getListVOByProductCode(String productCode);


    /**
     *
     *
     * 根据商品编码获取商品状态更变记录
     *
     *
     * @param productCode
     *
     * 商品编码
     *
     * @return
     */
    List<SellProductUpdateStatusLogEntity> getListProductCode(String productCode);
}
