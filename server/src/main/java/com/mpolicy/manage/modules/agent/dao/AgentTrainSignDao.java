package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.agent.entity.AgentTrainSignEntity;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListVo;
import com.mpolicy.manage.modules.agent.vo.train.TrainSignListOut;
import com.mpolicy.manage.modules.agent.vo.train.UpdateTrainSignListVo;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理人培训签到表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
public interface AgentTrainSignDao extends ImsBaseMapper<AgentTrainSignEntity> {


    /**
     * 获取培训签到列表
     *
     * @param trainCode
     * @return
     */
    List<TrainSignListOut> findTrainSignList(String trainCode);

    /**
     * 更新培训签到数据
     *
     * @param list
     */
    void beachUpdateTrainSignList(@Param("list") List<UpdateTrainSignListVo> list);

    /**
     * 批量新增签到人员
     *
     * @param trainCode
     * @param participants
     */
    void beachInsertTrainSign(@Param("trainCode") String trainCode, @Param("participants") List<String> participants);


    /**
     * 获取代理人培训记录
     * @param page
     * @param params
     * @return
     */
    IPage<AgentTrainInfoListOut> findPageList(@Param("page") Page<AgentTrainInfoListVo> page, @Param("params") AgentTrainInfoListVo params);
}
