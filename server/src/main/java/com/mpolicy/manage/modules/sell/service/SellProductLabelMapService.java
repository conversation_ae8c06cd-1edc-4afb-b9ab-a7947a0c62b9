package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.SellProductLabelMapEntity;

import java.util.Map;

/**
 * 售卖商品标签
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-22 12:02:26
 */
public interface SellProductLabelMapService extends IService<SellProductLabelMapEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

