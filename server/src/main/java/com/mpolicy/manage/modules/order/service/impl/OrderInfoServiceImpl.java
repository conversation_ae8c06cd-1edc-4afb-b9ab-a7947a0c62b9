package com.mpolicy.manage.modules.order.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.modules.order.dao.OrderBaseInfoDao;
import com.mpolicy.manage.modules.order.entity.TraceSerialRecallDataEntity;
import com.mpolicy.manage.modules.order.service.OrderApplicantInfoService;
import com.mpolicy.manage.modules.order.service.OrderBaseInfoService;
import com.mpolicy.manage.modules.order.service.OrderInfoService;
import com.mpolicy.manage.modules.order.service.TraceSerialRecallDataService;
import com.mpolicy.manage.modules.order.vo.RecallList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service("orderinfoservice")
public class OrderInfoServiceImpl implements OrderInfoService {

    @Autowired
    private OrderBaseInfoService orderBaseInfoService;

    @Autowired
    private OrderApplicantInfoService orderApplicantInfoService;

    @Autowired
    private TraceSerialRecallDataService traceSerialRecallDataService;

    @Autowired
    private OrderBaseInfoDao orderBaseInfoDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
       long page= Long.valueOf((String) params.get("page"));
       long limit=  Long.valueOf((String)params.get("limit"));
        RecallList recallList = new RecallList();
        recallList.setOrderCode((String) params.get("orderCode"));
        recallList.setOrderStatus((String) params.get("orderStatus"));
        recallList.setPolicyNO((String) params.get("policyNO"));
        recallList.setApplicantName((String)params.get("applicantName"));
        recallList.setApplicantTime((String) params.get("applicantTime"));
        recallList.setCompanyName( (String) params.get("companyName"));
        recallList.setProductName((String) params.get("productName"));
        recallList.setApplicantIdCard((String) params.get("applicantIdCard"));
        IPage<RecallList> pageList = orderBaseInfoDao.findPageList(new Page<RecallList>(page,limit), recallList);
        return new PageUtils(pageList);
    }

    @Override
    public List backPicture(String orderCode) {
        List list = new ArrayList();
        List<TraceSerialRecallDataEntity> traceSerialRecallDataEntityList = traceSerialRecallDataService.lambdaQuery().eq(TraceSerialRecallDataEntity::getTraceSerialId, orderCode).list();
        traceSerialRecallDataEntityList.forEach(x->{
            list.add(DomainUtil.addOssDomainIfNotExist(x.getShotImgPath()));
        });
        return list;
    }
}
