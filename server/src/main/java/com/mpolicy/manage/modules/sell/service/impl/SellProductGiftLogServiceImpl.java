package com.mpolicy.manage.modules.sell.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.sell.dao.SellProductGiftLogDao;
import com.mpolicy.manage.modules.sell.entity.SellProductGiftLogEntity;
import com.mpolicy.manage.modules.sell.enums.SellProductGiftLogTypeEnum;
import com.mpolicy.manage.modules.sell.service.SellProductGiftLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 销售产品的赠品管理信息service的实现类
 *
 *
 * @create 2024/12/2
 * @since 1.0.0
 */
@Slf4j
@Service("sellProductGiftLogService")
public class SellProductGiftLogServiceImpl extends ServiceImpl<SellProductGiftLogDao, SellProductGiftLogEntity> implements SellProductGiftLogService {
    @Override
    public Integer saveForLogType(SellProductGiftLogTypeEnum sellProductGiftLogTypeEnum, List<SellProductGiftLogEntity> sellProductGiftLogEntityList) {

        if (Objects.isNull(sellProductGiftLogTypeEnum)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("日志类型不能为空"));
        }

        for (SellProductGiftLogEntity sellProductGiftLogEntity : sellProductGiftLogEntityList) {
            sellProductGiftLogEntity.setLogType(sellProductGiftLogTypeEnum.getCode());
        }
        this.saveBatch(sellProductGiftLogEntityList);
        return sellProductGiftLogEntityList.size();
    }

    @Override
    public void saveForLogType(SellProductGiftLogTypeEnum sellProductGiftLogTypeEnum, SellProductGiftLogEntity sellProductGiftLogEntity) {

        if (Objects.isNull(sellProductGiftLogTypeEnum)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("日志类型不能为空"));
        }

        sellProductGiftLogEntity.setLogType(sellProductGiftLogTypeEnum.getCode());

        this.save(sellProductGiftLogEntity);
    }
}
