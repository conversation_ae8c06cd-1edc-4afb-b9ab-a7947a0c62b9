package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentAccessoryEntity;
import com.mpolicy.manage.modules.agent.vo.resp.AgentAccessoryRespVo;

import java.util.List;
import java.util.Map;

/**
 * 经纪人人员附件
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:48
 */
public interface AgentAccessoryService extends IService<AgentAccessoryEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveOrUpdateAgentAccessoryList(String agentCode, List<AgentAccessoryEntity> agentAccessoryEntityList);

    boolean delete(String agentCode);

    List<AgentAccessoryRespVo> list(String agentCode);

    String downloadAsZip(String type, String agentCode);
}

