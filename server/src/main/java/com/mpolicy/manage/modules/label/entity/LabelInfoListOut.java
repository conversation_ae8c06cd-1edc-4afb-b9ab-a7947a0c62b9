package com.mpolicy.manage.modules.label.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class LabelInfoListOut implements Serializable {
    private static final long serialVersionUID = -1956409454728168965L;
    @ApiModelProperty(value = "标签名称")
    private String labelCode;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "标签分组(字典)")
    private String labelGroup;

    @ApiModelProperty(value = "标签库编码")
    private String libraryCode;

    @ApiModelProperty(value = "保障类型(字典)")
    private String portfolioType;

    @ApiModelProperty(value = "标签备注信息")
    private String remark;

    @ApiModelProperty(value = "标签名称")
    private String libraryName;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
