package com.mpolicy.manage.modules.policy.vo.policy;

import lombok.Data;

import java.io.Serializable;

@Data
    public class FourUpChangeDetailOut implements Serializable {
    private static final long serialVersionUID = -8691444312105806530L;


    /**
     * 数据id
     */
    private Integer id;

    /**
     * 保单号
     */
    private String policyCode;
    /**
     * 原信息
     */
    private PolicyBaseInfoOut policyBaseInfo;    /**
     * 原信息
     */
    private FourUpChangeDetailItem sourceItem;

    /**
     * 更新后的信息
     */
    private FourUpChangeDetailItem targetItem;
}
