package com.mpolicy.manage.modules.agent.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 国寿财参数配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "alilog")
@EnableConfigurationProperties(value = AliLogProperty.class)
public class AliLogProperty{

    /**
     *
     */
    private String accessId;

    /**
     *
     */
    private String accessKey;

    /**
     * 域名
     */
    private String host;
}
