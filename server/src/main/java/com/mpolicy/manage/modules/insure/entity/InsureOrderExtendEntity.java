package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保订单扩展信息表
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_extend")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderExtendEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单单号
	 */
	private String insureOrderCode;
	/**
	 * 保单形式 纸质保单、电子保单
	 */
	private String policyForm;
	/**
	 * 是否开通续期 开通/不开通
	 */
	private String autoPayment;
	/**
	 * 首期银行
	 */
	private String firstPayBankCode;
	/**
	 * 首期开户地
	 */
	private String firstPayBankAddr;
	/**
	 * 首期银行账号
	 */
	private String firstPayAcctNo;
	/**
	 * 首期账户名
	 */
	private String firstPayAcctName;
	/**
	 * 首期交费方式
	 */
	private String firstPayType;
	/**
	 * 续期银行
	 */
	private String partPayBankCode;
	/**
	 * 续期开户地
	 */
	private String partPayBankAddr;
	/**
	 * 续期银行账号
	 */
	private String partPayAcctNo;
	/**
	 * 续期账户名
	 */
	private String partPayAcctName;
	/**
	 * 续期交费方式
	 */
	private String partPayType;

	/**
	 * 是否自动续保
	 */
	private String autoRenewFlag;
	/**
	 * 自动续保持卡人姓名
	 */
	private String payCardHolderName;
	/**
	 * 自动续保身份证号码
	 */
	private String payCardHolderId;
	/**
	 * 自动续保银行卡编码
	 */
	private String payBankCode;
	/**
	 * 自动续保银行卡号
	 */
	private String payAcctNo;
	/**
	 * 自动续保手机号码
	 */
	private String payCardHolderMobile;
	/**
	 * 绑卡协议号
	 */
	private String protocolNo;

	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
