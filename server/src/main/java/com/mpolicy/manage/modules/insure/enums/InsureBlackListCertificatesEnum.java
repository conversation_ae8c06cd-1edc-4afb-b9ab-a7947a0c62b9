package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 投保黑名单证件类型枚举
 *
 * <AUTHOR>
 * @since 2022/5/9
 */
@Getter
public enum InsureBlackListCertificatesEnum {

    /**
     * 投保黑名单证件类型枚举
     */
    SFZ("SFZ","身份证");

    /**
     * 模式编码
     */
    private final String code;

    /**
     * 模式名称
     */
    private final String name;

    InsureBlackListCertificatesEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static InsureBlackListCertificatesEnum decode(String code) {
        return Arrays.stream(InsureBlackListCertificatesEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }
}
