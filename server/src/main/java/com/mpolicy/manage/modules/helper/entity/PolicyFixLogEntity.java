package com.mpolicy.manage.modules.helper.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("r_policy_fix_log")
public class PolicyFixLogEntity {

    @TableId
    private Integer id;

    @TableField("policy_no")
    private String policyNo;

    @TableField("type")
    private Integer type;

    @TableField("op_number")
    private Integer opNumber;

    @TableField("remark")
    private String remark;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;
}
