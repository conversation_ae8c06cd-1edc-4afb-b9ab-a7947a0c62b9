package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.InvalidCacheEnum;
import com.mpolicy.manage.modules.agent.dao.AgentCertificateDao;
import com.mpolicy.manage.modules.agent.entity.AgentCertificateEntity;
import com.mpolicy.manage.modules.agent.service.AgentCertificateService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentCertificateVo;
import com.mpolicy.manage.modules.common.service.IInvalidCacheService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("agentCertificateService")
public class AgentCertificateServiceImpl extends ServiceImpl<AgentCertificateDao, AgentCertificateEntity> implements AgentCertificateService {

    @Autowired
    private IInvalidCacheService invalidCacheService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentCertificateEntity> page = this.page(
                new Query<AgentCertificateEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateAgentCertificateList(String agentCode, List<AgentCertificateEntity> agentCertificateEntityList) {
        this.delete(agentCode);
        if (agentCertificateEntityList != null && !agentCertificateEntityList.isEmpty()) {
            List<AgentCertificateEntity> collect = agentCertificateEntityList.stream().peek(agentCertificateEntity -> {
                agentCertificateEntity.setAgentCode(agentCode);
            }).collect(Collectors.toList());

            baseMapper.insertBatchSomeColumn(collect);
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO,agentCode);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean delete(String agentCode) {
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO,agentCode);
        return this.remove(
                Wrappers.<AgentCertificateEntity>lambdaQuery()
                        .eq(AgentCertificateEntity::getAgentCode, agentCode)
        );
    }

    /**
     * 代理人证件列表
     *
     * @param agentCode
     * @return
     */
    @Override
    public List<AgentCertificateVo> findAgentCertificateList(String agentCode) {
        return baseMapper.findAgentCertificateList(agentCode);
    }
}
