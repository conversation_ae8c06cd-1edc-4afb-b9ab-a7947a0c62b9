package com.mpolicy.manage.modules.agent.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/16 10:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelApplicationVo extends ChannelVo{

    @ApiModelProperty(value = "应用编码")
    private String applicationCode;

    @ApiModelProperty(value = "应用启用状态 1:启用;0:关闭")
    private Integer applicationEnabled;

    @ApiModelProperty(value = "顾问规则 0:指定;1:范围;2:自由")
    private Integer agentSelectType;

    @ApiModelProperty(value = "顾问列表")
    private List<AgentInfoVo> agentInfoVoList;
}
