package com.mpolicy.manage.modules.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoExportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoQueryVO;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoVO;
import com.mpolicy.service.common.datasources.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 保险订单客户风险信息服务类
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
public interface InsureOrderCustomerRiskInfoService extends IService<InsureOrderCustomerRiskInfoEntity> {


    /**
     *
     * 根据查询条件的VO进行查询
     *
     *
     * @param queryVO
     *
     *
     * 查询条件VO
     *
     * @return
     *
     * 风险客户信息分页
     *
     */
    @DataSource("slave")
    IPage<InsureOrderCustomerRiskInfoVO> getByQueryVO(InsureOrderCustomerRiskInfoQueryVO queryVO);

    /**
     *
     * 根据查询条件的VO进行查询
     *
     *
     * @param insureOrderCustomerRiskInfoQueryVO
     *
     *
     * 查询条件VO
     *
     * @return
     *
     * 风险客户信息分页
     *
     */
    @DataSource("slave")
    List<InsureOrderCustomerRiskInfoVO> getListByQueryVO(InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO);

    /**
     *
     *
     * excel文件批量导入可风险信息
     *
     *
     * @param fileCode
     *
     * 文件编码
     *
     * @return
     *
     * 导入的详情信息
     *
     */
    String importInsureOrderCustomerRiskInfoData(String fileCode);


    /**
     *
     *
     * 是否包含该身份号码
     *
     *
     * @param identityNumber
     *
     * 证件号码
     *
     * @return
     */
    boolean containByIdentityNumber(String identityNumber);

    /**
     *
     *
     * 获取风险客户导出的数据
     *
     *
     *
     *
     * @param insureOrderCustomerRiskInfoQueryVO
     *
     * 查询数据
     *
     * @return
     */
    List<InsureOrderCustomerRiskInfoExportExcel> getExportByQueryVO(InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO);


}
