package com.mpolicy.manage.modules.policy.vo.renewal;

import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年11月 28日11:23
 */
@Data
public class RenewalTermContext {

    private Map<String, ChannelInfoEntity> channelMap;

    private Map<String, OrgInfoEntity> orgMap;

    private Map<String, AgentUserInfoEntity> agentMap;

    private Map<String, ChannelBranchInfoEntity> channelBranchMap;

    public ChannelInfoEntity safeGetChannel(String code) {
        if (!CollectionUtils.isEmpty(channelMap)) {
            return channelMap.get(code);
        }
        return null;
    }

    public OrgInfoEntity safeGetOrg(String code) {
        if (!CollectionUtils.isEmpty(orgMap)) {
            return orgMap.get(code);
        }
        return null;
    }

    public AgentUserInfoEntity safeGetAgent(String code) {
        if (!CollectionUtils.isEmpty(agentMap)) {
            return agentMap.get(code);
        }
        return null;
    }

    public ChannelBranchInfoEntity safeGetChannelBranch(String code) {
        if (!CollectionUtils.isEmpty(channelBranchMap)) {
            return channelBranchMap.get(code);
        }
        return null;
    }

}
