package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.AdminChangeHistoryEntity;

import java.util.Map;

/**
 * 数据变更操作记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-22 11:43:37
 */
public interface AdminChangeHistoryService extends IService<AdminChangeHistoryEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);
}

