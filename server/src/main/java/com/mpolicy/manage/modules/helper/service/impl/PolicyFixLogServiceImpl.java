package com.mpolicy.manage.modules.helper.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.helper.dao.PolicyFixLogDao;
import com.mpolicy.manage.modules.helper.entity.PolicyFixLogEntity;
import com.mpolicy.manage.modules.helper.service.PolicyFixLogService;
import com.mpolicy.manage.modules.policy.entity.PreservationApplyEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationProductInsuredMapEntity;
import com.mpolicy.manage.modules.policy.entity.preservation.PreservationSurrenderEntity;
import com.mpolicy.manage.modules.policy.enums.PreservationProjectEnum;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationSurrenderService;
import com.mpolicy.manage.modules.policy.service.preservation.project.PreservationSurrender;
import com.netflix.discovery.converters.Auto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 保单数据修正
 */
@Service
public class PolicyFixLogServiceImpl extends ServiceImpl<PolicyFixLogDao, PolicyFixLogEntity> implements PolicyFixLogService {
}
