package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.dao.AgentInvestigateDetailDao;
import com.mpolicy.manage.modules.agent.entity.AgentInvestigateDetailEntity;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireAnswerEntity;
import com.mpolicy.manage.modules.agent.service.AgentInvestigateDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service("agentInvestigateDetailService")
public class AgentInvestigateDetailServiceImpl extends ServiceImpl<AgentInvestigateDetailDao, AgentInvestigateDetailEntity> implements AgentInvestigateDetailService {


    @Override
    public void deleteByQuestionnaireId(Integer questionnaireId) {
        boolean result = this.update(
                new UpdateWrapper<AgentInvestigateDetailEntity>().lambda()
                        .eq(AgentInvestigateDetailEntity::getQuestionnaireId, questionnaireId)
                        .set(AgentInvestigateDetailEntity::getDeleted, Constant.DELETE_FLAG));
        if(!result){
            log.warn("代理人问卷调研记录删除失败 questionnaireId= {}",questionnaireId);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人问卷调研记录删除失败"));
        }
    }
}
