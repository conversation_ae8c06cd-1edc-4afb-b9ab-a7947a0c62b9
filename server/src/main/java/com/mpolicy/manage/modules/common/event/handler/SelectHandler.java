package com.mpolicy.manage.modules.common.event.handler;

import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.common.model.SelectVo;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class SelectHandler implements InitializingBean {

    /**
     * 获取下拉列表
     *
     * @param select
     * @return
     */
    public abstract List<SelectOut> findSelectList(SelectVo select);
}
