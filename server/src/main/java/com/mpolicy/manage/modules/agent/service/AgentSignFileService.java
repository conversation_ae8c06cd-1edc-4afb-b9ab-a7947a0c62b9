package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFilePageList;

import java.util.Map;

/**
 * 代理人签约文件信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileService extends IService<AgentSignFileEntity> {

    /**
     * 代理人文件签署分页查询
     * @param params 分页条件
     */
    PageUtils<AgentSignFilePageList> pageList(Map<String, Object> params);
}

