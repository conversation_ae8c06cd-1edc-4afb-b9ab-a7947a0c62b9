package com.mpolicy.manage.modules.policy.po.renewal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RenewalTermShowPo {

    private String contractCode;

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("产品类型")
    private String policyProductType;

    @ApiModelProperty("续期状态：0=未续期；1=已续期")
    private Integer status;

    @ApiModelProperty("产品名称")
    private String mainProductName;

    @ApiModelProperty("保险公司")
    private String companyName;

    @ApiModelProperty("保险期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保险期间")
    private Integer insuredPeriod;

    @ApiModelProperty("缴费方式")
    private String periodType;

    @ApiModelProperty("保单年度")
    private String policyPeriodYear;

    @ApiModelProperty("缴费期次")
    private Integer period;

    @ApiModelProperty("应收日期")
    private Date duePaymentTime;

    @ApiModelProperty("应收金额")
    private BigDecimal duePaymentAmount;

    @ApiModelProperty("保单状态")
    private String policyStatus;

    @ApiModelProperty("实收日期")
    private Date paymentTime;

    @ApiModelProperty("实收金额")
    private String paymentAmount;

    @ApiModelProperty("宽限期剩余天数")
    private Integer graceDay;

    @ApiModelProperty("投保人")
    private String applicantName;

    @ApiModelProperty("被保人")
    private String insuredName;

    @ApiModelProperty("销售方式")
    private Integer salesType;

    private Integer referrerType;

    @ApiModelProperty("代理人")
    private String agentCode;

    @ApiModelProperty("代理人")
    private String agentName;

    @ApiModelProperty("推荐人")
    private String referrerCode;

    @ApiModelProperty("推荐人")
    private String referrerName;

    @ApiModelProperty("渠道分支")
    private String channelBranchName;

    @ApiModelProperty("销售渠道")
    private String channelCode;

    @ApiModelProperty("销售渠道")
    private String channelName;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("操作时间")
    private Date createTime;

}
