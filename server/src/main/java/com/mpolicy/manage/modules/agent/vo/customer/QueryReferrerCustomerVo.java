package com.mpolicy.manage.modules.agent.vo.customer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ClassName: QueryReferrerCustomerVo
 * Description: 查询推荐人客户查询条件
 * date: 2023/8/10 15:29
 *
 * <AUTHOR>
 */
@Data
public class QueryReferrerCustomerVo implements Serializable{
    private static final long serialVersionUID = -125430983691594613L;

    @ApiModelProperty(value = "推荐人编码")
    @NotBlank(message = "推荐人编码不能为空")
    private String referrerCode;

    @ApiModelProperty(value = "查询key 身份证号或手机号")
    private String key;
}
