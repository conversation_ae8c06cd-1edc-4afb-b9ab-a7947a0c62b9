package com.mpolicy.manage.modules.agent.vo.orginfo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrgPageListOut implements Serializable {
    private static final long serialVersionUID = -8165786803110973799L;

    private String orgCode;

    private Integer id;

    private String orgType;

    private String orgLevel;

    private Integer orgStatus;

    private String orgName;

    private String orgAddr;

    private String provinceName;

    private String cityName;

    private String orgSuperiorName;

    private String orgSuperiorCode;

    private List<OrgPageListOut> children;
}
