package com.mpolicy.manage.modules.policy.service.preservation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.policy.dao.PreservationFlowDao;
import com.mpolicy.manage.modules.policy.entity.PreservationFlowEntity;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationFlowService;

import org.springframework.stereotype.Service;



@Service("preservationFlowService")
public class PreservationFlowServiceImpl extends ServiceImpl<PreservationFlowDao, PreservationFlowEntity> implements PreservationFlowService {


}
