package com.mpolicy.manage.modules.policy.vo.preservation.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.protocol.utils.ValidatePropertyUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保全-分销渠道编码变更-批量导入
 *
 * <AUTHOR>
 * @date 2024/10/16 19:22
 */
@Data
public class PreservationChannelDistributionExcelVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 98465132L;

    @ExcelProperty(value = "序号", index = 0)
    private String serialNo;

    @ExcelProperty(value = "保单号", index = 1)
    private String policyCode;

    @ExcelProperty(value = "变更前图例", index = 2)
    private String beforeChannelDistributionCode;

    @ExcelProperty(value = "变更后图例", index = 3)
    private String correctedChannelDistributionCode;

    public String valid() {
        StringBuilder str = new StringBuilder();
        if (StringUtils.isBlank(serialNo)) {
            str.append("缺少序号;");
        }
        if (StringUtils.isBlank(policyCode)) {
            str.append("缺少保单号;");
        }
        if (StringUtils.isBlank(beforeChannelDistributionCode)) {
            str.append("缺少变更前图例;");
        }
        if (StringUtils.isBlank(correctedChannelDistributionCode)) {
            str.append("缺少变更后图例;");
        }
        return str.toString();
    }

    public boolean isEmptyLine() {
        if (StringUtils.isBlank(serialNo)
                && StringUtils.isBlank(policyCode)
                && StringUtils.isBlank(beforeChannelDistributionCode)
                && StringUtils.isBlank(correctedChannelDistributionCode)) {
            return true;
        }
        return false;
    }
}
