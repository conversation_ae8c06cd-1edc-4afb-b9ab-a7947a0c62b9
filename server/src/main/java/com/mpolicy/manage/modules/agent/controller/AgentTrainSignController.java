package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.service.AgentTrainSignService;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListOut;
import com.mpolicy.manage.modules.agent.vo.train.AgentTrainInfoListVo;
import com.mpolicy.manage.modules.agent.vo.train.TrainSignListOut;
import com.mpolicy.manage.modules.agent.vo.train.UpdateTrainSignVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 代理人培训签到表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
@RestController
@RequestMapping("agent/trainSign")
@Api(tags = "代理人培训")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTrainSignController {


    private final AgentTrainSignService agentTrainSignService;


    @ApiOperation(value = "获取培训签到列表", notes = "获取培训签到列表", httpMethod = "GET")
    @GetMapping("findTrainSignListByCode/{trainCode}")
    public Result<List<TrainSignListOut>> findTrainSignList(@PathVariable(value = "trainCode") String trainCode) {
        List<TrainSignListOut> resultList = agentTrainSignService.findTrainSignList(trainCode);
        return Result.success(resultList);
    }


    @ApiOperation(value = "更新培训签到数据", notes = "更新培训签到数据", httpMethod = "POST")
    @PostMapping("updateTrainSignList")
    public Result updateTrainSignList(@RequestBody @Valid UpdateTrainSignVo vo) {
        agentTrainSignService.updateTrainSignList(vo);
        return Result.success();
    }

    @ApiOperation(value = "获取代理人培训信息表列表", notes = "分页获取代理人培训信息表列表", httpMethod = "GET")
    @GetMapping("findPageList")
    public Result<PageUtils<AgentTrainInfoListOut>> findPageList(AgentTrainInfoListVo params) {
        PageUtils<AgentTrainInfoListOut> page = agentTrainSignService.findPageList(params);
        return Result.success(page);
    }
}
