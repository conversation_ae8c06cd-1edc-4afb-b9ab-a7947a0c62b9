package com.mpolicy.manage.modules.tools.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 测试报告常用语
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-23 16:50:03
 */
@TableName("tools_common_words")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ToolsCommonWordsEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
	@ApiModelProperty(value = "内容Id 传入则为更新")
    @TableId
    private Integer id;
    /**
     * 内容编码
     */
	@ApiModelProperty(value = "内容编码",hidden = true)
    private String code;
    /**
     * 正文
     */
	@ApiModelProperty(required = true, value = "正文")
    @NotBlank(message = "正文不能为空")
    @Length(min=50,max=1000,message = "正文长度是50-1000")
    private String commonWords;
    /**
     * 来源编码
     */
	@ApiModelProperty(hidden = true)
    private String sourceCode;
    /**
     * 标题
     */

    @ApiModelProperty(value = "标题")
    @NotBlank(message = "标题不能为空")
    @Length(min=1,max=20,message = "标题长度是1-20")
    private String title;
    /**
     * 内容使用次数
     */
    @ApiModelProperty(hidden = true)
    private String contentUsage;
    /**
     * 模块类型
     */
    @ApiModelProperty(hidden = true)
    private String moduleType;
    /**
     * 备注
     */
	@ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
	@ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
	@ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
	@ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
	@ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
	@ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
	@ApiModelProperty(value = "更新时将服务器传出的该字段直接传回即可")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
}
