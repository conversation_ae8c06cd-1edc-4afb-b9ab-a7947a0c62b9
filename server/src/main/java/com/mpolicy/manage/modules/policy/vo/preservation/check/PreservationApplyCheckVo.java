package com.mpolicy.manage.modules.policy.vo.preservation.check;


import com.mpolicy.manage.modules.policy.vo.preservation.PreservationChannelDistributionChangeForm;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationChannelReferrerChangeVo;
import com.mpolicy.manage.modules.policy.vo.preservation.PreservationCustomerManagerChangeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "保全项检查")
public class PreservationApplyCheckVo {

    @ApiModelProperty(value = "保单号", required = true)
    @NotBlank(message = "保单号不能为空")
    private String policyNo;

    @ApiModelProperty(value = "保全项目", required = true)
    @NotBlank(message = "保全项目不能为空")
    private String preservationProject;

    @ApiModelProperty(value = "保全类型", required = true)
    @NotBlank(message = "保全类型不能为空")
    private String preservationType;

    @ApiModelProperty(value = "保全批单号")
    private String endorsementNo;

    @ApiModelProperty(value = "退保金额", example = "100.52")
    private BigDecimal surrenderCash;

    @ApiModelProperty(value = "团险分单层-初始渠道推荐人变更集合")
    PreservationChannelReferrerChangeVo channelReferrerChange;

    @ApiModelProperty(value = "保单层-初始渠道推荐人变更")
    PreservationCustomerManagerChangeForm preservationCustomerManagerChangeForm;

    @ApiModelProperty(value = "图例编码变更详情")
    PreservationChannelDistributionChangeForm channelDistributionChangeForm;

    @ApiModelProperty(value = "保单层-销售渠道变更")
    private String channelCode;
}
