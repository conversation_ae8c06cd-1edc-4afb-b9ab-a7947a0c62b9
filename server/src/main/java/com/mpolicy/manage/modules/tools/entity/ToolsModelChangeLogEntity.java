package com.mpolicy.manage.modules.tools.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mpolicy.manage.modules.tools.enums.ToolsModelChangeLogTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @author: yangdonglin
 * @create: 2023-06-12 10:36
 * @description: 模型变更记录
 */
@TableName("tools_model_change_log")
@Accessors(chain = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ToolsModelChangeLogEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键标识
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 类型
     */
    private String type;
    /**
     * 变更标识
     */
    private String changeId;
    /**
     * 变更编码
     */
    private String changeNbr;
    /**
     * 变更前数据
     */
    private String startData;
    /**
     * 变更后数据
     */
    private String endData;
    /**
     * 是否删除
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private Date updateTime;
}
