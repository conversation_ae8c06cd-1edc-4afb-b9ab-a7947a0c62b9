package com.mpolicy.manage.modules.policy.vo.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 理赔申请提交信息
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔申请材料文件信息")
@Data
public class ClaimDocFileInfo {

    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码")
    @NotBlank(message = "文件编码不能为空")
    private String fileCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    @NotNull(message = "文件大小不能为空")
    private Integer fileSize;

    /**
     * 文件访问地址
     */
    @ApiModelProperty(value = "文件访问地址")
    @NotBlank(message = "文件访问地址不能为空")
    private String filePath;

    /**
     * 文件访问绝对地址
     */
    @ApiModelProperty(value = "文件访问绝对地址")
    @NotBlank(message = "文件访问绝对地址不能为空")
    private String domainPath;
}
