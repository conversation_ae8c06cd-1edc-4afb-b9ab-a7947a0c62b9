package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureCustomerWhiteRosterEntity;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerInfoOut;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerWhiteRosterOut;
import com.mpolicy.manage.modules.insure.vo.InsureCustomerWhiteRosterVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 投保客户经理人脸识别白名单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-02 14:37:30
 */
public interface InsureCustomerWhiteRosterService extends IService<InsureCustomerWhiteRosterEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<InsureCustomerWhiteRosterOut> queryPage(Map<String, Object> paramMap);

    /**
     * 获取客户经理信息
     *
     * @param mobile: 手机号
     * @return : com.mpolicy.manage.modules.insure.vo.InsureCustomerInfoOut
     * <AUTHOR>
     * @date 2023/8/2 15:08
     */
    List<InsureCustomerInfoOut> getCustomerInfo(String mobile);

    /**
     * 客户经理白名单添加修改
     *
     * @param customerVo: 白名单信息
     * @return : void
     * <AUTHOR>
     * @date 2023/8/2 15:45
     */
    void customerSaveOrUpdate(InsureCustomerWhiteRosterVo customerVo);

    /**
     * 客户经理白名单导入
     *
     * @param fileCode:文件编码
     * @return : java.lang.Object
     * <AUTHOR>
     * @date 2023/8/2 17:57
     */
    List<String> customerUpload(String fileCode);
}

