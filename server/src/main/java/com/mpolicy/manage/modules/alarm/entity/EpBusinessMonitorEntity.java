package com.mpolicy.manage.modules.alarm.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
@Data
@TableName("ep_business_monitor")
public class EpBusinessMonitorEntity {

    @TableId
    private Integer id;

    @TableField("type")
    private Integer type;

    @TableField("alarm_info")
    private String alarmInfo;

    @TableField("service_name")
    private String serviceName;

    @TableLogic
    private Integer deleted;

    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
