package com.mpolicy.manage.modules.order.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 保险订单客户风险信息实体类
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("insure_order_customer_risk_info")
public class InsureOrderCustomerRiskInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID，自动递增
     */
    private Integer id;

    /**
     * 客户姓名
     */
    @NotBlank
    @ExcelProperty(value = "客户姓名", index = 0)
    private String customerName;

    /**
     * 证件号码
     */
    @NotBlank
    @ExcelProperty(value = "证件号码", index = 1)
    private String identityNumber;

    /**
     * 证件类型
     */
    @NotBlank
    @ExcelProperty(value = "证件类型", index = 2)
    private String identityType;

    /**
     * 客户类型 0 个人，1是企业
     */
    @NotBlank
    @ExcelProperty(value = "客户类型", index = 3)
    private String customerType;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄", index = 4)
    private Integer age;

    /**
     * 性别 0是女，1是男
     */
    @ExcelProperty(value = "性别", index = 5)
    private String gender;

    /**
     * 婚姻状态 0未婚, 1已婚, 2离异, 3丧偶
     */
    @ExcelProperty(value = "婚姻状态", index = 6)
    private String maritalStatus;

    /**
     * 信用分值
     */
    @ExcelProperty(value = "信用分值", index = 7)
    private Integer creditScore;


    /**
     *
     *
     * 首次投保获取信息时投保产品名称
     *
     *
     */
    private String firstInsureProductName;



    /**
     * 评分等级
     */
    @ExcelProperty(value = "评分等级", index = 8)
    @NotBlank
    private String ratingLevel;

    /**
     * 风险来源 0系统，1导入
     */
    @NotBlank
    @ExcelProperty(value = "风险来源", index =9)

    private String riskSource;

    /**
     * 渠道来源 0中和农信渠道，1程序渠道
     */
    @ExcelProperty(value = "渠道来源", index = 10)
    private String channelSource;
    /**
     * 是否是白名单  0不是，1是
     */
    @ExcelProperty(value = "是否是白名单", index = 11)
    private Integer isWhite;

    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long revision;
    /**
     * 创建人
     */
    private String createUser;
}
