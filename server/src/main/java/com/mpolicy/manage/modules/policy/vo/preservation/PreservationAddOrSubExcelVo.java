package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.protocol.utils.ValidatePropertyUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保全增减员导入数据
 *
 * <AUTHOR>
 * @date 2023/1/9 19:22
 */
@Data
public class PreservationAddOrSubExcelVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 98465132L;

    @ExcelProperty(value = "批单号", index = 0)
    private String endorsementNo;

    @ExcelProperty(value = "保单号", index = 1)
    private String policyCode;

    @ExcelProperty(value = "保全生效日", index = 2)
    private String preservationEffectTime;

    @ExcelProperty(value = "退补费金额", index = 3)
    private String surrenderCash;

    /**
     * 行号
     */
    private Integer numNo;


    public void valid(StringBuilder str) {
        if (StringUtils.isBlank(policyCode)) {
            str.append("缺少保单号;");
        }

        if (StringUtils.isBlank(preservationEffectTime)) {
            str.append("缺少保全生效日;");
        }else if (!ValidatePropertyUtil.isExcelDate(preservationEffectTime)){
            str.append("保全生效日格式错误;");
        }

        if (StringUtils.isBlank(surrenderCash)) {
            str.append("缺少退补费金额;");
        } else if (!ValidatePropertyUtil.isDecimal(surrenderCash) && !StringUtils.equals("0", surrenderCash)) {
            str.append("补退费金额格式错误;");
        }
    }

    public boolean isEmptyLine() {
        if (StringUtils.isBlank(endorsementNo)
                && StringUtils.isBlank(policyCode)
                && StringUtils.isBlank(preservationEffectTime)
                && StringUtils.isBlank(surrenderCash)) {
            return true;
        }
        return false;
    }

    public BigDecimal convertSurrenderCash() {
        if (ValidatePropertyUtil.isDecimal(surrenderCash) || StringUtils.equals("0", surrenderCash)) {
            return new BigDecimal(surrenderCash);
        }
        return null;
    }

    public Date convertPreservationEffectTime() {
        if (ValidatePropertyUtil.isExcelDate(preservationEffectTime)) {
            return DateUtils.formatExcelDate(preservationEffectTime);
        }
        return null;
    }

}
