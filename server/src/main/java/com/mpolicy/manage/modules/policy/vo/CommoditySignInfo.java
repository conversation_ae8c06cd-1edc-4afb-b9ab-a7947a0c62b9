package com.mpolicy.manage.modules.policy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保确认列表
 *
 */
@ApiModel(value = "投保确认列表", description = "投保确认列表")
@EqualsAndHashCode(callSuper = true)
@Data
public class CommoditySignInfo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 签约唯一编号
     */
    @ApiModelProperty(value = "签约唯一编号")
    private String signCode;
    /**
     * 业务编号：保单号/预保单号
     */
    @ApiModelProperty(value = "业务编号")
    private String businessCode;
    /**
     * 保险公司编码
     */
    @ApiModelProperty(value = "保险公司编码")
    private String companyCode;
    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保司名称")
    @ExcelProperty(value = "保司名称")
    private String companyName;
    /**
     * 售卖商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称")
    private String commodityName;
    /**
     * 完成签约时间
     */
    @ApiModelProperty(value = "签署日期")
//    @ExcelProperty(value = "签署日期")
    private String finishSignTime;
    /**
     * 手机号码
     */
    @ApiModelProperty(value = "签署人手机号")
    @ExcelProperty(value = "签署人手机号")
    private String mobile;
    /**
     * 推荐人编码
     */
    @ApiModelProperty(value = "推荐人编码")
    private String referrerCode;
    /**
     * 推荐人名称
     */
    @ApiModelProperty(value = "推荐人")
    @ExcelProperty(value = "推荐人")
    private String referrerName;
    /**
     * 文件地址
     */
    @ApiModelProperty(value = "文件地址")
    private String url;
    /**
     * 自定义名称1
     */
    @ApiModelProperty(value = "自定义名称1")
    @ExcelProperty(value = "自定义名称1")
    private String problemContent1;
    /**
     * 自定义名称2
     */
    @ApiModelProperty(value = "自定义名称2")
    @ExcelProperty(value = "自定义名称2")
    private String problemContent2;
    /**
     * 签约状态 0 待签约;1:签约成功 -1:签约失败
     */
    @ApiModelProperty(value = "签约状态 0 待签约;1:签约成功 -1:签约失败")
    private Integer signStatus;
}
