package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投保订单被保人产品信息
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_insured_product")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderInsuredProductEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单编号
	 */
	private String insureOrderCode;
	/**
	 * 投保被保人唯一编号
	 */
	private String insureOrderInsuredCode;
	/**
	 * 险种编码
	 */
	private String productCode;
	/**
	 * 险种名称
	 */
	private String productName;
	/**
	 * 保司编码
	 */
	private String companyCode;
	/**
	 * 保司名称
	 */
	private String companyName;
	/**
	 * 组合编码
	 */
	private String portfolioCode;
	/**
	 * 组合名称
	 */
	private String portfolioName;
	/**
	 * 协议产品编码
	 */
	private String protocolProductCode;
	/**
	 * 主附险标记 1为主险 0为附加险
	 */
	private Integer mainProductFlag;
	/**
	 * 附加险类型 0-其他类型附加险 1-附加投保人豁免
	 */
	private Integer additionalRisksType;
	/**
	 * 险种计划
	 */
	private String productPlan;
	/**
	 * 保险期间
	 */
	private String coverageYear;
	/**
	 * 交费期间
	 */
	private String payPeriod;
	/**
	 * 保险金额
	 */
	private String amount;
	/**
	 * 交费方式
	 */
	private String payMode;
	/**
	 * 份数
	 */
	private Integer numberOfIns;
	/**
	 * 保费
	 */
	private BigDecimal premium;
	/**
	 * 生效日期
	 */
	private String effectiveDate;
	/**
	 * 失效日期
	 */
	private String invalidDate;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
