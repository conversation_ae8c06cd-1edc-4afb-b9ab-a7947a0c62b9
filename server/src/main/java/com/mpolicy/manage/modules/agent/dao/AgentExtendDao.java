package com.mpolicy.manage.modules.agent.dao;

import com.mpolicy.manage.modules.agent.entity.AgentExtendEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentExtendVo;

/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentExtendDao extends BaseMapper<AgentExtendEntity> {


    /**
     * 获取代理人扩展信息
     * @param agentCode
     * @return
     */
    AgentExtendVo findAgentExtendByCode(String agentCode);
}
