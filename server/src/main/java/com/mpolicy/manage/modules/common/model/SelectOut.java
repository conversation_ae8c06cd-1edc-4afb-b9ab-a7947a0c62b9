package com.mpolicy.manage.modules.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectOut implements Serializable {
    private static final long serialVersionUID = 7443052713966570727L;

    /**
     * 标签值
     */
    private String value;

    /**
     * 标签
     */
    private String label;
    /**
     * 是否禁用
     */
    private Boolean disabled;
    /**
     * 子标签
     */
    private String subLabel;
}
