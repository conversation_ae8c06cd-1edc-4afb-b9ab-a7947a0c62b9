package com.mpolicy.manage.modules.policy.vo.preservation;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mpolicy.manage.modules.policy.validate.PreservationSupplementValidate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 保全申请数据
 *
 * <AUTHOR>
 * @date 2022-03-21 10:39
 */
@ApiModel(value = "保全申请数据")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreservationApplyInput {

    @ApiModelProperty(value = "保单中心保单唯一编号", required = true)
    @NotBlank(message = "保单中心保单唯一编号不能为空")
    private String contractCode;

    @ApiModelProperty(value = "保全流水号")
    private String preservationCode;

    @ApiModelProperty(value = "保单号", required = true)
    @NotBlank(message = "保单号不能为空")
    private String policyCode;

    @ApiModelProperty(value = "保全批单号")
    private String endorsementNo;

    @ApiModelProperty(value = "当前续期数")
    private Integer renewalTermPeriod;

    @ApiModelProperty(value = "保全类型", required = true)
    @NotBlank(message = "保全类型不能为空")
    private String preservationType;

    @ApiModelProperty(value = "保全项目", required = true)
    @NotBlank(message = "保全项目不能为空")
    private String preservationProject;

    /**
     * 保全变更原因
     */
    @ApiModelProperty(value = "保全变更原因", required = true)
    @NotBlank(message = "保全变更原因不能为空")
    private String preservationWhy;

    @ApiModelProperty(value = "保全补充说明")
    private String additionalRemark;

    @ApiModelProperty(value = "保全信息备注")
    private String remark;

    /**
     * 保全生效日期
     */
    @ApiModelProperty(value = "保全生效日期", required = true)
    @NotNull(message = "保全生效日期不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preservationEffectTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "保全生效日期")
    private Date preservationSubtractEffectTime;


    @ApiModelProperty(value = "入库时间",hidden = true)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inputTime;


    @ApiModelProperty(value = "保全电子保单", required = true)
    private String ePolicyUrl;

    /**
     * 退保金额
     */
    @ApiModelProperty(value = "退保金额", example = "100.52")
    private BigDecimal surrenderCash;

    /**
     * 附加险解约类型
     */
    @ApiModelProperty(value = "附加险解约类型", example = "POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:TERMINATION_PRODUCT:HESITATION_CANCEL")
    private String terminationProductType;

    /**
     * 附加险解约信息集合
     */
    @ApiModelProperty(value = "附加险解约信息集合")
    List<ProductTerminationInfo> productTerminationList;

    @ApiModelProperty(value = "增减员信息")
    List<PreservationPolicyBasicChange> addOrSubtractList;

    @ApiModelProperty(value = "团险分单层-初始渠道推荐人变更集合")
    PreservationChannelReferrerChangeVo channelReferrerChange;

    @ApiModelProperty(value = "操作员")
    private String operator;

    @ApiModelProperty(value = "修改保单号")
    PreservationPolicyNoChangeForm preservationPolicyNoChangeForm;

    @ApiModelProperty(value = "保全基础信息变更")
    PreservationInfoChangeForm preservationInfoChangeForm;

    @ApiModelProperty(value = "保单层-初始渠道推荐人变更")
    PreservationCustomerManagerChangeForm preservationCustomerManagerChangeForm;

    @ApiModelProperty(value = "退保明细")
    List<PreservationSurrenderDetailVo> surrenderDetailList;

    @ApiModelProperty(value = "被保人基础信息变更")
    PreservationInsuredChangeForm insuredChangeForm;

    @ApiModelProperty(value = "投保人基础信息变更")
    PreservationApplicantChangeForm preservationApplicantChangeForm;

    @ApiModelProperty(value = "是否子保全")
    Boolean sonPreservation;

    @ApiModelProperty(value = "车险信息变更")
    PreservationVehicleInfoChangeForm preservationVehicleInfoChangeForm;

    @ApiModelProperty(value = "保全数据来源:EnumPreservationDataSource")
    private Integer preservationDataSource;

    @ApiModelProperty("保单保费变更明细")
    private PreservationPolicyPremiumChangeForm policyPremiumChangeForm;

    @ApiModelProperty("保单分销渠道编码变更明细")
    private PreservationChannelDistributionChangeForm channelDistributionChangeForm;

    @ApiModelProperty(value = "保单险种变更明细")
    List<PreservationProductChangeVo> productChangeList;

    @ApiModelProperty("保单渠道变更明细")
    private PreservationChannelChangeForm preservationChannelChangeForm;

    @ApiModelProperty("特殊补退费变更")
    @NotNull(message = "特殊补退费信息不能为空",groups = PreservationSupplementValidate.class)
    private PreservationPolicySupplementForm policySupplementForm;
}
