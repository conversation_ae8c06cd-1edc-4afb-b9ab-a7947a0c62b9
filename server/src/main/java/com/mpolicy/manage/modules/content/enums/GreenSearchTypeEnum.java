package com.mpolicy.manage.modules.content.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/28 10:42
 */
public enum GreenSearchTypeEnum {

    // 搜索类型为 组合编号
    PRODUCT_CODE(0, "组合编号"),
    // 搜索类型为 医院名称
    HOSPITAL_NAME(1, "医院名称"),
    // 搜索类型为 正文内容
    TEXT(2, "正文内容");

    private final Integer code;
    private final String name;

    GreenSearchTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static GreenSearchTypeEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
