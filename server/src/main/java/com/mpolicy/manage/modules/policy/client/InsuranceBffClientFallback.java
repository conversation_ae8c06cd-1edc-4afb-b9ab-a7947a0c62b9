package com.mpolicy.manage.modules.policy.client;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsResponseVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Insurance BFF Project 服务降级处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Component
@Slf4j
public class InsuranceBffClientFallback implements FallbackFactory<InsuranceBffClient> {

    @Override
    public InsuranceBffClient create(Throwable throwable) {
        return new InsuranceBffClient() {

            @Override
            public Result<ConversationRecordsResponseVo> getConversationRecords(
                    Integer page, Integer pageSize, String keyword, 
                    String userName, String startTime, String endTime) {
                
                log.warn("调用Insurance BFF Project服务异常 - 参数: page={}, pageSize={}, keyword={}, userName={}, startTime={}, endTime={}", 
                        page, pageSize, keyword, userName, startTime, endTime, throwable);
                
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("调用AI对话记录服务异常"));
            }
        };
    }
}
