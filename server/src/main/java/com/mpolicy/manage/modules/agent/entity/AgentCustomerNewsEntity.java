package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-18 15:18:42
 */
@TableName("agent_customer_news")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCustomerNewsEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 经纪人编码
     */
    private String agentCode;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 标题
     */
    private String title;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;
}
