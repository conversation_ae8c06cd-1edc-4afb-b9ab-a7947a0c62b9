package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.authorize.client.AuthorizeCenterClient;
import com.mpolicy.authorize.common.sms.SendMsgInput;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mail.IMailService;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.Pinyin4jUtil;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.customer.client.CustomerClient;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.enums.InvalidCacheEnum;
import com.mpolicy.manage.enums.QueueEnum;
import com.mpolicy.manage.enums.SmsCodeEnum;
import com.mpolicy.manage.modules.agent.dao.AgentUserAccountDao;
import com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.enums.AgentStatusEnum;
import com.mpolicy.manage.modules.agent.enums.AgentTypeEnum;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.util.AgentConstantUtil;
import com.mpolicy.manage.modules.agent.validator.group.AgentSaveOrUpdateGroup;
import com.mpolicy.manage.modules.agent.vo.AgentBaseInfoVo;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.manage.modules.agent.vo.AgentUserInfoExportVo;
import com.mpolicy.manage.modules.agent.vo.AgentVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.*;
import com.mpolicy.manage.modules.agentApply.entity.FddCustomerContractEntity;
import com.mpolicy.manage.modules.agentApply.enums.AgentSignModelEnum;
import com.mpolicy.manage.modules.agentApply.service.BlAgentOnlineJoinApplyService;
import com.mpolicy.manage.modules.agentApply.service.FddCustomerContractService;
import com.mpolicy.manage.modules.chat.service.ChatListService;
import com.mpolicy.manage.modules.chat.service.ChatWorkOrderService;
import com.mpolicy.manage.modules.common.service.AsyncService;
import com.mpolicy.manage.modules.common.service.IInvalidCacheService;
import com.mpolicy.manage.modules.customer.dao.CustomerAgentMapDao;
import com.mpolicy.manage.modules.customer.entity.CustomerAgentMapEntity;
import com.mpolicy.manage.modules.customer.service.CustomerAgentMapService;
import com.mpolicy.manage.modules.customer.service.CustomerBasicInfoService;
import com.mpolicy.manage.modules.customer.service.CustomerMessageInfoService;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.policy.entity.EpPolicyTransferTaskEntity;
import com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo;
import com.mpolicy.manage.modules.policy.service.EpPolicyTransferTaskService;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.validator.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("agentUserInfoService")
public class AgentUserInfoServiceImpl extends ServiceImpl<AgentUserInfoDao, AgentUserInfoEntity> implements AgentUserInfoService {

    @Autowired
    private AgentExtendService agentExtendService;

    @Autowired
    private AgentAccessoryService agentAccessoryService;

    @Autowired
    private AgentUserManagerRegionService agentUserManagerRegionService;

    @Autowired
    private AgentCertificateService agentCertificateService;

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private RabbitMQService rabbitMQService;

    @Autowired
    private AgentUserAccountService agentUserAccountService;

    @Autowired
    private EpPolicyTransferTaskService epPolicyTransferTaskService;

    @Autowired
    private AgentUserAccountDao agentUserAccountDao;

    @Autowired
    private AgentStudioIpService agentStudioIpService;

    @Autowired
    private AgentMessageInfoService agentMessageInfoService;

    @Autowired
    private AuthorizeCenterClient authorizeCenterClient;

    @Autowired
    private OrgInfoService orgInfoService;

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private CustomerAgentMapDao customerAgentMapDao;

    @Autowired
    private CustomerAgentMapService customerAgentMapService;

    @Autowired
    private IInvalidCacheService invalidCacheService;

    @Autowired
    private CustomerMessageInfoService customerMessageInfoService;

    @Autowired
    private CustomerBasicInfoService customerBasicInfoService;

    @Autowired
    private IMailService mailService;

    @Autowired
    private AgentCompanyAccountService agentCompanyAccountService;

    @Autowired
    private BlAgentOnlineJoinApplyService blAgentOnlineJoinApplyService;

    @Autowired
    private AgentSignFileDetailService agentSignFileDetailService;

    @Autowired
    private ChatListService chatListService;

    @Autowired
    private ChatWorkOrderService chatWorkOrderService;

    @Autowired
    private AgentReceptionService agentReceptionService;

    @Autowired
    private AgentCustomerNewsService agentCustomerNewsService;

    @Autowired
    private FddCustomerContractService fddCustomerContractService;

    @Autowired
    @Lazy
    private AsyncService asyncService;

    @Value("${wx.miniapp.appid}")
    private String appid;


    @Override
    public List<AgentBaseInfoVo> getAgentBaseInfo(String orgCode, String agentNameOrBusinessCode, boolean isNeedPermission) {
        SysUserEntity userEntity = ShiroUtils.getUserEntity();
        String[] orgCodeArray = new String[0];
        if (isNeedPermission && StatusEnum.NORMAL.getCode().equals(userEntity.getIsAllOrg())) {
            String orgPermissions = userEntity.getOrgCodeList();
            orgCodeArray = StrUtil.splitToArray(orgPermissions, ',');
            if (orgCodeArray.length == 0) {
                return new ArrayList<>();
            }
        }
        //获取代理人职级map
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);
        List<AgentUserInfoEntity> agentUserInfoEntityList = this.lambdaQuery()
                .in(orgCodeArray.length > 0, AgentUserInfoEntity::getOrgCode, orgCodeArray)
                .eq(StringUtils.isNotBlank(orgCode), AgentUserInfoEntity::getOrgCode, orgCode)
                .in(AgentUserInfoEntity::getAgentStatus, AgentStatusEnum.INVALID.getCode(), AgentStatusEnum.VALID.getCode())
                .and(StringUtils.isNotBlank(agentNameOrBusinessCode),
                        x -> x.like(AgentUserInfoEntity::getBusinessCode, agentNameOrBusinessCode)
                                .or()
                                .like(AgentUserInfoEntity::getAgentName, agentNameOrBusinessCode)
                )
                .list();
        //获取机构信息map
        List<OrgInfoEntity> list = orgInfoService.list();
        Map<String, OrgInfoEntity> orgInfoEntityMap = list.stream()
                .map(x -> {
                    OrgInfoEntity orgInfoEntity = new OrgInfoEntity();
                    BeanUtils.copyProperties(x, orgInfoEntity);
                    return orgInfoEntity;
                })
                .collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));
        // 构建机构名称树
        Map<String, OrgInfoEntity> orgTreeMap = list.stream().peek(x -> {
            String orgSuperiorCode = x.getOrgSuperiorCode();
            while (StringUtils.isNotBlank(orgSuperiorCode)) {
                OrgInfoEntity orgInfoEntity = orgInfoEntityMap.get(orgSuperiorCode);
                if (orgInfoEntity != null) {
                    x.setOrgName(orgInfoEntity.getOrgName() + "/" + x.getOrgName());
                    orgSuperiorCode = orgInfoEntity.getOrgSuperiorCode();
                } else {
                    orgSuperiorCode = null;
                }
            }
        }).collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));

        return agentUserInfoEntityList.stream().map(x -> {
            AgentBaseInfoVo agentBaseInfoVo = new AgentBaseInfoVo();
            BeanUtils.copyProperties(x, agentBaseInfoVo);
            agentBaseInfoVo.setPosition(agentPositionMap.get(x.getPosition()));
            OrgInfoEntity orgInfo = orgTreeMap.get(x.getOrgCode());
            if (orgInfo != null) {
                agentBaseInfoVo.setOrgName(orgInfo.getOrgName());
            }
            return agentBaseInfoVo;
        }).collect(Collectors.toList());
    }

    @Value("${spring.profiles.active}")
    private String active;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteEntity(String agentCode) {
        // 删除经纪人附件信息
        boolean accessoryDeleted = agentAccessoryService.remove(
                Wrappers.<AgentAccessoryEntity>lambdaQuery().eq(AgentAccessoryEntity::getAgentCode, agentCode)
        );

        // 删除经纪人证件信息
        boolean certificateDeleted = agentCertificateService.remove(
                Wrappers.<AgentCertificateEntity>lambdaQuery().eq(AgentCertificateEntity::getAgentCode, agentCode)
        );

        // 删除经纪人工作室信息
        boolean studioIpDeleted = agentStudioIpService.remove(
                Wrappers.<AgentStudioIpEntity>lambdaQuery().eq(AgentStudioIpEntity::getAgentCode, agentCode)
        );

        // 删除经纪人扩展信息
        boolean extendDeleted = agentExtendService.remove(
                Wrappers.<AgentExtendEntity>lambdaQuery().eq(AgentExtendEntity::getAgentCode, agentCode)
        );

        // 删除经纪人基本信息
        boolean userInfoDeleted = this.remove(
                Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getAgentCode, agentCode)
        );
        if (!(extendDeleted && userInfoDeleted)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, agentCode);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateEntity(AgentVo agentReqVo, boolean isTemp) {
        // 处理代理人基本信息
        AgentUserInfoEntity agentUserInfo = agentReqVo.getAgentUserInfo();

        // 获取agentCode
        String agentCode = agentUserInfo.getAgentCode();

        // 进行操作前，判断手机号是否存在
        String mobile = agentUserInfo.getMobile();
        if (!isTemp) {
            validAgentEntity(agentUserInfo);
            List<AgentUserAccountEntity> list = agentUserAccountService.list(Wrappers.<AgentUserAccountEntity>lambdaQuery()
                    .eq(AgentUserAccountEntity::getAccount, mobile)
                    .ne(StringUtils.isNotBlank(agentCode), AgentUserAccountEntity::getAgentCode, agentCode)
            );
            if (list != null && list.size() > 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该手机号已存在账号"));
            }
        }

        // 判断是更新还是保存
        boolean isSave = StringUtils.isBlank(agentCode);

        // 构建用户信息
        buildInfo(agentUserInfo, isTemp);
        agentCode = agentUserInfo.getAgentCode();
        // 处理日期问题
        if (StringUtils.isBlank(agentUserInfo.getEntryDate())) {
            agentUserInfo.setEntryDate(null);
        }


        // 处理代理人扩展信息
        AgentExtendEntity agentExtend = agentReqVo.getAgentExtend();
        if (StringUtils.isBlank(agentExtend.getStartDate())) {
            agentExtend.setStartDate(null);
        }
        if (StatusEnum.NORMAL.getCode().equals(agentExtend.getLongTerm()) || StringUtils.isBlank(agentExtend.getEndDate())) {
            agentExtend.setEndDate(null);
        }
        agentExtend.setAgentCode(agentCode);


        // 处理代理人ip信息
        AgentStudioIpEntity agentStudioIpEntity = agentReqVo.getAgentStudioIpEntity() == null ? new AgentStudioIpEntity() : agentReqVo.getAgentStudioIpEntity();
        agentStudioIpEntity.setAgentCode(agentCode);


        // 保存
        agentAccessoryService.saveOrUpdateAgentAccessoryList(agentCode, agentReqVo.getAgentAccessoryList());
        agentCertificateService.saveOrUpdateAgentCertificateList(agentCode, agentReqVo.getAgentCertificateList());
        if (isSave) {
            saveEntity(agentUserInfo, agentExtend, agentStudioIpEntity);
        } else {
            updateEntity(agentUserInfo, agentExtend, agentStudioIpEntity);
        }
        log.info("{}经纪人[{}]成功，证件号[{}]", isSave ? "保存" : "修改", agentUserInfo.getAgentCode(), agentUserInfo.getIdCard());
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, agentCode);
    }

    /**
     * 验证是否存在必要信息
     *
     * @param agentUserInfo 代理人信息
     */
    private void validAgentEntity(AgentUserInfoEntity agentUserInfo) {
        ValidatorUtils.validateEntity(agentUserInfo, AgentSaveOrUpdateGroup.class);
        if (AgentTypeEnum.AREA_MANAGER.getKey().equals(agentUserInfo.getAgentType())) {
            if (StringUtils.isBlank(agentUserInfo.getAreaManagerRegion())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("区域经理必须指定区域"));
            }
        } else {
            if (!StringUtils.isNoneBlank(agentUserInfo.getOrgCode(), agentUserInfo.getPosition())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人职位和组织为必选"));
            }
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void saveEntity(AgentUserInfoEntity agentUserInfo, AgentExtendEntity agentExtend, AgentStudioIpEntity agentStudioIpEntity) {
        agentExtendService.save(agentExtend);
        agentStudioIpService.save(agentStudioIpEntity);

        if (!this.save(agentUserInfo)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, agentUserInfo.getAgentCode());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEntity(AgentUserInfoEntity agentUserInfo, AgentExtendEntity agentExtend, AgentStudioIpEntity agentStudioIpEntity) {
        String agentCode = agentUserInfo.getAgentCode();
        agentExtendService.update(
                agentExtend,
                Wrappers.<AgentExtendEntity>lambdaQuery().eq(AgentExtendEntity::getAgentCode, agentCode)
        );

        agentStudioIpService.update(
                agentStudioIpEntity,
                Wrappers.<AgentStudioIpEntity>lambdaQuery().eq(AgentStudioIpEntity::getAgentCode, agentCode)
        );

        if (!this.update(
                agentUserInfo,
                Wrappers.<AgentUserInfoEntity>lambdaQuery()
                        .eq(AgentUserInfoEntity::getAgentCode, agentCode))) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, agentUserInfo.getAgentCode());
    }

    /**
     * 构建用户信息
     *
     * @param agentUserInfo 经纪人信息
     * @param isTemped      是否为暂存状态
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void buildInfo(AgentUserInfoEntity agentUserInfo, boolean isTemped) {
        // 如果是第一次入库就创建agentCode
        if (StringUtils.isBlank(agentUserInfo.getAgentCode())) {
            agentUserInfo.setAgentCode(CommonUtils.createCode("ag"));
        }
        // 如果不是区域专家就清空区域专家地区字段
        if (!AgentTypeEnum.AREA_MANAGER.getKey().equals(agentUserInfo.getAgentType())) {
            agentUserInfo.setAreaManagerRegion("");
        }

        // 构建用户信息
        String agentName = agentUserInfo.getAgentName();
        if (StringUtils.isNotBlank(agentName)) {
            agentUserInfo.setPinyin(String.valueOf(Pinyin4jUtil.converterToFirstSpell(agentName).charAt(0)));
        }
        agentUserInfo.setAvatar(DomainUtil.removeDomain(agentUserInfo.getAvatar()));
        // TODO 入职途径 线下
        agentUserInfo.setEntryType("ENTRY_TYPE:0");

        // 如果是暂存就修改状态，保存则对账号做处理
        if (isTemped) {
            agentUserInfo.setAgentStatus(AgentStatusEnum.conversionToTemp(agentUserInfo.getAgentStatus()));
        } else {
            agentUserInfo.setAgentStatus(AgentStatusEnum.conversionToSave(agentUserInfo.getAgentStatus()));

            // 如果是已经保存过并指定为区域专家则不可以修改地区
            AgentUserInfoEntity one = this.getOne(Wrappers.<AgentUserInfoEntity>lambdaQuery()
                    .eq(AgentUserInfoEntity::getAgentCode, agentUserInfo.getAgentCode())
            );
            if (one != null) {
                if (StringUtils.isNotBlank(one.getAreaManagerRegion())) {
                    agentUserInfo.setAreaManagerRegion(one.getAreaManagerRegion());
                }
            }
            agentUserInfo.setBirthday(IdcardUtil.getBirthDate(agentUserInfo.getIdCard()).toDateStr());
            agentUserInfo.setAppletsCode(createQRCode(agentUserInfo.getAgentCode()));
            // 创建或者更新账号
            String idCard = agentUserInfo.getIdCard();
            agentUserAccountService.createOrUpdateAccount(
                    agentUserInfo.getMobile(),
                    // 初始密码为身份证号后六位
                    idCard.substring(idCard.length() - 6),
                    agentUserInfo.getAgentCode(),
                    agentName, 0);
        }
    }

    /**
     * 生成微信二维码
     *
     * @param channelApplicationCode 渠道code
     */
    private String createQRCode(String channelApplicationCode) {
        Result<String> wxaCode = customerClient.createWxaCode(appid, AdminPublicConstant.WX_QR_AGENT, channelApplicationCode, null);
        if (wxaCode.isSuccess()) {
            return DomainUtil.removeDomain(wxaCode.getData());
        } else {
            log.warn("生成微信二维码失败.请求参数:{}失败原因:", channelApplicationCode, wxaCode.getMsg());
        }
        return null;
    }

    @Override
    public List<AgentInfoVo> getBaseInfo(String orgCode) {
        LambdaQueryWrapper<AgentUserInfoEntity> wrapper = Wrappers.lambdaQuery();
        // 获取组织编码列表
        if (StringUtils.isNotBlank(orgCode)) {
            List<String> orgCodeList = orgInfoService.list(
                    Wrappers.<OrgInfoEntity>lambdaQuery()
                            .eq(OrgInfoEntity::getOrgSuperiorCode, orgCode)
            ).stream().map(OrgInfoEntity::getOrgCode).collect(Collectors.toList());
            orgCodeList.add(orgCode);
            wrapper.in(AgentUserInfoEntity::getOrgCode, orgCodeList);
        }
        List<AgentUserInfoEntity> list = this.list(
                wrapper.eq(AgentUserInfoEntity::getAgentStatus, AgentStatusEnum.VALID.getCode())
        );
        return list.stream().map(item -> {
            AgentInfoVo agentInfoVo = new AgentInfoVo();
            BeanUtils.copyProperties(item, agentInfoVo);
            return agentInfoVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AgentUserInfoEntity> getList(List<String> orgCodeList, List<String> positionList) {
        // 若机构列表为空或代理人列表为空则返回空串
        if (positionList != null && positionList.isEmpty()) {
            return new ArrayList<>();
        }
        if (orgCodeList != null && orgCodeList.isEmpty()) {
            return new ArrayList<>();
        }
        return this.list(
                Wrappers.<AgentUserInfoEntity>lambdaQuery()
                        .in(AgentUserInfoEntity::getOrgCode, orgCodeList)
                        .in(positionList != null, AgentUserInfoEntity::getPosition, positionList)
        );
    }


    /**
     * 获取代理人详情信息
     *
     * @param agentCode
     * @return
     */
    @Override
    public com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo findAgentInfo(String agentCode) {
        //代理人基本信息
        AgentUserInfoVo agentUserInfo = baseMapper.findAgentInfo(agentCode);
        if (agentUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        //处理所属区域
        if (StrUtil.isNotBlank(agentUserInfo.getAreaManagerRegion())) {
            agentUserInfo.setAreaManagerRegionList(Arrays.asList(agentUserInfo.getAreaManagerRegion().split(",")));
        }
        //处理所属片区
        if (StrUtil.isNotBlank(agentUserInfo.getSubregion())) {
            agentUserInfo.setSubregionList(Arrays.asList(agentUserInfo.getSubregion().split(",")));
        }
        //处理机构信息
        if (StrUtil.isNotBlank(agentUserInfo.getOrgCode())) {
            //获取机构列表
            List<String> parentNode = orgInfoService.getParentNode(agentUserInfo.getOrgCode());
            agentUserInfo.setOrgParentNodeCode(parentNode);
        }
        //保险服务人员区域信息
        if (com.mpolicy.agent.common.enums.AgentTypeEnum.INSURANCE_SERVICE_ADVISOR.getKey().equals(agentUserInfo.getAgentType())) {
            List<String> managerRegionCodeList = agentUserManagerRegionService.lambdaQuery()
                    .eq(AgentUserManagerRegionEntity::getAgentCode, agentCode)
                    .list()
                    .stream()
                    .map(AgentUserManagerRegionEntity::getRegionCode)
                    .distinct()
                    .collect(Collectors.toList());
            agentUserInfo.setManagerRegionCodeList(managerRegionCodeList);
        }
        agentUserInfo.setAvatar(DomainUtil.addOssDomainIfNotExist(agentUserInfo.getAvatar()));
        //获取代理人附件列表
        List<AgentAccessoryVo> accessoryList = new ArrayList<>();
        agentAccessoryService.lambdaQuery()
                .eq(AgentAccessoryEntity::getAgentCode, agentCode).list()
                .forEach(action -> {
                    AgentAccessoryVo agentAccessory = new AgentAccessoryVo();
                    BeanUtil.copyProperties(action, agentAccessory);
                    accessoryList.add(agentAccessory);
                });
        //获取代理人扩展信息
        AgentExtendVo agentExtend = agentExtendService.findAgentExtendByCode(agentCode);
        if (agentExtend != null) {
            if (StrUtil.isNotBlank(agentExtend.getHonor())) {
                agentExtend.setHonorList(StrUtil.split(agentExtend.getHonor(), ','));
            }
            List<CompanyAccountVo> companyAccountList = agentCompanyAccountService.lambdaQuery()
                    .eq(AgentCompanyAccountEntity::getAgentCode, agentCode)
                    .list().stream().map(m -> {
                        CompanyAccountVo companyAccount = new CompanyAccountVo();
                        BeanUtil.copyProperties(m, companyAccount);
                        return companyAccount;
                    }).collect(Collectors.toList());
            agentExtend.setCompanyAccountList(companyAccountList);
        }
        //获取代理人工作室信息
        AgentStudioIpVo agentStudioIp = agentStudioIpService.findAgentStudioIpByCode(agentCode);
        //代理人证件列表
        List<AgentCertificateVo> certificateList = agentCertificateService.findAgentCertificateList(agentCode);
        if (certificateList.isEmpty()) {
            AgentCertificateVo agentCertificate = new AgentCertificateVo();
            certificateList.add(agentCertificate);
        }
        //代理人线上入职信息
        AgentOnlineFileInfoVo agentOnlineFileInfoVo = blAgentOnlineJoinApplyService.queryFileInfo(agentCode);
        com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo result = new com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo();
        result.setAgentCode(agentCode);
        result.setAgentUserInfo(agentUserInfo);
        result.setAccessoryList(accessoryList);
        result.setAgentExtend(agentExtend);
        result.setAgentStudioIp(agentStudioIp == null ? new AgentStudioIpVo() : agentStudioIp);
        result.setCertificateList(certificateList);
        result.setAgentOnlineFileInfo(agentOnlineFileInfoVo);
        return result;
    }


    /**
     * 设置员工离职
     *
     * @param update
     */
    @Override
    public void updateQuitStatus(UpdateQuitStatusVo update) {
        AgentUserInfoEntity agentUserInfo = baseMapper.selectOne(Wrappers.<AgentUserInfoEntity>lambdaQuery()
                .eq(AgentUserInfoEntity::getAgentCode, update.getAgentCode()));
        if (agentUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        //判断一下将要离职的代理人是否存在转移中的保单,免得他负责的保单变成孤儿单.
        Integer count = epPolicyTransferTaskService.lambdaQuery()
                .ne(EpPolicyTransferTaskEntity::getTaskStatus, StatusEnum.NORMAL.getCode())
                .and(x -> x.eq(EpPolicyTransferTaskEntity::getSourceAgentCode, update.getAgentCode())
                        .or().eq(EpPolicyTransferTaskEntity::getTargetAgentCode, update.getAgentCode()))
                .count();
        if (count != null && count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前代理人存在等待转移保单,请勿操作离职"));
        }

        //判读当前客户是否存在绑定的客户
        List<String> customerCodeList = new LambdaQueryChainWrapper<>(customerAgentMapDao)
                .eq(CustomerAgentMapEntity::getAgentCode, update.getAgentCode())
                .list().stream().map(CustomerAgentMapEntity::getCustomerCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(customerCodeList) && StrUtil.isBlank(update.getNewAgentCode())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前代理人存在绑定的客户,请指定代理人"));
        }

        if (customerCodeList.size() > 200) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前代理人存在绑定的客户数大于200"));
        }

        //修改离职状态 设置无效
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(AgentUserInfoEntity::getQuitStatus, update.getQuitStatus())
                .set(AgentUserInfoEntity::getQuitTime, DateUtil.parseDate(update.getQuitTime()))
                .set(AgentUserInfoEntity::getAgentStatus, AgentStatusEnum.INVALID.getCode())
                .eq(AgentUserInfoEntity::getId, agentUserInfo.getId())
                .update();
        //删除当前用户账号信息
        agentUserAccountDao.delete(
                Wrappers.<AgentUserAccountEntity>lambdaQuery()
                        .eq(AgentUserAccountEntity::getAgentCode, update.getAgentCode()));
        // 设置代理人离职时 发送mq消息
        MQMessage msg = new MQMessage();
        //当前解绑的代理人编码
        msg.setCode(agentUserInfo.getAgentCode());
        //agent_quit("admin_topic", "admin.global.agent.quit");
        msg.setOpeType(QueueEnum.agent_quit.getRouteKey());
        JSONObject msgData = new JSONObject();
        //解绑时间
        msgData.put("quitTime", update.getQuitTime());
        //重新绑定的代理人编码.可能为空
        msgData.put("newAgentCode", update.getNewAgentCode());
        //当前解绑的代理人姓名
        msgData.put("agentName", agentUserInfo.getAgentName());
        //解绑的客户列表 可以你
        // msgData.put("customerCodeList", customerCodeList);
        msg.setData(msgData);
        rabbitMQService.sendTopicMessage(QueueEnum.agent_quit.getExchange(), QueueEnum.agent_quit.getRouteKey(), msg);

        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, agentUserInfo.getAgentCode());

        //是否发送清退邮件
        if (StatusEnum.NORMAL.getCode().equals(update.getIsSendMail())
                && StrUtil.isNotBlank(agentUserInfo.getEmail())) {
            log.info("向代理人[{}]发送离职邮件,邮箱为:[{}]", agentUserInfo.getAgentName(), agentUserInfo.getEmail());
            //获取离职模版
            String content = ConstantCacheHelper.getValue("AGENT_DISMISSAL_TEMPLATE", "");
            String time = DateUtil.date().toString("yyyy年MM月dd日");
            mailService.asyncSendHtml(agentUserInfo.getEmail(), "终止保险代理合同通知书", StrUtil.format(content, time, time), null);
        }
    }


    /**
     * 获取代理人是否绑定客户
     *
     * @param agentCode
     * @return
     */
    @Override
    public AgentBindCustomerInfoOut findAgentBindCustomerInfo(String agentCode) {
        //判读当前客户是否存在绑定的客户
        Integer count = new LambdaQueryChainWrapper<>(customerAgentMapDao)
                .eq(CustomerAgentMapEntity::getAgentCode, agentCode)
                .count();
        AgentBindCustomerInfoOut out = new AgentBindCustomerInfoOut();
        out.setCustomerNum(count);
        return out;
    }

    /**
     * 获取交接代理人列表
     *
     * @param agentCode
     * @param agentName
     * @return
     */
    @Override
    public List<HandoverPersonListOut> findHandoverPersonList(String agentCode, String agentName) {
        AgentUserInfoEntity userInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentCode, agentCode)
                .one();
        if (userInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        List<AgentUserInfoEntity> list = new LambdaQueryChainWrapper<>(baseMapper)
                .ne(AgentUserInfoEntity::getAgentCode, agentCode)
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                .eq(AgentUserInfoEntity::getAgentStatus, StatusEnum.NORMAL.getCode())
                .like(StrUtil.isNotBlank(agentName), AgentUserInfoEntity::getAgentName, agentName)
                .list();
        List<HandoverPersonListOut> resultList = new ArrayList<>();
        list.forEach(action -> {
            HandoverPersonListOut out = new HandoverPersonListOut();
            out.setLabel(action.getAgentName() + "-" + action.getBusinessCode());
            out.setValue(action.getAgentCode());
            resultList.add(out);
        });
        return resultList;
    }

    /**
     * 分页获取经纪人用户信息表列表
     *
     * @param input
     * @return
     */
    @Override
    public PageUtils<AgentPageListOut> findAgentPageList(AgentPageListVo input) {
        SysUserEntity user = ShiroUtils.getUserEntity();
        List<String> orgList = null;
        if (StatusEnum.NORMAL.getCode().equals(user.getIsAllOrg())) {
            orgList = StrUtil.split(user.getOrgCodeList(), ',');
        }
        if (StrUtil.isNotBlank(input.getOrgCode())) {
            List<String> childNode = orgInfoService.getChildNode(input.getOrgCode());
            if (CollUtil.isNotEmpty(orgList)) {
                orgList.retainAll(childNode);
            } else {
                orgList = childNode;
            }
        }
        if(StringUtils.isNotBlank(input.getAcquisitionArea())){
            input.setAcquisitionAreas(Arrays.asList(input.getAcquisitionArea().split(",")));
        }
        input.setOrgList(orgList);
        IPage<AgentPageListOut> pageList = baseMapper.findAgentPageList(new Page(input.getPage(), input.getLimit()), input);
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);
        Map<String, String> agentEntryTypeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.ENTRY_TYPE);
        Map<String, String> idCardTypeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.ID_CARD_TYPE);
        //获取互联网签约合同信息
        List<String> agentCodeList = pageList.getRecords().stream().map(AgentPageListOut::getAgentCode).collect(Collectors.toList());
        List<FddCustomerContractEntity> list = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(agentCodeList)){
            list = fddCustomerContractService.lambdaQuery().in(FddCustomerContractEntity::getBuzNo, agentCodeList)
                    .eq(FddCustomerContractEntity::getModel, AgentSignModelEnum.MODEL3.getCode()).orderByDesc(FddCustomerContractEntity::getId).list();
        }
        List<FddCustomerContractEntity> finalList = list;
        pageList.getRecords().forEach(agent -> {
            // 经纪人入职方式
            agent.setEntryType(agentEntryTypeMap.get(agent.getEntryType()));
            agent.setPosition(agentPositionMap.get(agent.getPosition()));
            agent.setIdType(idCardTypeMap.get(agent.getIdType()));
            FddCustomerContractEntity fddCustomerContractEntity = finalList.stream().filter(contract -> contract.getBuzNo().equals(agent.getAgentCode())).findFirst().orElse(null);
            if(Objects.nonNull(fddCustomerContractEntity)){
                agent.setMarketingUrl(fddCustomerContractEntity.getUrl());
            }
        });
        return new PageUtils<>(pageList);
    }

    /**
     * 获取导出数据列表
     *
     * @param params 参数
     * @return
     */
    @Override
    public List<AgentUserInfoExportVo> export(AgentPageListVo params) {
        SysUserEntity user = ShiroUtils.getUserEntity();
        List<String> orgList = null;
        if (StatusEnum.NORMAL.getCode().equals(user.getIsAllOrg())) {
            orgList = StrUtil.split(user.getOrgCodeList(), ',');
        }
        if (StrUtil.isNotBlank(params.getOrgCode())) {
            List<String> childNode = orgInfoService.getChildNode(params.getOrgCode());
            if (CollUtil.isNotEmpty(orgList)) {
                orgList.retainAll(childNode);
            } else {
                orgList = childNode;
            }
        }
        if(StringUtils.isNotBlank(params.getAcquisitionArea())){
            params.setAcquisitionAreas(Arrays.asList(params.getAcquisitionArea().split(",")));
        }
        params.setOrgList(orgList);
        List<AgentUserInfoExportVo> export = baseMapper.findExportList(params);
        Map<String, String> idCardTypeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.ID_CARD_TYPE);
        Map<String, String> agentDegreeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_DEGREE);
        Map<String, String> agentPoliticsMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POLITICS);
        Map<String, String> agentTypeMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_TYPE);
        Map<String, String> agentAcquisitionAreaMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.SELL_PRODUCT_AREA);
        Map<String, String> agentNationMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.NATIONALITY_LIST);
        Map<String, String> agentCountryMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.COUNTRY_LIST);
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);
        Map<String, String> agentNatureMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_NATURE);
        export.forEach(agent -> {
            // 经纪人状态
            String status = agent.getAgentStatus();
            agent.setAgentStatus(AgentStatusEnum.getNameByCode(AgentStatusEnum.toTempOut(Integer.valueOf(status))).getName());
            agent.setQuitStatus("0".equals(agent.getQuitStatus()) ? "在职" : "离职");
            agent.setGender(Objects.equals(agent.getGender(), "0") ? "女" : "男");
            agent.setDegree(agentDegreeMap.get(agent.getDegree()));
            agent.setPolitics(agentPoliticsMap.get(agent.getPolitics()));
            agent.setAcquisitionArea(agentAcquisitionAreaMap.get(agent.getAcquisitionArea()));
            agent.setIdType(idCardTypeMap.get(agent.getIdType()));
            agent.setNation(agentNationMap.get(agent.getNation()));
            agent.setCountry(agentCountryMap.get(agent.getCountry()));
            agent.setPosition(agentPositionMap.get(agent.getPosition()));
            agent.setAgentNature(agentNatureMap.get(agent.getAgentNature()));
            if (StrUtil.isNotBlank(agent.getBirthday())) {
                agent.setBirthday(DateUtil.parse(agent.getBirthday().replace("-", ""), "yyyyMMdd").toString("yyyy-MM-dd"));
            }
            if(agent.getAgentType().equals(com.mpolicy.agent.common.enums.AgentTypeEnum.INSURANCE_SERVICE_ADVISOR.getKey())){
                List<String> managerRegionCodeList = agentUserManagerRegionService.lambdaQuery()
                        .eq(AgentUserManagerRegionEntity::getAgentCode, agent.getAgentCode())
                        .list()
                        .stream()
                        .map(AgentUserManagerRegionEntity::getRegionCode)
                        .distinct()
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(managerRegionCodeList)){
                    agent.setAreaManagerRegion(managerRegionCodeList
                            .stream()
                            .map(DicCacheHelper::getValue).distinct().collect(Collectors.joining(",")));
                }
            }else{
                if (StrUtil.isNotBlank(agent.getAreaManagerRegion())) {
                    agent.setAreaManagerRegion(Arrays.asList(agent.getAreaManagerRegion()
                                    .split(","))
                            .stream()
                            .map(DicCacheHelper::getValue).distinct().collect(Collectors.joining(",")));
                }
            }
            agent.setAgentType(agentTypeMap.get(agent.getAgentType()));
            agent.setPositionDegree(DicCacheHelper.getValue(agent.getPositionDegree()));
        });
        return export;
    }


    /**
     * 新增/暂存代理人信息
     *
     * @param save
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAgentInfo(com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo save) {
        if (StrUtil.isNotBlank(save.getAgentUserInfo().getMobile())) {
            int count = agentUserAccountService.count(Wrappers.<AgentUserAccountEntity>lambdaQuery()
                    .eq(AgentUserAccountEntity::getAccount, save.getAgentUserInfo().getMobile())
            );
            if (count > 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该手机号已存在账号"));
            }
        }
        if (StrUtil.isNotBlank(save.getAgentUserInfo().getIdCard())) {
            if (!IdcardUtil.isValidCard(save.getAgentUserInfo().getIdCard())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请输入正确的身份证号"));
            }
        }
        AgentUserInfoVo saveAgentUserInfo = save.getAgentUserInfo();
        String agentCode = CommonUtils.createCode("ag");
        this.handleRegion(saveAgentUserInfo, agentCode);
        //创建代理人信息
        AgentUserInfoEntity insertAgentInfo = new AgentUserInfoEntity();
        BeanUtil.copyProperties(save.getAgentUserInfo(), insertAgentInfo);
        insertAgentInfo.setAgentCode(agentCode);
        //兼容处理时间
        if (save.getAgentUserInfo().getEntryDate() != null) {
            insertAgentInfo.setEntryDate(DateUtil.formatDate(save.getAgentUserInfo().getEntryDate()));
        }
        // 如果不是区域专家就清空区域专家地区字段
        if (!AgentTypeEnum.AREA_MANAGER.getKey().equals(insertAgentInfo.getAgentType())) {
            insertAgentInfo.setAreaManagerRegion("");
        }
        if (com.mpolicy.agent.common.enums.AgentTypeEnum.INSURANCE_SERVICE_ADVISOR.getKey()
                .equals(save.getAgentUserInfo().getAgentType()) && CollUtil.isNotEmpty(save.getAgentUserInfo().getManagerRegionCodeList())) {
            agentUserManagerRegionService.saveBatch(save.getAgentUserInfo().getManagerRegionCodeList().stream().map(m -> {
                AgentUserManagerRegionEntity agentUserManagerRegion = new AgentUserManagerRegionEntity();
                agentUserManagerRegion.setAgentName(insertAgentInfo.getAgentName());
                agentUserManagerRegion.setAgentCode(insertAgentInfo.getAgentCode());
                agentUserManagerRegion.setRegionCode(m);
                agentUserManagerRegion.setRegionName(DicCacheHelper.getValue(m));
                return agentUserManagerRegion;
            }).collect(Collectors.toList()));
        }
        // 用户拼音
        if (StringUtils.isNotBlank(insertAgentInfo.getAgentName())) {
            insertAgentInfo.setPinyin(String.valueOf(Pinyin4jUtil.converterToFirstSpell(insertAgentInfo.getAgentName()).charAt(0)));
        }
        insertAgentInfo.setAvatar(DomainUtil.removeDomain(insertAgentInfo.getAvatar()));
        // TODO 入职途径 线下
        insertAgentInfo.setEntryType("ENTRY_TYPE:0");
        if (!active.contains("dev")) {
            insertAgentInfo.setAppletsCode(createQRCode(insertAgentInfo.getAgentCode()));
        }
        if (StrUtil.isNotBlank(save.getAgentUserInfo().getIdCard())) {
            insertAgentInfo.setBirthday(IdcardUtil.getBirthByIdCard(save.getAgentUserInfo().getIdCard()));
        }
        baseMapper.insert(insertAgentInfo);

        //经纪人工作室信息
        AgentStudioIpEntity agentStudioIp = new AgentStudioIpEntity();
        BeanUtil.copyProperties(save.getAgentStudioIp(), agentStudioIp);
        agentStudioIp.setAgentCode(insertAgentInfo.getAgentCode());
        agentStudioIpService.save(agentStudioIp);

        //经纪人人员证件附件
        if (CollUtil.isNotEmpty(save.getAccessoryList())) {
            save.getAccessoryList().forEach(action -> {
                AgentAccessoryEntity agentAccessory = new AgentAccessoryEntity();
                BeanUtil.copyProperties(action, agentAccessory);
                agentAccessory.setAgentCode(insertAgentInfo.getAgentCode());
                agentAccessoryService.save(agentAccessory);
            });
        }
        //经纪人人员证件信息
        if (CollUtil.isNotEmpty(save.getCertificateList())) {
            save.getCertificateList().forEach(action -> {
                if (StrUtil.isBlank(action.getCertificateNum())) {
                    return;
                }
                AgentCertificateEntity agentCertificate = new AgentCertificateEntity();
                BeanUtil.copyProperties(action, agentCertificate);
                if (action.getStartDate() != null) {
                    agentCertificate.setStartDate(DateUtil.formatDate(action.getStartDate()));
                }
                if (action.getEndDate() != null) {
                    agentCertificate.setEndDate(DateUtil.formatDate(action.getEndDate()));
                }
                agentCertificate.setAgentCode(insertAgentInfo.getAgentCode());
                agentCertificateService.save(agentCertificate);
            });
        }
        //人员扩展信息
        AgentExtendEntity agentExtend = new AgentExtendEntity();
        BeanUtil.copyProperties(save.getAgentExtend(), agentExtend);
        agentExtend.setAgentCode(insertAgentInfo.getAgentCode());
        if (save.getAgentExtend().getStartDate() != null) {
            agentExtend.setStartDate(DateUtil.formatDate(save.getAgentExtend().getStartDate()));
        }
        if (save.getAgentExtend().getEndDate() != null) {
            agentExtend.setEndDate(DateUtil.formatDate(save.getAgentExtend().getEndDate()));
        }
        if (CollUtil.isNotEmpty(save.getAgentExtend().getHonorList())) {
            agentExtend.setHonor(CollUtil.join(save.getAgentExtend().getHonorList(), ","));
        }
        agentExtendService.save(agentExtend);
        //代理人保司账号
        if (CollUtil.isNotEmpty(save.getAgentExtend().getCompanyAccountList())) {
            List<AgentCompanyAccountEntity> companyAccountList = save.getAgentExtend().getCompanyAccountList().stream()
                    .filter(f -> (StrUtil.isNotBlank(f.getCompanyAccount()) && StrUtil.isNotBlank(f.getCompanyCode())))
                    .map(m -> {
                        AgentCompanyAccountEntity agentCompanyAccount = new AgentCompanyAccountEntity();
                        BeanUtil.copyProperties(m, agentCompanyAccount);
                        InsuranceCompanyEntity info = insuranceCompanyService.info(m.getCompanyCode());
                        agentCompanyAccount.setCompanyName(info.getCompanyName());
                        agentCompanyAccount.setShortName(info.getShortName());
                        agentCompanyAccount.setAgentCode(agentCode);
                        return agentCompanyAccount;
                    }).collect(Collectors.toList());
            if (!companyAccountList.isEmpty()) {
                agentCompanyAccountService.saveBatch(companyAccountList);
            }
        }
        //创建账号
        if (save.getAgentUserInfo().getAgentStatus() == 1) {
            buildAccount(insertAgentInfo.getMobile(), insertAgentInfo.getAgentCode(), insertAgentInfo.getIdCard(), insertAgentInfo.getAgentName());
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO, insertAgentInfo.getAgentCode());
        //代理人创建完成后,生成代理人要签署的文件信息
        this.agentSignFileDetailService.generateFile(agentCode);
    }

    /**
     * 处理区域信息
     *
     * @param agentCode           代理人编码
     * @param updateAgentUserInfo 代理人信息
     */
    private void handleRegion(AgentUserInfoVo updateAgentUserInfo, String agentCode) {
        //不是区域保险专家需要做置空处理
        if (!AgentTypeEnum.AREA_MANAGER.getKey().equals(updateAgentUserInfo.getAgentType())) {
            updateAgentUserInfo.setAreaManagerRegionList(Collections.emptyList());
            updateAgentUserInfo.setSubregionList(Collections.emptyList());
            updateAgentUserInfo.setAreaManagerRegion(null);
            updateAgentUserInfo.setSubregion(null);
            return;
        }
        //获取只选择了区域,没有选择片区的区域信息
        List<String> regionList = updateAgentUserInfo.getAreaManagerRegionList().stream().filter(f -> {
            List<String> subList = DicCacheHelper.getSons(f).stream().map(m1 -> m1.getKey()).collect(Collectors.toList());
            if (CollUtil.intersection(updateAgentUserInfo.getSubregionList(), subList).isEmpty()) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(regionList)) {
            //从数据库中获取只选择了区域没有选择片区的的区域保险专家
            List<AgentUserInfoEntity> agentList = new LambdaQueryChainWrapper<>(baseMapper)
                    .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                    .list();
            //获取只选了区域没有选择片区的区域
            List<String> notSubregionList = agentList.stream()
                    .filter(f -> StrUtil.isNotBlank(f.getAreaManagerRegion())
                            && StrUtil.isBlank(f.getSubregion())
                            && !Objects.equals(f.getAgentCode(), agentCode))
                    .map(m ->
                        StrUtil.split(m.getAreaManagerRegion(), ",")
                    ).flatMap(Arrays::stream).collect(Collectors.toList());
            List<String> regionName = CollUtil.intersection(regionList, notSubregionList).stream().map(m -> DicCacheHelper.getValue(m)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(regionName)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("区域:{}已经存在区域保险专家", regionName)));
            }
        }
        updateAgentUserInfo.setAreaManagerRegion(CollUtil.join(updateAgentUserInfo.getAreaManagerRegionList(), ","));
        updateAgentUserInfo.setSubregion(CollUtil.join(updateAgentUserInfo.getSubregionList(), ","));
    }

    /**
     * 修改代理人信息
     *
     * @param update
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentInfo(com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo update) {
        if (StrUtil.isNotBlank(update.getAgentUserInfo().getIdCard())) {
            if (!IdcardUtil.isValidCard(update.getAgentUserInfo().getIdCard())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请输入正确的身份证号"));
            }
        }
        //判断是否为区域专家
        AgentUserInfoVo updateAgentUserInfo = update.getAgentUserInfo();
        //处理区域信息.
        this.handleRegion(updateAgentUserInfo, update.getAgentCode());

        agentUserManagerRegionService.lambdaUpdate()
                .eq(AgentUserManagerRegionEntity::getAgentCode, update.getAgentCode())
                .remove();
        if (com.mpolicy.agent.common.enums.AgentTypeEnum.INSURANCE_SERVICE_ADVISOR.getKey()
                .equals(updateAgentUserInfo.getAgentType()) && CollUtil.isNotEmpty(updateAgentUserInfo.getManagerRegionCodeList())) {
            agentUserManagerRegionService.saveBatch(updateAgentUserInfo.getManagerRegionCodeList().stream().map(m -> {
                AgentUserManagerRegionEntity agentUserManagerRegion = new AgentUserManagerRegionEntity();
                agentUserManagerRegion.setAgentName(updateAgentUserInfo.getAgentName());
                agentUserManagerRegion.setAgentCode(update.getAgentCode());
                agentUserManagerRegion.setRegionCode(m);
                agentUserManagerRegion.setRegionName(DicCacheHelper.getValue(m));
                return agentUserManagerRegion;
            }).collect(Collectors.toList()));
        }

        AgentUserInfoEntity agentUserInfo = baseMapper.selectOne(Wrappers.<AgentUserInfoEntity>lambdaQuery()
                .eq(AgentUserInfoEntity::getAgentCode, update.getAgentCode()));
        if (agentUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        if (StrUtil.isNotBlank(update.getAgentUserInfo().getMobile())
                && update.getAgentUserInfo().getMobile().equals(agentUserInfo.getMobile())) {
            int count = agentUserAccountService.count(Wrappers.<AgentUserAccountEntity>lambdaQuery()
                    .ne(AgentUserAccountEntity::getAgentCode, update.getAgentCode())
                    .eq(AgentUserAccountEntity::getAccount, update.getAgentUserInfo().getMobile())
            );
            if (count > 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该手机号已存在账号"));
            }
        }
        //修改代理人信息
        AgentUserInfoEntity updateAgentInfo = new AgentUserInfoEntity();
        BeanUtil.copyProperties(update.getAgentUserInfo(), updateAgentInfo);
        updateAgentInfo.setId(agentUserInfo.getId());
        //兼容处理时间
        if (update.getAgentUserInfo().getEntryDate() != null) {
            updateAgentInfo.setEntryDate(DateUtil.formatDate(update.getAgentUserInfo().getEntryDate()));
        }
        // 如果不是区域专家就清空区域专家地区字段
        if (!AgentTypeEnum.AREA_MANAGER.getKey().equals(updateAgentInfo.getAgentType())) {
            updateAgentInfo.setAreaManagerRegion("");
            updateAgentInfo.setSubregion("");
        }
        // 用户拼音
        if (StringUtils.isNotBlank(updateAgentInfo.getAgentName())) {
            updateAgentInfo.setPinyin(String.valueOf(Pinyin4jUtil.converterToFirstSpell(updateAgentInfo.getAgentName()).charAt(0)));
        }
        updateAgentInfo.setAvatar(DomainUtil.removeDomain(updateAgentInfo.getAvatar()));
        // TODO 入职途径 线下
        updateAgentInfo.setEntryType("ENTRY_TYPE:0");
        if (StrUtil.isNotBlank(update.getAgentUserInfo().getIdCard())) {
            updateAgentInfo.setBirthday(IdcardUtil.getBirthByIdCard(update.getAgentUserInfo().getIdCard()));
        }
        baseMapper.updateById(updateAgentInfo);

        //经纪人工作室信息
        AgentStudioIpEntity agentStudioIp = agentStudioIpService.getOne(Wrappers.<AgentStudioIpEntity>lambdaQuery()
                .eq(AgentStudioIpEntity::getAgentCode, update.getAgentCode()));
        if (agentStudioIp == null) {
            agentStudioIp = new AgentStudioIpEntity();
            agentStudioIp.setAgentCode(update.getAgentCode());
        }
        BeanUtil.copyProperties(update.getAgentStudioIp(), agentStudioIp);
        agentStudioIpService.saveOrUpdate(agentStudioIp);

        //经纪人人员证件附件
        agentAccessoryService.remove(Wrappers.<AgentAccessoryEntity>lambdaQuery()
                .eq(AgentAccessoryEntity::getAgentCode, update.getAgentCode()));
        if (CollUtil.isNotEmpty(update.getAccessoryList())) {
            update.getAccessoryList().forEach(action -> {
                AgentAccessoryEntity agentAccessory = new AgentAccessoryEntity();
                BeanUtil.copyProperties(action, agentAccessory);
                agentAccessory.setAgentCode(update.getAgentCode());
                agentAccessoryService.save(agentAccessory);
            });
        }
        //经纪人人员证件信息
        agentCertificateService.remove(Wrappers.<AgentCertificateEntity>lambdaQuery()
                .eq(AgentCertificateEntity::getAgentCode, update.getAgentCode()));
        if (CollUtil.isNotEmpty(update.getCertificateList())) {
            update.getCertificateList().forEach(action -> {
                if (StrUtil.isBlank(action.getCertificateNum())) {
                    return;
                }
                AgentCertificateEntity agentCertificate = new AgentCertificateEntity();
                BeanUtil.copyProperties(action, agentCertificate);
                agentCertificate.setAgentCode(update.getAgentCode());
                if (action.getStartDate() != null) {
                    agentCertificate.setStartDate(DateUtil.formatDate(action.getStartDate()));
                }
                if (action.getEndDate() != null) {
                    agentCertificate.setEndDate(DateUtil.formatDate(action.getEndDate()));
                }
                if (action.isLongTerm()) {
                    agentCertificate.setEndDate(null);
                    agentCertificate.setLongTerm(1);
                } else {
                    agentCertificate.setLongTerm(0);
                    if (update.getAgentExtend().getEndDate() != null) {
                        agentCertificate.setEndDate(DateUtil.formatDate(update.getAgentExtend().getEndDate()));
                    }
                }
                agentCertificateService.save(agentCertificate);
            });
        }
        //人员扩展信息
        AgentExtendEntity agentExtend = agentExtendService.getOne(Wrappers.<AgentExtendEntity>lambdaQuery()
                .eq(AgentExtendEntity::getAgentCode, update.getAgentCode()));
        if (agentExtend == null) {
            agentExtend = new AgentExtendEntity();
        }
        BeanUtil.copyProperties(update.getAgentExtend(), agentExtend, "startDate", "endDate");
        agentExtend.setAgentCode(update.getAgentCode());
        if (update.getAgentExtend().getStartDate() != null) {
            agentExtend.setStartDate(DateUtil.formatDate(update.getAgentExtend().getStartDate()));
        }
        if (update.getAgentExtend().isLongTerm()) {
            agentExtend.setEndDate(null);
            agentExtend.setLongTerm(1);
        } else {
            agentExtend.setLongTerm(0);
            if (update.getAgentExtend().getEndDate() != null) {
                agentExtend.setEndDate(DateUtil.formatDate(update.getAgentExtend().getEndDate()));
            }
        }
        if (CollUtil.isNotEmpty(update.getAgentExtend().getHonorList())) {
            agentExtend.setHonor(CollUtil.join(update.getAgentExtend().getHonorList(), ","));
        }
        //代理人保司账号
        if (CollUtil.isNotEmpty(update.getAgentExtend().getCompanyAccountList())) {
            List<Integer> dbIds = agentCompanyAccountService.lambdaQuery()
                    .eq(AgentCompanyAccountEntity::getAgentCode, update.getAgentCode())
                    .list().stream().map(m -> m.getId()).collect(Collectors.toList());
            List<String> reqIds = update.getAgentExtend().getCompanyAccountList().stream()
                    .filter(f -> (StrUtil.isNotBlank(f.getCompanyAccount()) && StrUtil.isNotBlank(f.getCompanyCode()) && f.getId() != null))
                    .map(m -> m.getCompanyCode())
                    .collect(Collectors.toList());
            dbIds.removeAll(reqIds);
            if (CollUtil.isNotEmpty(dbIds)) {

                agentCompanyAccountService.removeByIds(dbIds);
            }
            List<AgentCompanyAccountEntity> companyAccountList = update.getAgentExtend().getCompanyAccountList().stream()
                    .filter(f -> (StrUtil.isNotBlank(f.getCompanyAccount()) && StrUtil.isNotBlank(f.getCompanyCode())))
                    .map(m -> {
                        AgentCompanyAccountEntity agentCompanyAccount = new AgentCompanyAccountEntity();
                        BeanUtil.copyProperties(m, agentCompanyAccount);
                        InsuranceCompanyEntity info = insuranceCompanyService.info(m.getCompanyCode());
                        agentCompanyAccount.setCompanyName(info.getCompanyName());
                        agentCompanyAccount.setShortName(info.getShortName());
                        agentCompanyAccount.setAgentCode(update.getAgentCode());
                        return agentCompanyAccount;
                    }).collect(Collectors.toList());
            if (!companyAccountList.isEmpty()) {
                agentCompanyAccountService.saveOrUpdateBatch(companyAccountList);
            }
        } else {
            agentCompanyAccountService.lambdaUpdate()
                    .eq(AgentCompanyAccountEntity::getAgentCode, update.getAgentCode())
                    .remove();
        }

        agentExtendService.saveOrUpdate(agentExtend);
        if (update.getAgentUserInfo().getAgentStatus() != 2) {
            buildAccount(update.getAgentUserInfo().getMobile(), update.getAgentCode(), update.getAgentUserInfo().getIdCard(), update.getAgentUserInfo().getAgentName());
        }
    }

    private void buildAccount(String mobile, String agentCode, String idCard, String agentName) {
        if (StrUtil.isBlank(mobile)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("手机号不存在"));
        }
        if (StrUtil.isBlank(agentCode)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人编码不存在"));
        }
        if (StrUtil.isBlank(idCard)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("证件号不存在"));
        }
        if (StrUtil.isBlank(agentName)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人姓名不存在"));
        }
        //判断账号信息是否存在
        AgentUserAccountEntity accountInfo = new LambdaQueryChainWrapper<>(agentUserAccountDao)
                .eq(AgentUserAccountEntity::getAccountType, 0)
                .eq(AgentUserAccountEntity::getAgentCode, agentCode).one();
        if (accountInfo == null) {
            String salt = RandomUtil.randomString(4);
            AgentUserAccountEntity agentUserAccount = new AgentUserAccountEntity();
            agentUserAccount.setAccount(mobile);
            agentUserAccount.setAccountType(0);
            agentUserAccount.setAgentCode(agentCode);
            agentUserAccount.setPassword(SecureUtil.md5("xj" + idCard.substring(idCard.length() - 6) + salt));
            agentUserAccount.setSalt(salt);
            agentUserAccountDao.insert(agentUserAccount);
            // 进行短信发送
            SendMsgInput sendMsgInput = new SendMsgInput();
            sendMsgInput.setMobile(mobile);
            sendMsgInput.setSmsCode(SmsCodeEnum.NEW_AGENT.getCodeType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", agentName);
            sendMsgInput.setData(jsonObject);
            if (!active.contains("dev")) {
                authorizeCenterClient.sendSms(AdminPublicConstant.SMS_CHANNEL_CODE, sendMsgInput);
            }
        } else if (!accountInfo.getAccount().equals(mobile)) {
            new LambdaUpdateChainWrapper<>(agentUserAccountDao)
                    .set(AgentUserAccountEntity::getAccount, mobile)
                    .eq(AgentUserAccountEntity::getAccount, accountInfo.getAccount())
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateAreaManagerRegion(UpdateAreaManagerRegionVo update) {
        //获取当前代理人的区域信息
        AgentUserInfoEntity userInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentCode, update.getAgentCode())
                .one();
        if (userInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        //获取目标的区域信息
        AgentUserInfoEntity replaceUserInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentCode, update.getReplaceAgentCode())
                .one();
        if (replaceUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("代理人信息不存在"));
        }
        //判断目标代理人是不是客户经理
        if (!AgentTypeEnum.AREA_MANAGER.getKey().equals(replaceUserInfo.getAgentType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("目标代理人不是区域经理"));
        }
        //获取当前操作代理人的区域信息
        List<String> areaManagerRegionList = StrUtil.split(userInfo.getAreaManagerRegion(), ',');
        //判断转移的区域是否有权限
        if (!areaManagerRegionList.contains(update.getAreaManagerRegion())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("没有权限操作这个区域"));
        }
        //获取当前操作代理人的片区列表
        List<String> subregionList = StrUtil.split(userInfo.getSubregion(), ',');
        //获取目标代理人的区域和片区信息
        List<String> replaceAreaManagerRegionList = StrUtil.split(replaceUserInfo.getAreaManagerRegion(), ',');
        List<String> replaceSubregionList = StrUtil.split(replaceUserInfo.getSubregion(), ',');
        //传入了片区
        if (CollUtil.isNotEmpty(update.getSubregionList())) {
            //获取当前操作的区域的下的片区列表
            List<String> sbuList = DicCacheHelper.getSons(update.getAreaManagerRegion()).stream().map(m -> m.getKey()).collect(Collectors.toList());
            //如果操作的片区不在这集合里面 说明他没有权限操作片区信息
            for (String subregion : update.getSubregionList()) {
                if (!sbuList.contains(subregion)) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("没有权限操作其中的片区[" + DicCacheHelper.getValue(subregion) + "]"));
                }
            }
            //删除转移的片区信息
            subregionList.removeAll(update.getSubregionList());
            replaceSubregionList.addAll(update.getSubregionList());
        }
        //如果转移的这个代理人下面没有片区了,那么直接删除这个代理人的区域,存在片区不删除
        if (CollUtil.isEmpty(subregionList)) {
            areaManagerRegionList.remove(update.getAreaManagerRegion());
        }
        //往目标代理人身上添加区域和片区
        replaceAreaManagerRegionList.add(update.getAreaManagerRegion());

        //更新修改的代理人信息
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(AgentUserInfoEntity::getAreaManagerRegion, CollUtil.join(areaManagerRegionList.stream().filter(
                    StrUtil::isNotBlank).distinct().collect(Collectors.toList()), ","))
                .set(AgentUserInfoEntity::getSubregion, CollUtil.join(subregionList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()), ","))
                .eq(AgentUserInfoEntity::getAgentCode, update.getAgentCode())
                .update();
        //更新目标代理人信息
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(AgentUserInfoEntity::getAreaManagerRegion, CollUtil.join(replaceAreaManagerRegionList.stream().filter(
                    StrUtil::isNotBlank).distinct().collect(Collectors.toList()), ","))
                .set(AgentUserInfoEntity::getSubregion, CollUtil.join(replaceSubregionList.stream().filter(
                    StrUtil::isNotBlank).distinct().collect(Collectors.toList()), ","))
                .eq(AgentUserInfoEntity::getAgentCode, update.getReplaceAgentCode())
                .update();
        //同时批量迁移所有原区域保险专家名下的客户
        if (StatusEnum.NORMAL.getCode().intValue() == update.getIsTransferCustomer().intValue()) {
            log.info("同时批量迁移所有原区域保险专家名下的客户");
            asyncService.transferCustomer(update);
        }
        //同时批量迁移当前区域所有保单至新区域保险专家
        if (StatusEnum.NORMAL.getCode().intValue() == update.getIsTransferPolicy().intValue()) {
            log.info("同时批量迁移当前区域所有保单至新区域保险专家");
            asyncService.transferPolicy(update);
        }
        if (StatusEnum.NORMAL.getCode().intValue() == update.getIsTransferCustomerAndPolicy().intValue()) {
            log.info("同时批量迁移所有原区域保险专家名下的客户,同时批量迁移当前区域所有保单至新区域保险专家");
            asyncService.transferCustomer(update);
            asyncService.transferPolicy(update);
        }
    }




    /**
     * 获取区域代理人列表
     *
     * @return
     */
    @Override
    public List<AreaManagerRegionAgentOut> findAreaManagerRegionAgentList() {
        List<AreaManagerRegionAgentOut> resultList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentType, AgentTypeEnum.AREA_MANAGER.getKey())
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                .list().stream().map(m -> {
                    AreaManagerRegionAgentOut out = new AreaManagerRegionAgentOut();
                    out.setAgentCode(m.getAgentCode());
                    out.setLabel(m.getAgentName() + "-" + m.getBusinessCode());
                    return out;
                }).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 获取区域列表
     *
     * @param agentCode 代理人编码
     * @return
     */
    @Override
    public List<SubregionListOut> findRegionList(String agentCode) {
        List<AgentUserInfoEntity> agentList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getQuitStatus, StatusEnum.INVALID.getCode())
                .list();
        //获取不是当前代理人的区域
        List<String> regionList = agentList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getAreaManagerRegion()) && !f.getAgentCode().equals(agentCode))
                .map(m -> StrUtil.split(m.getAreaManagerRegion(), ",")
                ).flatMap(Arrays::stream).collect(Collectors.toList());
        //获取不是当前代理人的片区
        List<String> subregionList = agentList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getSubregion()) && !f.getAgentCode().equals(agentCode))
                .map(m -> StrUtil.split(m.getSubregion(), ",")
                ).flatMap(Arrays::stream).collect(Collectors.toList());
        //获取只选了区域没有选择片区的区域
        List<String> notSubregionList = agentList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getAreaManagerRegion()) && StrUtil.isBlank(f.getSubregion()) && !f.getAgentCode().equals(agentCode))
                .map(m -> StrUtil.split(m.getAreaManagerRegion(), ",")
                ).flatMap(Arrays::stream).collect(Collectors.toList());
        //获取区域列表
        List<DicCacheHelper.DicEntity> referrerRegionList = DicCacheHelper.getSons("REFERRER_REGION");
        List<SubregionListOut> resultList = new ArrayList<>();
        referrerRegionList.forEach(action -> {
            SubregionListOut result = new SubregionListOut();
            result.setLabel(action.getValue());
            result.setValue(action.getKey());
            result.setDisabled(false);
            //1.判断这个区域下是否存在片区
            List<DicCacheHelper.DicEntity> subList = DicCacheHelper.getSons(action.getKey());
            if (CollUtil.isEmpty(subList)) {
                //2.不存在片区,判断这个区域是否被人领走
                if (regionList.contains(action.getKey())) {
                    result.setDisabled(true);
                }
            } else {
                //3.判断他的分区是否被全部选走,存在没有被选走的分区 可以选择区域
                result.setDisabled(true);
                subList.forEach(sub -> {
                    if (!subregionList.contains(sub.getKey())) {
                        result.setDisabled(false);
                        return;
                    }
                });
                //4.判断只选了区域没有选择片区的区域是否存在
               /* if (!notSubregionList.contains(action.getKey())) {
                    result.setDisabled(false);
                }*/
            }
            resultList.add(result);
        });
        return resultList;
    }

    /**
     * 获取区域下片区列表
     *
     * @param vo 区域信息和代理人信息
     * @return
     */
    @Override
    public List<SubregionListOut> findSubregionList(SubregionListVo vo) {
        if (CollUtil.isEmpty(vo.getRegionCodeList())) {
            return Collections.emptyList();
        }
        //获取已经被选择的片区
        List<String> subregionList = new LambdaQueryChainWrapper<>(baseMapper)
                .list().stream()
                .filter(f -> StrUtil.isNotBlank(f.getSubregion()) && !f.getAgentCode().equals(vo.getAgentCode()))
                .map(m -> StrUtil.split(m.getSubregion(), ",")
                ).flatMap(Arrays::stream).collect(Collectors.toList());
        List<SubregionListOut> resultList = new ArrayList<>();
        vo.getRegionCodeList().forEach(action -> {
            List<DicCacheHelper.DicEntity> list = DicCacheHelper.getSons(action);
            if (CollUtil.isEmpty(list)) {
                return;
            }
            for (DicCacheHelper.DicEntity dic : list) {
                SubregionListOut result = new SubregionListOut();
                result.setValue(dic.getKey());
                result.setLabel(dic.getValue());
                result.setDisabled(false);
                if (subregionList.contains(dic.getKey())) {
                    result.setDisabled(true);
                }
                resultList.add(result);
            }
        });
        return resultList;
    }

    /**
     * 获取当前代理人的区域列表
     *
     * @param agentCode
     * @return
     */
    @Override
    public List<SubregionListOut> findAreaManagerRegionAgentListByAgentCode(String agentCode) {
        AgentUserInfoEntity agentUserInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentCode, agentCode).one();
        if (agentUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("用户信息不存在"));
        }
        return StrUtil.split(agentUserInfo.getAreaManagerRegion(), ',').stream().map(m -> {
            SubregionListOut result = new SubregionListOut();
            result.setDisabled(false);
            result.setValue(m);
            result.setLabel(DicCacheHelper.getValue(m));
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 获取当前代理人的片区列表
     *
     * @param vo
     * @return
     */
    @Override
    public List<SubregionListOut> findSubregionListByAgentCode(SubregionListByAgentCodeVo vo) {
        AgentUserInfoEntity agentUserInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(AgentUserInfoEntity::getAgentCode, vo.getAgentCode()).one();
        if (agentUserInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("用户信息不存在"));
        }
        String subregion = agentUserInfo.getSubregion();
        if (StrUtil.isBlank(subregion)) {
            return Collections.emptyList();
        }
        List<String> subregionList = StrUtil.split(subregion, ',');
        //获取区域下的片区列表
        return DicCacheHelper.getSons(vo.getAreaManagerRegion()).stream()
                .filter(f -> subregionList.contains(f.getKey()))
                .map(m -> {
                    SubregionListOut result = new SubregionListOut();
                    result.setDisabled(false);
                    result.setLabel(m.getValue());
                    result.setValue(m.getKey());
                    return result;
                }).collect(Collectors.toList());
    }

    @Override
    public List<FastAgentUserInfo> listFastAgentUserInfo(List<String> agentCodeList) {
        return baseMapper.listFastAgentUserInfo(agentCodeList);
    }

    @Override
    public List<FastAgentUserInfo> listByBusinessCode(List<String> businessCode) {
        return baseMapper.listByBusinessCode(businessCode);
    }

    @Override
    public FastAgentUserInfo queryOne(String agentCode) {
        return baseMapper.queryOne(agentCode);
    }

    @Override
    public List<String> findOnlineAgentCodeList(String startTime, String endTime) {
        return baseMapper.findOnlineAgentCodeList(startTime, endTime);
    }
}
