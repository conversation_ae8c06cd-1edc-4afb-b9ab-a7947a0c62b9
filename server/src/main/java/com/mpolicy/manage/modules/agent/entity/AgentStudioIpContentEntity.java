package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人工作室IP内容
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 13:47:00
 */
@TableName("agent_studio_ip_content")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentStudioIpContentEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * ip内容编码
     */
    @ApiModelProperty(value = "ip内容编码")
    private String ipContentCode;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "经纪人工号", required = true)
    @NotBlank(message = "经纪人工号不能为空")
    private String agentCode;
    /**
     * 内容平台
     */
    @ApiModelProperty(value = "内容平台", required = true)
    @NotBlank(message = "内容平台不能为空")
    private String platform;
    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期", required = true)
    @Pattern(regexp = "(19|20)\\d{2}-(0[0-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])", message = "发布日期格式错误[yyyy-MM-dd]")
    @NotBlank(message = "发布日期不能为空")
    private String releaseDate;
    /**
     * 客户可见 1:可见;0:不可见
     */
    @ApiModelProperty(value = "客户可见 1:可见;0:不可见", required = true)
    @NotNull(message = "客户可见状态不能为空")
    @Range(min = 0, max = 1, message = "客户可见状态只能为1和0")
    private Integer isVisible;
    /**
     * 内容标题
     */
    @ApiModelProperty(value = "内容标题", required = true)
    @NotBlank(message = "内容标题不能为空")
    private String title;
    /**
     * 内容正文
     */
    @ApiModelProperty(value = "内容正文")
    private String contentText;
    /**
     * 内容摘要
     */
    @ApiModelProperty(value = "内容摘要", hidden = true)
    private String contentDigest;
    /**
     * 正文长度
     */
    @ApiModelProperty(value = "正文长度", hidden = true)
    private Integer contentLength;
    /**
     * 缩略图链接
     */
    @ApiModelProperty(value = "缩略图链接", hidden = true)
    private String imagePath;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(hidden = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁 更新时必须")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

}
