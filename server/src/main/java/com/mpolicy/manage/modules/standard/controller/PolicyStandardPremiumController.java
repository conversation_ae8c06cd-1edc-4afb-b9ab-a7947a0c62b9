package com.mpolicy.manage.modules.standard.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.standard.service.PolicyStandardPremiumService;
import com.mpolicy.manage.modules.standard.vo.StandardPremiumProductDetail;
import com.mpolicy.manage.modules.standard.vo.StandardPremiumProductInfo;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 保单标准保费产品控制器
 *
 * <AUTHOR>
 * @date 2024-03-26 13:39:42
 */
@RestController
@RequestMapping("standard/premium")
@Api(tags = "保单标准保费")
public class PolicyStandardPremiumController {

    @Autowired
    private PolicyStandardPremiumService policyStandardPremiumService;


    /**
     * 标准保费产品信息列表
     *
     * @param params 查询条件
     * @return 分页-保单标准保费产品配置基本信息
     * <AUTHOR>
     * @since 2024/3/26 2:02 PM
     */
    @ApiOperation(value = "标准保费产品信息列表", notes = "标准保费产品信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "productCode", dataType = "String", value = "险种编码"),
            @ApiImplicitParam(paramType = "query", name = "productName", dataType = "String", value = "险种名称"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保司编码"),
            @ApiImplicitParam(paramType = "query", name = "standardPremiumStatus", dataType = "int", value = "标准保费状态;0待生效、1生效中、2已失效"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"policy:risk:standard:all"})
    public Result<PageUtils<StandardPremiumProductInfo>> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        PageUtils<StandardPremiumProductInfo> page = policyStandardPremiumService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 根据标准保费主编码获取详细信息
     *
     * @param standardPremiumCode 标准保费主编码
     * @return 保单标准保费产品配置详细信息
     * <AUTHOR>
     * @since 2024/3/26 2:01 PM
     */
    @ApiOperation(value = "标准保费配置详细信息", notes = "标准保费配置详细信息")
    @GetMapping("/detail")
    @RequiresPermissions(value = {"policy:risk:standard:all"})
    public Result<StandardPremiumProductDetail> info(@ApiParam(value = "标准保费主编码", required = true) @RequestParam("standardPremiumCode") String standardPremiumCode) {
        StandardPremiumProductDetail result = policyStandardPremiumService.queryDetail(standardPremiumCode);
        return Result.success(result);
    }

    /**
     * 删除标准保费配置详细
     *
     * @param standardPremiumCode 标准保费主编码
     * @return 保单标准保费产品配置详细信息
     * <AUTHOR>
     * @since 2024/3/26 2:01 PM
     */
    @PostMapping("/remove")
    @RequiresPermissions(value = {"policy:risk:standard:all"})
    @ApiOperation(value = "删除标准保费配置详细", notes = "删除标准保费配置详细")
    public Result<String> remove(@ApiParam(value = "标准保费主编码", required = true) @RequestParam("standardPremiumCode") String standardPremiumCode) {
        policyStandardPremiumService.removeStandardPremiumCode(standardPremiumCode);
        return Result.success(Constant.DEFAULT_SUCCESS);
    }
}
