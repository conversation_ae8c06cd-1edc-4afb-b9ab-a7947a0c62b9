package com.mpolicy.manage.modules.insure.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保实名认证人脸识别白名单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-17 14:36:49
 */
@TableName("insure_uesr_white_roster")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureUserWhiteRosterEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 身份证号
	 */
	private String cardNo;
	/**
	 * 名单类型 0：实名认证 1：人脸识别
	 */
	private Integer rosterType;
	/**
	 * 影响平台
	 */
	private String platformType;
	/**
	 * 有效开始时间
	 */
	private Date startDate;
	/**
	 * 有效结束时间
	 */
	private Date endDate;
	/**
	 * 是否长期有效：0：是，1：否
	 */
	private Integer isLongTerm;
	/**
	 * 身份证照片
	 */
	private String cardFileCode;
	/**
	 * 识别视频
	 */
	private String faceVideoCode;
	/**
	 * 是否启用 0:停用;1:启用
	 */
	private Integer isStatus;
	/**
	 * 是否导入 0:不是;1:是
	 */
	private Integer isImport;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
