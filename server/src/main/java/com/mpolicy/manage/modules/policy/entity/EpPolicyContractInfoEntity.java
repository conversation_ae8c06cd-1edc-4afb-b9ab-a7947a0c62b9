package com.mpolicy.manage.modules.policy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保险合同信息
 *
 * <AUTHOR>
 * @email 张超东@xiaowhale.onaliyun.com
 * @date 2021-12-21 15:54:47
 */
@TableName("ep_policy_contract_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EpPolicyContractInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 合同编码
     */
    private String contractCode;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 上一年保单号
     */
    private String sourcePolicyNo;
    /**
     * 家庭保单号
     */
    private String familyPolicyNo;
    /**
     * 投保单号
     */
    private String applicantPolicyNo;
    /**
     * 保单类型 0:预收件；1:承保件
     */
    private Integer policyType;
    /**
     * 保单产品类型(个、团、车、财)
     */
    private String policyProductType;
    /**
     * 保险公司编码
     */
    private String companyCode;
    /**
     * 来源平台
     */
    private String sourcePlatform;
    /**
     * 主代理人编码
     */
    private String agentCode;
    /**
     * 主代理人机构编码
     */
    private String orgCode;
    /**
     * 保险公司名称
     */
    private String companyName;
    /**
     * 保单主险编码
     */
    private String mainProductCode;
    /**
     * 保单主险名称
     */
    private String mainProductName;
    /**
     * 保单名称
     */
    private String portfolioName;
    /**
     * 组合类型
     */
    private String portfolioType;
    /**
     * 保单下所有险种类型，半角逗号分隔
     */
    private String productsType;
    /**
     * 投保人姓名
     */
    private String applicantName;

    /**
     * 被保人姓名
     */
    private String insuredName;
    /**
     * 保单下所有被保人集合，顿号分隔
     */
    private String insuredsName;
    /**
     * 保单状态
     */
    private String policyStatus;
    /**
     * 管理后台保单状态
     */
    private String adminPolicyStatus;
    /**
     * 总保费
     */
    private BigDecimal premiumTotal;
    /**
     * 保单来源
     */
    private String policySource;
    /**
     * 展示模式 0:托管;1:保单录入
     */
    private Integer showModel;
    /**
     * 三方流水号
     */
    private String serialNumber;
    /**
     * 销售方式
     */
    private Integer salesType;
    /**
     * 销售平台
     */
    private String salesPlatform;
    /**
     * 保障期间类型 主险
     */
    private String insuredPeriodType;
    /**
     * 保障时长 主险
     */
    private Integer insuredPeriod;
    /**
     * 主险 缴费方式-年交/半年交/季交/月交/趸交/不定期交/短险一次交清
     */
    private String periodType;
    /**
     * 缴费期间类型 主险
     */
    private String paymentPeriodType;
    /**
     * 缴费时长 主险
     */
    private Integer paymentPeriod;
    /**
     * 保单保额 主险最大
     */
    private BigDecimal policyCoverage;
    /**
     * 投保时间
     */
    private Date applicantTime;
    /**
     * 交单时间
     */
    private Date orderTime;
    /**
     * 承保时间
     */
    private Date approvedTime;
    /**
     * 生效时间
     */
    private Date enforceTime;
    /**
     * 保障截至时间
     */
    private Date insuredPeriodEndTime;
    /**
     * 退保时间
     */
    private Date surrenderTime;
    /**
     * 犹豫期(天)
     */
    private Integer hesitatePeriod;
    /**
     * 过犹豫期时间
     */
    private Date overHesitatePeriod;
    /**
     * 渠道推荐人类型 0:推荐人 1:代理人
     */
    private Integer referrerType;
    /**
     * 渠道推荐人编码
     */
    private String referrerCode;
    /**
     * 渠道推荐人姓名
     */
    private String referrerName;
    /**
     * 渠道推荐人分支编码
     */
    private String channelBranchCode;
    /**
     * 渠道推荐人分支名称
     */
    private String channelBranchName;
    /**
     * 销售渠道编码
     */
    private String channelCode;
    /**
     * 销售渠道名称
     */
    private String channelName;
    /**
     * 小鲸站长姓名
     */
    private String propagandistName;
    /**
     * 小鲸站长身份证号
     */
    private String propagandistIdCard;
    /**
     * 三方平台编码
     */
    private String thirdPartyPlatformCode;
    /**
     * 三方客户编码
     */
    private String thirdPartyCustomerCode;
    /**
     * 小程序客户编码
     */
    private String miniAppCustomerCode;
    /**
     * 保单渠道权限字段
     */
    private String policyChannelBranchCode;
    /**
     * 佣金发放状态 0:不参与发放、1:已发放、2:未发放、3:发放失败
     */
    private Integer settlementStatus;
    /**
     * 是否自保件 0:否;1:是
     */
    private Integer selfPreservation;
    /**
     * 是否孤儿单 0:否;1:是
     */
    private Integer orphanSingle;
    /**
     * 是否为完整保单 0:不完整;1:完整;2:暂存
     */
    private Integer intact;
    /**
     * 是否录入回访 0:否;1:是;2:不需要
     */
    private Integer isRevisit;
    /**
     * 是否录入回执 0:否;1:是;2:不需要
     */
    private Integer isReceipt;
    /**
     * 备注
     */
    private String remark;
    /**
     * 赠险标识：0：非赠险、1：赠险
     */
    private Integer giveFlag;

    /**
     * 销售渠道编码-台账
     */
    private Integer superviseChannelCode;

    /**
     * 分销渠道编码
     */
    private String channelDistributionCode;
    /**
     * 是否签署客户告知书
     */
    private Integer offlineProductSignStatus;
    /**
     * 客户经理
     */
    private String customerManagerCode;

    /**
     * 客户经理渠道编码
     */
    private String customerManagerChannelCode;
    /**
     * 客户经理所属分支机构
     */
    private String customerManagerOrgCode;
    /**
     * 客户经理渠道机构编码
     */
    private String customerManagerChannelOrgCode;
    /**
     * 客户经理督导
     */
    private String customerManagerSupervisor;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
