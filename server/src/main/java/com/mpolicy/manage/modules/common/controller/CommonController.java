package com.mpolicy.manage.modules.common.controller;

import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.common.AdminCommonKeys;
import com.mpolicy.manage.modules.common.event.factory.SelectFactory;
import com.mpolicy.manage.modules.common.event.handler.SelectHandler;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.common.model.SelectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "公共服务")
@RestController
@RequestMapping("common")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonController {

    @Autowired
    private IRedisService redisService;


    /**
     * 获取下拉列表
     *
     * @param vo
     * @return
     */
    @GetMapping("findSelectList")
    @ApiOperation(value = "获取搜索列表", notes = "获取搜索列表", httpMethod = "GET")
    public Result<List<SelectOut>> findSelectList(SelectVo vo) {

        SelectHandler handler = SelectFactory.getInvoke(vo.getSelectType());
        List<SelectOut> selectList = handler.findSelectList(vo);
        return Result.success(selectList);
    }

    @GetMapping("refreshRequest")
    @ApiOperation(value = "刷新请求", notes = "刷新请求", httpMethod = "GET")
    public Result refreshRequest() {
        return Result.success();
    }

    /**
     * 查询处理状态
     *
     * @param key
     * @return
     */
    @GetMapping("findHandleStatus/{key}")
    @ApiOperation(value = "查询处理状态", notes = "查询处理状态", httpMethod = "GET")
    public Result findHandleStatus(@PathVariable(required = false) @NotBlank(message = "查询的Key不能为空") String key) {
        String result = redisService.get(AdminCommonKeys.HANDLE_STATUS, key, String.class);
        return Result.success(result);
    }
}
