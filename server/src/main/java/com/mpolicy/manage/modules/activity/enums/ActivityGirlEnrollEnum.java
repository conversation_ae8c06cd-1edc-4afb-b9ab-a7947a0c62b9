package com.mpolicy.manage.modules.activity.enums;

import lombok.Getter;

/**
 * ClassName: ActivityGirlEnrollEnum
 * Description: 女神节报名状态枚举
 * date: 2023/2/23 11:00
 *
 * <AUTHOR>
 */
@Getter
public enum ActivityGirlEnrollEnum {
    VALID(1,"报名成功"),
    CANCEL(2,"取消报名");

    private Integer status;

    private String desc;

    ActivityGirlEnrollEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }
}
