package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("bi_group_agent_entrust_policy")
public class BiGroupAgentEntrustPolicyEntity implements Serializable {
    private static final long serialVersionUID = 7405821466923318291L;


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("agent_code")
    private String agentCode;

    @TableField("agent_name")
    private String agentName;

    @TableField("business_code")
    private String businessCode;

    @TableField("group_name")
    private String groupName;

    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Version
    @TableField(value = "revision", fill = FieldFill.INSERT)
    private Integer revision;

}
