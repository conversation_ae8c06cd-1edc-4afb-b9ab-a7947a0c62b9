package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * 团险分单数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/12 14:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PolicyGroupSubVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 主附被保人标识
     */
    @ApiModelProperty(value = "主附被保人标识")
    private String mainFlag;
    /**
     * 对应主被保人
     */
    @ApiModelProperty(value = "对应主被保人")
    private String mainInsuredName;
    /**
     * 与主被保人关系
     */
    @ApiModelProperty(value = "与主被保人关系")
    private String firstInsuredRelation;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "被保人性别 0：女 1：男")
    private Integer insuredGender;
    /**
     * 被保人出生日期
     */
    @ApiModelProperty(value = "被保人出生日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date insuredBirthday;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "被保人证件类型")
    private String insuredIdType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "被保人证件号码")
    private String insuredIdCard;
    /**
     * 保险计划编码
     */
    @ApiModelProperty(value = "保险计划编码")
    private String productCode;


}
