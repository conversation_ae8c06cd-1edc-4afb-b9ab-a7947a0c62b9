package com.mpolicy.manage.modules.order.dao;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.order.entity.OrderBaseInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.order.vo.RecallList;


/**
 * 客户订单信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-16 10:21:13
 */
public interface OrderBaseInfoDao extends BaseMapper<OrderBaseInfoEntity> {

    /**
     * 获取回溯信息
     */
    IPage<RecallList> findPageList(Page<RecallList> page, RecallList vo);
	
}
