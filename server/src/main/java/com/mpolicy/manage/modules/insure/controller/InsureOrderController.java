package com.mpolicy.manage.modules.insure.controller;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.insure.service.InsureOrderInfoService;
import com.mpolicy.manage.modules.insure.vo.*;
import com.mpolicy.manage.modules.settlement.vo.policy.SettlementReconcilePolicyInfo;
import com.mpolicy.order.common.order.PolicyGroupOrderDataOut;
import com.mpolicy.order.common.order.PolicyOrderDataOut;
import com.mpolicy.web.common.validator.ValidatorUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;


/**
 * 投保订单管理控制器
 *
 * <AUTHOR>
 * @date 2022-05-29 11:14:35
 */
@RestController
@RequestMapping("insure_order/manager")
@Api(tags = "投保订单管理")
@Slf4j
public class InsureOrderController {

    /**
     * 投保订单service
     */
    @Autowired
    private InsureOrderInfoService insureOrderInfoService;


    /**
     * <p>
     * 投保订单列表分页查询
     * </p>
     */
    @ApiOperation(value = "投保订单列表分页查询", notes = "投保订单列表分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "insureOrderCode", dataType = "String", value = "订单号"),
            @ApiImplicitParam(paramType = "query", name = "customerCode", dataType = "String", value = "出单客户编号"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", name = "holderName", dataType = "String", value = "投保人姓名或投保人证件号码"),
            @ApiImplicitParam(paramType = "query", name = "insuredName", dataType = "String", value = "被保人姓名或被保人证件号码"),
            @ApiImplicitParam(paramType = "query", name = "holderMobile", dataType = "String", value = "投保人手机号"),
            @ApiImplicitParam(paramType = "query", name = "policyCode", dataType = "String", value = "保单号或家庭单号"),
            @ApiImplicitParam(paramType = "query", name = "ruralProxyOrderType", dataType = "int", value = "是否四级分销单"),
            @ApiImplicitParam(paramType = "query", name = "insureOrderStatus", dataType = "int", value = "订单状态"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道"),
            @ApiImplicitParam(paramType = "query", name = "referrerCode", dataType = "String", value = "渠道推荐人"),
            @ApiImplicitParam(paramType = "query", name = "portfolioGroup", dataType = "String", value = "订单类型"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "商品编码"),
            @ApiImplicitParam(paramType = "query", name = "agentCode", dataType = "String", value = "代理人"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"policy:order:all","dev:lostpolicy:all"},logical = Logical.OR)
    public Result<PageUtils<InsureOrderList>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("获取投保订单列表，查询条件={}", params);
        PageUtils<InsureOrderList> page = insureOrderInfoService.queryInsureOrderListByPage(params);
        return Result.success(page);
    }

    /**
     * 获取投保订单详情
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.common.result.Result<com.mpolicy.order.common.order.PolicyOrderDataOut> }
     * <AUTHOR>
     * @since 2022/5/29
     */
    @ApiOperation(value = "投保订单详情", notes = "投保订单详情")
    @GetMapping("/detail/{insureOrderCode}")
    @RequiresPermissions(value = {"policy:order:all"})
    public Result<PolicyOrderDataOut> detail(@PathVariable("insureOrderCode") @ApiParam(name = "insureOrderCode", value = "投保订单唯一编号") String insureOrderCode) {
        return Result.success(insureOrderInfoService.queryInsureOrderDetail(insureOrderCode));
    }

    /**
     * 获取【团险】投保订单详情
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.common.result.Result<com.mpolicy.order.common.order.PolicyOrderDataOut> }
     * <AUTHOR>
     * @since 2022/5/29
     */
    @ApiOperation(value = "【团险】投保订单详情", notes = "投保订单详情")
    @GetMapping("/group/detail/{insureOrderCode}")
    public Result<PolicyGroupOrderDataOut> groupDetail(@PathVariable("insureOrderCode") @ApiParam(name = "insureOrderCode", value = "投保订单唯一编号") String insureOrderCode) {
        return Result.success(insureOrderInfoService.groupDetail(insureOrderCode));
    }

    /**
     * 投保订单回溯信息获取
     *
     * @param insureOrderCode: 投保订单唯一编号
     * @return {@link com.mpolicy.common.result.Result<com.mpolicy.manage.modules.insure.vo.InsureRecallOut> }
     * <AUTHOR>
     * @since 2022/5/29
     */
    @ApiOperation(value = "投保订单回溯信息", notes = "投保订单回溯信息")
    @GetMapping("/recall_data/{insureOrderCode}")
    @RequiresPermissions(value = {"policy:order:all"})
    public Result<InsureRecallOut> recallData(@PathVariable("insureOrderCode") @ApiParam(name = "insureOrderCode", value = "投保订单唯一编号") String insureOrderCode) {
        return Result.success(insureOrderInfoService.queryInsureRecallData(insureOrderCode));
    }

    /**
     * 添加STS文件地址
     *
     * @param insureOrderCode:
     * @param stsFilePath:
     * @return : com.mpolicy.common.result.Result
     * <AUTHOR>
     * @date 2022/11/7 16:47
     */
    @ApiOperation(value = "添加STS文件地址", notes = "添加STS文件地址")
    @PostMapping("/recall_update/{insureOrderCode}")
    public Result changeRecallSts(
            @ApiParam(name = "insureOrderCode", value = "订单编号", required = true)
            @PathVariable("insureOrderCode") String insureOrderCode,
            @ApiParam(name = "stsFilePath", value = "www.sts.com/qew/12345 ", required = true)
            @RequestParam("stsFilePath") String stsFilePath) {
        insureOrderInfoService.changeRecallSts(insureOrderCode, stsFilePath);
        return Result.success();
    }

    /**
     * 订单人工审核
     *
     * @param input 审核信息
     * @return
     */
    @ApiOperation(value = "订单人工审核", notes = "订单人工审核")
    @PostMapping("/personReview")
    public Result<String> personReview(@RequestBody @ApiParam(name = "policyNoList", required = true, value = "保单号列表") InsurePersonReviewInput input) {
        ValidatorUtils.validateEntity(input);
        insureOrderInfoService.personReview(input);
        return Result.success();
    }

    /**
     * 订单发起撤单
     *
     * @return : com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/7/5 11:05
     */
    @ApiOperation(value = "订单发起撤单", notes = "订单发起撤单")
    @PostMapping("/order/refund")
    public Result<String> refund(@RequestBody @ApiParam(name = "input", required = true, value = "订单撤单信息开始") InsureOrderRefundInfo input) {
        insureOrderInfoService.orderRefund(input);
        return Result.success();
    }

    /**
     * 订单列表导出
     *
     * @param response response返回
     * @param params   map参数
     * @return
     */
    @ApiOperation(value = "订单列表导出", notes = "订单列表导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "insureOrderCode", dataType = "String", value = "订单号"),
            @ApiImplicitParam(paramType = "query", name = "customerCode", dataType = "String", value = "出单客户编号"),
            @ApiImplicitParam(paramType = "query", name = "companyCode", dataType = "String", value = "保险公司编码"),
            @ApiImplicitParam(paramType = "query", name = "holderName", dataType = "String", value = "投保人姓名或投保人证件号码"),
            @ApiImplicitParam(paramType = "query", name = "insuredName", dataType = "String", value = "被保人姓名或被保人证件号码"),
            @ApiImplicitParam(paramType = "query", name = "holderMobile", dataType = "String", value = "投保人手机号"),
            @ApiImplicitParam(paramType = "query", name = "policyCode", dataType = "String", value = "保单号或家庭单号"),
            @ApiImplicitParam(paramType = "query", name = "ruralProxyOrderType", dataType = "int", value = "是否四级分销单"),
            @ApiImplicitParam(paramType = "query", name = "insureOrderStatus", dataType = "int", value = "订单状态"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道"),
            @ApiImplicitParam(paramType = "query", name = "referrerCode", dataType = "String", value = "渠道推荐人"),
            @ApiImplicitParam(paramType = "query", name = "portfolioGroup", dataType = "String", value = "订单类型"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "商品编码"),
            @ApiImplicitParam(paramType = "query", name = "agentCode", dataType = "String", value = "代理人"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions(value = {"policy:order:all"})
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        log.info("订单导出生成 .....开始");
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("订单.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, InsureOrderList.class);
            sheet.setSheetName("sheet1");

            int page = 1;
            params.put("limit", "2000");
            int  size = 0;
            while (true) {
                // 分批查询
                params.put("page", String.valueOf(page));
//                List<insureOrderInfoOut> list = insureOrderInfoService.queryInsureOrderList(params);
                List<InsureOrderList> list = insureOrderInfoService.queryInsureOrderListByPage(params).getList();
                if (!list.isEmpty()) {
                    writer.write(list, sheet);
                    log.info("订单导出生成，page={}, dataSize={}", page, list.size());
                    // 赋值maxId ： 需要+1
                    page++;
                    size += list.size();
                } else {
                    log.info("订单导出生成，退出构建，执行导出");
                    break;
                }
                if (size >= 10_000) {
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("订单导出生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.success();
    }

    /**
     * 订单列表导出
     *
     * @param response response返回
     * @param params   map参数
     * @return
     */
    @ApiOperation(value = "精简订单列表导出", notes = "精简订单列表导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "insureOrderStatus", dataType = "int", value = "订单状态"),
            @ApiImplicitParam(paramType = "query", name = "commodityCode", dataType = "String", value = "商品编码"),
            @ApiImplicitParam(paramType = "query", name = "startDate", dataType = "String", value = "开始时间"),
            @ApiImplicitParam(paramType = "query", name = "endDate", dataType = "String", value = "结束时间")
    })
    @GetMapping("/ZN/export")
    @RequiresPermissions(value = {"policy:order:all"})
    public Result anExport(HttpServletResponse response, @RequestParam Map<String, Object> params) throws IOException {
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        log.info("订单导出生成 .....开始");
        stopWatch.start();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            response.addHeader("Content-Disposition", "filename=" + URLUtil.encode("订单.xlsx"));
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
            // 设置SHEET名称
            Sheet sheet = new Sheet(1, 0, insureOrderInfoProOut.class);
            sheet.setSheetName("sheet1");

            int page = 1;
            params.put("limit", "2000");
            while (true) {
                // 分批查询
                params.put("page", String.valueOf(page));
                List<insureOrderInfoProOut> list = insureOrderInfoService.znQueryInsureOrderList(params);
                if (!list.isEmpty()) {
                    writer.write(list, sheet);
                    log.info("订单导出生成，page={}, dataSize={}", page, list.size());
                    // 赋值maxId ： 需要+1
                    page++;
                } else {
                    log.info("订单导出生成，退出构建，执行导出");
                    break;
                }
            }
            // 设置结束
            stopWatch.stop();
            // 获取执行毫秒值
            long millis = stopWatch.getTotalTimeMillis();
            log.info("订单导出生成完成 .....耗时={}", millis);
            writer.finish();
            out.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.success();
    }
}
