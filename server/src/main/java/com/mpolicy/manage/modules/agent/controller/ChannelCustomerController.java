package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.enums.ReferrerQueryTypeEnum;
import com.mpolicy.manage.modules.agent.service.ChannelCustomerService;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelCustomerVo;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


/**
 * 渠道客户
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-08-09 15:18:12
 */
@Validated
@RestController
@RequestMapping("agent/channelCustomer")
@Api(tags = "渠道客户")
public class ChannelCustomerController {

    @Autowired
    private ChannelCustomerService channelCustomerService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取渠道客户信息列表", notes = "获取渠道客户信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "customerRealName", dataType = "String", value = "客户实名", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "referrerName", dataType = "String", value = "绑定推荐人姓名", example = "张三"),
            @ApiImplicitParam(paramType = "query", name = "referrerWno", dataType = "String", value = "绑定推荐人工号", example = "ZHNX02147"),
            @ApiImplicitParam(paramType = "query", name = "referrerRegion", dataType = "String", value = "推荐人所属区域", example = "referrerRegion:1")
    })
    @GetMapping("/list/{applicationCode}")
    @RequiresPermissions(value = {"channel:zhnx:customer:all"})
    public Result<PageUtils<ChannelCustomerVo>> list(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode,
            @ApiParam(value = "分页参数", hidden = true)
            @RequestParam Map<String, Object> params) {
        PageUtils<ChannelCustomerVo> page = channelCustomerService.queryPage(applicationCode, params);
        return Result.success(page);
    }

    /**
     * 查询渠道客户数量
     */
    @ApiOperation(value = "查询渠道客户数量", notes = "查询渠道客户数量")
    @GetMapping("/totalAndEffective/{applicationCode}")
    public Result<Integer> totalAndEffective(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode) {
        Integer total = channelCustomerService.getTotal(applicationCode);

        return Result.success(total);
    }

    /**
     * 变更推荐人
     */
    @ApiOperation(value = "变更推荐人", notes = "变更推荐人")
    @PostMapping("/updateReferrer")
    public Result updateReferrer(
            @ApiParam(value = "客户编码", required = true)
            @RequestParam("customerCode") @NotBlank(message = "客户编码不能为空") String customerCode,
            @ApiParam(value = "推荐人编码")
            @RequestParam("referrerCode") String referrerCode) {
        channelCustomerService.updateReferrer(customerCode, referrerCode);

        return Result.success();
    }

    /**
     * 获取推荐人列表
     */
    @ApiOperation(value = "获取推荐人列表", notes = "获取推荐人列表")
    @GetMapping("/queryReferrer/{applicationCode}")
    public Result<List<ChannelApplicationReferrerVo>> queryReferrer(
            @ApiParam(value = "应用编码", required = true)
            @PathVariable("applicationCode") String applicationCode,
            @ApiParam(value = "类型 NAME：姓名；WORK_NO：工号", required = true)
            @RequestParam("referrerQueryTypeEnum") @NotNull(message = "类型不能为空") ReferrerQueryTypeEnum referrerQueryTypeEnum,
            @ApiParam(value = "搜索内容", required = true)
            @RequestParam("text") @NotBlank(message = "搜索内容不能为空") String text) {
        List<ChannelApplicationReferrerVo> result = channelCustomerService.queryReferrer(applicationCode, referrerQueryTypeEnum, text);

        return Result.success(result);
    }

}
