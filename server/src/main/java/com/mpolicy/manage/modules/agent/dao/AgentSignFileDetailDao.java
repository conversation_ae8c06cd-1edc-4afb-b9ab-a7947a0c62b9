package com.mpolicy.manage.modules.agent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileDetailEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonDetailPageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonFilePageList;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 代理人签署文件详细信息
 * 
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileDetailDao extends BaseMapper<AgentSignFileDetailEntity> {

    /**
     * 分页查询人员签署文件详情
     * @param page 分页参数
     * @param input 请求参数
     * @return
     */
    IPage<AgentPersonFilePageList> personFileList(@Param("page") IPage page, @Param("input") Map<String, Object> input);

    /**
     * 分页查询具体人员文件签署详情
     * @param page
     * @param input
     * @return
     */
    IPage<AgentPersonDetailPageList> detailPersonFileList(@Param("page") IPage page, @Param("input") Map<String, Object> input);

    List<AgentSignFileDetailEntity> queryAllEnabledFile(@Param("agentCode") String agentCode);
}
