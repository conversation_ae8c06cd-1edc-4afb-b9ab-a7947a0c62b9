package com.mpolicy.manage.modules.agent.vo.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TrainSignListOut implements Serializable {
    private static final long serialVersionUID = 5586619978460144511L;

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "考核状态0:未处理 1:通过 2:未通过")
    private Integer assessmentResult;

    @ApiModelProperty(value = "签到状态:0:未处理 1:签到  2:未签到")
    private Integer signStatus;

    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = " 代理人编码")
    private String agentCode;

    @ApiModelProperty(value = "代理人业务编码")
    private String businessCode;
}
