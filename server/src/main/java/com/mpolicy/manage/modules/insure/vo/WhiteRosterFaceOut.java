package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/18 17:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WhiteRosterFaceOut extends BaseRowModel {
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String realName;
    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String cardNo;
    /**
     * 有效开始时间
     */
    @ExcelProperty(value = "有效起始日期")
    private String startDate;

    /**
     * 有效结束时间
     */
    @ExcelProperty(value = "有效终止日期")
    private String endDate;
}
