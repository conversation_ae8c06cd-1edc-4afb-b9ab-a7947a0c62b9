package com.mpolicy.manage.modules.tools.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mpolicy.manage.modules.tools.entity.ToolsModelChangeLogEntity;
import com.mpolicy.manage.modules.tools.enums.ToolsModelChangeLogTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.function.Function;

/**
 * @author: yangdonglin
 * @create: 2023/12/1 15:06
 * @description: 模型变更记录Vo
 */
@EqualsAndHashCode(callSuper = true)
@TableName("tools_model_change_log")
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ToolsModelChangeLogVo extends ToolsModelChangeLogEntity {
    /**
     * 原值
     */
    private Object startDataObjects;
    /**
     * 变更记录
     */
    private ToolsModelChangeLogTypeEnum typeEnum;
    /**
     * 后置执行方法参数
     */
    private Object afterMethodParameter;
    /**
     * 后置执行方法
     */
    private Function<Object, Object> afterMethod;
}
