package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;

import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 组织信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-10 11:14:18
 */
@TableName("org_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrgInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    @NotBlank(message = "组织编码不能为空", groups = {UpdateGroup.class})
    private String orgCode;
    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称", required = true)
    @NotBlank(message = "组织名称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String orgName;

    @ApiModelProperty(value = "代理人类型0:机构 1:部门", required = true)
    private Integer orgType;
    /**
     * 城市对象
     */
    @ApiModelProperty(value = "城市对象 仅回显用")
    @TableField(exist = false)
    private SysRegionInfo regionInfo;
    /**
     * 城市代码
     */
    @ApiModelProperty(value = "城市代码", required = true)
    @NotBlank(message = "城市代码不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String orgCity;
    /**
     * 详细住址
     */
    @ApiModelProperty(value = "详细住址", required = true)
    @NotBlank(message = "详细住址不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String orgAddr;
    /**
     * 上级组织编码
     */
    @ApiModelProperty(value = "上级组织编码", required = true)
    private String orgSuperiorCode;
    /**
     * 上级组织名称
     */
    @ApiModelProperty(value = "上级组织名称 仅作返回用")
    @TableField(exist = false)
    private String orgSuperiorName;
    /**
     * 上级组织信息
     */
    @ApiModelProperty(value = "上级组织信息 仅作返回用")
    @TableField(exist = false)
    private List<OrgInfoVo> superiorOrg;
    /**
     * 组织层级
     */
    @ApiModelProperty(value = "组织层级", required = true)
    @Range(min = 1, max = 1, message = "仅可创建一级组织", groups = {AddGroup.class})
    @NotNull(message = "组织层级不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Integer orgLevel;


    @ApiModelProperty(value = "营业状态", required = true)
    @Range(min = 1, max = 1, message = "营业状态", groups = {AddGroup.class})
    @NotNull(message = "营业状态", groups = {AddGroup.class, UpdateGroup.class})
    private Integer orgStatus;
    /**
     * 下属人员数量
     */
    @ApiModelProperty(value = "下属人员数量 仅回显用")
    @TableField(exist = false)
    private Integer agentNum;
    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "负责人姓名", required = true)
    @NotBlank(message = "负责人姓名不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String principalName;
    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名", required = true)
    @NotBlank(message = "联系人姓名不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String contactName;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String contactTel;
    /**
     * 附加信息
     */
    @ApiModelProperty(value = "附件信息", required = true)
    @Valid
    @TableField(exist = false)
    private List<OrgInfoAccessoryEntity> orgInfoAccessoryEntityList;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁", notes = "更新时必须存在")
    @Version
    @TableField(fill = FieldFill.INSERT)
    private long revision;

    @ApiModelProperty(value = "注册地址")
    private String orgRegisterAddr;
    @ApiModelProperty(value = "许可证编号")
    private String orgLicence;
    @ApiModelProperty(value = "业务范围")
    private String orgScope;
}
