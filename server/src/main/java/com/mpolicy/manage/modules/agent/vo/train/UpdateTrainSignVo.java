package com.mpolicy.manage.modules.agent.vo.train;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateTrainSignVo implements Serializable {
    private static final long serialVersionUID = 5121592596877219148L;

    @NotBlank(message = "培训code不能为空")
    private String trainCode;

    @NotEmpty(message = "签到记录不能为空")
    private List<UpdateTrainSignListVo> trainSignList;
}
