package com.mpolicy.manage.modules.policy.vo.supervise;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/20 12:47 上午
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EpV2PolicyExportSupVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 保单类型
     */
    @ExcelProperty(value = "保单类型")
    private String policyType;

    /**
     * 管理后台保单状态
     */
    @ExcelProperty(value = "管理后台保单状态")
    private String adminPolicyStatus;

    /**
     * 保单产品类型(个、团、车、财)
     */
    @ExcelProperty(value = "产品类型")
    private String policyProductType;
    /**
     * 销售方式
     */
    @ExcelProperty(value = "销售方式")
    private String salesType;

    /**
     * 销售平台
     */
    @ExcelProperty(value = "销售平台")
    private String salesPlatform;
    /**
     * 投保单号
     */
    @ExcelProperty(value = "投保单号")
    private String applicantPolicyNo;
    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 保险公司名称
     */
    @ExcelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 主险名称
     */
    @ExcelProperty(value = "主险名称")
    private String mainProductName;

    /**
     * 协议险种名称
     */
    @ExcelProperty(value = "协议险种名称")
    private String protocolProductName;
    /**
     * 险种名称
     */
    @ExcelProperty(value = "险种名称")
    private String productName;

    /**
     * 是否主附险
     */
    @ExcelProperty(value = "是否主险")
    private String mainInsurance;

    /**
     * 险种类型编码
     */

    private String prodTypeCode;

    /**
     * 险种类型 todo
     */
    @ExcelProperty(value = "险种类型")
    private String prodTypeName;

    /**
     * 保险期间类型
     */
    @ExcelProperty(value = "保险期间类型")
    private String insuredPeriodType;
    /**
     * 保险期间
     */
    @ExcelProperty(value = "保险期间")
    private String insuredPeriod;
    /**
     * 缴费方式
     */
    @ExcelProperty(value = "缴费方式")
    private String periodType;

    /**
     * 缴费期间类型
     */
    @ExcelProperty(value = "缴费期间类型")
    private String paymentPeriodType;
    /**
     * 缴费期间
     */
    @ExcelProperty(value = "缴费期间")
    private String paymentPeriod;
    /**
     * 保费
     */
    @ExcelProperty(value = "保费")
    private String premium;

    /**
     * 退保金额 todo
     */
    @ExcelProperty(value = "退保金额")
    private String surrenderAmount;
    /**
     * 保额
     */
    @ExcelProperty(value = "保额")
    private String coverage;

    /**
     * 是否自保件
     */
    @ExcelProperty(value = "是否自保件")
    private String selfPreservation;

    /**
     * 险种状态
     */
    @ExcelProperty(value = "险种状态")
    private String productStatus;

    /**
     * 机构名称
     */
    @ExcelProperty(value = "代理人机构名称")
    private String orgSuperiorName;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String orgName;


    /**
     * 是否主代理人
     */
    @ExcelProperty(value = "是否主代理人")
    private String mainFlag;
    /**
     * 代理人编码
     */
    @ExcelProperty(value = "代理人编码")
    private String businessCode;
    /**
     * 代理人姓名
     */
    @ExcelProperty(value = "代理人姓名")
    private String agentName;

    /**
     * 分佣比例
     */
    @ExcelProperty(value = "分佣比例")
    private String commissionRate;

    /**
     * 推荐人工号
     */
//    @ExcelProperty(value = "推荐人工号")
    private String policyReferrerWno;
    /**
     * 推荐人姓名
     */
//    @ExcelProperty(value = "推荐人姓名")
    private String policyReferrerName;

    /**
     * 销售渠道
     */
//    @ExcelProperty(value = "销售渠道")
    private String channelName;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 分支名称
     */
//    @ExcelProperty(value = "分支名称")
    private String channelBranchName;

    /**
     * 渠道推荐人工号
     */
//    @ExcelProperty(value = "渠道推荐人工号")
    private String referrerWno;
    /**
     * 渠道推荐人姓名
     */
//    @ExcelProperty(value = "渠道推荐人姓名")
    private String referrerName;

    @ExcelProperty(value = "交单日期", format = "yyyy-MM-dd")
    private Date orderTime;
    /**
     * 投保日期
     */
    @ExcelProperty(value = "投保日期", format = "yyyy-MM-dd")
    private Date applicantTime;
    /**
     * 承保日期
     */
    @ExcelProperty(value = "承保日期", format = "yyyy-MM-dd")
    private Date approvedTime;
    /**
     * 生效日期
     */
    @ExcelProperty(value = "生效日期", format = "yyyy-MM-dd")
    private Date enforceTime;



    /**
     * 是否录入回执 todo
     */
    @ExcelProperty(value = "是否录入回执")
    private String isReceipt;
    /**
     * 回执签署时间
     */
    @ExcelProperty(value = "回执签署时间", format = "yyyy-MM-dd")
    private Date receiptSignTime;
    /**
     * 犹豫期（天）
     */
    @ExcelProperty(value = "犹豫期（天）")
    private String hesitatePeriod;
    /**
     * 过犹豫期日期
     */
    @ExcelProperty(value = "过犹豫期日期",format = "yyyy-MM-dd")
    private Date overHesitatePeriod;

    /**
     * 是否录入回访 todo
     */
    @ExcelProperty(value = "是否录入回访")
    private String isRevisit;
    /**
     * 回访日期
     */
    @ExcelProperty(value = "回访日期", format = "yyyy-MM-dd")
    private Date revisitTime;
    /**
     * 回访结果 0:失败; 1:成功
     */
    @ExcelProperty(value = "回访结果")
    private String revisitResult;

    /**
     * 退保时间
     */
    @ExcelProperty(value = "退保时间", format = "yyyy-MM-dd")
    private Date surrenderTime;

    //todo 创建时间
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", format = "yyyy-MM-dd")
    private Date createTime;
    /**
     * 承保录入时间
     */
    @ExcelProperty(value = "承保录入时间", format = "yyyy-MM-dd")
    private Date approvedRecordTime;
    /**
     * 回执录入时间
     */
    @ExcelProperty(value = "回执录入时间", format = "yyyy-MM-dd")
    private Date receiptRecordTime;

    /**
     * 回访录入时间
     */
    @ExcelProperty(value = "回访录入时间", format = "yyyy-MM-dd")
    private Date revisitRecordTime;

    /**
     * 失效时间
     */
    @ExcelProperty(value = "失效时间", format = "yyyy-MM-dd")
    private Date failureTime;
    /**
     * 终止时间
     */
    @ExcelProperty(value = "终止时间", format = "yyyy-MM-dd")
    private Date terminationTime;


    /**
     * 投保主体类型 0:非自然人; 1:自然人
     */
    @ExcelProperty(value = "投保主体类型")
    private String applicantType;
    /**
     * 投保主体姓名
     */
    @ExcelProperty(value = "投保主体姓名")
    private String applicantName;

    /**
     * 投保主体证件类型
     */
    @ExcelProperty(value = "投保主体证件类型")
    private String applicantIdType;
    /**
     * 投保主体证件号码
     */
    @ExcelProperty(value = "投保主体证件号码")
    private String applicantIdCard;

    /**
     * 投保人性别
     */
    @ExcelProperty(value = "投保人性别")
    private String applicantGender;


    /**
     * 投保人电话
     */
    @ExcelProperty(value = "投保人电话")
    private String applicantMobile;

    // todo 投保人投保时年龄

    /**
     * 投保人投保时年龄
     */
    @ExcelProperty(value = "投保人投保时年龄")
    private Integer applicantAge;

    /**
     * 投保人出生日期
     */
    @ExcelProperty(value = "投保人出生日期")
    private String applicantBirthday;

    /**
     * 投保主体详细地址
     */
//    @ExcelProperty(value = "投保主体详细地址")
    private String applicantAddress;

    /**
     * 被保人是投保人的
     */
    @ExcelProperty(value = "被保人是投保人的")
    private String insuredRelation;
    /**
     * 被保人姓名
     */
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件类型
     */
    @ExcelProperty(value = "被保人证件类型")
    private String insuredIdType;
    /**
     * 被保人证件号码
     */
    @ExcelProperty(value = "被保人证件号码")
    private String insuredIdCard;
    /**
     * 被保人性别
     */
    @ExcelProperty(value = "被保人性别")
    private String insuredGender;

    /**
     * 被保人电话
     */
    @ExcelProperty(value = "被保人电话")
    private String insuredMobile;
    //todo 被保人投保时年龄

    /**
     * 被保人投保时年龄
     */
    @ExcelProperty(value = "被保人投保时年龄")
    private String insuredPolicyAge;

    /**
     * 被保人出生日期
     */
    @ExcelProperty(value = "被保人出生日期")
    private String insuredBirthday;

    /**
     * 被保人详细地址
     */
//    @ExcelProperty(value = "被保人详细地址")
    private String insuredAddress;

    /**
     * 车主姓名
     */
    @ExcelProperty(value = "车主姓名")
    private String vehicleOwnerName;

    /**
     * 车主证件类型
     */
    @ExcelProperty(value = "车主证件类型")
    private String vehicleOwnerCardType;

    /**
     * 车主证件号码
     */
    @ExcelProperty(value = "车主证件号码")
    private String vehicleOwnerCardNo;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String vehicleLicensePlateNumber;


    /**
     * 佣金发放状态
     */
    private Integer settlementStatus;
    /**
     * 佣金发放状态 中文
     */
//    @ExcelProperty(value = "佣金发放状态")
    private String settlementStatusStr;
    /**
     * 发放月份
     */

    private Integer settlementMonth;
    /**
     * 佣金发放年 中文
     */
//    @ExcelProperty(value = "佣金发放年")
    private Integer settlementYear;
    /**
     * 佣金发放月 中文
     */
//    @ExcelProperty(value = "佣金发放月")
    private String settlementMonthStr;
    /**
     * 是否签署客户告知书
     */
    @ExcelProperty(value = "是否签署客户告知书")
    private String offlineProductSignStatusName;
    /**
     * 图例名称
     */
//    @ExcelProperty(value = "图例")
    private String channelDistributionName;
    /**
     * 图例名称
     */
    @ExcelProperty(value = "保司编码")
    private String companyCode;
    /**
     * 图例名称
     */
    @ExcelProperty(value = "险种编码")
    private String productCode;


    private Integer superviseChannelCode;

    /**
     * 渠道2
     */
    @ExcelProperty(value = "渠道2")
    private String superviseChannelName;
}
