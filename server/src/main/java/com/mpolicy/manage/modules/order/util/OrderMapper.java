package com.mpolicy.manage.modules.order.util;

import com.mpolicy.manage.modules.insurance.entity.CommodityMainProductNameMapEntity;
import com.mpolicy.manage.modules.insurance.vo.CommodityMainProductNameMapExportExcel;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoExportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoImportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 *
 * 订单映射工具
 *
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface OrderMapper {

    InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoImportExcel2InsureOrderCustomerRiskInfoEntity(InsureOrderCustomerRiskInfoImportExcel insureOrderCustomerRiskInfoImportExcel);

    InsureOrderCustomerRiskInfoVO insureOrderCustomerRiskInfoEntity2InsureOrderCustomerRiskInfoVO(InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoEntity);

    @Mapping(target = "isWhite",ignore = true)
    @Mapping(target = "isAccept",ignore = true)
    InsureOrderCustomerRiskInfoExportExcel InsureOrderCustomerRiskInfoVO2InsureOrderCustomerRiskInfoExportExcel(InsureOrderCustomerRiskInfoVO insureOrderCustomerRiskInfoVO);

    CommodityMainProductNameMapExportExcel CommodityMainProductNameMapEntity2CommodityMainProductNameMapExportExcel(CommodityMainProductNameMapEntity commodityMainProductNameMapEntity);
}
