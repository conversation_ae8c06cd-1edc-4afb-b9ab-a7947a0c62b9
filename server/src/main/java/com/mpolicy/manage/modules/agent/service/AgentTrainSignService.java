package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentTrainSignEntity;
import com.mpolicy.manage.modules.agent.vo.train.*;

import java.util.List;

/**
 * 代理人培训签到表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
public interface AgentTrainSignService extends IService<AgentTrainSignEntity> {

    /**
     * 获取培训签到列表
     * @param trainCode
     * @return
     */
    List<TrainSignListOut> findTrainSignList(String trainCode);

    /**
     * 更新培训签到数据
     * @param vo
     */
    void updateTrainSignList(UpdateTrainSignVo vo);

    /**
     * 获取代理人培训信息表列表
     * @param params
     * @return
     */
    PageUtils<AgentTrainInfoListOut> findPageList(AgentTrainInfoListVo params);
}

