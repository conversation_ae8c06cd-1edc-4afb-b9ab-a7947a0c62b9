package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 客户经理白名单信息
 *
 * <AUTHOR>
 * @date 2023-8-2
 */
@Data
@ApiModel(value = "客户经理白名单信息")
public class InsureCustomerWhiteRosterVo {

    @ApiModelProperty(value = "id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "客户编码", example = "C12345676545671")
    @NotBlank(message = "客户编码不能为空")
    private String customerCode;

    @ApiModelProperty(value = "姓名", example = "张三")
    @NotBlank(message = "姓名不能为空")
    private String realName;

    @ApiModelProperty(value = "证件号码", example = "123456789098765")
    @NotBlank(message = "证件号码不能为空")
    private String cardNo;

    @ApiModelProperty(value = "手机号码", example = "123455555555")
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    @ApiModelProperty(value = "所属渠道", example = "地区")
    private String referrerChannelName;

    @ApiModelProperty(value = "有效开始时间", example = "2023-08-01 22:22:22")
    private Date startDate;

    @ApiModelProperty(value = "有效结束时间", example = "2023-08-02 22:22:22")
    private Date endDate;

    @ApiModelProperty(value = "是否长期有效：0：否，1：是", example = "1")
    @NotNull(message = "长期有效标记不能为空")
    private Integer isLongTerm;

    @ApiModelProperty(value = "备注", example = "业务要求")
    private String remark;

}
