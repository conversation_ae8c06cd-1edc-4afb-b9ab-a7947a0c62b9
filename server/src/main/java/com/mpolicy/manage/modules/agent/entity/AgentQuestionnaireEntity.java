package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人问卷表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
@TableName("bl_agent_questionnaire")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentQuestionnaireEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 问卷标题
	 */
	private String title;
	/**
	 * 发布时间
	 */
	private Date publishTime;
	/**
	 * 开始采集时间
	 */
	private Date investigateStartTime;
	/**
	 * 结束采集时间
	 */
	private Date investigateEndTime;
	/**
	 * 已提交人数
	 */
	private Integer finishCnt;
	/**
	 * 状态: 0:待发布 , 1:已发布, 2: 已结束
	 */
	private Integer status;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@TableLogic
	private Integer deleted;
}
