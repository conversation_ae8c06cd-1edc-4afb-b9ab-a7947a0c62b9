package com.mpolicy.manage.modules.agent.vo.agentinfo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CompanyAccountVo implements Serializable {
    private static final long serialVersionUID = 2540110274207264867L;


    @TableId
    private Integer id;
    /**
     * 保司编码
     */
    private String companyCode;
    /**
     * 保司名称
     */
    private String companyName;
    /**
     * 保司简称
     */
    private String shortName;
    /**
     * 保司账号
     */
    private String companyAccount;
}
