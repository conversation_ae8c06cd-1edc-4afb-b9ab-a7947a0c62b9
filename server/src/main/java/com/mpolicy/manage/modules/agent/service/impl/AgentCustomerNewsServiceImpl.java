package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.AgentCustomerNewsDao;
import com.mpolicy.manage.modules.agent.entity.AgentCustomerNewsEntity;
import com.mpolicy.manage.modules.agent.service.AgentCustomerNewsService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("agentCustomerNewsService")
public class AgentCustomerNewsServiceImpl extends ServiceImpl<AgentCustomerNewsDao, AgentCustomerNewsEntity> implements AgentCustomerNewsService {
}
