package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireQuestionEntity;
import com.mpolicy.manage.modules.agent.vo.questionnaire.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 代理人问卷表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
public interface AgentQuestionnaireService extends IService<AgentQuestionnaireEntity> {

    /**
     * 分页查询
     * @param params
     * @return
     */
    PageUtils<AgentQuestionnaireOut> queryQuestionnairePage(Map<String, Object> params);

    /**
     * 保存问卷
     * @param blAgentQuestionnaireVo
     */
    void save(AgentQuestionnaireVo blAgentQuestionnaireVo);

    /**
     * 查询代理人答题详情
     * @param agentCode
     * @param questionnaireId
     * @return
     */
    List<AgentQuestionnaireQuestionVo> queryAgentAnswerPage(String agentCode, Integer questionnaireId);

    /**
     * 删除问卷
     * @param questionnaireId
     */
    void delete(Integer questionnaireId);

    /**
     * 问卷详情
     * @param questionnaireId
     * @return
     */
    AgentQuestionnaireVo detail(Integer questionnaireId);

    /**
     * 问卷进展分页查询
     * @param params
     * @return
     */
    PageUtils<AgentInvestigateOut> queryInvestigatePage(Map<String, Object> params);
}

