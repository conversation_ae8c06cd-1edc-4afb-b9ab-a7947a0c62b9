package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 导入团险数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/12 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EpPolicyGroupImportVo extends BaseRowModel  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 保单类型
     */
    @ExcelProperty(value = "保单类型",index = 0)
    private String policyType;
    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号",index = 1)
    private String policyNO;
    /**
     * 投保单号
     */
    @ExcelProperty(value = "投保单号",index = 2)
    private String applicantPolicyNo;
    /**
     * 保险公司
     */
    @ExcelProperty(value = "保险公司",index = 3)
    private String companyName;
    /**
     * 保险公司编码
     */
    @ExcelProperty(value = "保险公司编码",index = 4)
    private String companyCode;
    /**
     * 业务分类
     */
    @ExcelProperty(value = "业务分类",index = 5)
    private String salesType;
    /**
     * 销售平台
     */
    @ExcelProperty(value = "销售平台",index = 6)
    private String salesPlatform;
    /**
     * 保单状态
     */
    @ExcelProperty(value = "保单状态",index = 7)
    private String policyStatus;
    /**
     * 代理人编码
     */
    @ExcelProperty(value = "代理人编码",index = 8)
    private String agentCode;
    /**
     * 代理人姓名
     */
    @ExcelProperty(value = "代理人姓名",index = 9)
    private String agentName;
    /**
     * 渠道推荐人工号
     */
    @ExcelProperty(value = "渠道推荐人工号",index = 10)
    private String referrerWno;
    /**
     * 渠道推荐人姓名
     */
    @ExcelProperty(value = "渠道推荐人姓名",index = 11)
    private String referrerName;

    // TODO: 2021/12/27 推荐人工号  渠道推荐人类型
    @ExcelProperty(value = "职业代码",index = 12)
    private String insuredCareer;
    /**
     * 推荐人姓名
     */
    @ExcelProperty(value = "推荐人姓名",index = 13)
    private String policyReferrerName;
    /**
     * 联系方式
     */

    @ExcelProperty(value = "交单日期",index = 14)
    private String orderTime;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    @ExcelProperty(value = "电子邮箱",index = 15)
    private String insuredEmail;
    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    @ExcelProperty(value = "工作单位",index = 16)
    private String insuredCompany;


}
