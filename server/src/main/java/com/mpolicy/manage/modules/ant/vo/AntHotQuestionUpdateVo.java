package com.mpolicy.manage.modules.ant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ClassName: AntHotQuestionUpdateVo
 * Description: 热门问题更新
 * date: 2023/9/19 18:31
 *
 * <AUTHOR>
 */
@Data
public class AntHotQuestionUpdateVo implements Serializable {

    /**
     * 问题编码
     */
    @ApiModelProperty(value = "问题编码")
    @NotBlank(message = "问题编码不能为空")
    private String code;
    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型")
    @NotBlank(message = "问题类型不能为空")
    private String type;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    private String productCode;
}
