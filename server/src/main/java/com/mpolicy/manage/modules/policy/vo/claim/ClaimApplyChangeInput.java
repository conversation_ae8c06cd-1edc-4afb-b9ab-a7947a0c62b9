package com.mpolicy.manage.modules.policy.vo.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 理赔申请信息变更提交
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔申请信息变更提交")
@Data
public class ClaimApplyChangeInput {

    /**
     * 所属理赔客户编号
     */
    @ApiModelProperty(value = "理赔服务单号")
    @NotBlank(message = "理赔服务单号不能为空")
    private String claimNo;

    /**
     * 理赔状态 1已创建2待上传材料3预审中4待更新材料5预审不通过6预审通过7已赔付8保险公司拒赔9已取消
     */
    @ApiModelProperty(value = "理赔状态")
    @NotNull(message = "理赔状态不能为空")
    private Integer claimStatus;

    /**
     * 理赔所需材料编码
     */
    @ApiModelProperty(value = "理赔所需材料编码,多个逗号分割")
    private String claimNeedDocumentCodes;

    /**
     * 取消说明
     */
    @ApiModelProperty(value = "理赔说明")
    private String refusedDesc;
}
