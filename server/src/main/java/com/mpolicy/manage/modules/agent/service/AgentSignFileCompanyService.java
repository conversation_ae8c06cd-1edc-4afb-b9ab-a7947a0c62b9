package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileCompanyEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFileDetailPageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignSubmitUploadInput;

import java.util.List;
import java.util.Map;

/**
 * 代理人文件签约公司信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileCompanyService extends IService<AgentSignFileCompanyEntity> {

    void submitUpload(AgentSignSubmitUploadInput input);

    /**
     * 根据文件编码分页查询
     * @param fileCode 文件编码
     * @param params 分页参数
     */
    PageUtils<AgentSignFileDetailPageList> detailPageList(String fileCode, Map<String, Object> params);

    /**
     * 给指定机构下代理人发送短信提醒
     * @param orgCodeList 机构集合
     */
    void sendFileMsg(List<String> orgCodeList);

    /**
     * 清空所有有弹窗人的缓存
     */
    void cleanConfirm();
}

