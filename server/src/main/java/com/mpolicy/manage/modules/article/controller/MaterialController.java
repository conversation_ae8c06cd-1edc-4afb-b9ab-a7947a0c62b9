package com.mpolicy.manage.modules.article.controller;

import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.article.entity.MaterialArticleInfoEntity;
import com.mpolicy.manage.modules.article.service.MaterialArticleInfoService;
import com.mpolicy.manage.modules.article.service.MaterialBannerInfoService;
import com.mpolicy.manage.modules.article.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * ClassName: MaterialController
 * Description: 素材圈相关信息
 * date: 2023/7/19 16:47
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("material/poster")
@Api(tags = "素材圈相关信息")
public class MaterialController {

    @Autowired
    private MaterialArticleInfoService articleInfoService;
    @Autowired
    private MaterialBannerInfoService bannerInfoService;

    /**
     * 分页查询文章信息
     * @param input 分页参数信息
     * @return
     */
    @PostMapping("/article/pageList")
    @ApiOperation(value = "分页查询文章列表信息", notes = "分页查询文章列表信息")
    @RequiresPermissions(value = {"cms:article:all"})
    public Result<PageUtils<ArticlePageListOut>> pageList(@RequestBody ArticlePageListVo input) {
        PageUtils<ArticlePageListOut> pageList = articleInfoService.pageList(input);
        return Result.success(pageList);
    }

    /**
     * 更改文章上下架状态
     * @return
     */
    @GetMapping("/article/changeStatus/{articleCode}")
    @ApiOperation(value = "通过文章编码改变文章上下架状态", notes = "通过文章编码改变文章上下架状态")
    @RequiresPermissions(value = {"cms:article:all"})
    public Result<String> changeArticleStatus(@PathVariable("articleCode") String articleCode) {
        MaterialArticleInfoEntity info = articleInfoService.lambdaQuery().eq(MaterialArticleInfoEntity::getArticleCode, articleCode).one();
        if(Objects.isNull(info)){
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("文章信息不存在"));
        }
        MaterialArticleInfoEntity updateBean = new MaterialArticleInfoEntity();
        updateBean.setId(info.getId());
        if(info.getStatus().equals(StatusEnum.NORMAL.getCode())){
            updateBean.setStatus(StatusEnum.INVALID.getCode());
        }else{
            updateBean.setStatus(StatusEnum.NORMAL.getCode());
        }
        articleInfoService.updateById(updateBean);
        return Result.success();
    }

    /**
     * 文章信息保存
     * @param input 保存参数
     */
    @PostMapping("/article/saveOrUpdate")
    @ApiOperation(value = "文章信息保存", notes = "文章信息保存")
    @RequiresPermissions(value = {"cms:article:all"})
    public Result<String> articleSaveOrUpdate(@RequestBody @Valid ArticleSaveVo input) {
        articleInfoService.saveOrUpdate(input);
        return Result.success();
    }

    /**
     * banner分页查询
     * @param input 分页参数
     * @return
     */
    @PostMapping("/banner/pageList")
    @ApiOperation(value = "分页查询素材banner列表信息", notes = "分页查询素材banner列表信息")
    @RequiresPermissions(value = {"cms:banner:all"})
    public Result<PageUtils<BannerPageListOut>> bannerPageList(@RequestBody BannerPageListVo input) {
        PageUtils<BannerPageListOut> bannerPageListOutPageUtils = bannerInfoService.pageList(input);
        return Result.success(bannerPageListOutPageUtils);
    }

    /**
     * 通过banner编码改变banner上下架状态
     * @param bannerCode banner编码
     * @return
     */
    @GetMapping("/banner/changeStatus/{bannerCode}")
    @ApiOperation(value = "通过banner编码改变banner上下架状态", notes = "通过banner编码改变banner上下架状态")
    @RequiresPermissions(value = {"cms:banner:all"})
    public Result<String> changeBannerStatus(@PathVariable("bannerCode") String bannerCode) {
        bannerInfoService.updateBannerStatus(bannerCode);
        return Result.success();
    }

    /**
     * banner信息保存和更新
     * @param input banner信息
     * @return
     */
    @PostMapping("/banner/saveOrUpdate")
    @ApiOperation(value = "banner信息保存", notes = "banner信息保存")
    @RequiresPermissions(value = {"cms:banner:all"})
    public Result<String> bannerSaveOrUpdate(@RequestBody @Valid BannerSaveVo input) {
        this.bannerInfoService.saveOrUpdate(input);
        return Result.success();
    }
}
