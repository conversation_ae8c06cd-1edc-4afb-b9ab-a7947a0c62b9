package com.mpolicy.manage.modules.policy.vo.lost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 协议管理操作信息
 *
 * <AUTHOR>
 * @date 2021-12-04 11:14:35
 */
@ApiModel(value = "丢单登记-人工操作信息")
@Data
public class PolicyLostRegisterDataInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "丢单补偿主键")
    @NotNull(message = "丢单补偿主键不能为空")
    private Integer id;
    /**
     * 处理状态 0:待处理 1:处理成功 2:处理失败
     */
    @ApiModelProperty(value = "处理状态 11:处理成功 12:处理失败")
    private Integer handleResult;
    /**
     * 登记人工号
     */
    @ApiModelProperty(value = "人工处理拒绝原因")
    private String manualHandleResult;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateUser;

    /**
     * 处理成功类型
     */
    @ApiModelProperty(value = "处理成功类型",example = "1:系统问题 2:操作问题 3:操作问题 4:其他")
    private Integer handleSuccessType;
    /**
     * 处理成功结果
     */
    @ApiModelProperty(value = "处理成功结果")
    private String handleSuccessResult;
}
