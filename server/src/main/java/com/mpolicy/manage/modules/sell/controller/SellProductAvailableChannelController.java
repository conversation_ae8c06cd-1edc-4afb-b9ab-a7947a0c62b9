package com.mpolicy.manage.modules.sell.controller;

import com.mpolicy.manage.modules.sell.service.SellProductAvailableChannelService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 售卖商品可售渠道
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-06 14:09:38
 */
@RestController
@RequestMapping("xjxh/sellproductavailablechannel")
@Api(tags = "售卖商品可售渠道")
public class SellProductAvailableChannelController {

    @Autowired
    private SellProductAvailableChannelService sellProductAvailableChannelService;

}
