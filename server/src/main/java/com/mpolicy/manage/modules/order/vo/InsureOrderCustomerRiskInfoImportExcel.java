package com.mpolicy.manage.modules.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 导入时用到的数据对象
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Data
public class InsureOrderCustomerRiskInfoImportExcel extends BaseRowModel {

    /**
     * 客户类型 0 个人，1是企业
     */
    @ExcelProperty(value = "客户类型", index = 0)
    private String customerType;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户名称", index = 1)
    private String customerName;

    /**
     * 证件类型
     */
    @ExcelProperty(value = "证件类型", index = 2)
    private String identityType;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码", index = 3)
    private String identityNumber;


    /**
     * 婚姻状态 0未婚, 1已婚, 2离异, 3丧偶
     */
    @ExcelProperty(value = "婚姻状况", index = 4)
    private String maritalStatus;


    /**
     * 评分等级
     */
    @ExcelProperty(value = "评分等级", index = 5)
    private String ratingLevel;

    /**
     * 渠道来源 0中和农信渠道，1程序渠道
     */
    @ExcelProperty(value = "渠道来源", index = 6)
    private String channelSource;
}
