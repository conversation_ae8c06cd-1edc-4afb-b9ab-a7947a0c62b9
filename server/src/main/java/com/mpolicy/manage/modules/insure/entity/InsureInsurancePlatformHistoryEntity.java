package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保于中台交互流水表
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_insurance_platform_history")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureInsurancePlatformHistoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单单号
	 */
	private String insureOrderCode;

	/**
	 * 中台预保单号
	 */
	private String prePolicyNo;

	/**
	 * 操作用户编号
	 */
	private String userNo;
	/**
	 * 交互类型
	 */
	private String operateType;
	/**
	 * 交互名称
	 */
	private String operateName;

	/**
	 * 交互结果状态
	 */
	private Integer operateFlag;

	/**
	 * 请求报文
	 */
	private String requestData;
	/**
	 * 响应报文
	 */
	private String responseData;
	/**
	 * 交互备注
	 */
	private String operateRemark;
	/**
	 * 交互耗时
	 */
	private Integer consumTime;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
