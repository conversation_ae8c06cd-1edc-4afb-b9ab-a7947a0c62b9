package com.mpolicy.manage.modules.settlement.entity;

import cn.hutool.core.annotation.Alias;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ReconcileDetailOut implements Serializable {


    private static final long serialVersionUID = 4562551786252756052L;
    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 对账编码
     */
    private String reconcileBillCode;

    /**
     * 保单状态
     */
    private String policyStatus;

    /**
     * 保司手续金额
     */
    private BigDecimal poundageAmount;

    /**
     * 投保人姓名
     */
    private String applicantName;

    /**
     * 被保人姓名集合
     */
    private String insuredsName;

    /**
     * 投保时间
     */
    @JSONField(format = "yyyy/MM/dd HH:mm:ss")
    private Date approvedTime;

    /**
     * 费率
     */
    private String poundageRatio;

    /**
     * 税率
     */
    private String taxRatio;

    /**
     * 结算科目编码
     */
    private String subjectCode;

    /**
     * 结算科目
     */
    private String subjectName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 结算保费
     */
    private BigDecimal settlementPremium;
    /**
     * 实收保费
     */
    private BigDecimal premium;

}
