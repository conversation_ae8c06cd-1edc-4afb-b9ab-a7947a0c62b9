package com.mpolicy.manage.modules.agent.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.*;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.regulators.common.RegulatorsConstant;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.AddGroup;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 组织信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-10 11:14:18
 */
@RestController
@RequestMapping("sys/orginfo")
@Api(tags = "组织信息")
public class OrgInfoController {

    @Autowired
    private OrgInfoService orgInfoService;

    private final static String TOP_ORG_CODE = "OR20210222103029tIBLey";

    /**
     * 列表
     */
    @ApiOperation(value = "按条件分页获取组织信息列表", notes = "分页获取组织信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "orgName", dataType = "String", value = "公司名称", example = "小鲸"),
            @ApiImplicitParam(paramType = "query", name = "orgCity", dataType = "String", value = "组织所在地代码", example = "130100"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "String", value = "创建时间", example = "2021-02-22"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"org:list"})
    public Result<List<OrgPageListOut>> list(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        List<OrgPageListOut> resultList = orgInfoService.findOrgInfoList(params);
        return Result.success(resultList);
    }


    @ApiOperation("获取组织详情")
    @GetMapping("info/{code}")
    @RequiresPermissions(value = {"org:list"})
    public Result<OrgInfoOut> info(@PathVariable("code") String code) {
        OrgInfoOut out = orgInfoService.findOrgInfoByCode(code);
        return Result.success(out);
    }

    @ApiOperation("保存组织信息")
    @PostMapping("save")
    @RequiresPermissions(value = {"org:edit"})
    public Result<OrgInfoEntity> save(@RequestBody @Valid OrgInfoSaveVo save) {
        if(save.getOrgType() == 0){
            ValidatorUtils.validateEntity(save, AddGroup.class);
        }
        orgInfoService.saveOrgInfo(save);
        return Result.success();
    }

    @ApiOperation("修改组织信息")
    @PostMapping("/update")
    @RequiresPermissions(value = {"org:edit"})
    public Result<OrgInfoEntity> update(@RequestBody @Valid OrgInfoUpdateVo update) {
        if(update.getOrgType() == 0){
            ValidatorUtils.validateEntity(update, AddGroup.class);
        }
        orgInfoService.updateOrgInfo(update);
        return Result.success();
    }


    @ApiOperation(value = "根据code删除组织信息", hidden = true)
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"org:delete"})
    public Result<OrgInfoEntity> delete(@PathVariable("code") String code) {
        if (TOP_ORG_CODE.equals(code)) {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("禁止删除顶级组织"));
        }
        boolean delete = orgInfoService.remove(
                Wrappers.<OrgInfoEntity>lambdaQuery().eq(OrgInfoEntity::getOrgCode, code)
        );

        if (delete) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
    }


    /**
     * 根据code获取下级组织信息
     */
    @ApiOperation("根据code获取下级组织信息")
    @GetMapping("/getOrgSonList/{code}")
    public Result<List<OrgInfoVo>> getOrgSonList(@PathVariable("code") String code) {
        List<OrgInfoVo> orgInfoList = orgInfoService.getOrgSonList(code);

        return Result.success(orgInfoList);
    }

    /**
     * 获取顶级组织下属组织信息
     */
    @ApiOperation("获取顶级组织下属组织信息")
    @GetMapping("getOrgSonList")
    public Result<List<OrgInfoVo>> getOrgTopList() {
        List<OrgInfoVo> orgInfoList = orgInfoService.getOrgSonList("");

        return Result.success(orgInfoList);
    }

    @ApiOperation("获取顶级组织下属组织信息")
    @GetMapping("getChildNode/{code}")
    public Result<List<String>> getChildNode(@PathVariable("code") String code) {
        List<String> childNode = orgInfoService.getChildNode(code);
        return Result.success(childNode);
    }

    /**
     * 组织名称列表
     */
    @ApiOperation(value = "获取所有组织信息列表", notes = "获取组织信息列表")
    @GetMapping("listChannelName")
    public Result<List<OrgInfoVo>> listChannelName() {
        SysUserEntity user = ShiroUtils.getUserEntity();
        List<String> orgList = null;
        if (StatusEnum.NORMAL.getCode().equals(user.getIsAllOrg())) {
            orgList = StrUtil.split(user.getOrgCodeList(), ',');
        }
        List<OrgInfoVo> list = orgInfoService.lambdaQuery()
                .in(CollUtil.isNotEmpty(orgList), OrgInfoEntity::getOrgCode, orgList)
                .list().stream().map(item -> {
                    OrgInfoVo vo = new OrgInfoVo();
                    BeanUtils.copyProperties(item, vo);
                    return vo;
                }).collect(Collectors.toList());
        return Result.success(list);
    }

    @ApiOperation(value = "获取当前用户组织树列表", notes = "获取组织树列表")
    @GetMapping("treeList")
    public Result<List<TreeListOut>> treeList() {
        List<TreeListOut> list = orgInfoService.findOrgTreeList();
        return Result.success(list);
    }


    @GetMapping("findAllTreeList")
    @ApiOperation(value = "获取组织树列表", notes = "获取组织树列表")
    public Result<List<TreeListOut>> findAllTreeList() {
        List<TreeListOut> list = orgInfoService.findAllTreeList();
        return Result.success(list);
    }

    /**
     * 获取所有组织详情
     * 该接口目前用在产品中心-协议管理
     *
     * @return
     */
    @ApiOperation("获取所有组织详情")
    @GetMapping("queryAllList")
    public Result<List<OrgInfoEntity>> queryAllList() {
        List<OrgInfoEntity> allList = orgInfoService.queryAllList();
        return Result.success(allList);
    }


    /**
     * <p>
     * 需报备机构列表
     * 目前暂时支持：小鲸向海
     * </p>
     */
    @ApiOperation(value = "需报备机构列表", notes = "需报备机构列表")
    @GetMapping("/regulators/list")
    public Result<List<OrgInfoItem>> regulatorsOrgList() {

        // 获取机构报备 指定机构编码 + 父节点编码为此编码的机构列表
        List<OrgInfoEntity> list = orgInfoService.lambdaQuery().eq(OrgInfoEntity::getOrgType, 0).eq(OrgInfoEntity::getOrgStatus, 0).list();

        // 构建返回vo
        List<OrgInfoItem> result = new ArrayList<>();
        list.forEach(x -> {
            OrgInfoItem bean = new OrgInfoItem();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        });
        return Result.success(result);
    }


    /**
     * 获取所有分公司列表(一级机构,营业)
     *
     * @return
     */
    @ApiOperation(value = "获取所有分公司列表(一级机构)", notes = "获取所有分公司列表(一级机构)")
    @GetMapping("findApplicableBranchList")
    public Result<List<SelectOut>> findApplicableBranchList() {
        List<SelectOut> resultList = orgInfoService.lambdaQuery()
                .eq(OrgInfoEntity::getOrgStatus, StatusEnum.INVALID.getCode())
                .eq(OrgInfoEntity::getOrgType, StatusEnum.INVALID.getCode())
                .eq(OrgInfoEntity::getOrgLevel, StatusEnum.INVALID.getCode())
                .list().stream().map(m -> {
                    SelectOut out = new SelectOut();
                    out.setLabel(m.getOrgName());
                    out.setValue(m.getOrgCode());
                    return out;
                }).collect(Collectors.toList());
        return Result.success(resultList);
    }
}
