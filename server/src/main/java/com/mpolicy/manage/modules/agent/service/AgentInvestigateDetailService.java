package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.entity.AgentInvestigateDetailEntity;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;

import java.util.Map;

/**
 * 代理人问卷调查明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-10 16:03:04
 */
public interface AgentInvestigateDetailService extends IService<AgentInvestigateDetailEntity> {

    void deleteByQuestionnaireId(Integer questionnaireId);
}

