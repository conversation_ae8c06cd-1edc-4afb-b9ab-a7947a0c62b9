package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保订单投保人信息
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_holder")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderHolderEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单单号
	 */
	private String insureOrderCode;
	/**
	 * 姓名
	 */
	private String holderName;
	/**
	 * 全拼
	 */
	private String holderEname;
	/**
	 * 国籍
	 */
	private String holderNationality;
	/**
	 * 民族
	 */
	private String holderNation;
	/**
	 * 出生日期
	 */
	private String holderBirthDay;
	/**
	 * 证件类型
	 */
	private String holderCertiCode;
	/**
	 * 证据号码
	 */
	private String holderCertiNo;
	/**
	 * 证据长期有效 是/否
	 */
	private String holderCertiLongTerm;
	/**
	 * 证件生效日期
	 */
	private String holderCertiEffectiveDate;
	/**
	 * 证件失效日期
	 */
	private String holderCertiInvalidDate;
	/**
	 * 年龄
	 */
	private Integer holderAge;
	/**
	 * 学历
	 */
	private String holderEducation;
	/**
	 * 性别
	 */
	private String holderGender;
	/**
	 * 电子邮箱
	 */
	private String holderEmail;
	/**
	 * 省份编码
	 */
	private String provincesCode;
	/**
	 * 省份名称
	 */
	private String provincesName;
	/**
	 * 城市编码
	 */
	private String cityCode;
	/**
	 * 城市名称
	 */
	private String cityName;
	/**
	 * 区编码
	 */
	private String areaCode;
	/**
	 * 区名称
	 */
	private String areaName;
	/**
	 * 详细地址
	 */
	private String holderAddress;
	/**
	 * 邮编
	 */
	private String holderZip;
	/**
	 * 职业类别
	 */
	private String holderJobType;
	/**
	 * 职业编码一级
	 */
	private String holderJobCodeLevel1;

	/**
	 * 职业名称一级
	 */
	private String holderJobCodeName1;

	/**
	 * 职业编码二级
	 */
	private String holderJobCodeLevel2;

	/**
	 * 职业名称二级
	 */
	private String holderJobCodeName2;

	/**
	 * 职业编码三级
	 */
	private String holderJobCodeLevel3;
	/**
	 * 职业名称三级
	 */
	private String holderJobCodeName3;
	/**
	 * 工作单位类型
	 */
	private String holderWorkCompanyType;
	/**
	 * 工作单位名称
	 */
	private String holderWorkCompany;
	/**
	 * 单位/学校名称
	 */
	private String holderWorkCompanyName;
	/**
	 * 单位/学校地址
	 */
	private String holderWorkCompanyAddr;
	/**
	 * 联系电话
	 */
	private String holderTel;
	/**
	 * 手机号码
	 */
	private String holderMobile;
	/**
	 * 收入
	 */
	private String holderIncome;
	/**
	 * 身高
	 */
	private String holderHeight;
	/**
	 * 体重
	 */
	private String holderWeight;
	/**
	 * 婚姻状况
	 */
	private String holderMaritalStat;
	/**
	 * 健康状况
	 */
	private String holderHealthStatus;

	/**
	 * 是否抽烟
	 */
	private String holderIsSmoking;

	/**
	 * 住宅电话
	 */
	private String holderHomeTel;
	/**
	 * 办公电话
	 */
	private String holderJobTel;
	/**
	 * 健康告知信息
	 */
	private String healthData;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
