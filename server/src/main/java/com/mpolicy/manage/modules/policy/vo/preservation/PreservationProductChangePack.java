package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 保全-险种变更列表
 * <AUTHOR>
@Data
public class PreservationProductChangePack {

    @ApiModelProperty("变更前的险种集合")
    List<PreservationProductChangeVo> beforeProductList;

    @ApiModelProperty("变更后的险种集合")
    List<PreservationProductChangeVo> afterProductList;

}
