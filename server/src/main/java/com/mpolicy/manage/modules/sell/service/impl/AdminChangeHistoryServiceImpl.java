package com.mpolicy.manage.modules.sell.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.dao.AdminChangeHistoryDao;
import com.mpolicy.manage.modules.sell.entity.AdminChangeHistoryEntity;
import com.mpolicy.manage.modules.sell.service.AdminChangeHistoryService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("adminChangeHistoryService")
public class AdminChangeHistoryServiceImpl extends ServiceImpl<AdminChangeHistoryDao, AdminChangeHistoryEntity> implements AdminChangeHistoryService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AdminChangeHistoryEntity> page = this.page(
                new Query<AdminChangeHistoryEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
