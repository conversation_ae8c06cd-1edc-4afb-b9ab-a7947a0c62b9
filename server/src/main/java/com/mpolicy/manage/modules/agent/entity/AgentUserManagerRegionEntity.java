package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 代理人关联地区
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-20 00:31:20
 */
@TableName("agent_user_manager_region")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentUserManagerRegionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 代理人编码
	 */
	private String agentCode;
	/**
	 * 代理人姓名
	 */
	private String agentName;
	/**
	 * 区域编码
	 */
	private String regionCode;
	/**
	 * 区域名称
	 */
	private String regionName;
}
