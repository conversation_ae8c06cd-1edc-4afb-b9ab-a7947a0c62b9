package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人问卷答题表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
@TableName("bl_agent_questionnaire_answer")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentQuestionnaireAnswerEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * bl_agent_questionnaire_question表主键id
	 */
	private Integer questionId;
	/**
	 * 答案
	 */
	private String answer;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@TableLogic
	private Integer deleted;
}
