package com.mpolicy.manage.modules.baichuan.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.mpolicy.manage.modules.baichuan.dao.BcChannelSellProductDao;
import com.mpolicy.manage.modules.baichuan.entity.BcChannelSellProductEntity;
import com.mpolicy.manage.modules.baichuan.service.BcChannelSellProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 百川渠道销售产品配置
 *
 * <AUTHOR>
 * @date 2023-08-27 00:26:27
 */
@Slf4j
@Service("bcChannelSellProductService")
public class BcChannelSellProductServiceImpl extends ServiceImpl<BcChannelSellProductDao, BcChannelSellProductEntity> implements BcChannelSellProductService {

}
