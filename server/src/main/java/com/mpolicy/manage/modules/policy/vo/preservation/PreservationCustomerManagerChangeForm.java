package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @Date 2023/9/25 14:24
 * @Version 1.0
 */
@Data
public class PreservationCustomerManagerChangeForm {

    @ApiModelProperty(value = "变更前渠道推荐人编码(小鲸编码)")
    private String beforeCustomerManager;

    @ApiModelProperty(value = "变更前渠道推荐人姓名")
    private String beforeCustomerManagerName;

    @NotBlank(message = "变更前渠道推荐人工号不能为空")
    @ApiModelProperty(value = "变更前渠道推荐人工号(中和农信工号)")
    private String beforeCustomerManagerChannelCode;

    @ApiModelProperty(value = "变更后渠道推荐人")
    private String afterCustomerManager;

    @ApiModelProperty(value = "变更后渠道分支")
    private String afterCustomerManagerBranchCode;

    @ApiModelProperty(value = "变更后渠道")
    private String afterCustomerManagerChannel;

    @NotBlank(message = "变更后渠道推荐人工号不能为空")
    @ApiModelProperty(value = "变更后渠道推荐人工号(中和农信工号)")
    private String afterCustomerManagerChannelCode;

    @ApiModelProperty(value = "变更后渠道推荐人姓名")
    private String afterCustomerManagerName;

    @Deprecated
    @ApiModelProperty(value = "是否变更分单：0=否，1=是")
    private Integer changeSubPolicy;

}
