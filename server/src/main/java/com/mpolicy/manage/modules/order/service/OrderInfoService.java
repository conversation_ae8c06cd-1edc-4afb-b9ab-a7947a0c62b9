package com.mpolicy.manage.modules.order.service;


import com.mpolicy.common.utils.PageUtils;

import java.util.List;
import java.util.Map;

/**
 * 订单信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-15 15:53:34
 */
public interface OrderInfoService {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    /**
     * 查看回溯图片
     * @param orderCode
     * @return
     */
    List backPicture(String orderCode);
}
