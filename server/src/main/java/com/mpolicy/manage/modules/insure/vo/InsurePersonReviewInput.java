package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单审核结果
 *
 * <AUTHOR>
 * @since 2023/4/14
 */
@Data
@ApiModel(value = "订单审核结果", description = "订单审核结果")
public class InsurePersonReviewInput implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "投保订单唯一单号", example = "INS20220506103627429095", required = true)
    @NotEmpty(message = "订单号不能为空")
    private String insureOrderCode;

    @ApiModelProperty(value = "审核结果  0：不通过，1：通过", example = "1", required = true)
    @NotNull(message = "审核结果不能为空")
    private Integer reviewResult;

    @ApiModelProperty(value = "审核备注", example = "照片不清晰")
    private String reviewRemark;

}
