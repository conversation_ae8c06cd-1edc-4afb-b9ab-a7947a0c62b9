package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("bi_agent_group_stat")
public class BiAgentGroupStatEntity implements Serializable {
    private static final long serialVersionUID = 7405821466923318291L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("group_name")
    private String groupName;

    @TableField("customer_num")
    private Integer customerNum;

    @TableField("newly_added_customer_num")
    private Integer newlyAddedCustomerNum;

    @TableField("policy_num")
    private Integer policyNum;

    @TableField("newly_added_policy_num")
    private Integer newlyAddedPolicyNum;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

}
