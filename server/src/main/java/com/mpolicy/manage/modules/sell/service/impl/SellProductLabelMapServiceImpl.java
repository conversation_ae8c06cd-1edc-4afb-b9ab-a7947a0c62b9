package com.mpolicy.manage.modules.sell.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.dao.SellProductLabelMapDao;
import com.mpolicy.manage.modules.sell.entity.SellProductLabelMapEntity;
import com.mpolicy.manage.modules.sell.service.SellProductLabelMapService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Service("sellProductLabelMapService")
public class SellProductLabelMapServiceImpl extends ServiceImpl<SellProductLabelMapDao, SellProductLabelMapEntity> implements SellProductLabelMapService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SellProductLabelMapEntity> page = this.page(
                new Query<SellProductLabelMapEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
