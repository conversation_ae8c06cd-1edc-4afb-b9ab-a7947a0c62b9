package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.dao.AgentUserInfoDao;
import com.mpolicy.manage.modules.agent.dao.OrgInfoDao;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoAccessoryEntity;
import com.mpolicy.manage.modules.agent.entity.OrgInfoEntity;
import com.mpolicy.manage.modules.agent.service.OrgInfoAccessoryService;
import com.mpolicy.manage.modules.agent.service.OrgInfoService;
import com.mpolicy.manage.modules.agent.vo.OrgInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.*;
import com.mpolicy.manage.modules.sys.dao.SysUserDao;
import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import com.mpolicy.manage.modules.sys.entity.SysUserEntity;
import com.mpolicy.manage.modules.sys.service.SysRegionInfoService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service("orgInfoService")
public class OrgInfoServiceImpl extends ServiceImpl<OrgInfoDao, OrgInfoEntity> implements OrgInfoService {

    @Autowired
    OrgInfoAccessoryService orgInfoAccessoryService;

    @Autowired
    SysRegionInfoService sysRegionInfoService;

    @Autowired
    AgentUserInfoDao agentUserInfoDao;
    @Autowired
    SysUserDao sysUserDao;

    @Override
    public List<OrgPageListOut> findOrgInfoList(Map<String, Object> params) {
        // 判断组织名称是否为空
        List<OrgPageListOut> rootList = baseMapper.findOrgInfoList(params);
        List<OrgPageListOut> resultList = new ArrayList<>();
        rootList.forEach(action -> {
            if (StrUtil.isBlank(action.getOrgSuperiorCode())) {
                resultList.add(action);
            }
        });
        //递归处理树形结构的数据
        resultList.forEach(action -> {
            action.setChildren(getOrgChild(action, rootList));
        });
        return resultList;
    }

    private List<OrgPageListOut> getOrgChild(OrgPageListOut org, List<OrgPageListOut> root) {
        List<OrgPageListOut> childList = new ArrayList<>();
        root.forEach(action -> {
            if (org.getOrgCode().equals(action.getOrgSuperiorCode())) {
                if ("1".equals(action.getOrgType())) {
                    action.setProvinceName(org.getProvinceName());
                    action.setCityName(org.getCityName());
                    action.setOrgAddr(org.getOrgAddr());
                }
                childList.add(action);
            }
        });
        if (childList.isEmpty()) {
            return null;
        }
        // 把子菜单的子菜单再循环一遍
        childList.forEach(action -> {
            action.setChildren(getOrgChild(action, root));
        });
        return childList;
    }

    /**
     * 新增组织信息
     *
     * @param save
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrgInfo(OrgInfoSaveVo save) {
        OrgInfoEntity insert = new OrgInfoEntity();
        BeanUtil.copyProperties(save, insert);
        insert.setOrgCode(CommonUtils.createCode("OR"));
        if (StrUtil.isNotBlank(save.getOrgSuperiorCode())) {
            OrgInfoEntity orgInfo = new LambdaQueryChainWrapper<>(baseMapper)
                    .eq(OrgInfoEntity::getOrgCode, save.getOrgSuperiorCode())
                    .one();
            if (orgInfo != null) {
                insert.setOrgLevel(orgInfo.getOrgLevel() + 1);
            }
        }else{
            insert.setOrgSuperiorCode("");
        }
        baseMapper.insert(insert);
        //处理管理人员权限
        new LambdaQueryChainWrapper<>(sysUserDao)
                .eq(SysUserEntity::getIsAllOrg, 1)
                .list().forEach(action -> {
                    if (StrUtil.isNotBlank(action.getOrgCodeList())) {
                        List<String> orgCodeList = new ArrayList<>(Arrays.asList(action.getOrgCodeList().split("\\,")));
                        if (orgCodeList.contains(insert.getOrgSuperiorCode())) {
                            orgCodeList.add(insert.getOrgCode());
                            action.setOrgCodeList(CollUtil.join(orgCodeList, ","));
                            sysUserDao.updateById(action);
                        }
                    }
                });
        //创建组织
        if (CollUtil.isNotEmpty(save.getFileList())) {
            orgInfoAccessoryService.saveEntity(insert.getOrgCode(), save.getFileList());
        }
    }

    /**
     * 修改组织信息
     *
     * @param update
     */
    @Override
    public void updateOrgInfo(OrgInfoUpdateVo update) {
        OrgInfoEntity orgInfo = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(OrgInfoEntity::getOrgCode, update.getOrgCode())
                .one();
        if (orgInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("组织信息不存在"));
        }
        OrgInfoEntity updateInfo = new OrgInfoEntity();
        BeanUtil.copyProperties(update, updateInfo);
        updateInfo.setId(orgInfo.getId());
        if (StrUtil.isNotBlank(update.getOrgSuperiorCode())) {
            OrgInfoEntity orgSuperiorInfo = new LambdaQueryChainWrapper<>(baseMapper)
                    .eq(OrgInfoEntity::getOrgCode, update.getOrgSuperiorCode())
                    .one();
            if (orgInfo != null) {
                updateInfo.setOrgLevel(orgSuperiorInfo.getOrgLevel() + 1);
            }
        }
        baseMapper.updateById(updateInfo);
        //删除之前的文件
        orgInfoAccessoryService.remove(new LambdaQueryWrapper<OrgInfoAccessoryEntity>()
                .eq(OrgInfoAccessoryEntity::getOrgCode, update.getOrgCode()));
        //新增附件
        if (CollUtil.isNotEmpty(update.getFileList())) {
            orgInfoAccessoryService.saveEntity(orgInfo.getOrgCode(), update.getFileList());
        }
    }

    /**
     * 获取组织详情
     *
     * @param code
     * @return
     */
    @Override
    public OrgInfoOut findOrgInfoByCode(String code) {
        OrgInfoOut out = baseMapper.findOrgInfoByCode(code);
        if (out == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("组织信息不存在"));
        }
        //获取附件类表
        List<OrgInfoAccessoryEntity> fileList = orgInfoAccessoryService.list(
                Wrappers.<OrgInfoAccessoryEntity>lambdaQuery().eq(OrgInfoAccessoryEntity::getOrgCode, code)
        );
        fileList.forEach(action -> {
            action.setFilePath(DomainUtil.addOssDomainIfNotExist(action.getFilePath()));
        });
        //处理组织代码树orgParentNodeCode
        if (StrUtil.isNotBlank(out.getOrgSuperiorCode())) {
            out.setOrgParentNodeCode(getParentNode(out.getOrgSuperiorCode()));
        }
        out.setFileList(fileList);
        return out;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public OrgInfoEntity getByCode(String code) {
        return this.getOne(
                Wrappers.<OrgInfoEntity>lambdaQuery().eq(OrgInfoEntity::getOrgCode, code)
        );
    }

    @Override
    public OrgInfoEntity info(String code) {
        OrgInfoEntity orgInfo = this.getByCode(code);
        if (orgInfo != null) {
            // 组装附件信息
            List<OrgInfoAccessoryEntity> list = orgInfoAccessoryService.list(
                    Wrappers.<OrgInfoAccessoryEntity>lambdaQuery().eq(OrgInfoAccessoryEntity::getOrgCode, code)
            );
            if (list != null && list.size() > 0) {
                list = list.stream().peek(item -> item.setFilePath(DomainUtil.addOssDomainIfNotExist(item.getFilePath()))).collect(Collectors.toList());

            }
            orgInfo.setOrgInfoAccessoryEntityList(list);

            // 组装上级组织信息
            orgInfo.setSuperiorOrg(buildSuperiorList(orgInfo.getOrgSuperiorCode()));

            // 组装地区信息
            SysRegionInfo sysRegionInfo = sysRegionInfoService.infoByCityCode(orgInfo.getOrgCity());
            orgInfo.setRegionInfo(sysRegionInfo);

            // 组装下属人数
            int count = new LambdaQueryChainWrapper<>(agentUserInfoDao)
                    .eq(AgentUserInfoEntity::getOrgCode, orgInfo.getOrgCode()).count();
            orgInfo.setAgentNum(count);
        }
        return orgInfo;
    }

    /**
     * 构建上级组织信息列表
     *
     * @param superiorCode 上级组织code
     * @return 组装好的上级组织信息
     */
    private List<OrgInfoVo> buildSuperiorList(String superiorCode) {
        List<OrgInfoVo> orgSuperiorInfoVo = new ArrayList<>();
        while (StringUtils.isNotBlank(superiorCode)) {
            OrgInfoVo orgInfoVo = new OrgInfoVo();
            OrgInfoEntity entity = this.getOne(
                    Wrappers.<OrgInfoEntity>lambdaQuery()
                            .eq(OrgInfoEntity::getOrgCode, superiorCode)
            );
            BeanUtils.copyProperties(entity, orgInfoVo);
            orgSuperiorInfoVo.add(orgInfoVo);
            superiorCode = entity.getOrgSuperiorCode();
        }

        Collections.reverse(orgSuperiorInfoVo);

        return orgSuperiorInfoVo;
    }

    @Override
    public List<OrgInfoVo> getOrgSonList(String code) {
        List<OrgInfoEntity> list = this.getSonEntityList(code);
        if (list != null && list.size() > 0) {
            return list.stream().map(item -> {
                OrgInfoVo vo = new OrgInfoVo();
                BeanUtils.copyProperties(item, vo);
                vo.setOrgStatus(item.getOrgStatus());
                return vo;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取所有父节点信息,包含当前节点
     *
     * @param code
     * @return
     */
    @Override
    public List<String> getParentNode(String code) {
        if (StrUtil.isBlank(code)) {
            return Collections.emptyList();
        }
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper).list();
        List<String> resultList = new ArrayList<>();
        resultList.add(code);
        getParentNode(orgInfoEntityList, resultList);
        return resultList;
    }

    @Override
    public List<List<String>> getParentNodeList(List<String> code) {
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper).list();
        return code.stream().map(m -> {
            List<String> resultList = new ArrayList<>();
            resultList.add(m);
            getParentNode(orgInfoEntityList, resultList);
            return resultList;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, OrgInfoEntity> findFirstOrgInfo(List<String> codeList) {
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper).list();
        Map<String, OrgInfoEntity> map = new HashMap<>();
        codeList.stream().filter(f -> StrUtil.isNotBlank(f)).distinct().forEach(code -> {
            OrgInfoEntity orgInfo = orgInfoEntityList.stream().filter(x -> x.getOrgCode().equals(code)).findFirst().get();
            if (StrUtil.isNotBlank(orgInfo.getOrgSuperiorCode())) {
                orgInfo = getFirstOrgInfo(orgInfoEntityList, orgInfo);
            }
            map.put(code, orgInfo);
        });
        return map;
    }

    /**
     * 获取顶级节点信息
     *
     * @param orgList
     * @param orgInfo
     */
    private OrgInfoEntity getFirstOrgInfo(List<OrgInfoEntity> orgList, OrgInfoEntity orgInfo) {
        for (OrgInfoEntity action : orgList) {
            if (orgInfo.getOrgSuperiorCode().equals(action.getOrgCode())) {
                if (StrUtil.isNotBlank(action.getOrgSuperiorCode())) {
                    return getFirstOrgInfo(orgList, action);
                } else {
                    return action;
                }
            }
        }
        return orgInfo;
    }

    private void getParentNode(List<OrgInfoEntity> orgList, List<String> resultList) {
        String code = resultList.get(0);
        orgList.forEach(action -> {
            if (code.equals(action.getOrgCode())
                    && StrUtil.isNotBlank(action.getOrgSuperiorCode())) {
                resultList.add(0, action.getOrgSuperiorCode());
                getParentNode(orgList, resultList);
            }
        });
    }

    /**
     * 获取所有子节点信息,包含当前节点
     *
     * @param code
     * @return
     */
    @Override
    public List<String> getChildNode(String code) {
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper).list();
        List<String> resultList = new ArrayList<>();
        resultList.add(code);
        resultList.addAll(getChildNode(orgInfoEntityList, code));
        return resultList;
    }

    private List<String> getChildNode(List<OrgInfoEntity> orgList, String code) {
        List<String> resultList = new ArrayList<>();
        orgList.forEach(action -> {
            if (code.equals(action.getOrgSuperiorCode())) {
                resultList.add(action.getOrgCode());
                resultList.addAll(getChildNode(orgList, action.getOrgCode()));
            }
        });
        return resultList;
    }

    @Override
    public List<OrgInfoEntity> getSonEntityList(String code) {
        return this.list(
                Wrappers.<OrgInfoEntity>lambdaQuery()
                        .eq(OrgInfoEntity::getOrgSuperiorCode, code)
        );
    }


    /**
     * 获取组织树列表
     *
     * @return
     */
    @Override
    public List<TreeListOut> findOrgTreeList() {
        SysUserEntity userEntity = ShiroUtils.getUserEntity();
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(OrgInfoEntity::getOrgStatus, StatusEnum.INVALID.getCode())
                .orderByAsc(OrgInfoEntity::getId)
                .list();
        List<String> orgList = new ArrayList<>();
        if (userEntity.getIsAllOrg().equals(StatusEnum.NORMAL.getCode())) {
            List<String> split = StrUtil.split(userEntity.getOrgCodeList(), ',');
            if (split.isEmpty()) {
                return Collections.emptyList();
            }
            split.forEach(action -> {
                if (orgList.contains(action)) {
                    return;
                }
                orgList.add(action);
                getParentNode(orgInfoEntityList, orgList);
            });
        }
        List<TreeListOut> rootList = new ArrayList<>();
        List<TreeListOut> resultList = new ArrayList<>();
        orgInfoEntityList.forEach(action -> {
            if (userEntity.getIsAllOrg().equals(StatusEnum.NORMAL.getCode())
                    && !orgList.contains(action.getOrgCode())) {
                return;
            }
            TreeListOut out = new TreeListOut();
            out.setId(action.getOrgCode());
            out.setLabel(action.getOrgName());
            out.setParentId(action.getOrgSuperiorCode());
            rootList.add(out);
            if (StrUtil.isBlank(action.getOrgSuperiorCode())) {
                resultList.add(out);
            }
        });
        //递归处理树形结构的数据
        resultList.forEach(action -> {
            action.setChildren(getChild(action.getId(), rootList));
        });
        return resultList;
    }

    @Override
    public List<TreeListOut> findAllTreeList() {
        List<OrgInfoEntity> orgInfoEntityList = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(OrgInfoEntity::getOrgStatus, StatusEnum.INVALID.getCode())
                .orderByAsc(OrgInfoEntity::getId)
                .list();
        List<TreeListOut> rootList = new ArrayList<>();
        List<TreeListOut> resultList = new ArrayList<>();
        orgInfoEntityList.forEach(action -> {
            TreeListOut out = new TreeListOut();
            out.setId(action.getOrgCode());
            out.setLabel(action.getOrgName());
            out.setParentId(action.getOrgSuperiorCode());
            rootList.add(out);
            if (StrUtil.isBlank(action.getOrgSuperiorCode())) {
                resultList.add(out);
            }
        });
        //递归处理树形结构的数据
        resultList.forEach(action -> {
            action.setChildren(getChild(action.getId(), rootList));
        });
        return resultList;
    }

    /**
     * @param parentId
     * @param root
     * @return
     */
    private List<TreeListOut> getChild(String parentId, List<TreeListOut> root) {
        List<TreeListOut> childList = new ArrayList<>();
        root.forEach(action -> {
            if (parentId.equals(action.getParentId())) {
                childList.add(action);
            }
        });
        if (childList.isEmpty()) {
            return null;
        }
        // 把子菜单的子菜单再循环一遍
        childList.forEach(action -> {
            action.setChildren(getChild(action.getId(), root));
        });
        return childList;
    }

    /**
     * 获取所有组织列表
     * 该接口目前用在产品中心-协议管理
     *
     * @return
     */
    @Override
    public List<OrgInfoEntity> queryAllList() {
        List<OrgInfoEntity> orgInfoEntityList = list();
        return orgInfoEntityList;
    }

}
