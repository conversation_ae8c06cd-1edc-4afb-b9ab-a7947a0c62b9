package com.mpolicy.manage.modules.policy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 导出保单数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PolicyExportVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 保单状态
     */
    @ExcelProperty(value = "保单状态")
    private String policyStatus;
    /**
     * 组合名称
     */
    @ExcelProperty(value = "投保产品")
    private String portfolioName;
    /**
     * 险种名称
     */
    @ExcelProperty(value = "险种名称")
    private String productName;
    /**
     * 缴费期间类型
     */
    @ExcelProperty(value = "缴费期间类型")
    private String paymentPeriodType;
    /**
     * 缴费时长
     */
    @ExcelProperty(value = "缴费时长")
    private String paymentPeriod;
    /**
     * 保障期间类型
     */
    @ExcelProperty(value = "保障期间类型")
    private String insuredPeriodType;
    /**
     * 保障时长
     */
    @ExcelProperty(value = "保障时长")
    private String insuredPeriod;
    /**
     * 缴费方式
     */
    @ExcelProperty(value = "缴费方式")
    private String periodType;
    /**
     * 保额
     */
    @ExcelProperty(value = "保额")
    private BigDecimal coverage;
    /**
     * 保费
     */
    @ExcelProperty(value = "保费")
    private BigDecimal premium;
    /**
     * 投保日期
     */
    @ExcelProperty(value = "投保日期")
    private Date applicantTime;
    /**
     * 生效日期
     */
    @ExcelProperty(value = "生效日期")
    private Date effectiveDate;
    /**
     * 回执签署日期
     */
    @ExcelProperty(value = "回执签署日期")
    private Date receiptSignTime;
    /**
     * 回访时间
     */
    @ExcelProperty(value = "回访时间")
    private Date revisitTime;
    /**
     * 保司名称
     */
    @ExcelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 投保人姓名
     */
    @ExcelProperty(value = "投保人姓名")
    private String applicantName;
    /**
     * 投保人性别
     */
    @ExcelProperty(value = "投保人性别")
    private String applicantGender;
    /**
     * 投被保人关系
     */
    @ExcelProperty(value = "投被保人关系")
    private String insuredRelation;
    /**
     * 被保人姓名
     */
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人性别
     */
    @ExcelProperty(value = "被保人性别")
    private String insuredGender;
    /**
     * 代理人姓名
     */
    @ExcelProperty(value = "代理人姓名")
    private String agentName;
    /**
     * 代理人业务编码
     */
    @ExcelProperty(value = "代理人业务编码")
    private String businessCode;
    /**
     * 推荐人姓名
     */
    @ExcelProperty(value = "推荐人姓名")
    private String referrerName;
    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String referrerWno;
    /**
     * 推荐人地区
     */
    @ExcelProperty(value = "推荐人地区")
    private String referrerRegion;
    /**
     * 渠道
     */
    @ExcelProperty(value = "渠道")
    private String channelName;
}
