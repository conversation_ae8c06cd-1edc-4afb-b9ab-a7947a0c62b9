package com.mpolicy.manage.modules.policy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.utils.DesensitizedUtil;
import com.mpolicy.manage.common.utils.PolicyPermissionHelper;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.enums.AuthorityEnum;
import com.mpolicy.manage.enums.ProdTypeEnum;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.util.AgentConstantUtil;
import com.mpolicy.manage.modules.insurance.entity.InsuranceProductInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceProductInfoService;
import com.mpolicy.manage.modules.insurance.vo.ProductDetail;
import com.mpolicy.manage.modules.policy.dao.PolicyContractInfoDao;
import com.mpolicy.manage.modules.policy.entity.EpPolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyTransferTaskEntity;
import com.mpolicy.manage.modules.policy.entity.PolicyContractInfoEntity;
import com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo;
import com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer;
import com.mpolicy.manage.modules.policy.enums.PolicyExportMethodEnum;
import com.mpolicy.manage.modules.policy.service.EpPolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.EpPolicyTransferTaskService;
import com.mpolicy.manage.modules.policy.service.PolicyContractInfoService;
import com.mpolicy.manage.modules.policy.service.common.EpPolicyBaseService;
import com.mpolicy.manage.modules.policy.service.common.PolicyChannelHelper;
import com.mpolicy.manage.modules.policy.service.preservation.PreservationApplyService;
import com.mpolicy.manage.modules.policy.service.common.PolicyProductBaseService;
import com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo;
import com.mpolicy.manage.modules.policy.vo.EpV2PolicyExportVo;
import com.mpolicy.manage.modules.policy.vo.PolicyInfoVo;
import com.mpolicy.manage.modules.policy.vo.policy.*;
import com.mpolicy.manage.modules.policy.vo.product.PolicyInsuredProductVo;
import com.mpolicy.manage.modules.policy.vo.supervise.EpV2PolicyExportSupVo;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.manage.modules.sys.util.LambdaUtils;
import com.mpolicy.manage.modules.tools.component.ToolsModelChangeLogHelp;
import com.mpolicy.manage.modules.tools.enums.ToolsModelChangeLogTypeEnum;
import com.mpolicy.manage.modules.tools.vo.ToolsModelChangeLogVo;
import com.mpolicy.order.common.order.PolicyInsured;
import com.mpolicy.policy.client.EpPolicyClient;
import com.mpolicy.policy.client.PolicyRenewalTermClient;
import com.mpolicy.policy.common.enums.*;
import com.mpolicy.policy.common.enums.contract.PolicyCorrectedModuleTypeEnum;
import com.mpolicy.policy.common.ep.policy.*;
import com.mpolicy.policy.common.ep.policy.brief.EpContractBriefInfoVo;
import com.mpolicy.policy.common.ep.policy.corrected.PolicyCorrectedLogQuery;
import com.mpolicy.policy.common.ep.policy.corrected.PolicyCorrectedLogVo;
import com.mpolicy.policy.common.ep.policy.preserve.product.PolicyProductBeneficiaryForm;
import com.mpolicy.policy.common.ep.policy.product.EpInsuredProductVo;
import com.mpolicy.policy.common.ep.policy.renewal.EpPolicyRenewalTermVo;
import com.mpolicy.policy.common.policy.vo.sync.PolicyInsuredInfoVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("policyContractInfoService")
@Slf4j
public class PolicyContractInfoServiceImpl extends ServiceImpl<PolicyContractInfoDao, PolicyContractInfoEntity> implements PolicyContractInfoService {

    /**
     * 页面导出最大记录数
     */
    public static final int EXPORT_LARGE_RECORDS = 20000;
    /**
     * 最大in数量
     */
    public static final int MAXIMUM_IN = 4000;
    /**
     * 导出时一个sheet大数量
     */
    public static final int SHEET_MAX = 1000000;
    /**
     * 每次查询的id数量【只包含id数据】
     */
    public static final int QUANTITY_PER_PROCESSING_ID = 100000;

    @Autowired
    private EpPolicyContractInfoService epPolicyContractInfoService;
    @Autowired
    private AgentUserInfoService agentUserInfoService;
    @Autowired
    private EpPolicyClient epPolicyClient;
    @Autowired
    private EpPolicyBaseService policyBaseService;
    @Autowired
    private SysDocumentService sysDocumentService;
    @Autowired
    private ChannelInfoService channelInfoService;
    @Autowired
    private ChannelBranchInfoService channelBranchInfoService;
    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private EpPolicyBaseService epPolicyBaseService;
    @Autowired
    private PolicyRenewalTermClient renewalTermClient;
    @Autowired
    private EpPolicyTransferTaskService epPolicyTransferTaskService;
    @Autowired
    private ChannelDistributionInfoService channelDistributionInfoService;
    @Autowired
    private InsuranceProductInfoService insuranceProductInfoService;
    @Autowired
    private PreservationApplyService preservationApplyService;
    @Autowired
    private PolicyProductBaseService policyProductBaseService;

    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public Integer queryCount(Map<String, Object> params) {
        LambdaQueryWrapper<EpPolicyContractInfoEntity> queryWrapper = buildQueryWrapper(params);
        if (queryWrapper == null) {
            return 0;
        }
        return epPolicyContractInfoService.count(queryWrapper);
    }

    @Override
    public PageUtils<PolicyInfoVo> queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<EpPolicyContractInfoEntity> queryWrapper = buildQueryWrapper(params);
        if (queryWrapper == null) {
            return new PageUtils<>(Collections.emptyList(), 0, 0, 1);
        }

        IPage<EpPolicyContractInfoEntity> policyInfoVoIPage = epPolicyContractInfoService.page(
                new Query<EpPolicyContractInfoEntity>().getPage(params).setSearchCount(false),
                queryWrapper
        );
        List<EpPolicyContractInfoEntity> records = policyInfoVoIPage.getRecords();

        //构建渠道信息map
        Map<String, String> channelNameMap = channelInfoService.list().stream().collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, ChannelInfoEntity::getChannelName));

        //构建机构信息map
        Map<String, String> orgNameMap = orgInfoService.list().stream().collect(Collectors.toMap(OrgInfoEntity::getOrgCode, OrgInfoEntity::getOrgName));
        //构建代理人信息map
        Map<String, AgentUserInfoEntity> agentNameMap = agentUserInfoService.list().stream().collect(Collectors.toMap(AgentUserInfoEntity::getAgentCode, Function.identity()));

        //构建渠道分支信息map
        Map<String, String> channelBranchNameMap = channelBranchInfoService.list().stream().collect(Collectors.toMap(ChannelBranchInfoEntity::getBranchCode, ChannelBranchInfoEntity::getBranchName));
        //构建渠道推荐人信息map
        List<String> referrerCodeList = records.stream()
                .filter(x -> StatusEnum.INVALID.getCode().equals(x.getReferrerType()))
                .map(EpPolicyContractInfoEntity::getReferrerCode).collect(Collectors.toList());
        Map<String, ChannelApplicationReferrerEntity> referrerNameMap = Collections.EMPTY_MAP;
        if (!referrerCodeList.isEmpty()) {
            referrerNameMap = channelApplicationReferrerService.lambdaQuery()
                    .in(ChannelApplicationReferrerEntity::getReferrerCode, referrerCodeList)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(ChannelApplicationReferrerEntity::getReferrerCode, Function.identity()));
        }

        Map<String, ChannelApplicationReferrerEntity> finalReferrerNameMap = referrerNameMap;
        List<PolicyInfoVo> policyInfoVoList = records.stream().map(x -> {
            PolicyInfoVo policyInfoVo = new PolicyInfoVo();
            BeanUtils.copyProperties(x, policyInfoVo);
            //保单提交状态
            if (x.getIntact().equals(PolicyIntactTypeEnum.TEMP.getCode())) {
                policyInfoVo.setIsSubmit("暂存");
            } else {
                policyInfoVo.setIsSubmit("已提交");
            }
            //产品类型code
            policyInfoVo.setPolicyProductTypeCode(x.getPolicyProductType());
            //保单状态
            if (StringUtils.isNotBlank(x.getAdminPolicyStatus())) {
                policyInfoVo.setPolicyStatus(PolicyContractStatusEnum.getPolicyContractStatusEnum(x.getAdminPolicyStatus()).getStatusDesc());
            }
            //保单类型
            policyInfoVo.setPolicyType(PolicyTypeEnum.getProdTypeEnum(x.getPolicyType()).getDesc());
            //产品类型
            PolicyProductTypeEnum prodTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(x.getPolicyProductType());
            if (prodTypeEnum != null) {
                policyInfoVo.setPolicyProductType(prodTypeEnum.getPolicyProductType());
            }

            AgentUserInfoEntity policyReferrer = agentNameMap.get(x.getAgentCode());
            if(policyReferrer!=null){
                policyInfoVo.setPolicyReferrerName(policyReferrer.getAgentName());
                // 线下则设置代理人，否则代理人为空
                if (Objects.equals(PolicySalesTypeEnum.OUT_LINE.getCode(), x.getSalesType())) {
                    policyInfoVo.setAgentName(policyReferrer.getAgentName());
                }
            }

            policyInfoVo.setChannelName(channelNameMap.get(x.getChannelCode()));
            //根据渠道推荐人类型来做不同的逻辑
            if (StatusEnum.NORMAL.getCode().equals(x.getReferrerType())) {
                AgentUserInfoEntity referrer = agentNameMap.get(x.getAgentCode());
                if(referrer!=null){
                    policyInfoVo.setReferrerName(referrer.getAgentName());
                    policyInfoVo.setReferrerWno(referrer.getBusinessCode());
                }

                AgentUserInfoEntity customerManager = agentNameMap.get(x.getCustomerManagerCode());
                if(customerManager!=null){
                    policyInfoVo.setCustomerManagerName(customerManager.getAgentName());
                    policyInfoVo.setCustomerManagerChannelCode(customerManager.getBusinessCode());
                }
                policyInfoVo.setChannelBranchName(orgNameMap.get(x.getChannelBranchCode()));
            } else {
                ChannelApplicationReferrerEntity referrer = finalReferrerNameMap.get(x.getReferrerCode());
                if(referrer!=null){
                    policyInfoVo.setReferrerName(referrer.getReferrerName());
                    policyInfoVo.setReferrerWno(referrer.getReferrerWno());
                }

                ChannelApplicationReferrerEntity customerManager = finalReferrerNameMap.get(x.getCustomerManagerCode());
                if(customerManager!=null){
                    policyInfoVo.setCustomerManagerName(customerManager.getReferrerName());
                    policyInfoVo.setCustomerManagerChannelCode(customerManager.getReferrerWno());
                }
                policyInfoVo.setChannelBranchName(channelBranchNameMap.get(x.getChannelBranchCode()));
            }

            // 构建佣金发放状态
            PolicySettlementStatusEnum settlementStatusEnum = PolicySettlementStatusEnum.getSettlementStatusEnum(x.getSettlementStatus());
            policyInfoVo.setSettlementStatus(Optional.ofNullable(settlementStatusEnum).map(PolicySettlementStatusEnum::getDesc).orElse(""));
            return policyInfoVo;
        }).collect(Collectors.toList());

        return new PageUtils(policyInfoVoList, (int) policyInfoVoIPage.getTotal(), (int) policyInfoVoIPage.getSize(), (int) policyInfoVoIPage.getCurrent());
    }

    /**
     * 构建查询条件
     *
     * @param params
     * @return
     */
    private LambdaQueryWrapper<EpPolicyContractInfoEntity> buildQueryWrapper(Map<String, Object> params) {
        // 权限 机构 如果是部分才处理
        List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        if (orgCodeList != null && orgCodeList.size() == 0) {
            return null;
        }
        List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();

        if (channelBranchCodeList != null && channelBranchCodeList.size() == 0) {
            return null;
        }
        List<String> channelDistributionCodeList = PolicyPermissionHelper.getChannelDistributionCodeList();
        final boolean condition = !ShiroUtils.getSubject().isPermitted("policy:onLineVisible");
        return buildQueryWrapper(params, orgCodeList, channelBranchCodeList, channelDistributionCodeList, condition);
    }

    /**
     * 构建查询条件
     *
     * @param params
     * @return
     */
    public LambdaQueryWrapper<EpPolicyContractInfoEntity> buildQueryWrapper(Map<String, Object> params,
                                                                             List<String> orgCodeList,
                                                                             List<String> channelBranchCodeList,
                                                                             List<String> channelDistributionCodeList,
                                                                             boolean condition) {
        params.put("sidx", "id");
        params.put("order", "desc");
        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //投保人证件号
        String applicantIdCard = RequestUtils.objectValueToString(params, "applicantIdCard");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //被保人证件号
        String insuredIdCard = RequestUtils.objectValueToString(params, "insuredIdCard");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //业务分类
        String salesType = RequestUtils.objectValueToString(params, "salesType");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBeginOrderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");
        //赠险标识
        Integer giveFlag = RequestUtils.objectValueToInteger(params, "giveFlag");
        //续保标识
        Integer renewalType = RequestUtils.objectValueToInteger(params, "renewalType");
        //是否签署客户告知书
        Integer customerAgreed = RequestUtils.objectValueToInteger(params, "customerAgreed");
        //长短险标识 0短险1长险
        final Integer longShortFlag = RequestUtils.objectValueToInteger(params, "longShortFlag");
        // 销售渠道
        final Integer superviseChannelCode = RequestUtils.objectValueToInteger(params, "superviseChannelCode");
        //分销渠道
        final String[] selectChannelDistribution = RequestUtils.objectValueToStringList(params, "selectChannelDistribution");
        // 家庭保单号
        String familyPolicyNo = RequestUtils.objectValueToString(params, "familyPolicyNo");
        //承保起始日期
        String approvedTimeStart = RequestUtils.objectValueToString(params, "approvedTimeStart");
        //承保终止日期
        String approvedTimeEnd = RequestUtils.objectValueToString(params, "approvedTimeEnd");
        //提交状态
        List<Integer> intactList = null;
        Integer isSubmit = RequestUtils.objectValueToInteger(params, "isSubmit");
        if (isSubmit != null) {
            if (isSubmit == 0) {
                intactList = Collections.singletonList(PolicyIntactTypeEnum.TEMP.getCode());
            } else {
                intactList = Arrays.asList(PolicyIntactTypeEnum.INTACT.getCode(), PolicyIntactTypeEnum.UN_INTACT.getCode());
            }
        }
        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }
        LambdaQueryWrapper<EpPolicyContractInfoEntity> queryWrapper = new LambdaQueryWrapper<EpPolicyContractInfoEntity>()
                .eq(EpPolicyContractInfoEntity::getShowModel, StatusEnum.NORMAL.getCode())
                .in(policyProductTypes != null, EpPolicyContractInfoEntity::getPolicyProductType, policyProductTypes)
                .eq(StringUtils.isNotBlank(policyNo), EpPolicyContractInfoEntity::getPolicyNo, policyNo)
                .eq(StringUtils.isNotBlank(applicantPolicyNo), EpPolicyContractInfoEntity::getApplicantPolicyNo, applicantPolicyNo)
                .eq(StringUtils.isNotBlank(familyPolicyNo),EpPolicyContractInfoEntity::getFamilyPolicyNo, familyPolicyNo)
                .eq(giveFlag != null, EpPolicyContractInfoEntity::getGiveFlag, giveFlag)
                // 销售渠道编码-台账
                .eq(superviseChannelCode != null, EpPolicyContractInfoEntity::getSuperviseChannelCode, superviseChannelCode)
                .in(companyCodes != null, EpPolicyContractInfoEntity::getCompanyCode, companyCodes)
                .eq(StringUtils.isNotBlank(applicantName), EpPolicyContractInfoEntity::getApplicantName, applicantName)
                .eq(StringUtils.isNotBlank(policySource), EpPolicyContractInfoEntity::getSourcePlatform, policySource)
                .eq(StringUtils.isNotBlank(salesType), EpPolicyContractInfoEntity::getSalesType, salesType)
                .in(policyStatuses != null, EpPolicyContractInfoEntity::getAdminPolicyStatus, policyStatuses)
                .eq(settlementStatus != null, EpPolicyContractInfoEntity::getSettlementStatus, settlementStatus)
                .in(channelCodes != null, EpPolicyContractInfoEntity::getChannelCode, channelCodes)
                .inSql(StringUtils.isNotBlank(applicantIdCard), EpPolicyContractInfoEntity::getContractCode, "select contract_code from ep_policy_applicant_info where deleted=0 and applicant_id_card = '" + applicantIdCard + "'")
                .inSql(StringUtils.isNotBlank(insuredIdCard), EpPolicyContractInfoEntity::getContractCode, "select contract_code from ep_policy_insured_info where deleted=0 and insured_id_card = '" + insuredIdCard + "'")
                .inSql(StringUtils.isNotBlank(insuredName), EpPolicyContractInfoEntity::getContractCode, "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                .in(orgCodes != null, EpPolicyContractInfoEntity::getOrgCode, orgCodes)
                .isNull(renewalType != null && renewalType == 0, EpPolicyContractInfoEntity::getSourcePolicyNo)
                .isNotNull(renewalType != null && renewalType == 1, EpPolicyContractInfoEntity::getSourcePolicyNo)
                .and(StringUtils.isNotBlank(portfolioNameOrCode),
                        x -> x.likeRight(EpPolicyContractInfoEntity::getPortfolioName, portfolioNameOrCode))
                .in(referrerCodes != null, EpPolicyContractInfoEntity::getReferrerCode, referrerCodes)
                .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                        x.eq(EpPolicyContractInfoEntity::getIsReceipt, StatusEnum.INVALID.getCode())
                                .or()
                                .eq(EpPolicyContractInfoEntity::getIsRevisit, StatusEnum.INVALID.getCode())
                )
                .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                        x.eq(suspendTypeSet.contains("1"), EpPolicyContractInfoEntity::getIsReceipt, StatusEnum.INVALID.getCode())
                                .or()
                                .eq(suspendTypeSet.contains("2"), EpPolicyContractInfoEntity::getIsRevisit, StatusEnum.INVALID.getCode()))
                .in(intactList != null, EpPolicyContractInfoEntity::getIntact, intactList)
                .inSql(agentCodes != null, EpPolicyContractInfoEntity::getContractCode, StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                .eq(customerAgreed != null, EpPolicyContractInfoEntity::getOfflineProductSignStatus, customerAgreed)
                .ge(StringUtils.isNotBlank(orderTimeBeginOrderTimeBegin), EpPolicyContractInfoEntity::getOrderTime, orderTimeBeginOrderTimeBegin)
                .le(StringUtils.isNotBlank(orderTimeEnd), EpPolicyContractInfoEntity::getOrderTime, StrUtil.concat(true, orderTimeEnd, " 23:59:59"))
                .ge(StringUtils.isNotBlank(enforceTimeBegin), EpPolicyContractInfoEntity::getEnforceTime, enforceTimeBegin)
                .le(StringUtils.isNotBlank(enforceTimeEnd), EpPolicyContractInfoEntity::getEnforceTime, StrUtil.concat(true, enforceTimeEnd, " 23:59:59"))
                .ge(StringUtils.isNotBlank(approvedTimeStart), EpPolicyContractInfoEntity::getApprovedTime, approvedTimeStart)
                .le(StringUtils.isNotBlank(approvedTimeEnd), EpPolicyContractInfoEntity::getApprovedTime, StrUtil.concat(true, approvedTimeEnd, " 23:59:59"))
                // 1.4.1添加保单权限
                .in(channelBranchCodeList != null, EpPolicyContractInfoEntity::getPolicyChannelBranchCode, channelBranchCodeList)
                .in(orgCodeList != null, EpPolicyContractInfoEntity::getOrgCode, orgCodeList)
                .in(CollectionUtils.isNotEmpty(channelDistributionCodeList), EpPolicyContractInfoEntity::getChannelDistributionCode, channelDistributionCodeList)
                // 如果不存在线上单查看权限添加 线下单 条件
                .eq(condition, EpPolicyContractInfoEntity::getSalesType, PolicySalesTypeEnum.OUT_LINE.getCode())
                // 添加长短险
                .inSql(longShortFlag != null, EpPolicyContractInfoEntity::getMainProductCode,
                        StrUtil.format("SELECT product_code FROM  insurance_product_info  WHERE deleted=0 AND long_short_flag='{}'",
                                longShortFlag))
                .in(Objects.nonNull(selectChannelDistribution) && selectChannelDistribution.length != 0
                        , EpPolicyContractInfoEntity::getChannelDistributionCode
                        , selectChannelDistribution)
                .comment("xiaowhale query");

        log.info("buildQueryWrapper queryWrapper map  = {}",JSON.toJSONString(queryWrapper.getParamNameValuePairs()));
        log.info("buildQueryWrapper queryWrapper SqlSegment = {}",JSON.toJSONString(queryWrapper.getExpression().getSqlSegment()));
        return queryWrapper;
    }


    @Override
    public int getExportPolicyMaxEpcid(Map<String, Object> params) {
        List<String> orgCodeList = (List<String>) params.get("orgCodeList");
        List<String> channelBranchCodeList = (List<String>) params.get("channelBranchCodeList");
        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //业务分类
        String salesType = RequestUtils.objectValueToString(params, "salesType");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");

        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }
        Integer exportPolicyMaxEpcid = baseMapper.getExportPolicyMaxEpcid(
                new QueryWrapper<>()
                        .ne("intact", PolicyIntactTypeEnum.TEMP.getCode())
                        .eq("show_model", StatusEnum.NORMAL.getCode())
                        .in(policyProductTypes != null, "policy_product_type", policyProductTypes)
                        .eq(StringUtils.isNotBlank(policyNo), "policy_no", policyNo)
                        .eq(StringUtils.isNotBlank(applicantPolicyNo), "applicant_policy_no", applicantPolicyNo)
                        .in(companyCodes != null, "company_code", companyCodes)
                        .likeRight(StringUtils.isNotBlank(applicantName), "epai.applicant_name", applicantName)
                        .eq(StringUtils.isNotBlank(policySource), "source_platform", policySource)
                        .eq(StringUtils.isNotBlank(salesType), "sales_type", salesType)
                        .in(policyStatuses != null, "admin_policy_status", policyStatuses)
                        .inSql(StringUtils.isNotBlank(insuredName), "epci.contract_code", "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                        .in(channelCodes != null, "epci.channel_code", channelCodes)
                        .in(orgCodes != null, "epci.org_code", orgCodes)
                        .and(StringUtils.isNotBlank(portfolioNameOrCode),
                                x -> x.likeRight("portfolio_name", portfolioNameOrCode))
                        .in(referrerCodes != null, "epci.referrer_code", referrerCodes)
                        .inSql(agentCodes != null, "epci.contract_code", StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                        .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                                x.eq("epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq("epci.is_revisit", StatusEnum.INVALID.getCode())
                        )
                        .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                                x.eq(suspendTypeSet.contains("1"), "epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq(suspendTypeSet.contains("2"), "epci.is_revisit", StatusEnum.INVALID.getCode()))
                        .eq(settlementStatus != null, "epci.settlement_status", settlementStatus)
                        .ge(StringUtils.isNotBlank(orderTimeBegin), "epci.order_time", orderTimeBegin)
                        .le(StringUtils.isNotBlank(orderTimeEnd), "epci.order_time", StrUtil.concat(true, orderTimeEnd, " 23:59:59"))
                        .ge(StringUtils.isNotBlank(enforceTimeBegin), "epci.enforce_time", enforceTimeBegin)
                        .le(StringUtils.isNotBlank(enforceTimeEnd), "epci.enforce_time", StrUtil.concat(true, enforceTimeEnd, " 23:59:59"))
                        .in(channelBranchCodeList != null, "epci.policy_channel_branch_code", channelBranchCodeList)
                        .in(orgCodeList != null, "epci.org_code", orgCodeList)
                        // 如果不存在线上单查看权限添加 线下单 条件
                        .eq(!ShiroUtils.getSubject().isPermitted("policy:onLineVisible"), "epci.sales_type", PolicySalesTypeEnum.OUT_LINE.getCode())
                        .orderByDesc("epci.id")
                        .last(StrUtil.format(" limit 1"))
        );
        if (exportPolicyMaxEpcid == null) {
            return 0;
        }
        return exportPolicyMaxEpcid;
    }


    /**
     * 导出功能明细，改版为exportPolicyV2
     *
     * @param params
     * @return
     */
    @Override
    @Deprecated
    public List<EpPolicyExportVo> exportPolicy(Map<String, Object> params, boolean sensitiveDataAccess) {
        List<String> orgCodeList = (List<String>) params.get("orgCodeList");
        List<String> channelBranchCodeList = (List<String>) params.get("channelBranchCodeList");
        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //业务分类
        String salesType = RequestUtils.objectValueToString(params, "salesType");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");

        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }

        //  分批查询导出的需要, 1 按条件导出采用 maxId + limitSize 2 全导出采用 startSize + limitSize(between条件查询)
        Integer betweenSize = RequestUtils.objectValueToInteger(params, "betweenSize");
        Integer startSize = RequestUtils.objectValueToInteger(params, "startSize");
        Integer maxId = RequestUtils.objectValueToInteger(params, "maxId");
        Integer limitSize = RequestUtils.objectValueToInteger(params, "limitSize");

        List<EpPolicyExportVo> exportList = baseMapper.exportPolicy(
                new QueryWrapper<>()
                        .ne("intact", PolicyIntactTypeEnum.TEMP.getCode())
                        .eq("show_model", StatusEnum.NORMAL.getCode())
                        .in(policyProductTypes != null, "policy_product_type", policyProductTypes)
                        .eq(StringUtils.isNotBlank(policyNo), "policy_no", policyNo)
                        .eq(StringUtils.isNotBlank(applicantPolicyNo), "applicant_policy_no", applicantPolicyNo)
                        .in(companyCodes != null, "company_code", companyCodes)
                        .likeRight(StringUtils.isNotBlank(applicantName), "epai.applicant_name", applicantName)
                        .eq(StringUtils.isNotBlank(policySource), "source_platform", policySource)
                        .eq(StringUtils.isNotBlank(salesType), "sales_type", salesType)
                        .in(policyStatuses != null, "admin_policy_status", policyStatuses)
                        .inSql(StringUtils.isNotBlank(insuredName), "epci.contract_code", "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                        .in(channelCodes != null, "epci.channel_code", channelCodes)
                        .in(orgCodes != null, "epci.org_code", orgCodes)
                        .and(StringUtils.isNotBlank(portfolioNameOrCode),
                                x -> x.likeRight("portfolio_name", portfolioNameOrCode))
                        .in(referrerCodes != null, "epci.referrer_code", referrerCodes)
                        .inSql(agentCodes != null, "epci.contract_code", StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                        .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                                x.eq("epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq("epci.is_revisit", StatusEnum.INVALID.getCode())
                        )
                        .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                                x.eq(suspendTypeSet.contains("1"), "epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq(suspendTypeSet.contains("2"), "epci.is_revisit", StatusEnum.INVALID.getCode()))
                        .eq(settlementStatus != null, "epci.settlement_status", settlementStatus)
                        .ge(StringUtils.isNotBlank(orderTimeBegin), "epci.order_time", orderTimeBegin)
                        .le(StringUtils.isNotBlank(orderTimeEnd), "epci.order_time", StrUtil.concat(true, orderTimeEnd, " 23:59:59"))
                        .ge(StringUtils.isNotBlank(enforceTimeBegin), "epci.enforce_time", enforceTimeBegin)
                        .le(StringUtils.isNotBlank(enforceTimeEnd), "epci.enforce_time", StrUtil.concat(true, enforceTimeEnd, " 23:59:59"))
                        //  方式1 between 导出
                        .between(betweenSize != null, "epci.id", betweenSize, limitSize)
                        .last(startSize != null, StrUtil.format("limit {},{}", startSize, limitSize))
                        .gt(maxId != null, "epci.id", maxId)
                        .last(maxId != null, StrUtil.format(" limit {}", limitSize))
                        // 1.4.1添加保单权限
                        .in(channelBranchCodeList != null, "epci.policy_channel_branch_code", channelBranchCodeList)
                        .in(orgCodeList != null, "epci.org_code", orgCodeList)
                        // 如果不存在线上单查看权限添加 线下单 条件
                        .eq(!ShiroUtils.getSubject().isPermitted("policy:onLineVisible"), "epci.sales_type", PolicySalesTypeEnum.OUT_LINE.getCode())
                        .comment("xiaowhale query")
        );

        //获取机构信息map
        List<OrgInfoEntity> list = orgInfoService.list();
        Map<String, OrgInfoEntity> orgInfoEntityMap = list.stream()
                .map(x -> {
                    OrgInfoEntity orgInfoEntity = new OrgInfoEntity();
                    BeanUtils.copyProperties(x, orgInfoEntity);
                    return orgInfoEntity;
                })
                .collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));
        // 构建机构名称树
        Map<String, OrgInfoEntity> orgTreeMap = list.stream().peek(x -> {
            String orgSuperiorCode = x.getOrgSuperiorCode();
            while (StringUtils.isNotBlank(orgSuperiorCode)) {
                OrgInfoEntity orgInfoEntity = orgInfoEntityMap.get(orgSuperiorCode);
                if (orgInfoEntity != null) {
                    if (StatusEnum.INVALID.getCode().equals(orgInfoEntity.getOrgType())) {
                        x.setOrgSuperiorName(orgInfoEntity.getOrgName());
                        break;
                    }
                    orgSuperiorCode = orgInfoEntity.getOrgSuperiorCode();
                } else {
                    orgSuperiorCode = null;
                }
            }
        }).collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));
        // 获取国家列表
        Map<String, String> countryMap = DicCacheHelper.getSons("COUNTRY_LIST").stream()
                .collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
        // 行业类别
        Map<String, String> industryCategoriesMap = DicCacheHelper.getSons("POLICY:INDUSTRY_CATEGORIES").stream()
                .collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
        // 单位性质
        Map<String, String> companyNatureMap = DicCacheHelper.getSons("POLICY:COMPANY_NATURE").stream()
                .collect(Collectors.toMap(DicCacheHelper.DicEntity::getKey, DicCacheHelper.DicEntity::getValue));
        List<EpPolicyExportVo> collect = exportList.stream().peek(x -> {
            // 保单信息
            x.setPolicyType(PolicyTypeEnum.getProdTypeEnum(Integer.valueOf(x.getPolicyType())).getDesc());
            x.setPolicyProductType(PolicyProductTypeEnum.getProdTypeEnum(x.getPolicyProductType()).getPolicyProductType());
            x.setSalesType(
                    Optional.ofNullable(PolicySalesTypeEnum.getSalesTypeEnum(Integer.valueOf(x.getSalesType())))
                            .map(PolicySalesTypeEnum::getDesc).orElse("")
            );
            x.setPolicyStatus(PolicyContractStatusEnum.getPolicyContractStatusEnum(x.getPolicyStatus()).getStatusDesc());
            x.setSalesPlatform(
                    Optional.ofNullable(PolicySalesPlatformEnum.getSalesPlatformEnum(x.getSalesPlatform()))
                            .map(PolicySalesPlatformEnum::getDesc).orElse("")
            );
            if (x.getSelfPreservation() != null) {
                x.setSelfPreservation("0".equals(x.getSelfPreservation()) ? "否" : "是");
            }
            if (x.getRevisitResult() != null) {
                x.setRevisitResult(PolicyOptResultEnum.getByCode(Integer.valueOf(x.getRevisitResult())).getDesc());
            }
            // 构建佣金发放状态
            PolicySettlementStatusEnum settlementStatusEnum = PolicySettlementStatusEnum.getSettlementStatusEnum(x.getSettlementStatus());
            x.setSettlementStatusStr(Optional.ofNullable(settlementStatusEnum).map(PolicySettlementStatusEnum::getDesc).orElse(""));
            if (PolicySettlementStatusEnum.SETTLEMENT.getCode().equals(x.getSettlementStatus())) {
                x.setSettlementMonthStr(StrUtil.format("{}-{}", x.getSettlementYear(), String.format("%02d", x.getSettlementMonth())));
            }

            // 代理人信息
            String mainFlag = x.getMainFlag();
            if (mainFlag != null) {
                x.setMainFlag("0".equals(mainFlag) ? "否" : "是");
            }
            String commissionRate = x.getCommissionRate();
            if (StringUtils.isNotBlank(commissionRate)) {
                x.setCommissionRate(new BigDecimal(commissionRate).multiply(new BigDecimal(100)).toPlainString() + "%");
            }
            String agentOrgCode = x.getOrgCode();
            if (StringUtils.isNotBlank(agentOrgCode)) {
                x.setOrgSuperiorName(orgTreeMap.getOrDefault(agentOrgCode, new OrgInfoEntity()).getOrgSuperiorName());
            }

            // 险种信息
            x.setMainInsurance("0".equals(x.getMainInsurance()) ? "否" : "是");
            x.setPaymentPeriodType(
                    Optional.ofNullable(PolicyPaymentPeriodTypeEnum.getPaymentPeriodTypeEnumByCode(x.getPaymentPeriodType()))
                            .map(PolicyPaymentPeriodTypeEnum::getPeriodDesc).orElse("")
            );
            x.setInsuredPeriodType(Optional.ofNullable(PolicyInsuredPeriodTypeEnum.getInsuredTypeEnumByCode(x.getInsuredPeriodType())).map(PolicyInsuredPeriodTypeEnum::getInsuredDesc).orElse(""));
            x.setPeriodType(PolicyPaymentTypeEnum.getPaymentDescByCode(x.getPeriodType()));
            x.setProductStatus(Optional.ofNullable(PolicyContractStatusEnum.getPolicyContractStatusEnum(x.getProductStatus())).map(PolicyContractStatusEnum::getDesc).orElse(""));

            // 投保人
            x.setApplicantNation(countryMap.get(x.getApplicantNation()));
            x.setApplicantType(PolicySubjectTypeEnum.getPolicySubjectTypeEnum(Integer.valueOf(x.getApplicantType())).getPolicySubjectType());
            x.setApplicantIdType(PolicyIdCardTypeEnum.getNameByCode(x.getApplicantIdType()));
            x.setApplicantMarital(Optional.ofNullable(PolicyMaritalEnum.getEnumByCode(x.getApplicantMarital())).map(PolicyMaritalEnum::getDesc).orElse(""));
            String applicantGender = x.getApplicantGender();
            if (StringUtils.isNotBlank(applicantGender)) {
                x.setApplicantGender("0".equals(applicantGender) ? "女" : "男");
            }
            x.setLegalPersonIdType(PolicyIdCardTypeEnum.getNameByCode(x.getLegalPersonIdType()));
            x.setApplicantIndustryCategory(industryCategoriesMap.get(x.getApplicantIndustryCategory()));
            x.setCompanyNature(companyNatureMap.get(x.getCompanyNature()));

            // 被保人
            x.setInsuredNation(countryMap.get(x.getInsuredNation()));
            x.setInsuredIdType(PolicyIdCardTypeEnum.getNameByCode(x.getInsuredIdType()));
            if (x.getInsuredType() != null) {
                x.setInsuredType(Optional.ofNullable(PolicySubjectTypeEnum.getPolicySubjectTypeEnum(Integer.valueOf(x.getInsuredType()))).map(PolicySubjectTypeEnum::getPolicySubjectType).orElse(""));
            }
            x.setInsuredRelation(Optional.ofNullable(PolicyFamilyTypeEnum.decode(x.getInsuredRelation())).map(PolicyFamilyTypeEnum::getFamilyDesc).orElse(""));
            String insuredGender = x.getInsuredGender();
            if (StringUtils.isNotBlank(insuredGender)) {
                x.setInsuredGender("0".equals(insuredGender) ? "女" : "男");
            }
            String insuredEducation = x.getInsuredEducation();
            if (StringUtils.isNotBlank(insuredEducation)) {
                x.setInsuredEducation(Optional.ofNullable(PolicyDegreeEnum.getProdTypeEnum(insuredEducation)).map(PolicyDegreeEnum::getPolicyDegreeType).orElse(""));
            }
            x.setInsuredMarital(Optional.ofNullable(PolicyMaritalEnum.getEnumByCode(x.getInsuredMarital())).map(PolicyMaritalEnum::getDesc).orElse(""));


            // 车险
            if (StringUtils.isNotBlank(x.getVehicleOwnerName())) {
                x.setVehicleOwnerCardType(PolicyIdCardTypeEnum.getNameByCode(x.getVehicleOwnerCardType()));

                String vehicleOwnerNature = x.getVehicleOwnerNature();
                if (StringUtils.isNotBlank(vehicleOwnerNature)) {
                    x.setVehicleOwnerNature(PolicyVehicleOwnerNatureEnum.getProdTypeEnum(vehicleOwnerNature).getPolicyVehicleOwnerNatureType());
                }

                String vehicleUsage = x.getVehicleUsage();
                if (StringUtils.isNotBlank(vehicleUsage)) {
                    x.setVehicleUsage("0".equals(vehicleUsage) ? "非营业" : "营业");
                }

                String vehicleNew = x.getVehicleNew();
                if (StringUtils.isNotBlank(vehicleNew)) {
                    x.setVehicleNew("0".equals(vehicleNew) ? "否" : "是");
                }

                String vehicleTransferOwnership = x.getVehicleTransferOwnership();
                if (StringUtils.isNotBlank(vehicleTransferOwnership)) {
                    x.setVehicleTransferOwnership("0".equals(vehicleTransferOwnership) ? "否" : "是");
                }

                String vehicleVariety = x.getVehicleVariety();
                if (StringUtils.isNotBlank(vehicleVariety)) {
                    x.setVehicleVariety(PolicyVehicleVarietyEnum.getProdTypeEnum(vehicleVariety).getPolicyVehicleOwnerVarietyType());
                }
            }
            if (!sensitiveDataAccess) {
                x.setApplicantIdCard(DesensitizedUtil.idCardNum(x.getApplicantIdCard(), 1, 1));
                x.setInsuredIdCard(DesensitizedUtil.idCardNum(x.getInsuredIdCard(), 1, 1));
                x.setLegalPersonIdCard(DesensitizedUtil.idCardNum(x.getLegalPersonIdCard(), 1, 1));
                x.setApplicantMobile(DesensitizedUtil.mobilePhone(x.getApplicantMobile()));
                x.setInsuredMobile(DesensitizedUtil.mobilePhone(x.getInsuredMobile()));
                x.setCompanyContactMobile(DesensitizedUtil.mobilePhone(x.getCompanyContactMobile()));
                x.setVehicleOwnerMobile(DesensitizedUtil.mobilePhone(x.getVehicleOwnerMobile()));
                x.setApplicantAddress(DesensitizedUtil.address(x.getApplicantAddress(), Integer.MAX_VALUE));
                x.setInsuredAddress(DesensitizedUtil.address(x.getInsuredAddress(), Integer.MAX_VALUE));
            }
        }).collect(Collectors.toList());

        return collect;
    }

    @Override
    public List<EpV2PolicyExportVo> exportPolicyV2(Map<String, Object> params, boolean sensitiveDataAccess) {

        log.info("执行导出的的条件信息={} ", JSON.toJSONString(params));

        List<String> orgCodeList = (List<String>) params.get("orgCodeList");
        List<String> channelBranchCodeList = (List<String>) params.get("channelBranchCodeList");
        List<String> channelDisCodeList = (List<String>) params.get("channelDisCodeList");

        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //业务分类
        String salesType = RequestUtils.objectValueToString(params, "salesType");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");
        //是否签署客户告知书
        Integer customerAgreed = RequestUtils.objectValueToInteger(params, "customerAgreed");


        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }

        //  分批查询导出的需要, 1 按条件导出采用 maxId + limitSize 2 全导出采用 startSize + limitSize(between条件查询)
        Integer betweenSize = RequestUtils.objectValueToInteger(params, "betweenSize");
        Integer startSize = RequestUtils.objectValueToInteger(params, "startSize");
        Integer maxId = RequestUtils.objectValueToInteger(params, "maxId");
        Integer limitSize = RequestUtils.objectValueToInteger(params, "limitSize");

        List<EpV2PolicyExportVo> exportList = baseMapper.exportPolicyV2(
                new QueryWrapper<>()
                        .ne("intact", PolicyIntactTypeEnum.TEMP.getCode())
                        .eq("show_model", StatusEnum.NORMAL.getCode())
                        .in(policyProductTypes != null, "policy_product_type", policyProductTypes)
                        .eq(StringUtils.isNotBlank(policyNo), "policy_no", policyNo)
                        .eq(StringUtils.isNotBlank(applicantPolicyNo), "applicant_policy_no", applicantPolicyNo)
                        .in(companyCodes != null, "epci.company_code", companyCodes)
                        .likeRight(StringUtils.isNotBlank(applicantName), "epai.applicant_name", applicantName)
                        .eq(StringUtils.isNotBlank(policySource), "source_platform", policySource)
                        .eq(StringUtils.isNotBlank(salesType), "sales_type", salesType)
                        .in(policyStatuses != null, "admin_policy_status", policyStatuses)
                        .inSql(StringUtils.isNotBlank(insuredName), "epci.contract_code", "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                        .in(channelCodes != null, "epci.channel_code", channelCodes)
                        .in(orgCodes != null, "epci.org_code", orgCodes)
                        .and(StringUtils.isNotBlank(portfolioNameOrCode),
                                x -> x.likeRight("portfolio_name", portfolioNameOrCode))
                        .in(referrerCodes != null, "epci.referrer_code", referrerCodes)
                        .inSql(agentCodes != null, "epci.contract_code", StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                        .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                                x.eq("epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq("epci.is_revisit", StatusEnum.INVALID.getCode())
                        )
                        .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                                x.eq(suspendTypeSet.contains("1"), "epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq(suspendTypeSet.contains("2"), "epci.is_revisit", StatusEnum.INVALID.getCode()))
                        .eq(settlementStatus != null, "epci.settlement_status", settlementStatus)
                        .eq(customerAgreed != null, "epci.offline_product_sign_status", customerAgreed)
                        .ge(StringUtils.isNotBlank(orderTimeBegin), "epci.order_time", orderTimeBegin)
                        .le(StringUtils.isNotBlank(orderTimeEnd), "epci.order_time", StrUtil.concat(true, orderTimeEnd, " 23:59:59"))
                        .ge(StringUtils.isNotBlank(enforceTimeBegin), "epci.enforce_time", enforceTimeBegin)
                        .le(StringUtils.isNotBlank(enforceTimeEnd), "epci.enforce_time", StrUtil.concat(true, enforceTimeEnd, " 23:59:59"))
                        //  方式1 between 导出
                        .between(betweenSize != null, "epci.id", betweenSize, limitSize)
                        .last(startSize != null, StrUtil.format("limit {},{}", startSize, limitSize))
                        .gt(maxId != null, "epci.id", maxId)
                        .last(maxId != null, StrUtil.format(" limit {}", limitSize))
                        // 1.4.1添加保单权限
                        .in(channelBranchCodeList != null, "epci.policy_channel_branch_code", channelBranchCodeList)
                        .in(orgCodeList != null, "epci.org_code", orgCodeList)
                        .in(CollectionUtils.isNotEmpty(channelDisCodeList), "epci.channel_distribution_code", channelDisCodeList)
                        // 如果不存在线上单查看权限添加 线下单 条件
                        .eq(!ShiroUtils.getSubject().isPermitted("policy:onLineVisible"), "epci.sales_type", PolicySalesTypeEnum.OUT_LINE.getCode())
                        .comment("xiaowhale query")
        );
        //字段丰富
        List<EpV2PolicyExportVo> collect = getEpV2PolicyExportVos(sensitiveDataAccess, exportList);
        return collect;
    }

    /**
     * 字段丰富
     *
     * @param sensitiveDataAccess
     * @param exportList
     * @return
     */
    public List<EpV2PolicyExportVo> getEpV2PolicyExportVos(final Map<String, OrgInfoEntity> orgInfoEntityMap,
                                                            boolean sensitiveDataAccess,
                                                            List<EpV2PolicyExportVo> exportList,
                                                            final Map<String, String>  productInfoMap) {
        List<String> customerManagerList = exportList.stream().map(x -> x.getCustomerManagerCode()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        PolicyChannelHelper helper = new PolicyChannelHelper();
        helper.init(customerManagerList);

        List<EpV2PolicyExportVo> collect = exportList.stream().peek(x -> {
            // 保单信息
            if (StringUtils.isNotEmpty(x.getPolicyType())) {
                x.setPolicyType(PolicyTypeEnum.getProdTypeEnum(Integer.valueOf(x.getPolicyType())).getDesc());
            }
            x.setPolicyProductType(PolicyProductTypeEnum.getProdTypeEnum(x.getPolicyProductType()).getPolicyProductType());
            if (StringUtils.isNotEmpty(x.getSalesType())) {
                x.setSalesType(
                        Optional.ofNullable(PolicySalesTypeEnum.getSalesTypeEnum(Integer.valueOf(x.getSalesType())))
                                .map(PolicySalesTypeEnum::getDesc).orElse("")
                );
            }
            x.setAdminPolicyStatus(
                    Optional.ofNullable(PolicyContractStatusEnum.getPolicyContractStatusEnum(x.getAdminPolicyStatus()))
                            .map(PolicyContractStatusEnum::getStatusDesc).orElse("")
            );
            x.setSalesPlatform(
                    Optional.ofNullable(PolicySalesPlatformEnum.getSalesPlatformEnum(x.getSalesPlatform()))
                            .map(PolicySalesPlatformEnum::getDesc).orElse("")
            );
            if (x.getSelfPreservation() != null) {
                x.setSelfPreservation("0".equals(x.getSelfPreservation()) ? "否" : "是");
            }
            if (x.getRevisitResult() != null) {
                x.setRevisitResult(PolicyOptResultEnum.getByCode(Integer.valueOf(x.getRevisitResult())).getDesc());
            }

            if (Objects.equals(PolicyInsuredPeriodTypeEnum.LIFE.getCode(), x.getInsuredPeriodType())) {
                x.setInsuredPeriod(null);
            }

            // 构建佣金发放状态
            PolicySettlementStatusEnum settlementStatusEnum = PolicySettlementStatusEnum.getSettlementStatusEnum(x.getSettlementStatus());
            x.setSettlementStatusStr(Optional.ofNullable(settlementStatusEnum).map(PolicySettlementStatusEnum::getDesc).orElse(""));
            if (PolicySettlementStatusEnum.SETTLEMENT.getCode().equals(x.getSettlementStatus())) {
                x.setSettlementMonthStr(String.format("%02d", x.getSettlementMonth()));
            }
            //add by pc-006
            if (x.getIsRevisit() != null) {
                if (Objects.equals(x.getIsRevisit(), "0")) {
                    x.setIsRevisit("否");
                } else if (Objects.equals(x.getIsRevisit(), "1")) {
                    x.setIsRevisit("是");
                } else {
                    x.setIsRevisit("不需要");
                }
            }
            //add by pc-006
            if (x.getIsReceipt() != null) {
                if (Objects.equals(x.getIsReceipt(), "0")) {
                    x.setIsReceipt("否");
                } else if (Objects.equals(x.getIsReceipt(), "1")) {
                    x.setIsReceipt("是");
                } else {
                    x.setIsReceipt("不需要");
                }
            }

            // 销售渠道编码-台账
            if(x.getSuperviseChannelCode() != null){
                if(x.getSuperviseChannelCode() == 1){
                    x.setSuperviseChannelName("农服");
                } else if(x.getSuperviseChannelCode() == 2){
                    x.setSuperviseChannelName("恒基");
                } else if(x.getSuperviseChannelCode() == 3){
                    x.setSuperviseChannelName("嵯峨");
                } else{
                    x.setSuperviseChannelName("其他");
                }
            }

            // 代理人信息
            String mainFlag = x.getMainFlag();
            if (mainFlag != null) {
                x.setMainFlag("0".equals(mainFlag) ? "否" : "是");
            }
            String commissionRate = x.getCommissionRate();
            if (StringUtils.isNotBlank(commissionRate)) {
                x.setCommissionRate(new BigDecimal(commissionRate).multiply(new BigDecimal(100)).toPlainString() + "%");
            }
            String agentOrgCode = x.getOrgCode();
            if (StringUtils.isNotBlank(agentOrgCode)) {
                x.setOrgSuperiorName(Optional
                        .ofNullable(orgInfoEntityMap.get(agentOrgCode))
                        .map(OrgInfoEntity::getOrgName).orElse(null));
            }

            // 险种信息
            x.setMainInsurance("0".equals(x.getMainInsurance()) ? "否" : "是");
            x.setPaymentPeriodType(
                    Optional.ofNullable(PolicyPaymentPeriodTypeEnum.getPaymentPeriodTypeEnumByCode(x.getPaymentPeriodType()))
                            .map(PolicyPaymentPeriodTypeEnum::getPeriodDesc).orElse("")
            );
            x.setInsuredPeriodType(Optional.ofNullable(PolicyInsuredPeriodTypeEnum.getInsuredTypeEnumByCode(x.getInsuredPeriodType())).map(PolicyInsuredPeriodTypeEnum::getInsuredDesc).orElse(""));
            x.setPeriodType(PolicyPaymentTypeEnum.getPaymentDescByCode(x.getPeriodType()));
            x.setProductStatus(Optional.ofNullable(PolicyContractStatusEnum.getPolicyContractStatusEnum(x.getProductStatus())).map(PolicyContractStatusEnum::getDesc).orElse(""));
            //险种类型 add by pc-006
            //x.setProdTypeName(ProdTypeEnum.getProdTypeEnum(x.getProdTypeCode()).getProdDesc());
            // 投保人
            //x.setApplicantNation(countryMap.get(x.getApplicantNation()));
            //x.setApplicantType(PolicySubjectTypeEnum.getPolicySubjectTypeEnum(Integer.valueOf(x.getApplicantType())).getPolicySubjectType());
            if (StringUtils.isNotBlank(x.getApplicantType())) {
                x.setApplicantType(PolicySubjectTypeEnum.getPolicySubjectTypeEnum(Integer.valueOf(x.getApplicantType())).getPolicySubjectType());
            }
            x.setApplicantIdType(PolicyIdCardTypeEnum.getNameByCode(x.getApplicantIdType()));
            //x.setApplicantMarital(Optional.ofNullable(PolicyMaritalEnum.getEnumByCode(x.getApplicantMarital())).map(PolicyMaritalEnum::getDesc).orElse(""));
            String applicantGender = x.getApplicantGender();
            if (StringUtils.isNotBlank(applicantGender)) {
                x.setApplicantGender("0".equals(applicantGender) ? "女" : "男");
            }
            //x.setLegalPersonIdType(PolicyIdCardTypeEnum.getNameByCode(x.getLegalPersonIdType()));
            //x.setApplicantIndustryCategory(industryCategoriesMap.get(x.getApplicantIndustryCategory()));
            //x.setCompanyNature(companyNatureMap.get(x.getCompanyNature()));

            // 被保人
            //x.setInsuredNation(countryMap.get(x.getInsuredNation()));
            x.setInsuredIdType(PolicyIdCardTypeEnum.getNameByCode(x.getInsuredIdType()));
            /*if (x.getInsuredType() != null) {
                x.setInsuredType(Optional.ofNullable(PolicySubjectTypeEnum.getPolicySubjectTypeEnum(Integer.valueOf(x.getInsuredType()))).map(PolicySubjectTypeEnum::getPolicySubjectType).orElse(""));
            }*/
            x.setInsuredRelation(Optional.ofNullable(PolicyFamilyTypeEnum.decode(x.getInsuredRelation())).map(PolicyFamilyTypeEnum::getFamilyDesc).orElse(""));
            String insuredGender = x.getInsuredGender();
            if (StringUtils.isNotBlank(insuredGender)) {
                x.setInsuredGender("0".equals(insuredGender) ? "女" : "男");
            }
            /*String insuredEducation = x.getInsuredEducation();
            if (StringUtils.isNotBlank(insuredEducation)) {
                x.setInsuredEducation(Optional.ofNullable(PolicyDegreeEnum.getProdTypeEnum(insuredEducation)).map(PolicyDegreeEnum::getPolicyDegreeType).orElse(""));
            }
            x.setInsuredMarital(Optional.ofNullable(PolicyMaritalEnum.getEnumByCode(x.getInsuredMarital())).map(PolicyMaritalEnum::getDesc).orElse(""));
            */
            x.setCustomerManagerChannelCode(helper.queryChannelReferrerWno(x.getCustomerManagerCode()));
            x.setCustomerManagerName(helper.queryChannelReferrerName(x.getCustomerManagerCode()));

            // 车险
            if (StringUtils.isNotBlank(x.getVehicleOwnerName())) {
                x.setVehicleOwnerCardType(PolicyIdCardTypeEnum.getNameByCode(x.getVehicleOwnerCardType()));
                /*
                String vehicleOwnerNature = x.getVehicleOwnerNature();
                if (StringUtils.isNotBlank(vehicleOwnerNature)) {
                    x.setVehicleOwnerNature(PolicyVehicleOwnerNatureEnum.getProdTypeEnum(vehicleOwnerNature).getPolicyVehicleOwnerNatureType());
                }

                String vehicleUsage = x.getVehicleUsage();
                if (StringUtils.isNotBlank(vehicleUsage)) {
                    x.setVehicleUsage("0".equals(vehicleUsage) ? "非营业" : "营业");
                }

                String vehicleNew = x.getVehicleNew();
                if (StringUtils.isNotBlank(vehicleNew)) {
                    x.setVehicleNew("0".equals(vehicleNew) ? "否" : "是");
                }

                String vehicleTransferOwnership = x.getVehicleTransferOwnership();
                if (StringUtils.isNotBlank(vehicleTransferOwnership)) {
                    x.setVehicleTransferOwnership("0".equals(vehicleTransferOwnership) ? "否" : "是");
                }

                String vehicleVariety = x.getVehicleVariety();
                if (StringUtils.isNotBlank(vehicleVariety)) {
                    x.setVehicleVariety(PolicyVehicleVarietyEnum.getProdTypeEnum(vehicleVariety).getPolicyVehicleOwnerVarietyType());
                }*/
            }
            if (!sensitiveDataAccess) {
                x.setApplicantIdCard(DesensitizedUtil.idCardNum(x.getApplicantIdCard(), 1, 1));
                x.setInsuredIdCard(DesensitizedUtil.idCardNum(x.getInsuredIdCard(), 1, 1));
                //x.setLegalPersonIdCard(DesensitizedUtil.idCardNum(x.getLegalPersonIdCard(), 1, 1));
                x.setApplicantMobile(DesensitizedUtil.mobilePhone(x.getApplicantMobile()));
                x.setInsuredMobile(DesensitizedUtil.mobilePhone(x.getInsuredMobile()));
                //x.setCompanyContactMobile(DesensitizedUtil.mobilePhone(x.getCompanyContactMobile()));
                //x.setVehicleOwnerMobile(DesensitizedUtil.mobilePhone(x.getVehicleOwnerMobile()));
                x.setApplicantAddress(DesensitizedUtil.address(x.getApplicantAddress(), Integer.MAX_VALUE));
                x.setInsuredAddress(DesensitizedUtil.address(x.getInsuredAddress(), Integer.MAX_VALUE));
            }
            //佣金发放状态、佣金发放年和佣金发放月
            if(Objects.nonNull(x)){
                x.setSettlementStatusStr(null);
                x.setSettlementYear(null);
                x.setSettlementMonthStr(null);
            }
            //险种类型实时取
            if (MapUtils.isNotEmpty(productInfoMap)
                    && StringUtils.isNotBlank(x.getProductCode())
                    && productInfoMap.containsKey(x.getProductCode())) {
                final String productType = productInfoMap.get(x.getProductCode());
                x.setProdTypeName(Optional.ofNullable(ProdTypeEnum.getProdTypeEnum(productType))
                        .map(ProdTypeEnum::getProdDesc)
                        .orElse(null));
            } else {
                x.setProdTypeName(null);
            }
//            x.setCreateTime(Optional.ofNullable(x.getCreateTime()).map(e->).orElse(null));
//            x.setOverHesitatePeriod(Optional.ofNullable(x.getOverHesitatePeriod()).map(e->).orElse(null));
        }).collect(Collectors.toList());
        return collect;
    }


    /**
     * 字段丰富
     *
     * @param sensitiveDataAccess
     * @param exportList
     * @return
     */
    private List<EpV2PolicyExportVo> getEpV2PolicyExportVos(boolean sensitiveDataAccess, List<EpV2PolicyExportVo> exportList) {
        return getEpV2PolicyExportVos(getStringOrgInfoEntityMap(), sensitiveDataAccess, exportList,null);
    }

    /**
     * 获取组织map
     *
     * @return Map
     */
    public Map<String, OrgInfoEntity> getStringOrgInfoEntityMap() {
        List<OrgInfoEntity> orgInfoEntities = orgInfoService.list();
        final Map<String, OrgInfoEntity> mapOrgs = orgInfoEntities.stream()
                .collect(Collectors.toMap(OrgInfoEntity::getOrgCode, Function.identity(), (s, d) -> d));
        final HashMap<String,OrgInfoEntity> orgInfoEntityHashMap=new HashMap<>();
        for (OrgInfoEntity orgInfoEntity : orgInfoEntities) {
            OrgInfoEntity  topOrg = orgInfoEntity;
            //限制最大曾级
            for (int i=0;i<10;i++){
                if(StringUtils.isNotBlank(topOrg.getOrgSuperiorCode())
                        && mapOrgs.containsKey(topOrg.getOrgSuperiorCode())
                        && Objects.nonNull(mapOrgs.get(topOrg.getOrgSuperiorCode()))
                        &&!StringUtils.equals(mapOrgs.get(topOrg.getOrgSuperiorCode()).getOrgCode(),topOrg.getOrgCode())){
                    topOrg=mapOrgs.get(topOrg.getOrgSuperiorCode());
                    continue;
                }
                if(topOrg.getOrgType() != 0){
                    topOrg=null;
                }
                break;
            }
            orgInfoEntityHashMap.put(orgInfoEntity.getOrgCode(),topOrg);
        }
        return orgInfoEntityHashMap;
    }

    @Override
    public String tempSaveOrUpdateEntity(EpContractInfoVo entryContractInfoVo) {
        entryContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //补全附件列表信息
        EpContractInfoVo entryContractInfo = this.completionAttachment(entryContractInfoVo);
        Result<String> stringResult = epPolicyClient.tempSave(entryContractInfo);
        //调用失败抛异常
        if (!stringResult.isSuccess()) {
            throw new GlobalException(stringResult);
        }
        return stringResult.getData();
    }

    @Override
    public String submit(EpContractInfoVo entryContractInfoVo) {
        entryContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //补全附件列表信息
        EpContractInfoVo entryContractInfo = this.completionAttachment(entryContractInfoVo);
        checkPolicy(entryContractInfo);
        //调用保单中心
        Result<String> save = epPolicyClient.save(entryContractInfo);
        //调用失败抛异常
        if (!save.isSuccess()) {
            throw new GlobalException(save);
        }
        return save.getData();
    }

    /**
     * 业务录单-保单校验
     * @param contractInfo 保单合同信息
     */
    private void checkPolicy(EpContractInfoVo contractInfo) {
        String policyProductType = contractInfo.getPolicyProductType();
        log.info("业务录单-开始校验保单信息是否正确:{}",policyProductType);
        if(Objects.equals(PolicyProductTypeEnum.GROUP.getPolicyProductType(),policyProductType)){
            checkGroupPolicy(contractInfo);
        }
    }
    /**
     * 保单校验
     * @param contractInfo 保单合同信息
     */
    private void checkGroupPolicy(EpContractInfoVo contractInfo) {
        List<EpProductInfoVo> productList = contractInfo.getProductInfoList();
        if(CollectionUtils.isEmpty(productList)){
            return;
        }
        //校验新契约团险计划险种的总保费=计划险种的单人保费*该计划的投保人数
        for(EpProductInfoVo productVo:productList){
            BigDecimal unitPremium = productVo.getUnitPremium();
            BigDecimal productTotalPremium = productVo.getPremium();
            Integer member =productVo.getPlanInsuredNum();
            if(productTotalPremium==null){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("团险保单险种总保费不能为空:"+productVo.getProductCode()));
            }
            if(member==null){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("团险计划人数不能为空:"+productVo.getPlanCode()));
            }
            if(unitPremium==null){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("团险保单险种单人保费不能为空:"+productVo.getProductCode()));
            }
            BigDecimal calPremium = unitPremium.multiply(new BigDecimal(member));
            if(calPremium.compareTo(productTotalPremium)!=0){
                throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("团险保单险种总保费跟该险种的单人保费之和不相等:"+productVo.getProductCode()));
            }
        }
    }

    @Override
    public String adminUpdate(EpContractInfoVo entryContractInfoVo) {
        entryContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //查询变更前信息
        final EpContractInfoVo epContractInfoVo = this.getInfo(entryContractInfoVo.getContractCode());
        //补全附件列表信息
        EpContractInfoVo entryContractInfo = this.completionAttachment(entryContractInfoVo);
        //调用保单中心
        Result<String> save = epPolicyClient.adminUpdate(entryContractInfo);
        //调用失败抛异常
        if (!save.isSuccess()) {
            throw new GlobalException(save);
        }
        final ToolsModelChangeLogVo changeLogVo=new ToolsModelChangeLogVo();
        changeLogVo.setStartDataObjects(epContractInfoVo);
        changeLogVo.setTypeEnum(ToolsModelChangeLogTypeEnum.POLICY_ADMIN_UP);
        changeLogVo.setAfterMethodParameter(entryContractInfoVo.getContractCode());
        changeLogVo.setAfterMethod(s->this.getInfo((String) s));
        changeLogVo.setCreateUser(ShiroUtils.getUserEntity().getUsername());
        changeLogVo.setChangeId(epContractInfoVo.getContractCode());
        changeLogVo.setChangeNbr(Optional.ofNullable(epContractInfoVo.getContractBaseInfo())
                .map(EpContractBaseInfoVo::getPolicyNo)
                .orElse(null));
        ToolsModelChangeLogHelp.save(changeLogVo);
        return save.getData();
    }

    @Override
    public String correctedPolicyBeneficiary(String contractCode, List<EpPersonalProductInfoVo> productList) {
        PolicyProductBeneficiaryForm form = new PolicyProductBeneficiaryForm();
        form.setContractCode(contractCode);
        form.setProductList(productList);
        form.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //调用保单中心
         policyProductBaseService.correctedPolicyBeneficiary(form);
         return contractCode;
    }

    @Override
    public String updateAdvance(EpContractInfoVo entryContractInfoVo) {
        entryContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //补全附件列表信息
        EpContractInfoVo entryContractInfo = this.completionAttachment(entryContractInfoVo);
        //调用保单中心
        Result<String> save = epPolicyClient.updateAdvance(entryContractInfo);
        //调用失败抛异常
        if (!save.isSuccess()) {
            throw new GlobalException(save);
        }
        return save.getData();
    }

    @Override
    public void presaleToUnderwrite(EpContractInfoVo epContractInfoVo) {
        epContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //调用保单中心
        Result<String> stringResult = epPolicyClient.conversionUnderwriting(epContractInfoVo);
        if (!stringResult.isSuccess()) {
            throw new GlobalException(stringResult);
        }
    }

    @Override
    public EpContractInfoVo completionAttachment(EpContractInfoVo entryContractInfoVo) {
        //获取附件列表
        List<EpContractAttachInfoVo> contractAttachList = entryContractInfoVo.getContractAttachList();
        //补全附件列表的信息
        List<EpContractAttachInfoVo> contractAttachBaseList = contractAttachList.stream().peek(x -> {
            if ("sign".equals(x.getFileType()) && StringUtils.isBlank(x.getFileCode())) {
                return;
            }
            SysDocumentEntity document = Optional.ofNullable(
                    sysDocumentService.lambdaQuery()
                            .eq(SysDocumentEntity::getFileCode, x.getFileCode())
                            .one()
            ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("附件未找到")));
            x.setFilePath(document.getFilePath());
            x.setFileName(document.getFileName());
        }).collect(Collectors.toList());
        entryContractInfoVo.setContractAttachList(contractAttachBaseList);
        return entryContractInfoVo;
    }

    @Override
    public EpContractInfoVo getInfo(String contractCode) {
        Result<EpContractInfoVo> entryContractInfoVoResult = epPolicyClient.queryPolicyContractDetailInfo(contractCode);

        if (!entryContractInfoVoResult.isSuccess()) {
            throw new GlobalException(entryContractInfoVoResult);
        }
        //获取返回的vo
        EpContractInfoVo entryContractInfoVo = entryContractInfoVoResult.getData();
        //获取代理人职级map
        Map<String, String> agentPositionMap = AgentConstantUtil.getDic2Map(AgentConstantUtil.AGENT_POSITION);

        //构建代理人信息map
        Map<String, AgentUserInfoEntity> agentMap = agentUserInfoService.list().stream().collect(Collectors.toMap(AgentUserInfoEntity::getAgentCode, x -> x));
        //构建机构信息map
        List<OrgInfoEntity> list = orgInfoService.list();
        Map<String, OrgInfoEntity> orgInfoEntityMap = list.stream().collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));
        // 构建机构名称树
        Map<String, OrgInfoEntity> orgTreeMap = list.stream().peek(x -> {
            String orgSuperiorCode = x.getOrgSuperiorCode();
            while (StringUtils.isNotBlank(orgSuperiorCode)) {
                OrgInfoEntity orgInfoEntity = orgInfoEntityMap.get(orgSuperiorCode);
                if (orgInfoEntity != null) {
                    if (StatusEnum.INVALID.getCode().equals(orgInfoEntity.getOrgType())) {
                        x.setOrgSuperiorName(orgInfoEntity.getOrgName());
                        break;
                    }
                    orgSuperiorCode = orgInfoEntity.getOrgSuperiorCode();
                } else {
                    orgSuperiorCode = null;
                }
            }
        }).collect(Collectors.toMap(OrgInfoEntity::getOrgCode, x -> x));

        //补全保单代理人信息
        List<EpAgentInfoVo> agentInfoVoList = Optional.ofNullable(entryContractInfoVo.getAgentInfoList()).orElse(new ArrayList<>());
        if (!agentInfoVoList.isEmpty()) {
            agentInfoVoList.forEach(x -> {
                //获取代理人信息
                AgentUserInfoEntity agentUserInfo = agentMap.getOrDefault(x.getAgentCode(), new AgentUserInfoEntity());
                //代理人姓名
                x.setAgentName(agentUserInfo.getAgentName());
                //代理人职级
                x.setPosition(agentPositionMap.get(agentUserInfo.getPosition()));
                //所属机构名称
                x.setOrgName(orgTreeMap.getOrDefault(x.getOrgCode(), new OrgInfoEntity()).getOrgName());
                x.setBusinessCode(agentUserInfo.getBusinessCode());
            });
        }
        //添加代理人信息
        entryContractInfoVo.setAgentInfoList(agentInfoVoList);

        //补全渠道信息
        EpPolicyChannelInfoVo channelInfo = entryContractInfoVo.getChannelInfo();
        if (channelInfo != null) {
            // 构建推荐人信息
            String policyReferrerName = Optional.ofNullable(agentMap.get(channelInfo.getPolicyReferrerCode())).map(AgentUserInfoEntity::getAgentName).orElse("");
            channelInfo.setPolicyReferrerName(policyReferrerName);

            //构建渠道信息
            Map<String, String> channelNameMap = channelInfoService.list().stream().collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, ChannelInfoEntity::getChannelName));

            channelInfo.setChannelName(channelNameMap.get(channelInfo.getChannelCode()));
            channelInfo.setPolicyReferrerBusinessCode(Optional.ofNullable(agentMap.get(channelInfo.getPolicyReferrerCode())).map(AgentUserInfoEntity::getBusinessCode).orElse(""));


            // 构建渠道推荐人信息
            // 根据渠道推荐人类型来做不同的逻辑
            if (StatusEnum.NORMAL.getCode().equals(channelInfo.getReferrerType())) {
                AgentUserInfoEntity referrerAgent = agentMap.getOrDefault(channelInfo.getReferrerCode(), new AgentUserInfoEntity());
                channelInfo.setReferrerName(referrerAgent.getAgentName());
                channelInfo.setReferrerWno(referrerAgent.getBusinessCode());
                channelInfo.setBranchName(orgTreeMap.getOrDefault(referrerAgent.getOrgCode(), new OrgInfoEntity()).getOrgName());

                AgentUserInfoEntity customerManager = agentMap.getOrDefault(channelInfo.getCustomerManagerCode(), new AgentUserInfoEntity());
                channelInfo.setCustomerManagerName(customerManager.getAgentName());
                channelInfo.setCustomerManagerChannelCode(customerManager.getBusinessCode());
            } else {
                //构建渠道推荐人信息
                ChannelApplicationReferrerEntity referrerEntity = Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getReferrerCode, channelInfo.getReferrerCode())
                        .one()
                ).orElse(new ChannelApplicationReferrerEntity());
                channelInfo.setReferrerName(referrerEntity.getReferrerName());
                channelInfo.setReferrerWno(referrerEntity.getReferrerWno());
                //构建渠道分支信息
                ChannelBranchInfoEntity channelBranchInfoEntity = channelBranchInfoService.getOne(
                        Wrappers.<ChannelBranchInfoEntity>lambdaQuery().eq(ChannelBranchInfoEntity::getBranchCode, channelInfo.getChannelBranchCode())
                );
                channelInfo.setBranchName(Optional.ofNullable(channelBranchInfoEntity).map(ChannelBranchInfoEntity::getBranchName).orElse(""));
                //构建初始推荐人
                ChannelApplicationReferrerEntity customerManager = Optional.ofNullable(channelApplicationReferrerService.lambdaQuery()
                        .eq(ChannelApplicationReferrerEntity::getReferrerCode, channelInfo.getCustomerManagerCode())
                        .one()
                ).orElse(new ChannelApplicationReferrerEntity());
                channelInfo.setCustomerManagerName(customerManager.getReferrerName());
                channelInfo.setCustomerManagerChannelCode(customerManager.getReferrerWno());
            }
        }
        //添加渠道信息
        entryContractInfoVo.setChannelInfo(channelInfo);

        // 脱敏
        if (!ShiroUtils.getSubject().isPermitted("policy:query")) {
            Optional.ofNullable(entryContractInfoVo.getApplicantInfo()).ifPresent(x -> {
                x.setApplicantIdCard(DesensitizedUtil.idCardNum(x.getApplicantIdCard(), 1, 1));
                x.setApplicantMobile(DesensitizedUtil.mobilePhone(x.getApplicantMobile()));
                x.setApplicantAddress(DesensitizedUtil.address(x.getApplicantAddress(), Integer.MAX_VALUE));
                x.setCompanyContactMobile(DesensitizedUtil.mobilePhone(x.getCompanyContactMobile()));
                x.setLegalPersonIdCard(DesensitizedUtil.idCardNum(x.getLegalPersonIdCard(), 1, 1));
            });
            Optional.ofNullable(entryContractInfoVo.getInsuredInfoList()).ifPresent(insuredList -> {
                insuredList.stream().filter(Objects::nonNull).forEach(insuredInfoVo -> {
                    insuredInfoVo.setInsuredIdCard(DesensitizedUtil.idCardNum(insuredInfoVo.getInsuredIdCard(), 1, 1));
                    insuredInfoVo.setInsuredMobile(DesensitizedUtil.mobilePhone(insuredInfoVo.getInsuredMobile()));
                    insuredInfoVo.setInsuredAddress(DesensitizedUtil.address(insuredInfoVo.getInsuredAddress(), Integer.MAX_VALUE));
                    Optional.ofNullable(insuredInfoVo.getProductInfoList()).ifPresent(productList ->
                            productList.forEach(product -> {
                                Optional.ofNullable(product.getBeneficiaryList()).ifPresent(beneficiaryList -> {
                                    beneficiaryList.forEach(x -> {
                                        x.setBeneficiaryIdCard(DesensitizedUtil.idCardNum(x.getBeneficiaryIdCard(), 1, 1));
                                        x.setBeneficiaryMobile(DesensitizedUtil.idCardNum(x.getBeneficiaryMobile(), 1, 1));
                                    });
                                });
                            }));
                });
            });
            Optional.ofNullable(entryContractInfoVo.getVehicleInfo())
                    .ifPresent(x -> x.setVehicleOwnerMobile(DesensitizedUtil.mobilePhone(x.getVehicleOwnerMobile())));
        }

        return entryContractInfoVo;
    }

    /**
     * 前端查询保单详情展示
     * @param contractCode 合同编码
     * @return 保单合同信息
     */
    @Override
    public PolicyContractInfoVo queryPolicyContractInfo(String contractCode) {
        EpContractInfoVo vo = getInfo(contractCode);

        PolicyContractInfoVo data = new PolicyContractInfoVo();
        BeanUtils.copyProperties(vo,data);

        //团险保单组装计划信息
        String policyProductType = data.getPolicyProductType();
        if(Objects.equals(PolicyProductTypeEnum.GROUP.getCode(),policyProductType)){
            List<EpProductInfoVo> productList = data.getProductInfoList();
            List<GroupPolicyPlanVo> planList = convertPolicyPlanList(productList);
            data.setGroupPolicyPlanList(planList);
        }
        return data;
    }

    /**
     * 组装团险保单计划表
     * 产品计划->虚拟计划->险种列表
     * @param productInfoList 保单险种信息
     * @return 保单计划信息
     */
    private List<GroupPolicyPlanVo> convertPolicyPlanList(List<EpProductInfoVo> productInfoList){
        if(CollectionUtils.isEmpty(productInfoList)){
            return Collections.emptyList();
        }
        //1. 按产品计划分组
        Map<String,List<EpProductInfoVo>> planProductMap = LambdaUtils.groupBy(productInfoList,EpProductInfoVo::getPlanCode);
        List<GroupPolicyPlanVo> planList = new ArrayList<>();
        for(Map.Entry<String,List<EpProductInfoVo>> entry:planProductMap.entrySet()){
            String planCode = entry.getKey();
            List<EpProductInfoVo> planProductList = entry.getValue();
            EpProductInfoVo p1 = planProductList.get(0);
            String planName = p1.getPlanName();
            if(StringUtils.isBlank(planName)){
                planName="产品计划";
            }

            GroupPolicyPlanVo planVo = new GroupPolicyPlanVo();
            planVo.setPlanCode(planCode);
            planVo.setPlanName(planName);
            planVo.setPlanInsuredNum(p1.getPlanInsuredNum());

            //2. 按虚拟计划分组
            Map<String,List<EpProductInfoVo>> premiumPlanProductMap = LambdaUtils.groupBy(planProductList,product->{
                String virtualPlanCode = product.getVirtualPlanCode();
                if(StringUtils.isBlank(virtualPlanCode)){
                    virtualPlanCode="";
                }
                return virtualPlanCode;
            });

            List<PremiumPlanVo> premiumPlanList = new ArrayList<>();
            for(Map.Entry<String,List<EpProductInfoVo>> productEntry:premiumPlanProductMap.entrySet()){
                String virtualPlanCode = productEntry.getKey();
                List<EpProductInfoVo> productList = productEntry.getValue();

                EpProductInfoVo p2 = productList.get(0);
                String virtualPlanName = p2.getVirtualPlanName();
                if(StringUtils.isBlank(virtualPlanName)){
                    virtualPlanName="保障计划";
                }
                PremiumPlanVo premiumPlanVo = new PremiumPlanVo();
                premiumPlanVo.setVirtualPlanCode(virtualPlanCode);
                premiumPlanVo.setVirtualPlanName(virtualPlanName);
                premiumPlanVo.setPolicyProductList(productList);
                premiumPlanList.add(premiumPlanVo);
            }
            planVo.setPremiumPlanList(premiumPlanList);
            planList.add(planVo);
        }
        return planList;
    }

    @Override
    public List<String> getAgentExitPolicyCheck(String agentCode) {
        //判断一下将要离职的代理人是否存在转移中的保单,免得他负责的保单变成孤儿单.
        Integer count = epPolicyTransferTaskService.lambdaQuery()
                .ne(EpPolicyTransferTaskEntity::getTaskStatus, StatusEnum.NORMAL.getCode())
                .and(x -> x.eq(EpPolicyTransferTaskEntity::getSourceAgentCode, agentCode)
                        .or().eq(EpPolicyTransferTaskEntity::getTargetAgentCode, agentCode))
                .count();
        if (count != null && count > 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("当前代理人存在等待转移保单,请勿操作离职"));
        }
        /*Result<List<EpPolicyAgentExitCheckVo>> listResult = epPolicyClient.queryAgentExitPolicyCheck(agentCode);
        if (!listResult.isSuccess()) {
            throw new GlobalException(listResult);
        }
        List<EpPolicyAgentExitCheckVo> epPolicyAgentExitCheckVoList = listResult.getData();
        AtomicInteger i = new AtomicInteger(1);
        return epPolicyAgentExitCheckVoList.stream().map(x ->
                i.getAndIncrement() + "、" + x.getCheckTypeEnum().getDesc()
        ).collect(Collectors.toList());*/
        return Collections.emptyList();
    }

    @Override
    public EpContractBriefInfoVo queryPreserveContractBriefInfoByPolicyNo(String policyNo) {
        EpContractBriefInfoVo contractBriefInfoVo = epPolicyBaseService.queryPolicyContractBriefInfoByPolicyNo(policyNo, true);

        //保单权限控制
        if (StringUtils.isNotBlank(contractBriefInfoVo.getContractCode())) {
            final Map<String, Object> params = PolicyPermissionHelper.setPolicyPermission();
            params.put("policyContractCode", contractBriefInfoVo.getContractCode());
            final List<EpPolicyContractInfoEntity> contract = preservationApplyService.listsContractByMap(params);
            if (CollectionUtils.isEmpty(contract)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("您无权操作该保单"));
            }
            if (contract.size() > 1) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合同查询异常"));
            }
        }

        if (PolicyTypeEnum.ADVANCE.getCode().equals(contractBriefInfoVo.getPolicyType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单为预收单"));
        }

        /**
         * 修改支持创建保全的保单状态
         * <AUTHOR>
         */
        if (!supportPreservation(contractBriefInfoVo.getAdminPolicyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单不是承保单"));
        }

        return contractBriefInfoVo;

    }

    /**
     * 是否支持创建保全
     * return PolicyContractStatusEnum.HESITATION_CANCEL.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.CANCELLATION.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.DISCONTINUE.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.SIGNED.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.TERMINATION.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.ACTIVE.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.AGREEMENT_TERMINATION.getCode().equals(policyStatus)
     * || PolicyContractStatusEnum.CLAIMS_TERMINATION.getCode().equals(policyStatus);
     *
     * @param policyStatus
     * @return
     */
    private boolean supportPreservation(String policyStatus) {
        return PolicyContractStatusEnum.HESITATION_CANCEL.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.CANCELLATION.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.DISCONTINUE.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.SIGNED.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.TERMINATION.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.ACTIVE.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.AGREEMENT_TERMINATION.getCode().equals(policyStatus)
                || PolicyContractStatusEnum.CLAIMS_TERMINATION.getCode().equals(policyStatus);
//        Set<String> supportStatus = new HashSet<>();
//        if (StringUtils.isNotBlank(preservationSupport)) {
//            supportStatus = Sets.newHashSet(preservationSupport.split(","));
//        }
//        return supportStatus.contains(policyStatus);
    }

    @Override
    public EpContractBriefInfoVo queryPolicyContractBriefInfoByPolicyNo(String policyNo) {
        EpContractBriefInfoVo contractBriefInfoVo = epPolicyBaseService.queryPolicyContractBriefInfoByPolicyNo(policyNo, false);

        // 如果是托管单则归到总公司
        if (StatusEnum.INVALID.getCode().equals(contractBriefInfoVo.getShowModel())) {
            contractBriefInfoVo.setOrgCode(AdminPublicConstant.HEAD_OFFICE_CODE);
        }
        if (PolicyPermissionHelper.isNoPermissionOptPolicy(contractBriefInfoVo.getOrgCode(), contractBriefInfoVo.getChannelBranchCode())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("您无权操作该保单"));
        }

        if (PolicyTypeEnum.ADVANCE.getCode().equals(contractBriefInfoVo.getPolicyType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单为预收单"));
        }
        if (!PolicyProductTypeEnum.PERSONAL.getCode().equals(contractBriefInfoVo.getPolicyProductType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单不是个险保单"));
        }
        if (!PolicyContractStatusEnum.ACTIVE.getCode().equals(contractBriefInfoVo.getPolicyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("该保单不是生效中的保单")));
        }

        return contractBriefInfoVo;
    }

    @Override
    public EpContractBriefInfoVo queryPolicyContractBriefInfo(String contractCode) {
        EpContractBriefInfoVo policyInfo = epPolicyBaseService.queryPolicyContractBriefInfo(contractCode, false);
        // 如果是托管单则归到总公司
        if (StatusEnum.INVALID.getCode().equals(policyInfo.getShowModel())) {
            policyInfo.setOrgCode(AdminPublicConstant.HEAD_OFFICE_CODE);
        }
        if (PolicyTypeEnum.ADVANCE.getCode().equals(policyInfo.getPolicyType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单为预收单"));
        }
        return policyInfo;
    }


    /**
     * 保全详情页-获取保单信息
     * @param policyNo 保单号
     * @return
     */
    @Override
    public FastPolicyContractInfoVo preservationFastPolicyContractInfo(String policyNo) {
        EpContractBriefInfoVo contractBriefInfoVo = epPolicyBaseService.queryPolicyContractBriefInfoByPolicyNo(policyNo, true);
        //保单权限控制
        if (StringUtils.isNotBlank(contractBriefInfoVo.getContractCode())) {
            final Map<String, Object> params = PolicyPermissionHelper.setPolicyPermission();
            params.put("policyContractCode", contractBriefInfoVo.getContractCode());
            final List<EpPolicyContractInfoEntity> contract = preservationApplyService.listsContractByMap(params);
            if (CollectionUtils.isEmpty(contract)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("您无权操作该保单"));
            }
            if (contract.size() > 1) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("合同查询异常"));
            }
        }
        if (PolicyTypeEnum.ADVANCE.getCode().equals(contractBriefInfoVo.getPolicyType())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单为预收单"));
        }
        /**
         * 修改支持创建保全的保单状态
         * <AUTHOR>
         */
        if (!supportPreservation(contractBriefInfoVo.getAdminPolicyStatus())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该单不是承保单"));
        }

        return convertFastPolicyInfo(contractBriefInfoVo);
    }

    @Override
    public FastPolicyContractInfoVo fastQueryPolicyInfo(String policyNo, boolean isFullInfo) {
        //查询保单信息
        Result<EpContractBriefInfoVo> result = epPolicyClient.queryPolicyContractBriefInfoByPolicyNo(policyNo, isFullInfo);
        if (!result.isSuccess()) {
            log.warn("根据保单号查询保单出现错误:{}", result.getMsg());
            throw new GlobalException(result);
        }
        EpContractBriefInfoVo policy = result.getData();
        if (policy == null) {
            log.warn("获取保单详情信息为空:{}",policyNo);
            return null;
        }
        return convertFastPolicyInfo(policy);
    }

    private FastPolicyContractInfoVo convertFastPolicyInfo(EpContractBriefInfoVo policy){
        if(policy==null){
            return null;
        }
        FastPolicyContractInfoVo data = new FastPolicyContractInfoVo();
        BeanUtils.copyProperties(policy,data);
        //2.查询渠道推荐人信息|代理人信息
        String customerManagerCode = data.getCustomerManagerCode();
        if(StringUtils.isNotBlank(customerManagerCode)) {
            FastChannelApplicationReferrer referrer = channelApplicationReferrerService.queryOne(customerManagerCode);
            if(referrer!=null){
                data.setCustomerManagerName(referrer.getReferrerName());
                data.setCustomerManagerOrgName(referrer.getBranchName());
            }else {
                FastAgentUserInfo agent = agentUserInfoService.queryOne(customerManagerCode);
                if(agent!=null){
                    data.setCustomerManagerName(agent.getAgentName());
                    data.setCustomerManagerOrgName(agent.getOrgName());
                }
            }
        }
        //3.查询续期实收数据
        String policyNo = policy.getPolicyNo();
        Result<EpPolicyRenewalTermVo> r2 = renewalTermClient.currentRenewalTerm(policyNo);
        data.setRenewalTermPeriod(1);
        if(!r2.isSuccess()){
            log.warn("获取保单续期数据异常:{}",policyNo);
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(r2.getMsg()));
        }
        EpPolicyRenewalTermVo renewalTerm = r2.getData();
        if (renewalTerm != null) {
            data.setEnforceTime(renewalTerm.getPeriodStartTime());
            data.setRenewalTermPeriod(renewalTerm.getPeriod());
            data.setPremiumTotal(renewalTerm.getPaymentAmount());
            data.setReferrerCode(renewalTerm.getReferrerCode());
        }
        return data;
    }

    /**
     * 页面查询导出
     *
     * @param paramMap
     */
    @Override
    public void queryExport(final boolean sensitiveDataAccess,
                            final Map<String, Object> paramMap,
                            final OutputStream out,
                            final PolicyExportMethodEnum exportMethodEnum) throws IOException {
        // 公用信息暂存
        final List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        final List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        final List<String> channelDistributionCodeList = PolicyPermissionHelper.getChannelDistributionCodeList();
        final Map<String, OrgInfoEntity> orgInfoEntityMap = getStringOrgInfoEntityMap();
        final boolean condition = !ShiroUtils.getSubject().isPermitted("policy:onLineVisible");
        final boolean channelManage = ShiroUtils.getSubject().isPermitted(AuthorityEnum.POLICY_CHANNEL_MANAGE.getCode());
        final Map<String, String> productInfoMap = getProductInfoMap();

        // Excel信息配置
        final ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
        int row = 0;
        int index = 1;
        int lastId = 0;
        Sheet sheet = new Sheet(index, 0, channelManage ? EpV2PolicyExportVo.class: EpV2PolicyExportSupVo.class);
        sheet.setSheetName("保单列表" + index);
        writer.write(new ArrayList<>(), sheet);

        //查询id最大值
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> maxQuery = buildQueryWrapper(paramMap, orgCodeList,
                channelBranchCodeList, channelDistributionCodeList, condition);
        maxQuery.select(EpPolicyContractInfoEntity::getId);
        maxQuery.orderByDesc(EpPolicyContractInfoEntity::getId);
        maxQuery.last(StrUtil.format("limit {}", 1));
        final List<EpPolicyContractInfoEntity> maxIdEntity = epPolicyContractInfoService.list(maxQuery);
        final Integer maxId = Optional.ofNullable(maxIdEntity).filter(CollectionUtils::isNotEmpty).map(e -> e.get(0).getId()).orElse(0);

        // 查询id小值
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> minQuery = buildQueryWrapper(paramMap, orgCodeList,
                channelBranchCodeList, channelDistributionCodeList, condition);
        minQuery.select(EpPolicyContractInfoEntity::getId);
        minQuery.orderByAsc(EpPolicyContractInfoEntity::getId);
        minQuery.last(StrUtil.format("limit {}", 1));
        final List<EpPolicyContractInfoEntity> minIdEntity = epPolicyContractInfoService.list(minQuery);
        final Integer minId = Optional.ofNullable(minIdEntity).filter(CollectionUtils::isNotEmpty).map(e -> e.get(0).getId()).orElse(0);

        // 按10万的id集一次处理
        for (int i = minId; i <= maxId; i++) {
            log.info("数据范围：{}到{},当前位置：{}", minId, maxId, i);
            final LambdaQueryWrapper<EpPolicyContractInfoEntity> thisQueryById = buildQueryWrapper(paramMap, orgCodeList,
                    channelBranchCodeList, channelDistributionCodeList, condition);
            //查询需要导出的id集
            thisQueryById.eq(EpPolicyContractInfoEntity::getDeleted, NumberUtils.INTEGER_ZERO);
            thisQueryById.orderByAsc(EpPolicyContractInfoEntity::getId);
            final int end = i + QUANTITY_PER_PROCESSING_ID;
            thisQueryById.between(EpPolicyContractInfoEntity::getId, i, maxId < end ? maxId : end);
            i = end;
            final List<Integer> ids = baseMapper.queryEpV2PolicyExportId(thisQueryById);
            if (CollectionUtils.isEmpty(ids)) {
                continue;
            }
            // 4000条的写入流
            for (List<Integer> thisids : ListUtils.partition(ids, MAXIMUM_IN)) {
                //页面导出2w限制
                if (PolicyExportMethodEnum.PAGE.equals(exportMethodEnum) && row > EXPORT_LARGE_RECORDS) {
                    break;
                }
                //导出数据集
                final List<EpV2PolicyExportVo> expData = baseMapper.queryEpV2PolicyExportVo(thisids);
                // vo数据赋值
                final List<EpV2PolicyExportVo> exportVos = getEpV2PolicyExportVos(orgInfoEntityMap, sensitiveDataAccess
                        , expData,productInfoMap);
                // 单个sheet数量限制
                if (row + exportVos.size() > SHEET_MAX) {
                    row = 0;
                    index++;
                    sheet = new Sheet(index, 0, channelManage ? EpV2PolicyExportVo.class: EpV2PolicyExportSupVo.class);
                    sheet.setSheetName("保单列表" + index);
                }
                writer.write(exportVos, sheet);
                row = row + exportVos.size();
            }
            //页面导出2w限制
            if (PolicyExportMethodEnum.PAGE.equals(exportMethodEnum) && row > EXPORT_LARGE_RECORDS) {
                break;
            }
        }
        writer.finish();
        out.flush();
    }

    /**
     * 导出到页面
     * @param sensitiveDataAccess
     * @param paramMap
     * @param out
     * @param exportMethodEnum
     * @throws IOException
     */
    public void queryExportPage(final boolean sensitiveDataAccess,
                                final Map<String, Object> paramMap,
                                final OutputStream out,
                                final PolicyExportMethodEnum exportMethodEnum) throws IOException {


        // 公用信息暂存
        final List<String> orgCodeList = PolicyPermissionHelper.getOrgCodeList();
        final List<String> channelBranchCodeList = PolicyPermissionHelper.getChannelBranchCodeList();
        final List<String> channelDistributionCodeList = PolicyPermissionHelper.getChannelDistributionCodeList();
        final Map<String, OrgInfoEntity> orgInfoEntityMap = getStringOrgInfoEntityMap();
        final boolean condition = !ShiroUtils.getSubject().isPermitted("policy:onLineVisible");
        final boolean channelManage = ShiroUtils.getSubject().isPermitted(AuthorityEnum.POLICY_CHANNEL_MANAGE.getCode());
        final Map<String, String> productInfoMap = getProductInfoMap();
        log.info("执行导出的基础条件 paramMap={} orgCodeList={} channelBranchCodeList={} channelDistributionCodeList={} ",paramMap,orgCodeList,channelBranchCodeList,channelDistributionCodeList);
        // 查询导出Id
        final LambdaQueryWrapper<EpPolicyContractInfoEntity> queryById = buildQueryWrapper(paramMap, orgCodeList,
                channelBranchCodeList, channelDistributionCodeList, condition);
        queryById.select(EpPolicyContractInfoEntity::getId);
        queryById.orderByAsc(EpPolicyContractInfoEntity::getId);
        queryById.eq(EpPolicyContractInfoEntity::getDeleted, NumberUtils.INTEGER_ZERO);
        queryById.last(StrUtil.format("limit {}", EXPORT_LARGE_RECORDS));
        final List<Integer> ids = baseMapper.queryEpV2PolicyExportId(queryById);

        // Excel信息配置
        final ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
        int row = 0;
        int index = 1;
        Sheet sheet = new Sheet(index, 0, channelManage ? EpV2PolicyExportVo.class: EpV2PolicyExportSupVo.class);
        sheet.setSheetName("保单列表" + index);
        writer.write(new ArrayList<>(), sheet);
        // 数据不为空时
        if (CollectionUtils.isNotEmpty(ids)) {
            final List<List<Integer>> groupIds = ListUtils.partition(ids, MAXIMUM_IN);
            for (List<Integer> thisIds : groupIds) {
                log.info("导出数据行数：{}", row);
                //页面导出2w限制
                if (PolicyExportMethodEnum.PAGE.equals(exportMethodEnum) && row > EXPORT_LARGE_RECORDS) {
                    break;
                }
                //根据id丰富字段
                final List<EpV2PolicyExportVo> expData = baseMapper.queryEpV2PolicyExportVo(thisIds);
                final List<EpV2PolicyExportVo> exportVos = getEpV2PolicyExportVos(orgInfoEntityMap,
                        sensitiveDataAccess, expData,productInfoMap);
                // 单个sheet数量限制
                if (row + exportVos.size() > SHEET_MAX) {
                    row = 0;
                    index++;
                    sheet = new Sheet(index, 0, channelManage ? EpV2PolicyExportVo.class: EpV2PolicyExportSupVo.class);
                    sheet.setSheetName("保单列表" + index);
                }
                writer.write(exportVos, sheet);
                row = row + exportVos.size();
            }
        }
        writer.finish();
        out.flush();
    }


    /**
     * 获取险种与险种类型Map
     * @return
     */
    public Map<String, String> getProductInfoMap() {
        //查询险种对象
        try {
            final List<InsuranceProductInfoEntity> productInfoEntities = insuranceProductInfoService.list(Wrappers.
                    <InsuranceProductInfoEntity>lambdaQuery()
                    .select(InsuranceProductInfoEntity::getProductCode, InsuranceProductInfoEntity::getProductType)
                    .eq(InsuranceProductInfoEntity::getDeleted, 0)
                    .last(" limit 200000"));
            return Optional.ofNullable(productInfoEntities)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(e -> e.stream()
                            .filter(s -> Objects.nonNull(s) &&
                                    StringUtils.isNotBlank(s.getProductCode())
                                    && StringUtils.isNotBlank(s.getProductType()))
                            .collect(Collectors
                                    .toMap(InsuranceProductInfoEntity::getProductCode,
                                            InsuranceProductInfoEntity::getProductType,
                                            (start, end) -> end)))
                    .orElseGet(Collections::emptyMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("查询产品编码异常",e);
        }
        return Collections.emptyMap();
    }

    @Override
    public List<EpV2PolicyExportVo> exportPolicyByCompliance(Map<String, Object> params, boolean sensitiveDataAccess) {

        log.info("执行导出的的条件信息={} ", JSON.toJSONString(params));

        List<String> orgCodeList = (List<String>) params.get("orgCodeList");
        List<String> channelBranchCodeList = (List<String>) params.get("channelBranchCodeList");
        List<String> channelDisCodeList = (List<String>) params.get("channelDisCodeList");

        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");
        //是否签署客户告知书
        Integer customerAgreed = RequestUtils.objectValueToInteger(params, "customerAgreed");
        String channelDistributionCode = RequestUtils.objectValueToString(params, "channelDistributionCode");


        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }

        //  分批查询导出的需要, 1 按条件导出采用 maxId + limitSize 2 全导出采用 startSize + limitSize(between条件查询)
        Integer salesType = RequestUtils.objectValueToInteger(params, "salesType");

        Integer betweenSize = RequestUtils.objectValueToInteger(params, "betweenSize");
        Integer startSize = RequestUtils.objectValueToInteger(params, "startSize");
        Integer maxId = RequestUtils.objectValueToInteger(params, "maxId");
        Integer limitSize = RequestUtils.objectValueToInteger(params, "limitSize");

        // 构建queryWrapper
        QueryWrapper<EpV2PolicyExportVo> queryWrapper = new QueryWrapper<EpV2PolicyExportVo>()
                .ne("intact", PolicyIntactTypeEnum.TEMP.getCode())
                .eq("show_model", StatusEnum.NORMAL.getCode())
                .eq(StringUtils.isNotBlank(channelDistributionCode),"epci.channel_distribution_code",channelDistributionCode)
                .in(policyProductTypes != null, "policy_product_type", policyProductTypes)
                .eq(StringUtils.isNotBlank(policyNo), "policy_no", policyNo)
                .eq(StringUtils.isNotBlank(applicantPolicyNo), "applicant_policy_no", applicantPolicyNo)
                .in(companyCodes != null, "epci.company_code", companyCodes)
                .likeRight(StringUtils.isNotBlank(applicantName), "epai.applicant_name", applicantName)
                .eq(StringUtils.isNotBlank(policySource), "source_platform", policySource)
                .in(policyStatuses != null, "admin_policy_status", policyStatuses)
                .inSql(StringUtils.isNotBlank(insuredName), "epci.contract_code", "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                .in(channelCodes != null, "epci.channel_code", channelCodes)
                .in(orgCodes != null, "epci.org_code", orgCodes)
                .and(StringUtils.isNotBlank(portfolioNameOrCode),
                        x -> x.likeRight("portfolio_name", portfolioNameOrCode))
                .in(referrerCodes != null, "epci.referrer_code", referrerCodes)
                .inSql(agentCodes != null, "epci.contract_code", StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                        x.eq("epci.is_receipt", StatusEnum.INVALID.getCode())
                                .or()
                                .eq("epci.is_revisit", StatusEnum.INVALID.getCode())
                )
                .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                        x.eq(suspendTypeSet.contains("1"), "epci.is_receipt", StatusEnum.INVALID.getCode())
                                .or()
                                .eq(suspendTypeSet.contains("2"), "epci.is_revisit", StatusEnum.INVALID.getCode()))
                .eq(settlementStatus != null, "epci.settlement_status", settlementStatus)
                .eq(customerAgreed != null, "epci.offline_product_sign_status", customerAgreed)
                .ge(StringUtils.isNotBlank(orderTimeBegin), "epci.order_time", orderTimeBegin)
                .lt(StringUtils.isNotBlank(orderTimeEnd), "epci.order_time", orderTimeEnd)
                .ge(StringUtils.isNotBlank(enforceTimeBegin), "epci.enforce_time", enforceTimeBegin)
                .lt(StringUtils.isNotBlank(enforceTimeEnd), "epci.enforce_time", enforceTimeEnd)
                .last(startSize != null, StrUtil.format("limit {},{}", startSize, limitSize))
                .gt(maxId != null, "epci.id", maxId)
                .last(maxId != null, StrUtil.format(" limit {}", limitSize))
                // 1.4.1添加保单权限
                .in(channelBranchCodeList != null, "epci.policy_channel_branch_code", channelBranchCodeList)
                .eq("epci.deleted", 0)
                // 线下单需要判断机构
                .in(Objects.equals(salesType,PolicySalesTypeEnum.OUT_LINE.getCode())&&CollectionUtils.isNotEmpty(orgCodeList), "epci.org_code", orgCodeList)
                // 线上单
                .eq( "epci.sales_type", salesType)
                //  方式1 between 导出
                .between(betweenSize != null, "epci.id", betweenSize, limitSize)
                .comment("xiaowhale query");

        log.info("执行的sql条件为：{}", getSelectString(queryWrapper));
        List<EpV2PolicyExportVo> exportList = baseMapper.exportPolicyV2(queryWrapper);
        //字段丰富
        List<EpV2PolicyExportVo> collect = getEpV2PolicyExportVos(sensitiveDataAccess, exportList);
        return collect;
    }

    private String convertInsuredPeriodType(String code) {
        PolicyInsuredPeriodTypeEnum[] enums = PolicyInsuredPeriodTypeEnum.values();
        for (PolicyInsuredPeriodTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getInsuredDesc();
            }
        }
        return "";
    }

    private String convertPeriodType(String code) {
        PolicyPaymentTypeEnum[] enums = PolicyPaymentTypeEnum.values();
        for (PolicyPaymentTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getPaymentDesc();
            }
        }
        return "";
    }

    private String convertPaymentPeriodType(String code) {
        PolicyPaymentPeriodTypeEnum[] enums = PolicyPaymentPeriodTypeEnum.values();
        for (PolicyPaymentPeriodTypeEnum entry : enums) {
            if (Objects.equals(code, entry.getCode())) {
                return entry.getPeriodDesc();
            }
        }
        return "";
    }

    @Override
    public int getExportPolicyMaxEpcidCompliance(Map<String, Object> params) {
        List<String> orgCodeList = (List<String>) params.get("orgCodeList");
        List<String> channelBranchCodeList = (List<String>) params.get("channelBranchCodeList");
        //产品类型
        String[] policyProductTypes = RequestUtils.objectValueToStringList(params, "policyProductTypes");
        //保单号
        String policyNo = RequestUtils.objectValueToString(params, "policyNo");
        //预报单号
        String applicantPolicyNo = RequestUtils.objectValueToString(params, "applicantPolicyNo");
        //公司编码
        String[] companyCodes = RequestUtils.objectValueToStringList(params, "companyCodes");
        //产品名称或编码
        String portfolioNameOrCode = RequestUtils.objectValueToString(params, "portfolioNameOrCode");
        //投保人姓名
        String applicantName = RequestUtils.objectValueToString(params, "applicantName");
        //被保人姓名
        String insuredName = RequestUtils.objectValueToString(params, "insuredName");
        //保单来源
        String policySource = RequestUtils.objectValueToString(params, "policySource");
        //保单状态
        String[] policyStatuses = RequestUtils.objectValueToStringList(params, "policyStatuses");
        // 发放状态
        Integer settlementStatus = RequestUtils.objectValueToInteger(params, "settlementStatus");
        //销售渠道
        String[] channelCodes = RequestUtils.objectValueToStringList(params, "channelCodes");
        //渠道推荐人姓名/编码
        String[] referrerCodes = RequestUtils.objectValueToStringList(params, "referrerCodes");
        //代理人机构
        String[] orgCodes = RequestUtils.objectValueToStringList(params, "orgCodes");
        //代理人姓名/编码
        String[] agentCodes = RequestUtils.objectValueToStringList(params, "agentCodes");
        //交单日期起始时间
        String orderTimeBegin = RequestUtils.objectValueToString(params, "orderTimeBegin");
        //交单日期终止时间
        String orderTimeEnd = RequestUtils.objectValueToString(params, "orderTimeEnd");
        //生效日期起始时间
        String enforceTimeBegin = RequestUtils.objectValueToString(params, "enforceTimeBegin");
        //生效日期终止时间
        String enforceTimeEnd = RequestUtils.objectValueToString(params, "enforceTimeEnd");
        Integer incomplete = RequestUtils.objectValueToInteger(params, "incomplete");
        Integer salesType = RequestUtils.objectValueToInteger(params, "salesType");
        String channelDistributionCode = RequestUtils.objectValueToString(params, "channelDistributionCode");

        String[] suspendTypes = RequestUtils.objectValueToStringList(params, "suspendTypes");
        Set<String> suspendTypeSet = new HashSet<>();
        if (suspendTypes != null) {
            suspendTypeSet.addAll(Arrays.asList(suspendTypes));
        }
        Integer exportPolicyMaxEpcid = baseMapper.getExportPolicyMaxEpcid(
                new QueryWrapper<>()
                        .ne("intact", PolicyIntactTypeEnum.TEMP.getCode())
                        .eq("show_model", StatusEnum.NORMAL.getCode())
                        .eq(StringUtils.isNotBlank(channelDistributionCode),"epci.channel_distribution_code",channelDistributionCode)
                        .in(policyProductTypes != null, "policy_product_type", policyProductTypes)
                        .eq(StringUtils.isNotBlank(policyNo), "policy_no", policyNo)
                        .eq(StringUtils.isNotBlank(applicantPolicyNo), "applicant_policy_no", applicantPolicyNo)
                        .in(companyCodes != null, "company_code", companyCodes)
                        .likeRight(StringUtils.isNotBlank(applicantName), "epai.applicant_name", applicantName)
                        .eq(StringUtils.isNotBlank(policySource), "source_platform", policySource)
                        .in(policyStatuses != null, "admin_policy_status", policyStatuses)
                        .inSql(StringUtils.isNotBlank(insuredName), "epci.contract_code", "select contract_code from ep_policy_insured_info where deleted=0 and insured_name like '" + insuredName + "%'")
                        .in(channelCodes != null, "epci.channel_code", channelCodes)
                        .in(orgCodes != null, "epci.org_code", orgCodes)
                        .and(StringUtils.isNotBlank(portfolioNameOrCode),
                                x -> x.likeRight("portfolio_name", portfolioNameOrCode))
                        .in(referrerCodes != null, "epci.referrer_code", referrerCodes)
                        .inSql(agentCodes != null, "epci.contract_code", StrUtil.format("select contract_code from ep_policy_agent_info where deleted=0 and agent_code in ({})", agentCodes != null ? Arrays.stream(agentCodes).map(x -> "\"" + x + "\"").collect(Collectors.joining(",")) : ""))
                        .and(incomplete != null && incomplete == 1 && suspendTypeSet.isEmpty(), x ->
                                x.eq("epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq("epci.is_revisit", StatusEnum.INVALID.getCode())
                        )
                        .and(incomplete != null && incomplete == 1 && !suspendTypeSet.isEmpty(), x ->
                                x.eq(suspendTypeSet.contains("1"), "epci.is_receipt", StatusEnum.INVALID.getCode())
                                        .or()
                                        .eq(suspendTypeSet.contains("2"), "epci.is_revisit", StatusEnum.INVALID.getCode()))
                        .eq(settlementStatus != null, "epci.settlement_status", settlementStatus)
                        .ge(StringUtils.isNotBlank(orderTimeBegin), "epci.order_time", orderTimeBegin)
                        .lt(StringUtils.isNotBlank(orderTimeEnd), "epci.order_time", orderTimeEnd)
                        .ge(StringUtils.isNotBlank(enforceTimeBegin), "epci.enforce_time", enforceTimeBegin)
                        .lt(StringUtils.isNotBlank(enforceTimeEnd), "epci.enforce_time", enforceTimeEnd)
                        .in(channelBranchCodeList != null, "epci.policy_channel_branch_code", channelBranchCodeList)
                        .eq("epci.deleted", 0)
                        // 线下单需要判断机构
                        .in(Objects.equals(salesType,PolicySalesTypeEnum.OUT_LINE.getCode())&&CollectionUtils.isNotEmpty(orgCodeList), "epci.org_code", orgCodeList)
                        // 线上单
                        .eq( "epci.sales_type", salesType)
                        .orderByDesc("epci.id")
                        .last(StrUtil.format(" limit 1"))
        );
        if (exportPolicyMaxEpcid == null) {
            return 0;
        }
        return exportPolicyMaxEpcid;
    }

    private String getSelectString(QueryWrapper wrapper) {


        String str1 = JSON.toJSONString(wrapper.getParamNameValuePairs());
        String str2 = JSON.toJSONString(wrapper.getExpression().getSqlSegment());
        HashMap<String, Object> hashMap = JSON.parseObject(str1, HashMap.class);

        for (Map.Entry<String, Object> next : hashMap.entrySet()) {

            String key = "#{ew.paramNameValuePairs." + next.getKey() + "}";
            System.out.println(key);
            str2 = str2.replace(key, String.valueOf(next.getValue()));
        }

        return str2;
    }

    /**
     * 查询当前保单的被保人险种列表
     * 1. 如果保单在保，则返回当前保单所有在保的险种清单
     * 2. 如果保单已经退保，则返回保单最后一次退保的险种清单
     * @param contractCode 保单合同号
     * @return 保单险种列表
     */
    @Override
    public List<PolicyInsuredProductVo> currentInsuredProductList(String contractCode) {
        EpContractBriefInfoVo policyInfo = this.queryPolicyContractBriefInfo(contractCode);
        String policyStatus = policyInfo.getPolicyStatus();
        //1. 如果保单已经退保，则从退保保全信息中获取退保明细信息
        List<EpInsuredProductVo> data;
        if(PolicyContractStatusEnum.isSurrenderStatus(policyStatus)){
            data =policyBaseService.querySurrenderInsuredProductList(contractCode);
        }
        else{
            //2. 获取当前保单在保的被保人险种集合
            data = policyBaseService.queryActiveInsuredProductList(contractCode);
        }
        return convertPolicyInsuredProductList(data);
    }

    @Override
    public List<PolicyInsuredProductVo> activeInsuredProductList(String contractCode) {
        List<EpInsuredProductVo> data =  policyBaseService.queryActiveInsuredProductList(contractCode);
        return convertPolicyInsuredProductList(data);
    }

    private List<PolicyInsuredProductVo> convertPolicyInsuredProductList(List<EpInsuredProductVo> data){
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(product->{
            PolicyInsuredProductVo vo = new PolicyInsuredProductVo();
            BeanUtils.copyProperties(product, vo);
            Integer mainInsurance = product.getMainInsurance();
            String mainFlag = Objects.equals(mainInsurance, 0) ? "否" : "是";
            vo.setMainInsurance(mainFlag);

            String insuredPeriodType = convertInsuredPeriodType(product.getInsuredPeriodType());
            vo.setInsuredPeriodType(insuredPeriodType);

            String periodType = convertPeriodType(product.getPeriodType());
            vo.setPeriodType(periodType);

            String paymentPeriodType = convertPaymentPeriodType(product.getPaymentPeriodType());
            vo.setPaymentPeriodType(paymentPeriodType);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean policyImageUpdate(PolicyImageUpdateInput policyImageUpdateInput) {
        PolicyContractInfoVo data = this.queryPolicyContractInfo(policyImageUpdateInput.getContractCode());
        log.info("policyImageUpdate data= 【{}】",JSON.toJSONString(data));
        String mainProductCode = data.getContractBaseInfo().getMainProductCode();
        ProductDetail productDetail = insuranceProductInfoService.queryProductDetail(mainProductCode);
        log.info("policyImageUpdate productDetail= 【{}】",JSON.toJSONString(productDetail));
        //业务分类 0.网销 1.线下 2.全部
        String businessClass = productDetail.getBusinessClass();
        if("0".equals(businessClass)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("网销单不可以上传电子保单"));
        }
        EpContractInfoVo entryContractInfoVo = new EpContractInfoVo();
        entryContractInfoVo.setPolicyUrl(policyImageUpdateInput.getPolicyUrl());
        entryContractInfoVo.setContractCode(policyImageUpdateInput.getContractCode());
        entryContractInfoVo.setContractAttachList(policyImageUpdateInput.getContractAttachList());
        log.info("policyImageUpdate getUserEntity= 【{}】",JSON.toJSONString(ShiroUtils.getUserEntity()));
        entryContractInfoVo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        //补全附件列表信息
        EpContractInfoVo entryContractInfo = this.completionAttachment(entryContractInfoVo);
        //调用保单中心
        Result<Boolean> save = epPolicyClient.policyImageUpdate(entryContractInfo);
        //调用失败抛异常
        if (!save.isSuccess()) {
            throw new GlobalException(save);
        }
        return save.getData();
    }

    @Override
    public String commonCorrectedPolicy(PolicyCorrectedModuleTypeEnum moduleType, EpContractInfoVo policyInfo) {
        log.info("开始修正保单信息:{},{}",moduleType,policyInfo);
        policyInfo.setOptUser(ShiroUtils.getUserEntity().getUsername());
        Result<String> result = epPolicyClient.correctedPolicyInfo(moduleType.getCode(),policyInfo);
        log.info("保单信息修正完成:{}",result);
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        return result.getData();
    }

    @Override
    public PageUtils<PolicyCorrectedLogVo> policyCorrectedLogPage(PolicyCorrectedLogQuery query) {
        log.info("开始查询保单变更记录:{}",query);
        Result<PageUtils<PolicyCorrectedLogVo>> result = epPolicyClient.policyCorrectedLogQuery(query);
        log.info("保单信息修正完成:{}",result);
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        return result.getData();
    }

    @Override
    public EpContractInfoVo policyCorrectedSnapshot(Integer id) {
        log.info("开始查询保单变更记录:{}",id);
        Result<EpContractInfoVo> result = epPolicyClient.policyCorrectedSnapshot(id);
        log.info("保单信息修正完成:{}",result);
        if(!result.isSuccess()){
            throw new GlobalException(result);
        }
        return result.getData();
    }

}
