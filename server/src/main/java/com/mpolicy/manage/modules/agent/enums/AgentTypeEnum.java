package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * 代理人人员类型枚举
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-10-30 10:06
 */
public enum AgentTypeEnum {
    /**
     * 内勤
     */
    INSIDE("AGENT:AGENT_TYPE:INSIDE", "内勤"),
    /**
     * 代理人
     */
    AGENT("AGENT:AGENT_TYPE:AGENT", "代理人"),
    /**
     * 区域经理
     */
    AREA_MANAGER("AGENT:AGENT_TYPE:AREA_MANAGER", "区域经理"),
    ;

    private final String key;
    private final String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    AgentTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static AgentTypeEnum getValueByKey(String key) {
        return Arrays.stream(values()).filter((x) -> x.key.equals(key)).findFirst().orElse(null);
    }

}
