package com.mpolicy.manage.modules.policy.vo.lost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: PolicyLostRegisterResultOut
 * Description: 丢单等级处理结果状态
 * date: 2024/2/27 13:51
 *
 * <AUTHOR>
 */
@ApiModel(value = "丢单等级处理结果状态")
@Data
public class PolicyLostRegisterResultOut {
    /**
     * 处理状态编码
     */
    @ApiModelProperty(value = "处理状态编码")
    private Integer code;
    /**
     * 处理状态结果
     */
    @ApiModelProperty(value = "处理状态结果")
    private String desc;
}
