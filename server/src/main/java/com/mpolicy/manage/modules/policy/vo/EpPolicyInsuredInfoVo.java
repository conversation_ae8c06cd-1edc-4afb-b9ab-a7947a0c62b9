package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.mpolicy.policy.common.ep.policy.product.EpPolicyInsuredProductVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 导入团险分单数据
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-03-11 17:24:10
 */
@Data
public class EpPolicyInsuredInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 被保人编码
     */
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;
    /**
     * 主被保人标识
     */
    @ApiModelProperty(value = "主被保人标识")
    private Integer mainFlag;
    /**
     * 对应主被保人编码
     */
    @ApiModelProperty(value = "对应主被保人编码")
    private String mainInsuredCode;
    /**
     * 对应主被保人姓名
     */
    @ApiModelProperty(value = "对应主被保人姓名")
    private String mainInsuredName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredName;

    @ApiModelProperty(value = "投被保人关系:[被保人是投保人的???]")
    private String insuredRelation;

    @ApiModelProperty(value = "被保人类型")
    private Integer insuredType;

    @ApiModelProperty(value = "与主被保人关系")
    private String firstInsuredRelation;

    @ApiModelProperty(value = "被保人性别")
    private String insuredGender;

    @ApiModelProperty(value = "被保人出生日期")
    private String insuredBirthday;

    @ApiModelProperty(value = "被保人证件类型")
    private String insuredIdType;

    @ApiModelProperty(value = "被保人证件号码")
    private String insuredIdCard;

    @ApiModelProperty(value = "计划名称")
    private String productName;

    @ApiModelProperty(value = "计划编码")
    private String productCode;

    @ApiModelProperty(value = "产品计划编码(产品计划编码)")
    private String planCode;

    @ApiModelProperty(value = "产品计划名称(产品计划名称)")
    private String planName;


    @ApiModelProperty(value = "保障计划编码(虚拟计划编码)")
    private String virtualPlanCode;

    @ApiModelProperty(value = "保障计划名称(虚拟计划名称)")
    private String virtualPlanName;

    @ApiModelProperty(value = "是否减员")
    private Integer surrendered;

    @ApiModelProperty(value = "操作")
    private String insuredOptType;

    @ApiModelProperty(value = "被保人生效日期")
    @JSONField(format = "yyyy-MM-dd")
    private String insuredEffectiveTime;

    @ApiModelProperty(value = "保费")
    private BigDecimal premium;

    @ApiModelProperty(value = "被保人失效日期")
    @JSONField(format = "yyyy-MM-dd")
    private String insuredEndTime;

    @ApiModelProperty(value = "推荐人|代理人工号")
    private String referrerWno;

    @ApiModelProperty(value = "推荐人|代理人名字")
    private String referrerName;

    @ApiModelProperty(value = "渠道推荐人编码")
    private String channelReferrerCode;

    @ApiModelProperty(value = "渠道推荐人工号")
    private String channelReferrerWno;

    @ApiModelProperty(value = "渠道推荐人名字")
    private String channelReferrerName;

    @ApiModelProperty(value = "销售渠道编码")
    private String channelCode;

    @ApiModelProperty(value = "销售渠道")
    private String channelName;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchCode;

    @ApiModelProperty(value = "渠道分支")
    private String channelBranchName;

    @ApiModelProperty(value = "初始推荐人编码")
    private String customerManagerCode;

    @ApiModelProperty(value = "初始推荐人名字")
    private String customerManagerName;

    @ApiModelProperty(value = "初始推荐人工号")
    private String customerManagerChannelCode;

    @ApiModelProperty(value = "初始推荐人所属机构")
    private String customerManagerOrgCode;

    @ApiModelProperty(value = "初始推荐人所属机构")
    private String customerManagerOrgName;

    private List<EpPolicyInsuredProductVo> productList;
}