package com.mpolicy.manage.modules.insurance.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 组合扩展信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-28 16:50:45
 */
@TableName("insurance_portfolio_extend")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsurancePortfolioExtendEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 自增ID
	 */
	@TableId
	private Integer id;
	/**
	 * 组合编码
	 */
	private String portfolioCode;
	/**
	 * 投保年龄
	 */
	@TableField(strategy= FieldStrategy.IGNORED)
	private String insureAge;
	/**
	 * 保额
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String amount;
	/**
	 * 保障期间
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String coveragePeriod;
	/**
	 * 缴费期间
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String payPeriod;
	/**
	 * 犹豫期
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String hesitationPeriod;
	/**
	 * 等待期
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String waitingPeriod;
	/**
	 * 职业类别
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String insurerJobType;
	/**
	 * 续保条件
	 */
	@TableField(strategy=FieldStrategy.IGNORED)
	private String renewalCondition;
	/**
	 * 是否删除 0否 1是
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private long revision;
}
