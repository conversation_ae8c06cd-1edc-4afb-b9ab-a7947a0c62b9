package com.mpolicy.manage.modules.policy.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ClassName: PolicyUserRecordMsgListVo
 * Description: 保单托管信息vo
 * date: 2023/11/2 15:00
 *
 * <AUTHOR>
 */
@Data
public class PolicyUserRecordMsgListVo {

    /**
     * id
     */
    private Integer id;
    /**
     * 上传流水号
     */
    private String recordSn;
    /**
     * 托管保单类型 0：本人相关保单；1：本人无关保单
     */
    private Integer trustPolicyType;
    /**
     * 客户家庭成员姓名
     */
    private String customerFamilyMemberName;
    /**
     * 客户家庭成员证件号
     */
    private String customerFamilyMemberIdCard;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 经纪人编码
     */
    private String agentCode;
    /**
     * 经纪人姓名
     */
    private String agentName;
    /**
     * 经纪人业务编码
     */
    private String agentBusinessCode;
    /**
     * 工单编码
     */
    private String orderCode;
    /**
     * 客户和投保人关系
     */
    private String customerApplicantRelationship;
    /**
     * 客户和投保人关系 字符
     */
    private String customerApplicantRelationshipStr;
    /**
     * 合同流水号
     */
    private String contractCode;
    /**
     * 投保人关系
     */
    private String applicantType;
    /**
     * 被保人关系
     */
    private String insuredType;
    /**
     * 补充说明
     */
    private String customerTips;
    /**
     * 修改说明
     */
    private String operatorTips;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 保险公司名称
     */
    private String companyName;
    /**
     * 投保人姓名
     */
    private String applicantName;
    /**
     * 被保人姓名
     */
    private String insuredsName;
    /**
     * 险种名称
     */
    private String portfolioName;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 保障期间详情
     */
    private String insuredPeriod;
    /**
     * 保障期间
     */
    private String insuredPeriodBrief;
    /**
     * 缴费期间详情
     */
    private String paymentPeriod;
    /**
     * 缴费期间
     */
    private String paymentPeriodBrief;
    /**
     * 当前缴费期次
     */
    private Integer paymentPhase;

    /**
     * 客户更新数据的时间
     */
    private Date customerUpdateTime;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 保单状态
     */
    private String policyStatus;
    /**
     * 托管状态
     */
    private String recordStatus;
    /**
     * 暂存数据
     */
    private String data;
    /**
     * 是否为代理人上传 1:是 0:否
     */
    private Integer isAgentUpload;
    /**
     * 后台运营提交时间
     */
    private Date commitTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 乐观锁
     */
    private long revision;
    /**
     * 用户姓名
     */
    private String realName;
    /**
     * 推荐人编码
     */
    private String referrerCode;
    /**
     * 内部推荐人编码
     */
    private String innerReferrerCode;

    private String identificationNum;
}
