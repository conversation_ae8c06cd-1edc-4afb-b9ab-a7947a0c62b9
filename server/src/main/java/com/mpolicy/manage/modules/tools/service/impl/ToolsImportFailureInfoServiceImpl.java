package com.mpolicy.manage.modules.tools.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.tools.dao.ToolsImportFailureInfoDao;
import com.mpolicy.manage.modules.tools.entity.ToolsImportFailureInfoEntity;
import com.mpolicy.manage.modules.tools.service.ToolsImportFailureInfoService;
import com.mpolicy.manage.modules.tools.vo.RenewalImportErrorExcelVo;
import com.mpolicy.manage.modules.tools.vo.RenewalTermImportExtendVo;
import com.mpolicy.policy.common.ep.policy.renewal.RenewalTermImportVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangdonglin
 * @create: 2023-05-05 18:48
 * @description: 导入失败信息记录impl
 */
@Service("toolsImportFailureInfoService")
public class ToolsImportFailureInfoServiceImpl extends ServiceImpl<ToolsImportFailureInfoDao, ToolsImportFailureInfoEntity> implements ToolsImportFailureInfoService {

    /**
     * 批量新建数量
     */
    public static final int BATCH_SIZE = 1000;
    /**
     * 错误信息分割符
     */
    public static final String STRING = "|";
    /**
     * 默认查询数量限制
     */
    public static final String LAST_LIMIT = " LIMIT 100000";
    /**
     * 导入续期数据SHEET_NAME
     */
    public static final String SHEET_NAME = "导入续期数据【错误清单】";
    /**
     * 字符串null
     */
    public static final String NULL_STR = "null";

    /**
     * 续保信息导入错误信息新建
     *
     * @param result
     * @return
     */
    @Override
    public Result<RenewalTermImportVo> insert(final Result<RenewalTermImportVo> result) {
        // 对象校验
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return result;
        }
        // 返回值中添加操作标识
        final Date data = new Date();
        RenewalTermImportExtendVo importExtendVo = new RenewalTermImportExtendVo();
        RenewalTermImportVo resultData = JSON.parseObject(JSON.toJSONString(result.getData()), RenewalTermImportVo.class);
        BeanUtils.copyProperties(resultData, importExtendVo);
        final String operationSign = IdUtil.simpleUUID();
        importExtendVo.setOperationSign(operationSign);
        result.setData(importExtendVo);
        // 无失败记录不往后走
        if (Objects.isNull(importExtendVo) || CollectionUtils.isEmpty(importExtendVo.getErrorList())) {
            return result;
        }
        // 错误信息存储
        final List<ToolsImportFailureInfoEntity> toolsImportFailureInfoEntities = importExtendVo
                .getErrorList()
                .stream()
                .map(e -> {
                    ToolsImportFailureInfoEntity entity = new ToolsImportFailureInfoEntity();
                    entity.setOperationSign(operationSign);
                    entity.setFailureInformation(e.getSeq() + STRING + e.getPolicyNo() + STRING + e.getMessage());
                    entity.setCreateTime(data);
                    return entity;
                }).collect(Collectors.toList());
        insertListBatch(toolsImportFailureInfoEntities);
        return result;
    }

    /**
     * 设置导出错误信息流
     *
     * @param entity
     * @param out
     * @throws IOException
     */
    @Override
    public void setImportFailureInfoFlow(final ToolsImportFailureInfoEntity entity, final OutputStream out) throws IOException {
        // 根据操作标识查询失败信息 最大10w
        final LambdaQueryWrapper<ToolsImportFailureInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNoneBlank(entity.getOperationSign())
                , ToolsImportFailureInfoEntity::getOperationSign
                , entity.getOperationSign())
                .orderByAsc(ToolsImportFailureInfoEntity::getId)
                .last(LAST_LIMIT);
        final List<ToolsImportFailureInfoEntity> entities = this.list(lambdaQueryWrapper);
        //转成Excel模型
        final List<RenewalImportErrorExcelVo> errorExcelVos = Optional.ofNullable(entities).filter(CollectionUtils::isNotEmpty).map(ens ->
                ens.stream().filter(en -> StringUtils.isNotEmpty(en.getFailureInformation())).map(en -> {
                    RenewalImportErrorExcelVo vo = new RenewalImportErrorExcelVo();
                    String[] row = StringUtils.split(en.getFailureInformation(), STRING, 3);
                    vo.setSeq(removeNull(row[0]));
                    vo.setPolicyNo(removeNull(row[1]));
                    vo.setMessage(removeNull(row[2]));
                    return vo;
                }).collect(Collectors.toList())).orElseGet(Collections::emptyList);
        // 生成保单文件
        final ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX);
        // 设置SHEET
        final Sheet sheet = new Sheet(1, 0, RenewalImportErrorExcelVo.class);
        sheet.setSheetName(SHEET_NAME);
        writer.write(errorExcelVos, sheet);
        writer.finish();
        out.flush();
    }

    /**
     * 批量新建
     *
     * @param list
     * @return
     */
    public int insertListBatch(final List<ToolsImportFailureInfoEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NumberUtils.INTEGER_ZERO;
        }
        return ListUtils.partition(list, BATCH_SIZE)
                .stream()
                .map(baseMapper::insertListBatch)
                .mapToInt(e -> e)
                .sum();
    }

    /**
     * 去除null空的情况
     *
     * @param str
     * @return
     */
    public String removeNull(final String str) {
        if (StringUtils.isNotEmpty(str) && NULL_STR.equals(str)) {
            return null;
        }
        return str;
    }
}
