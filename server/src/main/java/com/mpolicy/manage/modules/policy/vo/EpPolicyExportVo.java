package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 导出保单信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/12 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EpPolicyExportVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 保单类型
     */
    @ExcelProperty(value = "保单类型")
    private String policyType;
    /**
     * 保单产品类型(个、团、车、财)
     */
    @ExcelProperty(value = "产品类型")
    private String policyProductType;
    /**
     * 业务分类
     */
    @ExcelProperty(value = "业务分类")
    private String salesType;
    /**
     * 投保单号
     */
    @ExcelProperty(value = "投保单号")
    private String applicantPolicyNo;
    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;

    @ExcelProperty(value = "上一年保单号")
    private String sourcePolicyNo;
    /**
     * 保险公司名称
     */
    @ExcelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 主险名称
     */
    @ExcelProperty(value = "主险名称")
    private String mainProductName;
    /**
     * 销售平台
     */
    @ExcelProperty(value = "销售平台")
    private String salesPlatform;
    /**
     * 保单状态
     */
    @ExcelProperty(value = "保单状态")
    private String policyStatus;

    /**
     * 是否自保件
     */
    @ExcelProperty(value = "是否自保件")
    private String selfPreservation;
    /**
     * 佣金发放状态
     */
    private Integer settlementStatus;
    /**
     * 佣金发放状态 中文
     */
    @ExcelProperty(value = "佣金发放状态")
    private String settlementStatusStr;
    /**
     * 发放月份
     */
    private Integer settlementMonth;
    /**
     * 发放年份
     */
    private Integer settlementYear;
    /**
     * 发放月份 中文
     */
    @ExcelProperty(value = "发放月份")
    private String settlementMonthStr;
    /**
     * 险种名称
     */
    @ExcelProperty(value = "险种名称")
    private String productName;
    /**
     * 协议险种名称
     */
    @ExcelProperty(value = "协议险种名称")
    private String protocolProductName;
    /**
     * 险种状态
     */
    @ExcelProperty(value = "险种状态")
    private String productStatus;
    /**
     * 是否主附险
     */
    @ExcelProperty(value = "是否主附险")
    private String mainInsurance;
    /**
     * 缴费期间类型
     */
    @ExcelProperty(value = "缴费期间类型")
    private String paymentPeriodType;
    /**
     * 缴费期间
     */
    @ExcelProperty(value = "缴费期间")
    private String paymentPeriod;
    /**
     * 保险期间类型
     */
    @ExcelProperty(value = "保险期间类型")
    private String insuredPeriodType;
    /**
     * 保险期间
     */
    @ExcelProperty(value = "保险期间")
    private String insuredPeriod;
    /**
     * 缴费方式
     */
    @ExcelProperty(value = "缴费方式")
    private String periodType;
    /**
     * 保费
     */
    @ExcelProperty(value = "保费")
    private String premium;
    /**
     * 保额
     */
    @ExcelProperty(value = "保额")
    private String coverage;
    // TODO: 2022/1/11 是否联合展业

    /**
     * 是否主代理人
     */
    @ExcelProperty(value = "是否主代理人")
    private String mainFlag;
    /**
     * 代理人姓名
     */
    @ExcelProperty(value = "代理人姓名")
    private String agentName;
    /**
     * 代理人编码
     */
    @ExcelProperty(value = "代理人编码")
    private String businessCode;
    /**
     * 分佣比例
     */
    @ExcelProperty(value = "分佣比例")
    private String commissionRate;
    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String orgName;
    /**
     * 机构名称
     */
    @ExcelProperty(value = "机构名称")
    private String orgSuperiorName;
    /**
     * 渠道推荐人姓名
     */
    @ExcelProperty(value = "渠道推荐人姓名")
    private String referrerName;
    /**
     * 渠道推荐人工号
     */
    @ExcelProperty(value = "渠道推荐人工号")
    private String referrerWno;
    /**
     * 分支名称
     */
    @ExcelProperty(value = "分支名称")
    private String channelBranchName;
    /**
     * 销售渠道
     */
    @ExcelProperty(value = "销售渠道")
    private String channelName;
    /**
     * 推荐人工号
     */
    @ExcelProperty(value = "推荐人工号")
    private String policyReferrerWno;
    /**
     * 推荐人姓名
     */
    @ExcelProperty(value = "推荐人姓名")
    private String policyReferrerName;

    @ExcelProperty(value = "交单日期", format = "yyyy-MM-dd")
    private Date orderTime;
    /**
     * 投保日期
     */
    @ExcelProperty(value = "投保日期", format = "yyyy-MM-dd")
    private Date applicantTime;
    /**
     * 承保日期
     */
    @ExcelProperty(value = "承保日期", format = "yyyy-MM-dd")
    private Date approvedTime;
    /**
     * 生效日期
     */
    @ExcelProperty(value = "生效日期", format = "yyyy-MM-dd")
    private Date enforceTime;
    /**
     * 回执签署时间
     */
    @ExcelProperty(value = "回执签署时间", format = "yyyy-MM-dd")
    private Date receiptSignTime;
    /**
     * 回访日期
     */
    @ExcelProperty(value = "回访日期", format = "yyyy-MM-dd")
    private Date revisitTime;
    /**
     * 回访结果 0:失败; 1:成功
     */
    @ExcelProperty(value = "回访结果")
    private String revisitResult;
    /**
     * 回访失败原因
     */
    @ExcelProperty(value = "回访失败原因")
    private String revisitFailReason;
    /**
     * 撤单时间
     */
    @ExcelProperty(value = "撤单时间", format = "yyyy-MM-dd")
    private Date cancelTime;
    /**
     * 拒保时间
     */
    @ExcelProperty(value = "拒保时间", format = "yyyy-MM-dd")
    private Date declineTime;
    /**
     * 延期日期
     */
    @ExcelProperty(value = "延期日期", format = "yyyy-MM-dd")
    private Date postponeApprovedTime;
    /**
     * 犹豫期（天）
     */
    @ExcelProperty(value = "犹豫期（天）")
    private String hesitatePeriod;
    /**
     * 过犹豫期日期
     */
    @ExcelProperty(value = "过犹豫期日期")
    private String overHesitatePeriod;
    /**
     * 退保时间
     */
    @ExcelProperty(value = "退保时间", format = "yyyy-MM-dd")
    private Date surrenderTime;
    /**
     * 承保录入时间
     */
    @ExcelProperty(value = "承保录入时间", format = "yyyy-MM-dd")
    private Date approvedRecordTime;
    /**
     * 回执录入时间
     */
    @ExcelProperty(value = "回执录入时间", format = "yyyy-MM-dd")
    private Date receiptRecordTime;
    /**
     * 失效时间
     */
    @ExcelProperty(value = "失效时间", format = "yyyy-MM-dd")
    private Date failureTime;
    /**
     * 终止时间
     */
    @ExcelProperty(value = "终止时间", format = "yyyy-MM-dd")
    private Date terminationTime;
    /**
     * 复效时间
     */
    @ExcelProperty(value = "复效时间", format = "yyyy-MM-dd")
    private Date replyEffectiveTime;
    /**
     * 投保主体类型 0:非自然人; 1:自然人
     */
    @ExcelProperty(value = "投保主体类型")
    private String applicantType;
    /**
     * 投保人姓名
     */
    @ExcelProperty(value = "投保人姓名")
    private String applicantName;
    /**
     * 投保人性别
     */
    @ExcelProperty(value = "投保人性别")
    private String applicantGender;

    /**
     * 投保人出生日期
     */
    @ExcelProperty(value = "投保人出生日期")
    private String applicantBirthday;
    /**
     * 投保人证件类型
     */
    @ExcelProperty(value = "投保人证件类型")
    private String applicantIdType;
    /**
     * 投保人证件号码
     */
    @ExcelProperty(value = "投保人证件号码")
    private String applicantIdCard;

    /**
     * 投保人证件有效期
     */
    // TODO: 2021/12/29  投保人证件有效期
    @ExcelProperty(value = "投保人证件有效期")
    private String applicantIdCardValidityEnd;
    /**
     * 投保人电话
     */
    @ExcelProperty(value = "投保人电话")
    private String applicantMobile;
    /**
     * 投保人国籍
     */
    @ExcelProperty(value = "投保人国籍")
    private String applicantNation;
    /**
     * 投保人婚姻状况
     */
    @ExcelProperty(value = "投保人婚姻状况")
    private String applicantMarital;
    /**
     * 投保人职业
     */
    @ExcelProperty(value = "投保人职业")
    private String applicantCareer;
    /**
     * 投保人工作单位
     */
    @ExcelProperty(value = "投保人工作单位")
    private String applicantCompany;
    /**
     * 投保人详细地址
     */
    @ExcelProperty(value = "投保人详细地址")
    private String applicantAddress;
    /**
     * 投保主体行业类别
     */
    @ExcelProperty(value = "投保主体行业类别")
    private String applicantIndustryCategory;
    /**
     * 单位性质
     */
    @ExcelProperty(value = "单位性质")
    private String companyNature;
    /**
     * 社保登记号
     */
    @ExcelProperty(value = "社保登记号")
    private String companySocialSecurityNum;
    /**
     * 单位总人数
     */
    @ExcelProperty(value = "单位总人数")
    private String companyEmployeeNum;
    /**
     * 单位联系人姓名
     */
    @ExcelProperty(value = "单位联系人姓名")
    private String companyContactName;
    /**
     * 单位联系电话
     */
    @ExcelProperty(value = "单位联系电话")
    private String companyContactMobile;
    /**
     * 法人姓名
     */
    @ExcelProperty(value = "法人姓名")
    private String legalPersonName;
    /**
     * 法人证件类型
     */
    @ExcelProperty(value = "法人证件类型")
    private String legalPersonIdType;
    /**
     * 法人证件号码
     */
    @ExcelProperty(value = "法人证件号码")
    private String legalPersonIdCard;
    /**
     * 法人证件有效期
     */
    @ExcelProperty(value = "法人证件有效期")
    private String legalPersonIdCardValidityEnd;
    /**
     * 银行名称
     */
    @ExcelProperty(value = "银行名称")
    private String bankName;
    /**
     * 银行卡号
     */
    @ExcelProperty(value = "银行卡号")
    private String cardNo;
    /**
     * 银行卡名称
     */
    @ExcelProperty(value = "银行卡名称")
    private String cardName;

    /**
     * 被保主体类型
     */
    @ExcelProperty(value = "被保主体类型")
    private String insuredType;

    /**
     * 被保人是投保人的
     */
    @ExcelProperty(value = "被保人是投保人的")
    private String insuredRelation;
    /**
     * 被保人姓名
     */
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 被保人证件类型
     */
    @ExcelProperty(value = "被保人证件类型")
    private String insuredIdType;
    /**
     * 被保人证件号码
     */
    @ExcelProperty(value = "被保人证件号码")
    private String insuredIdCard;
    /**
     * 被保人性别
     */
    @ExcelProperty(value = "被保人性别")
    private String insuredGender;
    /**
     * 被保人出生日期
     */
    @ExcelProperty(value = "被保人出生日期")
    private String insuredBirthday;
    /**
     * 被保人证件有效期
     */
    @ExcelProperty(value = "被保人证件有效期")
    private String insuredIdCardValidityEnd;
    /**
     * 被保人电话
     */
    @ExcelProperty(value = "被保人电话")
    private String insuredMobile;
    /**
     * 被保人国籍
     */
    @ExcelProperty(value = "被保人国籍")
    private String insuredNation;
    /**
     * 被保人婚姻状况
     */
    @ExcelProperty(value = "被保人婚姻状况")
    private String insuredMarital;
    /**
     * 被保人职业
     */
    @ExcelProperty(value = "被保人职业")
    private String insuredCareer;
    /**
     * 被保人工作单位
     */
    @ExcelProperty(value = "被保人工作单位")
    private String insuredCompany;
    /**
     * 被保人详细地址
     */
    @ExcelProperty(value = "被保人详细地址")
    private String insuredAddress;
    /**
     * 被保人邮箱地址
     */
    @ExcelProperty(value = "被保人邮箱地址")
    private String insuredEmail;
    /**
     * 被保人学历
     */
    @ExcelProperty(value = "被保人学历")
    private String insuredEducation;
    /**
     * 保险标的物
     */
    @ExcelProperty(value = "保险标的物")
    private String insuranceSubjectMatter;
    /**
     * 车主姓名
     */
    @ExcelProperty(value = "车主姓名")
    private String vehicleOwnerName;
    /**
     * 车主手机号
     */
    @ExcelProperty(value = "车主手机号")
    private String vehicleOwnerMobile;
    /**
     * 车主证件类型
     */
    @ExcelProperty(value = "车主证件类型")
    private String vehicleOwnerCardType;
    /**
     * 车主性质
     */
    @ExcelProperty(value = "车主性质")
    private String vehicleOwnerNature;
    /**
     * 车主证件号码
     */
    @ExcelProperty(value = "车主证件号码")
    private String vehicleOwnerCardNo;
    /**
     * 车主性别
     */
    @ExcelProperty(value = "车主性别")
    private Integer vehicleOwnerGender;
    /**
     * 车主出生日期
     */
    @ExcelProperty(value = "车主出生日期", format = "yyyy-MM-dd")
    private Date vehicleOwnerBirthday;
    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String vehicleLicensePlateNumber;
    /**
     * 初登日期
     */
    @ExcelProperty(value = "初登日期", format = "yyyy-MM-dd")
    private Date vehicleInitialRegistryDate;
    /**
     * 机动车使用性质 0:非营业;1:营业
     */
    @ExcelProperty(value = "机动车使用性质")
    private String vehicleUsage;
    /**
     * 车辆品牌
     */
    @ExcelProperty(value = "车辆品牌")
    private String vehicleBrandName;
    /**
     * 车辆型号
     */
    @ExcelProperty(value = "车辆型号")
    private String vehicleType;
    /**
     * 车架号
     */
    @ExcelProperty(value = "车架号")
    private String vehicleFrameNumber;
    /**
     * 发动机号
     */
    @ExcelProperty(value = "发动机号")
    private String vehicleEngineNumber;
    /**
     * 机动车种类
     */
    @ExcelProperty(value = "机动车种类")
    private String vehicleVariety;
    /**
     * 座位
     */
    @ExcelProperty(value = "座位")
    private String vehicleSize;
    /**
     * 吨位
     */
    @ExcelProperty(value = "吨位")
    private String vehicleTonnage;
    /**
     * 排气量
     */
    @ExcelProperty(value = "排气量")
    private String vehicleVolume;
    /**
     * 功率
     */
    @ExcelProperty(value = "功率")
    private String vehiclePower;
    /**
     * 平均行驶里程
     */
    @ExcelProperty(value = "平均行驶里程")
    private String vehicleTravelMiles;
    /**
     * 车辆价格
     */
    @ExcelProperty(value = "车辆价格")
    private String vehiclePrice;
    /**
     * 出险次数
     */
    @ExcelProperty(value = "出险次数")
    private String vehiclePaidTimes;
    /**
     * 是否出口车 0:否;1:是
     */
    @ExcelProperty(value = "是否出口车")
    private String vehicleIsImported;
    /**
     * 是否新车 0:否;1:是
     */
    @ExcelProperty(value = "是否新车")
    private String vehicleNew;
    /**
     * 是否过户车 0:否;1:是
     */
    @ExcelProperty(value = "是否过户车")
    private String vehicleTransferOwnership;

    /**
     * 图例名称
     */
    @ExcelProperty(value = "图例")
    private String channelDistributionName;


}
