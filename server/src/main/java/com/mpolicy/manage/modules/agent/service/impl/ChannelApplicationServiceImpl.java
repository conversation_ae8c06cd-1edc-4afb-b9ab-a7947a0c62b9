package com.mpolicy.manage.modules.agent.service.impl;

import cn.hutool.core.codec.BCD;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.common.utils.other.RandomUtil;
import com.mpolicy.customer.client.CustomerClient;
import com.mpolicy.manage.common.AdminCommonKeys;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.constant.AdminPublicConstant;
import com.mpolicy.manage.modules.agent.dao.ChannelApplicationDao;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.entity.ApplicationAgentRelationEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationEntity;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserInfoService;
import com.mpolicy.manage.modules.agent.service.ApplicationAgentRelationService;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationService;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.manage.modules.agent.vo.ChannelApplicationVo;
import com.mpolicy.manage.modules.agent.vo.ChannelVo;
import com.mpolicy.manage.modules.baichuan.entity.BcChannelBasicInfoEntity;
import com.mpolicy.manage.modules.baichuan.service.BcChannelBasicInfoService;
import com.mpolicy.manage.modules.content.enums.ChannelApplicationSelectTypeEnum;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 渠道应用
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 19:32:39
 */
@Service("channelApplicationService")
public class ChannelApplicationServiceImpl extends ServiceImpl<ChannelApplicationDao, ChannelApplicationEntity> implements ChannelApplicationService {
    @Value("${wx.miniapp.appid}")
    private String appid;

    @Autowired
    ApplicationAgentRelationService applicationAgentRelationService;
    @Autowired
    AgentUserInfoService agentUserInfoService;
    @Autowired
    ChannelInfoService channelInfoService;

    @Autowired
    CustomerClient customerClient;
    @Autowired
    IRedisService redisService;
    @Autowired
    BcChannelBasicInfoService bcChannelBasicInfoService;


    @Override
    public PageUtils<ChannelApplicationEntity> queryPage(Map<String, Object> params) {
        HashMap<String, Object> channelInfoQueryPageParam = new HashMap<>(8);
        // 处理查询条件，查询渠道信息
        channelInfoQueryPageParam.put("channelName", params.get("channelName"));
        channelInfoQueryPageParam.put("channelType", params.get("channelType"));
        channelInfoQueryPageParam.put("channelCode", params.get("channelCode"));
        channelInfoQueryPageParam.put("limit", "-1");
        channelInfoQueryPageParam.put("page", "1");
        String applicationCode = RequestUtils.objectValueToString(params, "applicationCode");

        // 获取渠道信息列表
        List<ChannelInfoEntity> channelCodeList =
                channelInfoService.queryPage(channelInfoQueryPageParam).getList();
        if (channelCodeList == null || channelCodeList.size() == 0) {
            return new PageUtils<>(new Page<>());
        }
        // 渠道编码列表
        List<String> channelCodeStrings = channelCodeList.stream()
                .map(ChannelInfoEntity::getChannelCode).collect(Collectors.toList());

        // 判断创建日期是否存在
        String createTime = null;
        if (params.get("createTime") != null && StringUtils.isNotBlank(params.get("createTime").toString())) {
            createTime = params.get("createTime").toString();
        }

        IPage<ChannelApplicationEntity> page = this.page(
                new Query<ChannelApplicationEntity>().getPage(params),
                new QueryWrapper<ChannelApplicationEntity>().lambda()
                        .in(ChannelApplicationEntity::getChannelCode, channelCodeStrings)
                        .eq(applicationCode != null, ChannelApplicationEntity::getApplicationCode, applicationCode)
                        .apply(createTime != null, "date_format(create_time,'%Y-%m-%d') = {0}", createTime)
        );

        // 包装返回数据
        Map<String, ChannelInfoEntity> channelCodeListMap = channelCodeList.stream().collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, item -> item));

        List<ChannelApplicationEntity> collect = page.getRecords().stream().peek(channelApplicationEntity -> {
            String channel = channelApplicationEntity.getChannelCode();

            // 拷贝渠道表中的数据到返回实体中
            ChannelInfoEntity channelInfoEntity = channelCodeListMap.get(channel);
            channelApplicationEntity.setChannelName(channelInfoEntity.getChannelName());
            channelApplicationEntity.setChannelType(channelInfoEntity.getChannelType());
            channelApplicationEntity.setFilePath(DomainUtil.addOssDomainIfNotExist(channelApplicationEntity.getFilePath()));
        }).collect(Collectors.toList());

        // 替换原有记录
        page.setRecords(collect);

        return new PageUtils<>(page);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void saveEntity(ChannelApplicationEntity channelApplicationEntity) {
        channelApplicationEntity.setApplicationCode(DateUtils.convertDate2String("MMdd") + RandomUtil.generateMixString(2));

        List<AgentInfoVo> agentInfoVoList = channelApplicationEntity.getAgentInfoVoList();

        // 检查顾问选择是否符合规则
        checkSelectList(channelApplicationEntity.getAgentSelectType(), agentInfoVoList);

        // 1、保存相应代理人
        applicationAgentRelationService.saveOrUpdateAgentRelation(channelApplicationEntity.getApplicationCode(), agentInfoVoList);

        // 生成二维码
        channelApplicationEntity.setFilePath(createQRCode(channelApplicationEntity.getApplicationCode()));

        // 2、保存渠道应用
        this.save(channelApplicationEntity);

        // 3、缓存
        saveOrUpdateCache(channelApplicationEntity);
    }

    /**
     * 生成微信二维码
     *
     * @param channelApplicationCode 渠道code
     */
    private String createQRCode(String channelApplicationCode) {
        Result<String> wxaCode = customerClient.createWxaCode(appid, AdminPublicConstant.WX_QR_APPLICATION, channelApplicationCode, null);
        if (wxaCode.isSuccess()) {
            return DomainUtil.removeDomain(wxaCode.getData());
        }
        return null;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateEntity(ChannelApplicationEntity channelApplicationEntity) {

        // 检查顾问选择是否符合规则
        checkSelectList(channelApplicationEntity.getAgentSelectType(), channelApplicationEntity.getAgentInfoVoList());

        // 1、保存相应代理人
        applicationAgentRelationService.saveOrUpdateAgentRelation(channelApplicationEntity.getApplicationCode(), channelApplicationEntity.getAgentInfoVoList());

        // 生成二维码
        channelApplicationEntity.setFilePath(createQRCode(channelApplicationEntity.getApplicationCode()));

        // 2、保存渠道应用
        this.update(channelApplicationEntity,
                new QueryWrapper<ChannelApplicationEntity>()
                        .lambda()
                        .eq(ChannelApplicationEntity::getApplicationCode,
                                channelApplicationEntity.getApplicationCode())
        );
        // 3、缓存
        saveOrUpdateCache(channelApplicationEntity);
    }

    /**
     * 保存或更新缓存数据
     *
     * @param channelApplicationEntity 渠道应用实体
     */
    private void saveOrUpdateCache(ChannelApplicationEntity channelApplicationEntity) {
        String channelCode = channelApplicationEntity.getChannelCode();
        ChannelInfoEntity channelInfoEntity = channelInfoService.getOne(
                Wrappers.<ChannelInfoEntity>lambdaQuery()
                        .eq(ChannelInfoEntity::getChannelCode, channelCode)
        );
        ChannelApplicationVo channelApplicationVo = new ChannelApplicationVo();

        // 1、处理渠道
        BeanUtils.copyProperties(channelInfoEntity, channelApplicationVo);
        channelApplicationVo.setChannelEnabled(channelInfoEntity.getEnabled());

        // 2、处理渠道应用
        channelApplicationVo.setApplicationCode(channelApplicationEntity.getApplicationCode());
        channelApplicationVo.setApplicationEnabled(channelApplicationEntity.getEnabled());
        channelApplicationVo.setAgentSelectType(channelApplicationEntity.getAgentSelectType());
        channelApplicationVo.setAgentInfoVoList(channelApplicationEntity.getAgentInfoVoList());

        // 3、写入缓存
        redisService.hset(AdminCommonKeys.CHANNEL_APPLICATION,
                Constant.APPLICATION_INFO,
                channelApplicationEntity.getApplicationCode(),
                channelApplicationVo);
    }

    /**
     * 判断需求类型是否符合规则
     *
     * @param agentSelectType 顾问选择类型
     * @param list            顾问选择列表
     */
    public void checkSelectList(@NotNull Integer agentSelectType, @NotNull List<AgentInfoVo> list) {
        if (ChannelApplicationSelectTypeEnum.getNameByCode(agentSelectType) == null) {
            // 若未指定类型或指定的类型不存在报错
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请指定顾问选择类型"));
        } else if (Objects.equals(agentSelectType, ChannelApplicationSelectTypeEnum.APPOINT.getCode()) && list.size() != 1) {
            // 类型为指定时，若指定的不是一个顾问就报错
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("类型为指定时，只能指定一个顾问"));
        } else if (Objects.equals(agentSelectType, ChannelApplicationSelectTypeEnum.SCOPE.getCode()) && list.size() < 1) {
            // 类型为范围时，列表顾问数应大于0否则报错
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("类型为范围时，至少选择一个顾问"));
        } else if (Objects.equals(agentSelectType, ChannelApplicationSelectTypeEnum.FREE.getCode()) && list.size() != 0) {
            // 类型为自由时，列表顾问数应为0否则报错
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("类型为自由时，不应该选择顾问"));
        }
    }

    @Override
    public ChannelApplicationEntity getChannelApplication(String code) {
        ChannelApplicationEntity entity = this.getOne(
                new QueryWrapper<ChannelApplicationEntity>().lambda()
                        .eq(ChannelApplicationEntity::getApplicationCode, code));
        if (entity != null) {
            // 查询渠道信息
            ChannelInfoEntity channelInfoEntity = channelInfoService.getOne(new LambdaQueryWrapper<ChannelInfoEntity>()
                    .eq(ChannelInfoEntity::getChannelCode, entity.getChannelCode()));
            entity.setChannelName(channelInfoEntity.getChannelName());
            entity.setChannelType(channelInfoEntity.getChannelType());

            // 查询渠道应用相应代理人code
            List<String> agentCodeList = applicationAgentRelationService.list(
                    new QueryWrapper<ApplicationAgentRelationEntity>().lambda()
                            .eq(ApplicationAgentRelationEntity::getApplicationCode, code)
            ).stream().map(ApplicationAgentRelationEntity::getAgentCode).collect(Collectors.toList());

            // 根据代理人code查询代理人code和名字，组合成vo
            if (agentCodeList.size() > 0) {
                List<AgentInfoVo> agentInfoVoList = agentUserInfoService.list(
                        new QueryWrapper<AgentUserInfoEntity>().lambda()
                                .in(AgentUserInfoEntity::getAgentCode, agentCodeList)
                ).stream().map(agentUserInfoEntity -> {
                    AgentInfoVo agentInfoVo = new AgentInfoVo();
                    BeanUtils.copyProperties(agentUserInfoEntity, agentInfoVo);
                    return agentInfoVo;
                }).collect(Collectors.toList());
                entity.setAgentInfoVoList(agentInfoVoList);
            }
        }
        return entity;
    }

    @Override
    public void removeEntity(String code) {
        this.remove(new QueryWrapper<ChannelApplicationEntity>().lambda()
                .eq(ChannelApplicationEntity::getApplicationCode, code)
        );
        applicationAgentRelationService.remove(new QueryWrapper<ApplicationAgentRelationEntity>().lambda()
                .eq(ApplicationAgentRelationEntity::getApplicationCode, code)
        );

        redisService.hdel(AdminCommonKeys.CHANNEL_APPLICATION, Constant.APPLICATION_INFO, code);
    }

    @Override
    public boolean changeEnable(String code, Integer enabled, long revision) {
        if (StatusEnum.getNameByCode(enabled) == null) {
            return false;
        }

        ChannelApplicationEntity channelApplicationEntity = new ChannelApplicationEntity();
        channelApplicationEntity.setEnabled(enabled);
        channelApplicationEntity.setRevision(revision);
        ChannelApplicationEntity channelApplication = this.getChannelApplication(code);
        saveOrUpdateCache(channelApplication);

        return this.update(channelApplicationEntity, new QueryWrapper<ChannelApplicationEntity>().lambda().eq(ChannelApplicationEntity::getApplicationCode, code));
    }

    @Override
    public void refreshCache() {
        // 渠道编码map
        List<ChannelInfoEntity> channelInfoEntityList = channelInfoService.list();

        // 处理渠道
        Map<String, String> channelMap = channelInfoEntityList.stream().map(channelInfoEntity -> {
            ChannelVo channelVo = new ChannelVo();

            BeanUtils.copyProperties(channelInfoEntity, channelVo);
            channelVo.setChannelEnabled(channelInfoEntity.getEnabled());

            return channelVo;
        }).collect(Collectors.toMap(ChannelVo::getChannelCode, JSON::toJSONString));
        redisService.hmset(AdminCommonKeys.CHANNEL, Constant.CHANNEL_INFO, channelMap);

        // 经纪人信息map
        Map<String, AgentInfoVo> agentInfoMap = agentUserInfoService.list().stream()
                .map(agentUserInfoEntity -> {
                    AgentInfoVo agentInfoVo = new AgentInfoVo();
                    BeanUtils.copyProperties(agentUserInfoEntity, agentInfoVo);
                    return agentInfoVo;
                }).collect(Collectors.toMap(AgentInfoVo::getAgentCode, x -> x));
        Map<String, List<AgentInfoVo>> applicationAgentListMap = new HashMap<>(64);

        applicationAgentRelationService.list().forEach(x -> {
            String applicationCode = x.getApplicationCode();
            List<AgentInfoVo> agentInfoVos = applicationAgentListMap.get(applicationCode);
            if (agentInfoVos == null) {
                agentInfoVos = new ArrayList<>();
            }
            agentInfoVos.add(agentInfoMap.get(x.getAgentCode()));
            applicationAgentListMap.put(applicationCode, agentInfoVos);
        });

        Map<String, ChannelInfoEntity> channelCodeListMap = channelInfoEntityList.stream()
                .collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, item -> item));
        // 处理渠道应用
        Map<String, String> map = this.list().stream().map(channelApplicationEntity -> {
            ChannelApplicationVo channelApplicationVo = new ChannelApplicationVo();
            String channel = channelApplicationEntity.getChannelCode();

            ChannelInfoEntity channelInfoEntity = channelCodeListMap.get(channel);
            if (channelInfoEntity != null) {
                channelApplicationVo.setChannelCode(channelInfoEntity.getChannelCode());
                channelApplicationVo.setChannelClassification(channelInfoEntity.getChannelClassification());
                channelApplicationVo.setChannelName(channelInfoEntity.getChannelName());
                channelApplicationVo.setChannelEnabled(channelInfoEntity.getEnabled());
                channelApplicationVo.setChannelType(channelInfoEntity.getEnabled());
            }
            String applicationCode = channelApplicationEntity.getApplicationCode();
            channelApplicationVo.setApplicationCode(applicationCode);
            channelApplicationVo.setApplicationEnabled(channelApplicationEntity.getEnabled());
            channelApplicationVo.setAgentSelectType(channelApplicationEntity.getAgentSelectType());

            channelApplicationVo.setAgentInfoVoList(applicationAgentListMap.get(applicationCode));


            return channelApplicationVo;
        }).collect(Collectors.toMap(ChannelApplicationVo::getApplicationCode, JSON::toJSONString));

        redisService.hmset(AdminCommonKeys.CHANNEL_APPLICATION, Constant.APPLICATION_INFO, map);
    }
}
