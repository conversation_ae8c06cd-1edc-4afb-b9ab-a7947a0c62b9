package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单人工审核信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-14 14:27:24
 */
@TableName("insure_order_person_review")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderPersonReviewEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private Integer id;
	/**
	 * 订单号
	 */
	private String insureOrderCode;
	/**
	 * 审核结果  0：不通过，1：通过
	 */
	private Integer reviewResult;
	/**
	 * 审核备注
	 */
	private String reviewRemark;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
