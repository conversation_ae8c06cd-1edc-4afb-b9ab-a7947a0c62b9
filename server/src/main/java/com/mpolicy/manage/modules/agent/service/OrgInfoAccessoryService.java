package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.OrgInfoAccessoryEntity;

import java.util.List;
import java.util.Map;

/**
 * 组织附件信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-10 11:14:18
 */
public interface OrgInfoAccessoryService extends IService<OrgInfoAccessoryEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveEntity(String orgCode, List<OrgInfoAccessoryEntity> orgInfoAccessoryEntityList);

    void updateEntity(String orgCode, List<OrgInfoAccessoryEntity> orgInfoAccessoryEntityList);
}

