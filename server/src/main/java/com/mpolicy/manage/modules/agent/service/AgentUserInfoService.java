package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentPageListOut;
import com.mpolicy.manage.modules.agent.entity.AgentPageListVo;
import com.mpolicy.manage.modules.agent.entity.AgentUserInfoEntity;
import com.mpolicy.manage.modules.agent.vo.AgentBaseInfoVo;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.manage.modules.agent.vo.AgentUserInfoExportVo;
import com.mpolicy.manage.modules.agent.vo.AgentVo;
import com.mpolicy.manage.modules.agent.vo.agentinfo.*;
import com.mpolicy.manage.modules.policy.entity.channel.FastAgentUserInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 经纪人用户信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentUserInfoService extends IService<AgentUserInfoEntity> {

    void deleteEntity(String agentCode);

    void saveOrUpdateEntity(AgentVo agentReqVo, boolean isTemp);


    /**
     * 获取经纪人的code和name
     *
     * @param orgCode 组织编码
     * @return 经纪人基本信息列表
     */
    List<AgentInfoVo> getBaseInfo(String orgCode);

    /**
     * 根据组织编码和推送编码列表获取代理人信息
     *
     * @param orgCodeList  组织编码列表
     * @param positionList 职级列表
     * @return 经纪人基本信息列表
     */
    List<AgentUserInfoEntity> getList(List<String> orgCodeList, List<String> positionList);

    /**
     * 根据组织编码获取代理人信息
     *
     * @param orgCodeList 组织编码列表
     * @return 经纪人基本信息列表
     */
    default List<AgentUserInfoEntity> getList(List<String> orgCodeList) {
        return this.getList(orgCodeList, null);
    }

    /**
     * 导出
     *
     * @param params 参数
     * @return 导出列表
     */
    List<AgentUserInfoExportVo> export(AgentPageListVo params);


    /**
     * 新增代理人信息
     *
     * @param save
     */
    void saveAgentInfo(com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo save);

    /**
     * 修改代理人信息
     *
     * @param update
     */
    void updateAgentInfo(com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo update);

    /**
     * 获取代理人详情信息
     *
     * @param agentCode
     * @return
     */
    com.mpolicy.manage.modules.agent.vo.agentinfo.AgentInfoVo findAgentInfo(String agentCode);

    /**
     * 设置员工离职
     *
     * @param update
     */
    void updateQuitStatus(UpdateQuitStatusVo update);

    /**
     * 获取代理人及机构信息
     *
     * @return
     */
    List<AgentBaseInfoVo> getAgentBaseInfo(String orgCode, String agentNameOrBusinessCode, boolean isNeedPermission);

    /**
     * @see AgentUserInfoService#getAgentBaseInfo(String, String, boolean)
     */
    default List<AgentBaseInfoVo> getAgentBaseInfo(String orgCode, String agentNameOrBusinessCode) {
        return getAgentBaseInfo(orgCode, agentNameOrBusinessCode, false);
    }

    /**
     * 获取代理人是否绑定客户
     *
     * @param agentCode
     * @return
     */
    AgentBindCustomerInfoOut findAgentBindCustomerInfo(String agentCode);

    /**
     * 获取代理人交接列表
     *
     * @param agentCode
     * @param agentName
     * @return
     */
    List<HandoverPersonListOut> findHandoverPersonList(String agentCode, String agentName);

    /**
     * 分页获取经纪人用户信息表列表
     *
     * @param input
     * @return
     */
    PageUtils<AgentPageListOut> findAgentPageList(AgentPageListVo input);

    /**
     * 变更代理人区域信息
     *
     * @param update
     */
    void updateAreaManagerRegion(UpdateAreaManagerRegionVo update);

    /**
     * 获取区域代理人列表
     *
     * @return
     */
    List<AreaManagerRegionAgentOut> findAreaManagerRegionAgentList();

    /**
     * 获取区域下片区列表
     *
     * @param vo 区域信息和代理人信息
     * @return
     */
    List<SubregionListOut> findSubregionList(SubregionListVo vo);

    /**
     * 获取区域列表
     *
     * @param agentCode 代理人编码
     * @return
     */
    List<SubregionListOut> findRegionList(String agentCode);

    /**
     * 获取当前代理人的区域列表
     *
     * @param agentCode
     * @return
     */
    List<SubregionListOut> findAreaManagerRegionAgentListByAgentCode(String agentCode);

    /**
     * 获取当前代理人的片区列表
     *
     * @param vo
     * @return
     */
    List<SubregionListOut> findSubregionListByAgentCode(SubregionListByAgentCodeVo vo);

    /**
     * 查询代理人信息
     *
     * @param agentCodeList 经纪人工号
     * @return
     */
    List<FastAgentUserInfo> listFastAgentUserInfo(List<String> agentCodeList);
    /**
     * 查询代理人信息
     *
     * @param agentCodeList 经纪人工号
     * @return
     */
    List<FastAgentUserInfo> listByBusinessCode(List<String> agentCodeList);

    /**
     * 查询代理人信息
     *
     * @param agentCode 经纪人工号
     * @return
     */
    FastAgentUserInfo queryOne(String agentCode);


    /**
     * 根据时间查询在职代理人编码
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> findOnlineAgentCodeList(@Param("startTime") String startTime, @Param("endTime") String endTime);
}

