package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.*;
import com.mpolicy.manage.modules.sell.vo.PolicyCommodityVo;
import com.mpolicy.manage.modules.sell.vo.SellProductVO;
import com.mpolicy.product.common.portfolio.PortfolioPlanOut;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISellProductService extends IService<SellProductInfoEntity> {
    /**
     * 获取销售商品列表
     *
     * @param vo
     * @return
     */
    PageUtils<SellProductListOut> findPageList(SellProductListVo vo);

    /**
     * 获取销售商品详情
     *
     * @param id
     * @return
     */
    SellProductInfoOut info(String id);

    /**
     * 修改销售商品信息
     *
     * @param vo
     */
    void update(SellProductUpdateVo vo);

    /**
     * 新增销售商品信息
     *
     * @param vo
     */
    void save(SellProductSaveVo vo);

    /**
     * 删除销售商品信息
     *
     * @param id
     */
    void delete(String id);

    /**
     * 修改商品状态
     *
     * @param vo
     */
    void updateStatus(SellProductStatusVo vo);

    /**
     * 定时修改商品状态
     *
     * @param timeSellProductStatusVo
     */
    void timeUpdateStatus(TimeSellProductStatusVo timeSellProductStatusVo);

    /**
     * 修改商品状态,新增定时上下架
     * 内部实现改为XXL-JOB的定时任务
     * 由于定时下架需要极高的可靠性
     *
     *
     * @param vo 请求参数
     */
    void updateStatusV2(TimeSellProductStatusVo vo);

    /**
     * 根据产品编码获取产品名称
     *
     * @param productCode 产品编码
     * @return 产品名称
     * <AUTHOR> [<EMAIL>]
     */
    String getProductName(String productCode);

    /**
     * 获取产品售卖地区
     *
     * @return
     */
    List<SellAreaOut> findSellAreaList();

    /**
     * 获取农保区域
     *
     * @return
     */
    List<SellAreaOut> findRuralAreaList();

    /**
     * 获取产品关键词是否存在
     *
     * @param sellKeyword
     * @return
     */
    SellKeywordOut findIsSellKeyword(String sellKeyword);

    /**
     * 获取导出销售商品列表
     *
     * @param vo
     * @return
     */
    List<ExportProductListOut> findExportList(SellProductListVo vo);

    /**
     * 获取售卖商品列表
     *
     * @param vo
     * @return
     */
    List<SellSelectListOut> findSellSelectList(SellSelectListVo vo);

    /**
     * 获取产品工具箱列表
     *
     * @param productCode
     * @return
     */
    List<SellToolListOut> findSellToolList(String productCode);

    /**
     * 更新产品工具箱状态
     *
     * @param vo
     */
    void updateSellTool(SellToolVo vo);

    /**
     * 判断是否可以勾选分享计划
     *
     * @param portfolioCode 计划编码
     * @return
     */
    PortfolioPlanOut findIsSharePlan(String portfolioCode);

    /**
     * 投保确认签约
     *
     * <AUTHOR>
     * @date 2023/8/22 14:10
     * @param vo:
     * @return : void
     */
    void policyCommodity(PolicyCommodityVo vo);

    /**
     * 获取投保确认签约
     *
     * <AUTHOR>
     * @date 2023/8/22 14:34
     * @param productCode:
     * @return : com.mpolicy.manage.modules.sell.vo.PolicyCommodityVo
     */
    PolicyCommodityVo getPolicyCommodity(String productCode);

    /**
     * 获取智能问答产品列表
     */
    List<SellProductListOut> findIntelligentSolutionList();

    /**
     *
     * 处理上下架时间在 startDate和 endDate时间范围
     * 的所有定时更变状态的商品
     * 如果商品为上架状态就进行下架
     * 如果商品为下架状态就进行上架
     *
     * @param startDate 处理的日期
     * @param endDate 处理日期前n天的时间范围
     * @return 处理的所有的产品的产品编码集合
     */
    List<String> timeUpdateStatusV2(Date startDate,Date endDate);

    /**
     *
     *
     * 根据销售产品编码获取相对应的VO
     * 改VO其中包含组合编码对应想险种信息的集合
     *
     *
     * @param productCode
     * @return
     */
    SellProductVO getByProductCode(String productCode);
}
