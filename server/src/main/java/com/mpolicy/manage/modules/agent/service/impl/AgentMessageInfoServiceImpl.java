package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.AgentMessageInfoDao;
import com.mpolicy.manage.modules.agent.entity.AgentMessageInfoEntity;
import com.mpolicy.manage.modules.agent.service.AgentMessageInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 经纪人消息列表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/18 14:18
 */
@Service("AgentMessageInfoService")
public class AgentMessageInfoServiceImpl extends ServiceImpl<AgentMessageInfoDao, AgentMessageInfoEntity> implements AgentMessageInfoService {
    @Override
    public Integer insertBatchSomeColumn(Collection<AgentMessageInfoEntity> entityList) {
        return baseMapper.insertBatchSomeColumn(entityList);
    }
}
