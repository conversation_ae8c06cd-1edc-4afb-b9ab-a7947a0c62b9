package com.mpolicy.manage.modules.agent.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.entity.ChannelInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelInfoService;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.entity.ChannelApplicationEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;


/**
 * 渠道应用
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-01-29 19:32:39
 */
@RestController
@RequestMapping("sys/channelapplication")
@Api(tags = "渠道应用")
public class ChannelApplicationController {

    @Autowired
    private ChannelApplicationService channelApplicationService;

    @Autowired
    private ChannelInfoService channelInfoService;


    /**
     * 列表
     */
    @ApiOperation(value = "获取渠道应用列表", notes = "分页获取渠道应用列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "channelName", dataType = "String", value = "检索渠道名称", example = "平安好医生"),
            @ApiImplicitParam(paramType = "query", name = "channelType", dataType = "String", value = "渠道类型 只能为0和1", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "channelCode", dataType = "String", value = "渠道编码", example = "0"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "string(date-time)", value = "创建时间", example = "2021-01-28"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"channel:application:all","sales:channelapplication:all"},logical = Logical.OR)
    public Result<PageUtils> list(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params) {
        PageUtils page = channelApplicationService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation(value = "根据code获取应用")
    @GetMapping("/info/{code}")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result<ChannelApplicationEntity> info(@PathVariable("code") String code) {
        ChannelApplicationEntity channelApplication = channelApplicationService.getChannelApplication(code);
        return Result.success(channelApplication);
    }

    /**
     * 删除
     */
    @ApiOperation(value = "根据code删除应用")
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result delete(@PathVariable("code") String code) {
        channelApplicationService.removeEntity(code);
        return Result.success();
    }

    /**
     * 保存
     */
    @ApiOperation(value = "保存渠道应用")
    @PostMapping("/save")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result save(@RequestBody @Validated(AddGroup.class) ChannelApplicationEntity channelApplicationEntity) {
        channelApplicationService.saveEntity(channelApplicationEntity);

        return Result.success();
    }

    /**
     * 根据code修改
     */
    @ApiOperation(value = "修改渠道应用")
    @PostMapping("/update")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result update(@RequestBody @Validated(UpdateGroup.class) ChannelApplicationEntity channelApplicationEntity) {
        channelApplicationService.updateEntity(channelApplicationEntity);

        return Result.success();
    }

    /**
     * 刷新缓存
     */
    @ApiOperation(value = "刷新缓存", notes = "刷新缓存")
    @GetMapping("/refreshCache")
    public Result refreshCache() {
        channelApplicationService.refreshCache();
        return Result.success();
    }

    /**
     * 修改渠道应用启用状态
     *
     * @param code    渠道应用code
     * @param enabled 状态
     * @return
     */
    @ApiOperation(value = "修改渠道应用启用状态", notes = "修改渠道应用启用状态")
    @PostMapping("/changeEnable/{code}")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result changeEnable(
            @ApiParam(name = "code", value = "渠道应用code", required = true)
            @PathVariable("code") String code,
            @ApiParam(name = "enabled", value = "启用状态 0:禁用 1:启用 ", required = true)
            @RequestParam("enabled") Integer enabled,
            @ApiParam(name = "revision", value = "版本号", required = true)
            @RequestParam("revision") long revision) {
        boolean result = channelApplicationService.changeEnable(code, enabled, revision);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }

    /**
     * 获取渠道应用信息
     */
    @ApiOperation(value = "获取全部渠道应用信息", notes = "获取全部渠道应用信息")
    @GetMapping("/listAll")
    @RequiresPermissions(value = {"channel:application:all"})
    public Result<List<ChannelApplicationEntity>> list() {
        Map<String, String> channelInfoMap = channelInfoService.list().stream().collect(Collectors.toMap(ChannelInfoEntity::getChannelCode, ChannelInfoEntity::getChannelName));
        List<ChannelApplicationEntity> list = channelApplicationService.list().stream().peek(x -> {
            x.setChannelName(channelInfoMap.get(x.getChannelCode()));
        }).collect(Collectors.toList());

        return Result.success(list);
    }
}
