package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/7 14:04
 */
public enum AgentStatusEnum {

    // 无效 保存
    INVALID(0, "无效"),
    // 有效 保存
    VALID(1, "有效"),
    // 无效 暂存
    INVALID_TEMP(2, "无效 暂存"),
    // 有效 暂存
    VALID_TEMP(3, "有效 暂存"),

    ;

    private final Integer code;
    private final String name;

    AgentStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static AgentStatusEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }

    /**
     * 判断是否为暂存状态
     *
     * @param code
     * @return true为暂存
     */
    public static boolean isTemporary(Integer code) {
        return INVALID_TEMP.getCode().equals(code) || VALID_TEMP.getCode().equals(code);
    }

    /**
     * 判断是否为保存状态
     *
     * @param code
     * @return true为保存
     */
    public static boolean isSave(Integer code) {
        return INVALID.getCode().equals(code) || VALID.getCode().equals(code);
    }

    /**
     * 暂存转换为保存
     *
     * @param code
     * @return 返回null为转换失败
     */
    public static Integer conversionToSave(Integer code) {
        if (isTemporary(code)) {
            return code - 2;
        } else if (isSave(code)) {
            return code;
        }
        return null;
    }

    /**
     * 转换为暂存
     *
     * @param code
     * @return 返回null为转换失败
     */
    public static Integer conversionToTemp(Integer code) {
        if (isSave(code)) {
            return code + 2;
        } else if (isTemporary(code)) {
            return code;
        }
        return null;
    }

    /**
     * 无效、有效的暂存转暂存
     *
     * @param code
     * @return 对应值
     */
    public static Integer toTempOut(Integer code) {
        if (code == null || code >= INVALID_TEMP.code) {
            code = INVALID_TEMP.code;
        }
        return code;
    }

}
