package com.mpolicy.manage.modules.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.vo.InsureRecallList;
import com.mpolicy.manage.modules.order.entity.TraceSerialRecallDataEntity;
import com.mpolicy.order.common.order.PolicyTraceSerialRecallData;

import java.util.List;
import java.util.Map;

/**
 * 回溯采集信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-15 15:53:34
 */
public interface TraceSerialRecallDataService extends IService<TraceSerialRecallDataEntity> {

    /**
     * 获取投保回溯数据集合
     *
     * @param insureOrderCode: 投保唯一单号
     * @return {@link java.util.List<com.mpolicy.order.common.order.PolicyTraceSerialRecallData> }
     * <AUTHOR>
     * @since 2022/5/21
     */
    List<InsureRecallList> getPolicyTraceSerialRecallData(String insureOrderCode);
}

