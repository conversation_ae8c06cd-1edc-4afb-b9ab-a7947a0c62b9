package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保操作流水表
 * 
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_flow_history")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureFlowHistoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单单号
	 */
	private String insureOrderCode;
	/**
	 * 原投保订单单号
	 */
	private String sourceInsureOrderCode;
	/**
	 * 操作用户编号
	 */
	private String userNo;
	/**
	 * 操作类型编码
	 */
	private String operateType;
	/**
	 * 操作类型名称
	 */
	private String operateName;
	/**
	 * 操作被保人标记
	 */
	private String insuredTag;
	/**
	 * 操作请求报文
	 */
	private String requestData;
	/**
	 * 操作响应报文
	 */
	private String responseData;
	/**
	 * 操作备注信息
	 */
	private String operateRemark;
	/**
	 * 投保ip
	 */
	private String insureIp;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
