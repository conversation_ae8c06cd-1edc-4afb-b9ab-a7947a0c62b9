package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 经纪人人员证件信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-07 10:41:32
 */
@TableName("agent_certificate")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCertificateEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "经纪人工号", hidden = true)
    private String agentCode;
    /**
     * 证件类型代码
     */
    @ApiModelProperty(value = "证件类型代码", required = true)
    @NotNull
    private String identificationType;
    /**
     * 执业证编码
     */
    @ApiModelProperty(value = "执业证编码", required = true)
    @NotNull
    private String certificateNum;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull
    private String startDate;
    /**
     * 截至日期
     */
    @ApiModelProperty(value = "截至日期", required = true)
    @NotNull
    private String endDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    @ApiModelProperty(value = "是否长期有效")
    private Integer longTerm;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
