package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人签署文件详细信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
@TableName("agent_sign_file_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentSignFileDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    private String agentCode;
    /**
     * 组织编码
     */
    private String orgCode;

    private Integer companyId;
    /**
     * 所属公司对应orgCode
     */
    private String companyOrgCode;
    /**
     * 签署文件编码
     */
    private String fileCode;
    /**
     * 签署文件路径
     */
    private String filePath;
    /**
     * 签署时间
     */
    private Date signTime;
    /**
     * 签署状态(1:主动签署 2:自动确认)
     */
    private Integer signStatus;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
