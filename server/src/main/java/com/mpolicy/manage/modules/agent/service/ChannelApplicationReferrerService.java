package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.vo.ReferrerInfoVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.policy.entity.channel.FastChannelApplicationReferrer;
import com.mpolicy.manage.modules.policy.vo.EpPolicyChannelInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 推荐人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
public interface ChannelApplicationReferrerService extends IService<ChannelApplicationReferrerEntity> {

    /**
     * 分页查询
     *
     * @param applicationCode
     * @param paramMap
     * @return PageUtils
     */
    PageUtils<ChannelApplicationReferrerEntity> queryPage(String applicationCode, Map<String, Object> paramMap);

    /**
     * 统计总数和生效数
     *
     * @param applicationCode
     * @return 总数和生效数
     */
    Map<String, Integer> getTotalAndEffective(String applicationCode);

    /**
     * 修改推荐人启用状态
     *
     * @param id       推荐人id
     * @param enabled  状态
     * @param revision 版本号
     * @return 修改结果
     */
    boolean changeEnable(Integer id, Integer enabled, long revision);

    boolean sendMessage(Integer id);

    /**
     * 获取推荐人基础信息列表
     *
     * @param applicationCode
     * @return 推荐人信息列表
     */
    List<ChannelApplicationReferrerVo> getReferrerList(String applicationCode);

    /**
     * 查看详情，若不存在二维码则生成
     *
     * @param id id
     * @return 推荐人详情
     */
    ChannelApplicationReferrerEntity info(Integer id);

    /**
     * 生成微信二维码
     *
     * @param channelApplicationCode 渠道code
     * @param referrerCode           推荐人code
     * @param referrerWno            推荐人工号
     * @return filePath
     */
    String createQrCode(String channelApplicationCode, String referrerCode, String referrerWno);

    /**
     * 生成微信二维码
     *
     * @param channelApplicationReferrerEntity 推荐人实体
     * @return 操作状态
     */
    boolean saveOrUpdateEntity(ChannelApplicationReferrerEntity channelApplicationReferrerEntity);

    /**
     * 获取渠道推荐人信息信息(核心业务-保单中心)
     *
     * @param referrerCodeOrName
     * @param isNeedPermission
     * @return
     */
    List<EpPolicyChannelInfoVo> getChannelRecommender(String referrerCodeOrName, boolean isNeedPermission);

    /**
     * @see ChannelApplicationReferrerService#getChannelRecommender(String, boolean)
     */
    default List<EpPolicyChannelInfoVo> getChannelRecommender(String referrerCodeOrName) {
        return getChannelRecommender(referrerCodeOrName, false);
    }
    /**
     * 获取推荐人基础信息根据分支编码
     */
    List<ChannelApplicationReferrerVo> getReferrerByBranchCodeList(String branchCode);

    /**
     * 根据编码列表获取推荐人
     * @param referrerCodeList
     * @return
     */
    List<FastChannelApplicationReferrer> listFastEntity(List<String> referrerCodeList);

    List<FastChannelApplicationReferrer> listByJobNumber(List<String> jobNumberList);

    FastChannelApplicationReferrer queryOne(String referrerCode);

    ChannelApplicationReferrerEntity getByReferrerCode(String referrerCode);
}

