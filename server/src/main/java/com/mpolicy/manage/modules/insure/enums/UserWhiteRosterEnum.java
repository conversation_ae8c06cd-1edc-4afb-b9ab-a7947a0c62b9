package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 保全类型
 */
public enum UserWhiteRosterEnum {

    /**
     * 保全类型
     */
    PERSON_IDENTIFY(0, "API投保实名认证"),
    FACE_IDENTIFY(1, "API投保人脸识别"),
    COMPANY_IDENTIFY(2, "API投保企业认证");

    @Getter
    private Integer code;

    @Getter
    private String name;


    UserWhiteRosterEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UserWhiteRosterEnum decode(Integer code) {
        return Arrays.stream(UserWhiteRosterEnum.values())
                .filter(x -> Objects.equals(x.code,code))
                .findFirst().orElse(null);
    }
}
