package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 代理人培训签到表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
@TableName("agent_train_sign")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentTrainSignEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 代理人编码
	 */
	private String agentCode;
	/**
	 * 培训编码
	 */
	private String trainCode;
	/**
	 * 签到状态:0代签到 1:已签到  2:未签到
	 */
	private Integer signStatus;
	/**
	 * 考核结果0:未处理  1:考核通过 2:考核不通过
	 */
	private Integer assessmentResult;
}
