package com.mpolicy.manage.modules.policy.vo.policy;

import com.mpolicy.manage.common.BasePage;
import lombok.Data;

import java.io.Serializable;

@Data
public class EpPolicyActivityFourUpListVo  extends BasePage implements Serializable {
    private static final long serialVersionUID = 1342407407339626870L;

    /**
     * 保单号
     */
    private String policyCode;

    /**
     * 村代姓名
     */
    private String villageRepresentativeName;

    /**
     * 更新开始时间
     */
    private String beginTime;

    /**
     * 更新结束时间
     */
    private String endTime;
}
