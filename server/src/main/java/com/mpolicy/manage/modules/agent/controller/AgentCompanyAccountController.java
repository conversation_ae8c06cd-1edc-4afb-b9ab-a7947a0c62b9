package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.manage.enums.FileModelEnum;
import com.mpolicy.manage.modules.agent.service.AgentCompanyAccountService;
import com.mpolicy.manage.modules.protocol.vo.ImportInsuranceProductVo;
import com.mpolicy.service.common.annotation.SysLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 代理人保司账号
 * <AUTHOR>
 */
@Api(tags = "代理人保司账号")
@RestController
@RequestMapping("agent/company/account")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentCompanyAccountController {


    private final AgentCompanyAccountService agentCompanyAccountService;


    @SysLog("导入保司产品")
    @PostMapping("import")
    @RequiresPermissions(value = {"agent:list:add"})
    @ApiOperation(value = "导入保司产品", notes = "导入保司产品", httpMethod = "POST")
    public Result importCompanyAccount(ImportInsuranceProductVo input) {
        FileModelEnum fileModelEnum = FileModelEnum.decode(input.getFileSystem());
        if (fileModelEnum == null) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("文件操作分类参数错误"));
        }
        MultipartFile file = input.getFile();
        // 验证文件格式
        if (!fileModelEnum.checkFileType(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("上传文件格式不支持"));
        }
        // 上传文件
        agentCompanyAccountService.importCompanyAccount(input);
        return Result.success();
    }
}
