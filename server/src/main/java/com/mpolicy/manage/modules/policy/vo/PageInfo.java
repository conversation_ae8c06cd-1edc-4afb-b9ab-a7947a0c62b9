package com.mpolicy.manage.modules.policy.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页参数
 * <AUTHOR>
 */
@Data
public class PageInfo {

    public static final int MAX_PAGE_SIZE=20000;

    @ApiModelProperty("页码")
    protected int page = 1;

    @ApiModelProperty("偏移量")
    protected int offset;

    @ApiModelProperty("分页大小")
    protected int size;

    @ApiModelProperty("分页大小")
    protected int limit;

    public void init() {
        if (size == 0) {
            size = 10;
        }
        if (page <= 0) {
            page = 1;
        }
        offset = (page - 1) * size;
    }

    public void nextPage() {
        page++;
        offset += size;
    }

    public void nextPage(int maxOffset) {
        page++;
        offset += size;
        int cur = offset+size;
        if( cur > maxOffset ){
            size = maxOffset - offset;
        }
    }

}
