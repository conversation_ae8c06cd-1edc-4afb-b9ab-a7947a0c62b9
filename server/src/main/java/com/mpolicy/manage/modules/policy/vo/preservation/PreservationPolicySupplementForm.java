package com.mpolicy.manage.modules.policy.vo.preservation;

import com.mpolicy.manage.modules.policy.validate.PreservationSupplementValidate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保单保费变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单保费变更保全")
public class PreservationPolicySupplementForm implements Serializable {

    @NotNull(message = "数据录入类型不能为空",groups = {PreservationSupplementValidate.class})
    @ApiModelProperty("录入类型:0=系统默认，1=业务自行录入")
    private Integer inputType;

    @ApiModelProperty("保单补退费金额明细")
    List<PreservationProductInsuredMapVo> insuredProductList;
}
