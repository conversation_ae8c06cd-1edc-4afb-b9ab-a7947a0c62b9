package com.mpolicy.manage.modules.agent.controller;

import java.util.Map;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.modules.agent.vo.resp.AgentStudioIpContentRespVo;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.entity.AgentStudioIpContentEntity;
import com.mpolicy.manage.modules.agent.service.AgentStudioIpContentService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.result.Result;


/**
 * 经纪人工作室IP内容
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 13:47:00
 */
@RestController
@RequestMapping("agent/agentstudioipcontent")
@Api(tags = "经纪人工作室IP内容")
public class AgentStudioIpContentController {

    @Autowired
    private AgentStudioIpContentService agentStudioIpContentService;


    /**
	 * 列表
	 */
    @ApiOperation(value = "获取经纪人工作室IP内容列表", notes = "分页获取经纪人工作室IP内容列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "agentName", dataType = "String", value = "代理人姓名"),
            @ApiImplicitParam(paramType = "query", name = "businessCode", dataType = "String", value = "业务编码"),
            @ApiImplicitParam(paramType = "query", name = "platform", dataType = "String", value = "平台"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"cms:ip:all"})
    public Result<PageUtils<AgentStudioIpContentRespVo>> list(
            @ApiParam(hidden = true)
            @RequestParam Map<String, Object> params){
        PageUtils<AgentStudioIpContentRespVo> page = agentStudioIpContentService.queryPage(params);
        return Result.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation("根据code获取IP内容")
    @GetMapping("/info/{code}")
    @RequiresPermissions(value = {"cms:ip:all"})
    public Result<AgentStudioIpContentEntity> info(@PathVariable("code") String code) {
        AgentStudioIpContentEntity agentStudioIpContent = agentStudioIpContentService.info(code);

        return Result.success(agentStudioIpContent);
    }

    /**
     * 保存或修改经纪人工作室IP内容
     */
    @ApiOperation("保存或修改经纪人工作室IP内容")
    @PostMapping("/saveOrUpdate")
    @RequiresPermissions(value = {"cms:ip:all"})
    public Result<AgentStudioIpContentEntity> saveOrUpdate(@RequestBody @Validated AgentStudioIpContentEntity agentStudioIpContentEntity) {
        boolean result = agentStudioIpContentService.saveOrUpdateEntity(agentStudioIpContentEntity);

        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("操作失败"));
        }
    }


    /**
     * 删除
     */
    @ApiOperation(value = "根据code删除经纪人工作室IP内容")
    @GetMapping("/delete/{code}")
    @RequiresPermissions(value = {"cms:ip:all"})
    public Result<AgentStudioIpContentEntity> delete(@PathVariable("code") String code) {
        boolean delete = agentStudioIpContentService.deleteEntity(code);

        if (delete) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
    }

}
