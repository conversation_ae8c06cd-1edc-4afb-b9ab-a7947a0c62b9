package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileCompanyEntity;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileEntity;
import com.mpolicy.manage.modules.agent.service.AgentSignFileCompanyService;
import com.mpolicy.manage.modules.agent.service.AgentSignFileDetailService;
import com.mpolicy.manage.modules.agent.service.AgentSignFileService;
import com.mpolicy.manage.modules.agent.vo.sign.*;
import com.mpolicy.manage.modules.sys.controller.AbstractController;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @Description 代理人签约文件控制器
 * @return
 * @Date 2023/5/29 18:08
 * <AUTHOR>
 **/
@RestController
@RequestMapping("agent/sign/file")
@Api(tags = "经纪人文件管理信息")
@Slf4j
public class AgentFileSignInfoController extends AbstractController {

    @Autowired
    private AgentSignFileCompanyService agentSignFileCompanyService;
    @Autowired
    private AgentSignFileService agentSignFileService;
    @Autowired
    private AgentSignFileDetailService agentSignFileDetailService;

    /**
     * 文件管理列表
     * @param params 文件管理查询参数
     */
    @ApiOperation(value = "文件管理分页查询", notes = "文件管理分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "fileName", dataType = "String", value = "文件名称"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "String", value = "上传日期"),
            @ApiImplicitParam(paramType = "query", name = "enabled", dataType = "String", value = "启用状态(0:未启用1：启用)"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"agent:signfile:all"})
    public Result<PageUtils<AgentSignFilePageList>> pageList(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("文件管理分页查询，查询条件={}", params);
        PageUtils<AgentSignFilePageList> pageList = agentSignFileService.pageList(params);
        return Result.success(pageList);
    }

    /**
     * 文件上传提交
     * @param input 文件上传请求参数
     */
    @ApiOperation(value = "文件上传提交", notes = "文件上传提交")
    @PostMapping("/submitUpload")
    @RequiresPermissions(value = {"agent:signfile:all"})
    @NoRepeatSubmit(keyName = "token")
    public Result<String> submitUpload(@RequestBody @Valid AgentSignSubmitUploadInput input){
        agentSignFileCompanyService.submitUpload(input);
        return Result.success();
    }

    /**
     * 文件管理详情列表
     * @param params 文件管理详情列表
     */
    @ApiOperation(value = "文件管理-文件详情分页查询", notes = "文件管理-文件详情分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "fileName", dataType = "String", value = "文件名称"),
            @ApiImplicitParam(paramType = "query", name = "orgCode", dataType = "String", value = "公司名称编码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/detailPageList/{fileCode}")
    @RequiresPermissions(value = {"agent:signfile:all"})
    public Result<PageUtils<AgentSignFileDetailPageList>> detailPageList(@PathVariable("fileCode") String fileCode,@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("文件管理-文件详情分页查询，查询条件={}", params);
        PageUtils<AgentSignFileDetailPageList> detailPageList = agentSignFileCompanyService.detailPageList(fileCode, params);
        return Result.success(detailPageList);
    }

    /**
     * 更改文件启用状态
     * @param fileCode 文件编码
     */
    @ApiOperation(value = "文件管理-更改文件启用状态", notes = "文件管理-更改文件启用状态")
    @GetMapping("/changeStatus/{fileCode}")
    @RequiresPermissions(value = {"agent:signfile:all"})
    public Result<Void> changeStatus(@PathVariable("fileCode") String fileCode) {
        AgentSignFileEntity one = agentSignFileService.lambdaQuery().eq(AgentSignFileEntity::getFileCode, fileCode).one();
        if(Objects.isNull(one)){
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("文件信息不存在"));
        }
        if(Objects.equals(one.getEnabled(), StatusEnum.NORMAL.getCode())){
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("文件已启用"));
        }
        Date now = new Date();
        agentSignFileService.lambdaUpdate().eq(AgentSignFileEntity::getFileCode,fileCode)
                .set(AgentSignFileEntity::getEnableTime,now)
                .set(AgentSignFileEntity::getExpireTime, DateUtils.addDateDays(now,3))
                .set(AgentSignFileEntity::getEnabled,StatusEnum.NORMAL.getCode()).update();
        // 发送短信提醒
        List<String> orgCodeList;
        List<AgentSignFileCompanyEntity> list = this.agentSignFileCompanyService.lambdaQuery().eq(AgentSignFileCompanyEntity::getFileCode, fileCode).list();
        orgCodeList = list.stream().map(AgentSignFileCompanyEntity::getOrgCode).distinct().collect(Collectors.toList());
        this.agentSignFileCompanyService.sendFileMsg(orgCodeList);
        this.agentSignFileCompanyService.cleanConfirm();
        return Result.success();
    }

    /**
     * 代理人签署文件列表
     * @param params
     * @return
     */
    @ApiOperation(value = "代理人签署文件列表", notes = "代理人签署文件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "agentName", dataType = "String", value = "代理人姓名"),
            @ApiImplicitParam(paramType = "query", name = "agentType", dataType = "String", value = "人员类型"),
            @ApiImplicitParam(paramType = "query", name = "orgCode", dataType = "String", value = "组织机构编码"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/personFileList")
    @RequiresPermissions(value = {"agent:signfile:all"})
    public Result<PageUtils<AgentPersonFilePageList>> personFileList(@ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("代理人签署文件列表，查询条件={}", params);
        PageUtils<AgentPersonFilePageList> agentPersonFilePageListPageUtils = agentSignFileDetailService.personFileList(params);
        return Result.success(agentPersonFilePageListPageUtils);
    }

    /**
     * 指定代理人签署文件列表
     * @param agentCode 代理人编码
     * @param params 分页参数
     * @return
     */
    @ApiOperation(value = "指定代理人签署文件列表", notes = "指定代理人签署文件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "fileName", dataType = "String", value = "文件名称"),
            @ApiImplicitParam(paramType = "query", name = "signStatus", dataType = "Integer", value = "签署状态 0未签署 1已签署 2自动完成"),
            @ApiImplicitParam(paramType = "query", name = "signTime", dataType = "String", value = "签署日期"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/detailPersonFileList/{agentCode}")
    public Result<PageUtils<AgentPersonDetailPageList >> detailPersonFileList(@PathVariable("agentCode") String agentCode, @ApiParam(hidden = true) @RequestParam Map<String, Object> params) {
        log.info("指定代理人签署文件列表，查询条件={}", params);
        PageUtils<AgentPersonDetailPageList> agentPersonDetailPageListPageUtils = agentSignFileDetailService.detailPersonFileList(agentCode, params);
        return Result.success(agentPersonDetailPageListPageUtils);
    }

}
