package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 客户经理白名单信息
 *
 * <AUTHOR>
 * @date 2023-8-2
 */
@Data
@ApiModel(value = "客户经理白名单信息")
public class InsureCustomerWhiteRosterOut{

    @ApiModelProperty(value = "id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "客户编码", example = "C12345676545671")
    private String customerCode;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "证件号码", example = "123456789098765")
    @NotBlank(message = "证件号码不能为空")
    private String cardNo;

    @ApiModelProperty(value = "手机号码", example = "123455555555")
    private String mobile;

    @ApiModelProperty(value = "农保最后一级分支", example = "地区")
    private String referrerChannelName;

    @ApiModelProperty(value = "有效开始时间", example = "2023-08-01")
    private String startDate;

    @ApiModelProperty(value = "有效结束时间", example = "2023-08-02")
    private String endDate;

    @ApiModelProperty(value = "有效时间", example = "2023-08-02--2023-08-02")
    private String validDate;

    @ApiModelProperty(value = "是否长期有效：0：否，1：是", example = "1")
    private Integer isLongTerm;

    @ApiModelProperty(value = "备注", example = "业务要求")
    private String remark;

    @ApiModelProperty(value = "更新人", example = "张三")
    private String updateUser;

    @ApiModelProperty(value = "更新时间", example = "2023-08-01 22:22:22")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
