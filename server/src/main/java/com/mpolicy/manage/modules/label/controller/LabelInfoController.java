package com.mpolicy.manage.modules.label.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.label.entity.*;
import com.mpolicy.manage.modules.label.service.LabelInfoService;
import com.mpolicy.manage.modules.sell.entity.SellProductLabelMapEntity;
import com.mpolicy.manage.modules.sell.service.SellProductLabelMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;


/**
 * 标签信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
@Api(tags = "标签信息表")
@Validated
@RestController
@RequestMapping("label/info")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LabelInfoController {

    private final LabelInfoService labelInfoService;
    private final SellProductLabelMapService sellProductLabelMapService;


    @ApiOperation(value = "获取标签信息表列表", notes = "分页获取标签信息表列表")
    @GetMapping("list")
    @RequiresPermissions(value = {"label:currency:list","label:characteristic:list"},logical= Logical.OR)
    public Result<PageUtils<LabelInfoListOut>> list(LabelInfoListVo params) {
        PageUtils<LabelInfoListOut> page = labelInfoService.findPageList(params);
        return Result.success(page);
    }

    @ApiOperation(value = "新增标签信息", notes = "新增标签信息")
    @PostMapping("save")
    @RequiresPermissions(value = {"label:currency:save","label:characteristic:save"},logical= Logical.OR)
    public Result<LabelInfoEntity> save(@RequestBody @Valid LabelInfoSaveVo vo) {
        LabelInfoEntity labelInfo = LabelInfoEntity.builder()
                .labelCode(CommonUtils.createCodeLastNumber("LB"))
                .build();
        BeanUtil.copyProperties(vo, labelInfo);
        labelInfoService.save(labelInfo);
        return Result.success(labelInfo);
    }

    @ApiOperation(value = "修改标签信息", notes = "修改标签信息")
    @PostMapping("update")
    @RequiresPermissions(value = {"label:currency:update","label:characteristic:update"},logical= Logical.OR)
    public Result<LabelInfoEntity> update(@RequestBody @Valid LabelInfoUpdateVo vo) {
        LabelInfoEntity labelInfo = new LabelInfoEntity();
        BeanUtil.copyProperties(vo, labelInfo);
        labelInfoService.updateById(labelInfo);
        return Result.success(labelInfo);
    }

    @ApiOperation(value = "删除标签", notes = "删除标签")
    @PostMapping("delete/{labelCode}")
    @RequiresPermissions(value = {"label:currency:delete","label:characteristic:delete"},logical= Logical.OR)
    public Result<LabelInfoEntity> delete(@PathVariable(value = "labelCode", required = true)
                                          @Valid @NotBlank(message = "标签编码不能为空") String labelCode) {
        labelInfoService.removeById(labelCode);
        sellProductLabelMapService.
                remove(new QueryWrapper<SellProductLabelMapEntity>().lambda()
                        .eq(SellProductLabelMapEntity::getLabelCode, labelCode));
        return Result.success();
    }
}
