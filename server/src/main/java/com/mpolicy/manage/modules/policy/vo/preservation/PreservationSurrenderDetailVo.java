package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Deprecated
@ApiModel("保全退保明细")
public class PreservationSurrenderDetailVo {

    @ApiModelProperty("被保人编号")
    @NotBlank(message = "被保人编号不能为空")
    private String insuredCode;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("保单险种编码")
    @NotBlank(message = "保单险种编号不能为空")
    private String policyProductCode;

    @ApiModelProperty("险种编码")
    @NotBlank(message = "险种编码不能为空")
    private String productCode;

    @ApiModelProperty("退保保费")
    @NotNull(message = "退保保费不能为空")
    private BigDecimal surrenderAmount;

    @ApiModelProperty("险种编码")
    private String productName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("保障期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保障时长")
    private Integer insuredPeriod;

    @ApiModelProperty("缴费方式-年交/半年交/季交/月交/趸交/不定期交/短险一次交清")
    private String periodType;

    @ApiModelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ApiModelProperty("缴费时长")
    private Integer paymentPeriod;

    @ApiModelProperty("保额")
    private BigDecimal coverage;
}
