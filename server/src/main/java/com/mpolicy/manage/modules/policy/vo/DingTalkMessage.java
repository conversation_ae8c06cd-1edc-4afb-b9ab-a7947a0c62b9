package com.mpolicy.manage.modules.policy.vo;

import lombok.*;

/**
 * 钉钉消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/14 16:23
 */
@Getter
public class DingTalkMessage {
    public DingTalkMessage(String messageUrl, String title, String text) {
        this.link = new DingTalkLinkMessageDetail(messageUrl, title, text);
    }

    /**
     * 消息类型
     */
    private final String msgtype = "link";
    /**
     * 链接消息详情
     */
    private DingTalkLinkMessageDetail link;

    /**
     * 钉钉链接消息详情
     */
    @Getter
    private static class DingTalkLinkMessageDetail {
        /**
         * 链接url
         */
        private String messageUrl;
        /**
         * 封面图片
         */
        private final String picUrl = "@lALOACZwe2Rk";
        /**
         * 标题
         */
        private String title;
        /**
         * 详情文本
         */
        private String text;

        DingTalkLinkMessageDetail(String messageUrl, String title, String text) {
            this.messageUrl = messageUrl;
            this.title = title;
            this.text = text;
        }
    }
}
