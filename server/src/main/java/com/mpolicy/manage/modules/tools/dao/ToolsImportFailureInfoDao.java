package com.mpolicy.manage.modules.tools.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.mpolicy.manage.modules.policy.vo.PolicyInfoVo;
import com.mpolicy.manage.modules.tools.entity.ToolsImportFailureInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: yangdonglin
 * @create: 2023-05-05 18:44
 * @description: 导入失败信息记录dao
 */
public interface ToolsImportFailureInfoDao extends BaseMapper<ToolsImportFailureInfoEntity> {

    int insertListBatch(@Param("list") List<ToolsImportFailureInfoEntity> list);
}
