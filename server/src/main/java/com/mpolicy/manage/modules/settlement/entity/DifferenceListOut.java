package com.mpolicy.manage.modules.settlement.entity;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class DifferenceListOut implements Serializable {
    private static final long serialVersionUID = -6666146519950314045L;

    @ApiModelProperty(value = "对账月度", example = "2008年3月")
    private String monthStr;

    @ApiModelProperty(value = "保险公司", example = "xx")
    private String companyName;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "结算科目")
    private String premiumType;

    @ApiModelProperty(value = "费率")
    private String poundageRatio;

    @ApiModelProperty(value = "保司金额", example = "100.00")
    private BigDecimal poundageAmount;

    private String poundageAmountFormat;

    @ApiModelProperty(value = "我司金额", example = "100.00")
    private BigDecimal xjPoundageAmount;

    private String xjPoundageAmountFormat;

    @ApiModelProperty(value = "差异类型")
    private String differenceType;

    @ApiModelProperty(value = "差额", example = "0.00")
    private BigDecimal difference;

    private String differenceFormat;

    public String getPoundageAmountFormat() {
        if (poundageAmount != null) {
            poundageAmountFormat = "￥" + poundageAmount.toString();
        } else {
            poundageAmountFormat = "无";
        }
        return poundageAmountFormat;
    }

    public String getXjPoundageAmountFormat() {
        if (xjPoundageAmount != null) {
            xjPoundageAmountFormat = "￥" + xjPoundageAmount.toString();
        } else {
            xjPoundageAmountFormat = "无";
        }
        return xjPoundageAmountFormat;
    }

    public String getDifferenceFormat() {
        if (difference != null) {
            differenceFormat = "￥" + difference.toString();
        }
        return differenceFormat;
    }
}
