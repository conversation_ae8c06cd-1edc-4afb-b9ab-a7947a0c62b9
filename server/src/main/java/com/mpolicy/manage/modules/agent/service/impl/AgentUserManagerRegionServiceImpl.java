package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.agent.dao.AgentUserManagerRegionDao;
import com.mpolicy.manage.modules.agent.entity.AgentUserManagerRegionEntity;
import com.mpolicy.manage.modules.agent.service.AgentUserManagerRegionService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service("agentUserManagerRegionService")
public class AgentUserManagerRegionServiceImpl extends ServiceImpl<AgentUserManagerRegionDao, AgentUserManagerRegionEntity> implements AgentUserManagerRegionService {

}
