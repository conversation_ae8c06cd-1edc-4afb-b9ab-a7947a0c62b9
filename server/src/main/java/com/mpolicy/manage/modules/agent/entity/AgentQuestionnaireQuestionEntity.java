package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人问卷问题表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 19:21:44
 */
@TableName("bl_agent_questionnaire_question")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentQuestionnaireQuestionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * bl_agent_questionnaire表主键id
	 */
	private Integer questionnaireId;
	/**
	 * 题目
	 */
	private String title;
	/**
	 * 题型(1:单选题 2:多选题 3:判断题)
	 */
	private Integer type;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@TableLogic
	private Integer deleted;
}
