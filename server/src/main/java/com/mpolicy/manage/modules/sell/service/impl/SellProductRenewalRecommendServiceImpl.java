package com.mpolicy.manage.modules.sell.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.enums.ProdTypeEnum;
import com.mpolicy.manage.modules.sell.dao.SellProductRenewalRecommendDao;
import com.mpolicy.manage.modules.sell.entity.SellProductRenewalRecommendEntity;
import com.mpolicy.manage.modules.sell.service.SellProductRenewalRecommendService;
import com.mpolicy.manage.modules.sell.vo.RenewalProductRecommendInfo;
import com.mpolicy.manage.modules.sell.vo.RenewalProductRecommendType;
import com.mpolicy.web.common.utils.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("sellProductRenewalRecommendService")
public class SellProductRenewalRecommendServiceImpl extends ServiceImpl<SellProductRenewalRecommendDao, SellProductRenewalRecommendEntity> implements SellProductRenewalRecommendService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SellProductRenewalRecommendEntity> page = this.page(
                new Query<SellProductRenewalRecommendEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    public List<RenewalProductRecommendType> recommendList() {
        List<RenewalProductRecommendType> result = Lists.newArrayList();
        List<SellProductRenewalRecommendEntity> list = lambdaQuery().list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<String, List<SellProductRenewalRecommendEntity>> map = list.stream().collect(Collectors.groupingBy(SellProductRenewalRecommendEntity::getProductType));
        map.forEach((k, v) -> {
            RenewalProductRecommendType recommendType = new RenewalProductRecommendType();
            recommendType.setProductType(k);
            recommendType.setProductTypeName(ProdTypeEnum.getProdTypeEnum(k).getPlanMsg());
            List<RenewalProductRecommendInfo> recommendInfos = Lists.newArrayList();
            v.forEach(x -> {
                RenewalProductRecommendInfo info = new RenewalProductRecommendInfo();
                BeanUtils.copyProperties(x, info);
                recommendInfos.add(info);
            });
            recommendType.setRecommendInfos(recommendInfos);
            result.add(recommendType);
        });
        return result;
    }

    @Override
    public void recommendSave(RenewalProductRecommendInfo info) {
        List<SellProductRenewalRecommendEntity> list = lambdaQuery()
                .eq(SellProductRenewalRecommendEntity::getProductType, info.getProductType())
                .eq(SellProductRenewalRecommendEntity::getProductCode, info.getProductCode())
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该险种已添加"));
        }
        SellProductRenewalRecommendEntity entity = new SellProductRenewalRecommendEntity();
        BeanUtils.copyProperties(info, entity);
        save(entity);
    }

}
