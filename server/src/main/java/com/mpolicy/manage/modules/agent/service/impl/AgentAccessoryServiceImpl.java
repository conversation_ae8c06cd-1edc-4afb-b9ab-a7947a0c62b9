package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.manage.enums.InvalidCacheEnum;
import com.mpolicy.manage.modules.agent.dao.AgentAccessoryDao;
import com.mpolicy.manage.modules.agent.entity.AgentAccessoryEntity;
import com.mpolicy.manage.modules.agent.service.AgentAccessoryService;
import com.mpolicy.manage.modules.agent.vo.resp.AgentAccessoryRespVo;
import com.mpolicy.manage.modules.common.service.IInvalidCacheService;
import com.mpolicy.web.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("agentAccessoryService")
public class AgentAccessoryServiceImpl extends ServiceImpl<AgentAccessoryDao, AgentAccessoryEntity> implements AgentAccessoryService {

    @Value("${mp.download.folder:logs/}")
    String destPath;

    @Autowired
    StorageService storageService;

    @Autowired
    IInvalidCacheService invalidCacheService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentAccessoryEntity> page = this.page(
                new Query<AgentAccessoryEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveOrUpdateAgentAccessoryList(String agentCode, List<AgentAccessoryEntity> agentAccessoryEntityList) {
        this.delete(agentCode);
        if (agentAccessoryEntityList != null && !agentAccessoryEntityList.isEmpty()) {
            List<AgentAccessoryEntity> collect = agentAccessoryEntityList.stream().peek(agentAccessoryEntity -> {
                agentAccessoryEntity.setAgentCode(agentCode);
                agentAccessoryEntity.setFilePath(DomainUtil.removeDomain(agentAccessoryEntity.getFilePath()));
            }).collect(Collectors.toList());

            baseMapper.insertBatchSomeColumn(collect);
        }
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO,agentCode);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean delete(String agentCode) {
        invalidCacheService.invalidCache(InvalidCacheEnum.AGENT_INFO,agentCode);
        return this.remove(
                Wrappers.<AgentAccessoryEntity>lambdaQuery()
                        .eq(AgentAccessoryEntity::getAgentCode, agentCode)
        );
    }

    @Override
    public List<AgentAccessoryRespVo> list(String agentCode) {
        return baseMapper.listGroupByType(agentCode);
    }

    @Override
    public String downloadAsZip(String type, String agentCode) {
        List<AgentAccessoryEntity> list = this.list(Wrappers.<AgentAccessoryEntity>lambdaQuery()
                .eq(AgentAccessoryEntity::getIdentificationType, type)
                .eq(AgentAccessoryEntity::getAgentCode, agentCode));
        if (list != null && list.size() > 0) {
            String fileName = "file.zip";
            List<String> collect = list.stream().map(AgentAccessoryEntity::getFilePath).collect(Collectors.toList());
            storageService.downloadAsZip(collect, destPath, fileName, false);
            return "/sys/download/" + fileName;
        }
        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("没有可下载的文件"));
    }
}
