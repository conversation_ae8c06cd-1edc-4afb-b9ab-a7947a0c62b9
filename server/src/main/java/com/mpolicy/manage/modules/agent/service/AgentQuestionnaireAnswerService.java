package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireAnswerEntity;
import com.mpolicy.manage.modules.agent.entity.AgentQuestionnaireEntity;

import java.util.Map;

/**
 * 代理人问卷答题表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-09 17:34:00
 */
public interface AgentQuestionnaireAnswerService extends IService<AgentQuestionnaireAnswerEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String,Object> paramMap);

    void deleteByQuestionId(Integer questionId);
}

