package com.mpolicy.manage.modules.agent.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.manage.modules.agent.entity.ChannelBranchInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelBranchInfoService;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 分支机构信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-11-02 17:48:12
 */
@RestController
@RequestMapping("agent/channelbranchinfo")
@Api(tags = "分支机构信息")
public class ChannelBranchInfoController {

    @Autowired
    private ChannelBranchInfoService channelBranchInfoService;

    /**
     * 根据id查看详情
     */
    @ApiOperation(value = "根据id查看详情", notes = "根据id查看详情")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"channel:branch:all"})
    public Result<ChannelBranchInfoEntity> info(@PathVariable("id") Integer id) {
        ChannelBranchInfoEntity channelApplicationReferrer = channelBranchInfoService.getById(id);

        return Result.success(channelApplicationReferrer);
    }

    /**
     * 获取渠道下的分支
     */
    @ApiOperation(value = "获取渠道列表", notes = "获取渠道列表")
    @GetMapping("/channelList")
    @RequiresPermissions(value = {"channel:branch:all"})
    public Result<List<ChannelBranchInfoEntity>> channelBranchList() {
        List<ChannelBranchInfoEntity> list = channelBranchInfoService.getChannelList();

        return Result.success(list);
    }

    @ApiOperation(value = "获取渠道树列表", notes = "获取渠道树列表")
    @GetMapping("/findChannelTreeList")
    public Result<List<TreeListOut>> findChannelTreeList() {
        List<TreeListOut> list = channelBranchInfoService.findChannelTreeList();
        return Result.success(list);
    }

    /**
     * 根据销售渠道获取渠道下的分支
     */
    @ApiOperation(value = "根据销售渠道获取渠道下的分支", notes = "根据销售渠道获取渠道下的分支")
    @GetMapping("/getChannelList")
    public Result<List<ChannelBranchInfoEntity>> getChannelList(@RequestParam("channelCode") String channelCode) {
        List<ChannelBranchInfoEntity> list = channelBranchInfoService.getChannelBranchList(channelCode);
        return Result.success(list);
    }

    /**
     * 获取分支机构基础信息列表
     */
    @ApiOperation(value = "获取分支机构下层分支", notes = "获取分支机构下层分支")
    @GetMapping("/branchList")
    public Result<List<ChannelBranchInfoEntity>> branchList(
            @ApiParam(value = "父级组织编码", required = true) @NotBlank
            @RequestParam("parentCode") String parentCode,
            @ApiParam(value = "分支层级", required = true) @NotNull
            @RequestParam("branchLevel") Integer branchLevel) {
        List<ChannelBranchInfoEntity> list = channelBranchInfoService.getBranchList(parentCode, branchLevel);

        return Result.success(list);
    }

    /**
     * 新增或修改
     */
    @NoRepeatSubmit(keyName = "token")
    @ApiOperation(value = "新增或修改分支机构信息", notes = "新增或修改分支机构信息")
    @PostMapping("/saveOrUpdate")
    public Result saveOrUpdate(
            @ApiParam(value = "分支机构对象", required = true)
            @Validated @RequestBody ChannelBranchInfoEntity channelBranchInfoEntity) {
        Integer id = channelBranchInfoEntity.getId();
        if (id == null) {
            channelBranchInfoEntity.setBranchCode(CommonUtils.createCode("B"));
            if (channelBranchInfoEntity.getBranchLevel() == 1) {
                channelBranchInfoEntity.setChannelCode(channelBranchInfoEntity.getBranchParentCode());
                channelBranchInfoEntity.setBranchParentCode(null);
            }
        }
        boolean result = channelBranchInfoService.saveOrUpdate(channelBranchInfoEntity);

        if (result) {
            channelBranchInfoService.update(
                    Wrappers.<ChannelBranchInfoEntity>lambdaUpdate()
                            .set(ChannelBranchInfoEntity::getIsNotEmpty, StatusEnum.NORMAL.getCode())
                            .eq(ChannelBranchInfoEntity::getBranchCode, channelBranchInfoEntity.getBranchParentCode())
            );
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("操作失败"));
        }
    }

    /**
     * 修改分支机构启用状态
     *
     * @param id       分支机构id
     * @param enabled  状态
     * @param revision 版本号
     * @return 修改结果
     */
    @ApiOperation(value = "修改分支机构启用状态", notes = "修改分支机构启用状态")
    @PostMapping("/changeEnable")
    public Result changeEnable(
            @ApiParam(name = "id", value = "分支机构id", required = true)
            @RequestParam("id") Integer id,
            @ApiParam(name = "enabled", value = "启用状态 0:禁用 1:启用 ", required = true)
            @RequestParam("enabled") Integer enabled,
            @ApiParam(name = "revision", value = "版本号", required = true)
            @RequestParam("revision") long revision) {
        boolean result = channelBranchInfoService.changeEnable(id, enabled, revision);
        if (result) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }
}
