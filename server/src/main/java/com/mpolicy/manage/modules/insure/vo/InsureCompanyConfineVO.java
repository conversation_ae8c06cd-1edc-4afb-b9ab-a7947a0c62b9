package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 保司升级信息
 *
 * <AUTHOR>
 * @date 2022-10-09 13:42
 */
@Data
@ApiModel(value = "保司升级信息")
public class InsureCompanyConfineVO {

    @ApiModelProperty(value = "升级控制编码", example = "IFC1234567890")
    private String confineCode;

    @ApiModelProperty(value = "标题", required = true, example = "标题")
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "保司编码", required = true, example = "DCRS000000")
    @NotBlank(message = "保司不能为空")
    private String companyCodes;

    @ApiModelProperty(value = "产品编码", required = true, example = "P20210331113718425004")
    @NotBlank(message = "产品不能为空")
    private String productCodes;

    @ApiModelProperty(value = "平台 1:小程序", required = true, example = "1")
    @NotNull(message = "平台不能为空")
    private Integer clientType;

    @ApiModelProperty(value = "是否启用 0:停用;1:启用", required = true, example = "1")
    @NotNull(message = "启用状态不能为空")
    private Integer isStatus;

    @ApiModelProperty(value = "升级开始时间", required = true, example = "2022-10-13 12:38:23")
    @NotBlank(message = "升级开始时间不能为空")
    private String confineStartTime;

    @ApiModelProperty(value = "升级结束时间", required = true, example = "2022-10-13 12:38:23")
    @NotBlank(message = "升级结束时间不能为空")
    private String confineEndTime;

    @ApiModelProperty(value = "升级说明", required = true, example = "1")
    private String confineDesc;
}
