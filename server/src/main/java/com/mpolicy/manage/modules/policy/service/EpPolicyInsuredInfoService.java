package com.mpolicy.manage.modules.policy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.policy.entity.EpPolicyInsuredInfoEntity;
import com.mpolicy.manage.modules.policy.entity.EpPolicyProductInsuredMapEntity;
import com.mpolicy.manage.modules.policy.vo.EpPolicyInsuredInfoVo;
import com.mpolicy.manage.modules.policy.vo.query.PolicyInsuredQuery;

import java.util.List;
import java.util.Map;

/**
 * 保险团单被保人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-03-11 17:24:10
 */
public interface EpPolicyInsuredInfoService extends IService<EpPolicyInsuredInfoEntity> {

    /**
     * <p>
     * 根据团单合同号获取分单列表数据
     * </p>
     *
     * @param contractCode 团单合同号
     * @param paramMap     查询条件
     * @return
     */
    PageUtils<EpPolicyInsuredInfoVo> queryPage(String contractCode, Map<String, Object> paramMap);

    /**
     * <p>
     * 查询团单分单计划列表
     * </p>
     *
     * @param contractCode 团单合同号
     * @param paramMap     查询条件
     * @return
     */
    List<EpPolicyProductInsuredMapEntity> groupPlanList(String contractCode, Map<String, Object> paramMap);

    List<EpPolicyInsuredInfoVo> policyInsuredList(PolicyInsuredQuery query);

    List<EpPolicyInsuredInfoVo> policyInsuredListWithDiffChannelCode(String contractCode, String channelCode);

    /**
     *
     * 判断一个证件号码是否在数据库内有一个承保的单子
     *
     * @param idNo
     *
     * 证件号码
     *
     * @return
     */
    Boolean isAcceptByIdNo(String idNo);

    /**
     * 取当前保单可操作的被保人险种清单
     * @param contractCode 保单合同号
     * @return 保单被保人险种清单
     */
    List<EpPolicyInsuredInfoVo> policyInsuredProductList(String contractCode);
}

