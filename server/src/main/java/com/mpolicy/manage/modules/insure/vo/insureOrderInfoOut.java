package com.mpolicy.manage.modules.insure.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出信息
 *
 * <AUTHOR>
 * @since 2023-07-05 16:02
 */
@Data
@ApiModel(value = "订单导出信息", description = "订单导出信息")
public class insureOrderInfoOut extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1;

    @ExcelProperty(value = "订单号")
    private String insureOrderCode;

    private Integer insureOrderStatus;

    @ExcelProperty(value = "订单状态")
    private String insureOrderStatusDesc;

    @ExcelProperty(value = "订单金额")
    private String premium;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "支付时间")
    private Date insurePayTime;

    @ExcelProperty(value = "商品名称")
    private String commodityName;

    @ExcelProperty(value = "投保人姓名")
    private String holderName;

    @ExcelProperty(value = "投保人证件类型")
    private String holderCertiCode;

    @ExcelProperty(value = "投保人证件号码")
    private String holderCertiNo;

    @ExcelProperty(value = "被保人与投保人关系")
    private String insuredOwnerRela;

    @ExcelProperty(value = "被保人姓名")
    private String insuredName;

    @ExcelProperty(value = "被保人证件类型")
    private String insuredCertiCode;

    @ExcelProperty(value = "被保人证件号码")
    private String insuredCertiNo;

    @ExcelProperty(value = "被保人手机号码")
    private String insuredMobile;

    @ExcelProperty(value = "C端客户名")
    private String userName;

    @ExcelProperty(value = "C端客户编码")
    private String userNo;

    @ExcelProperty(value = "代理人编码（业务编码）")
    private String businessCode;

    @ExcelProperty(value = "代理人")
    private String agentName;

    @ExcelProperty(value = "渠道推荐人编码（工号）")
    private String referrerWno;

    @ExcelProperty(value = "渠道推荐人")
    private String referrerName;

    @ExcelProperty(value = "渠道")
    private String channelCode;
}
