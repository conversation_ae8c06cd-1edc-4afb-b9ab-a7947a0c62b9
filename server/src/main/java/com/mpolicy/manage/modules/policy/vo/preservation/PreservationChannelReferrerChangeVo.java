package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("保全-渠道推荐人变更详情")
public class PreservationChannelReferrerChangeVo {

    @ApiModelProperty(value = "是否变更所有:变更所有时[channelReferrerChangeItemList]字段必须为空",required = true)
    private Boolean changeAll;

    @ApiModelProperty(value = "变更后-保单层-推荐人编码")
    private String policyChannelReferrerCode;

    @ApiModelProperty(value = "变更后-保单层-推荐人名字(回显)")
    private String policyChannelReferrerName;

    @ApiModelProperty(value = "变更后-保单层-推荐人分支编码")
    private String policyChannelBranchCode;

    @ApiModelProperty(value = "变更后-保单层-推荐人分支编码(回显)")
    private String policyChannelBranchName;

    @ApiModelProperty(value = "变更后-销售渠道",required = true)
    private String policyChannelCode;

    @ApiModelProperty(value = "变更后-销售渠道(回显)")
    private String policyChannelName;

    @ApiModelProperty(value = "变更后-分单层渠道推荐人编码",required = true)
    private String channelReferrerCode;

    @ApiModelProperty("变更后-渠道推荐人工号(回险展示)")
    private String channelReferrerWno;

    @ApiModelProperty("变更后-渠道推荐人名字(回险展示)")
    private String channelReferrerName;

    @ApiModelProperty(value = "变更后-分单层渠道分支编码",required = true)
    private String channelBranchCode;

    @ApiModelProperty("变更后-分支名称(回险展示)")
    private String branchName;

    @ApiModelProperty(value = "变更后-分单层销售渠道",required = true)
    private String channelCode;

    @ApiModelProperty("变更后-销售渠道名称(回险展示)")
    private String channelName;

    @ApiModelProperty("变更前-保单渠道推荐人(回险展示)")
    private String beforeChannelReferrerCode;

    @ApiModelProperty("变更前-保单销售渠道(回险展示)")
    private String beforeChannelCode;

    @ApiModelProperty("变更前-保单渠道推荐人分支编码(回险展示)")
    private String beforeBranchCode;

    @ApiModelProperty("变更前-保单初始渠道推荐人编码(回险展示)")
    private String beforeCustomerManagerCode;

    @ApiModelProperty("变更前-保单初始渠道推荐人编码(回险展示)")
    private String beforeCustomerManagerName;

    @ApiModelProperty("变更前-保单初始渠道推荐人工号(回险展示)")
    private String beforeCustomerManagerChannelCode;

    @ApiModelProperty("变更前-保单初始渠道推荐人机构编码(回险展示)")
    private String beforeCustomerManagerOrgCode;

    @ApiModelProperty("变更前-保单初始渠道推荐人机构名字(回险展示)")
    private String beforeCustomerManagerOrgName;

    @ApiModelProperty("变更前-保单初始渠道推荐人机构编码(回险展示)")
    private String beforeCustomerManagerChannelOrgCode;

    @ApiModelProperty("变更前-保单初始渠道推荐人机构编码(回险展示)")
    private String beforeCustomerManagerSupervisor;

    @ApiModelProperty("被保人变更照列表(查看保全详情时，请单独请求接口分页查询)")
    List<ChannelReferrerChangeItem> channelReferrerChangeItemList;

    public void preHandle(){
        if(Objects.equals(channelReferrerCode,"无")){
            this.setChannelReferrerName("");
            this.setChannelReferrerWno("");
            this.setChannelReferrerCode("");
        }
        if(Objects.equals(policyChannelReferrerCode,"无")){
            this.setPolicyChannelReferrerCode("");
            this.setPolicyChannelReferrerName("");
            this.setPolicyChannelBranchCode("");
            this.setPolicyChannelBranchCode("");
        }
    }
    public void postHandle(){
        policyChannelReferrerCode = StringUtils.isBlank(policyChannelReferrerCode)?"无":policyChannelReferrerCode;
        policyChannelReferrerName = StringUtils.isBlank(policyChannelReferrerName)?"无":policyChannelReferrerName;
        policyChannelBranchName = StringUtils.isBlank(policyChannelBranchName)?"无":policyChannelBranchName;
        channelReferrerWno = StringUtils.isBlank(channelReferrerWno)?"无":channelReferrerWno;
        channelReferrerName = StringUtils.isBlank(channelReferrerName)?"无":channelReferrerName;
        channelBranchCode = StringUtils.isBlank(channelBranchCode)?"无":channelBranchCode;
        branchName = StringUtils.isBlank(branchName)?"无":branchName;
        channelCode = StringUtils.isBlank(channelCode)?"无":channelCode;
        channelName = StringUtils.isBlank(channelName)?"无":channelName;

        beforeChannelReferrerCode = StringUtils.isBlank(beforeChannelReferrerCode)?"无":beforeChannelReferrerCode;
        beforeChannelCode = StringUtils.isBlank(beforeChannelCode)?"无":beforeChannelCode;
        beforeBranchCode = StringUtils.isBlank(beforeBranchCode)?"无":beforeBranchCode;
        beforeCustomerManagerCode = StringUtils.isBlank(beforeCustomerManagerCode)?"无":beforeCustomerManagerCode;
        beforeCustomerManagerName = StringUtils.isBlank(beforeCustomerManagerName)?"无":beforeCustomerManagerName;
        beforeCustomerManagerChannelCode = StringUtils.isBlank(beforeCustomerManagerChannelCode)?"无":beforeCustomerManagerChannelCode;
        beforeCustomerManagerOrgCode = StringUtils.isBlank(beforeCustomerManagerOrgCode)?"无":beforeCustomerManagerOrgCode;
        beforeCustomerManagerOrgName = StringUtils.isBlank(beforeCustomerManagerOrgName)?"无":beforeCustomerManagerOrgName;
        beforeCustomerManagerChannelOrgCode = StringUtils.isBlank(beforeCustomerManagerChannelOrgCode)?"无":beforeCustomerManagerChannelOrgCode;
    }
}
