package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureInsurancePlatformHistoryDao;
import com.mpolicy.manage.modules.insure.entity.InsureInsurancePlatformHistoryEntity;
import com.mpolicy.manage.modules.insure.service.InsureInsurancePlatformHistoryService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投保于中台交互流水表接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Service("insureInsurancePlatformHistoryService")
public class InsureInsurancePlatformHistoryServiceImpl extends ServiceImpl<InsureInsurancePlatformHistoryDao, InsureInsurancePlatformHistoryEntity> implements InsureInsurancePlatformHistoryService {


    @Override
    @Async("asyncTaskExecutor")
    public void asyncSaveInsureInsurancePlatform(InsureInsurancePlatformHistoryEntity history) {
        save(history);
    }
}
