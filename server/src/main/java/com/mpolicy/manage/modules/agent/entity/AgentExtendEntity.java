package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;

import com.mpolicy.manage.modules.sys.entity.SysRegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-07 10:41:32
 */
@TableName("agent_extend")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentExtendEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 经纪人工号
     */
    @ApiModelProperty(value = "经纪人工号", hidden = true)
    private String agentCode;
    /**
     * 执业证编码
     */
    @ApiModelProperty(value = "执业证编码", required = true)
    @NotBlank(message = "执业证编码不能为空")
    private String certificateNum;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空")
    private String startDate;
    /**
     * 是否长期有效 1:长期;0:非长期
     */
    @ApiModelProperty(value = "是否长期有效 1:长期;0:非长期", required = true)
    @NotNull(message = "是否长期有效 1:长期;0:非长期 不能为空")
    private Integer longTerm;
    /**
     * 截至日期
     */
    @ApiModelProperty(value = "截至日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String endDate;
    /**
     * 银行所在地区
     */
    @ApiModelProperty(value = "银行所在地区，仅作返回")
    @TableField(exist = false)
    private SysRegionInfo bandLocationRegion;
    /**
     * 银行所在地区代码
     */
    @ApiModelProperty(value = "银行所在地区代码", required = true)
    @NotBlank(message = "银行所在地区代码不能为空")
    private String bandLocationCode;
    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行", required = true)
    @NotBlank(message = "开户银行不能为空")
    private String bandName;
    /**
     * 开户支行名称
     */
    @ApiModelProperty(value = "开户支行名称", required = true)
    @NotBlank(message = "开户支行名称不能为空")
    private String bandBranchName;
    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号", required = true)
    @NotBlank(message = "银行卡号不能为空")
    private String bandCardNum;

    @ApiModelProperty(value = "联行号")
    @NotBlank(message = "联行号不能为空")
    private String bankNumber;
    /**
     * 个人介绍
     */
    @ApiModelProperty(value = "个人介绍")
    private String profile;

    @ApiModelProperty(value = "荣誉信息")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String honor;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
