package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保单保费变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单保费变更-被保人险种保费变更明细")
public class PreservationInsuredProductPremiumChangeForm implements Serializable {

    @ApiModelProperty("险种编码")
    private String productCode;

    @ApiModelProperty("险种名称")
    private String productName;

    @ApiModelProperty("保单险种编码")
    private String policyProductCode;

    @ApiModelProperty("变更前保费")
    @NotNull(message = "变更前被保人险种保费不能为空")
    private BigDecimal beforePremium;

    @ApiModelProperty("变更后保费")
    @NotNull(message = "变更后被保人险种保费不能为空")
    private BigDecimal correctedPremium;
}
