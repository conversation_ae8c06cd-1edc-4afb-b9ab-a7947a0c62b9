package com.mpolicy.manage.modules.policy.vo.preservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 敏感类保全
 * 修正历史保全信息
 * @Date 2023/6/19 16:57
 * @Version 1.0
 */
@Data
@ApiModel("保全基础信息变更")
public class PreservationInfoChangeForm {

    @ApiModelProperty("原保全编号")
    @NotBlank(message = "原保全编号不能为空")
    private String originalPreservationCode;

    @ApiModelProperty("原批改单号")
    private String originalEndorsementNo;

    @ApiModelProperty("修正后批改单号")
    @NotBlank(message = "修改后保单号不能为空")
    private String correctedEndorsementNo;

}
