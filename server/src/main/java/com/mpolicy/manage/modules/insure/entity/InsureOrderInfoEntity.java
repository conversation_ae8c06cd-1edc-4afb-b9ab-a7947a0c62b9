package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 投保订单信息表
 *
 * <AUTHOR>
 * @date 2022-04-27 14:08:53
 */
@TableName("insure_order_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureOrderInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 投保订单单号
	 */
	private String insureOrderCode;
	/**
	 * 原投保订单单号
	 */
	private String sourceInsureOrderCode;

	/**
	 * 投保类型 个险、家庭单、团单
	 */
	private String insureOrderType;
	/**
	 * 组合类型，个险，财险
	 */
	private String portfolioGroup;
	/**
	 * 投保模式 首投、续期
	 */
	private String insureMode;
	/**
	 * 所属用户编码
	 */
	private String userNo;
	/**
	 * 所属用户名称
	 */
	private String userName;

	/**
	 * openid
	 */
	private String openId;

	/**
	 * 保单中心唯一编号
	 */
	private String contractCode;
	/**
	 * 业务平台预投保号
	 */
	private String insAdvancePolicyCode;

	/**
	 * 承保时间
	 */
	private Date underwriteTime;

	/**
	 * 保险业务平台核保结果信息
	 */
	private String insUnderwriteMsg;

	/**
	 * 家庭保单号
	 */
	private String familyPolicyCode;
	/**
	 * 投保单号
	 */
	private String applicantPolicyNo;
	/**
	 * 承保保单号
	 */
	private String policyCode;

	/**
	 * 保司电子保单地址
	 */
	private String companyPolicyUrl;

	/**
	 * 电子保单地址 电子保单地址
	 */
	private String policyUrl;

	/**
	 * 投保支付模式字典编码
	 */
	private String insurePayType;
	/**
	 * 投保支付号
	 */
	private String insurePayNo;
	/**
	 * 投保支付时间
	 */
	private Date insurePayTime;
	/**
	 * 保司编码
	 */
	private String companyCode;
	/**
	 * 保司名称
	 */
	private String companyName;

	/**
	 * 保司logo
	 */
	private String companyLogo;

	/**
	 * 保单类型
	 */
	private String policyType;
	/**
	 * 组合编码
	 */
	private String portfolioCode;
	/**
	 * 组合名称
	 */
	private String portfolioName;

	/**
	 * 商品销售模式
	 */
	private String commoditySellMode;

	/**
	 * 商品编码
	 */
	private String commodityCode;
	/**
	 * 商品名称
	 */
	private String commodityName;

	/**
	 * 主险投保计划
	 */
	private String planCode;
	/**
	 * 主险保险期间
	 */
	private String coverageYear;
	/**
	 * 主险交费期间
	 */
	private String payPeriod;
	/**
	 * 主险保险金额
	 */
	private String amount;
	/**
	 * 主险交费方式
	 */
	private String payMode;
	/**
	 * 保费试算支付总保费
	 */
	private BigDecimal premium;
	/**
	 * 实际支付保费
	 */
	private BigDecimal payPremium;
	/**
	 * 份数
	 */
	private Integer numberOfIns;

	/**
	 * 指定起保日期
	 */
	private String specifyEffectiveDate;

	/**
	 * 预计保障终止日期
	 */
	private String  estimateInvalidDate;

	/**
	 * 生效时间
	 */
	private Date effectiveDate;

	/**
	 * 失效时间
	 */
	private String  invalidDate;
	/**
	 * 是否开通续期 开通/不开通
	 */
	private String autoPayment;

	/**
	 * 是否自动续保 开通/不开通
	 */
	private String autoRenewFlag;

	/**
	 * 投保人姓名
	 */
	private String holderName;
	/**
	 * 投保人证据号码
	 */
	private String holderIdNo;
	/**
	 * 被保人姓名集合
	 */
	private String insuredNames;
	/**
	 * 被保人证据号码集合
	 */
	private String insuredIdNos;
	/**
	 * 管理机构编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String orgCode;
	/**
	 * 管理机构名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String orgName;
	/**
	 * 投保销售渠道编码
	 */
	private String channelCode;
	/**
	 * 投保销售渠道名称
	 */
	private String channelName;
	/**
	 * 用户渠道编码
	 */
	private String userChannelCode;
	/**
	 * 用户渠道名称
	 */
	private String userChannelName;
	/**
	 * 代理人类型 专属、分享
	 */
	private String insureAgentType;
	/**
	 * 代理人编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String agentCode;
	/**
	 * 代理人名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String agentName;
	/**
	 * 推荐人编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String referrerCode;
	/**
	 * 推荐人名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String referrerName;
	/**
	 * 推荐人证件号码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String referrerIdNo;
	/**
	 * 四级分销-客户经理推荐人来源 1:客情，2：保险
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer referrerSource;
	/**
	 * 农保工号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String manageWno;
	/**
	 * 子渠道编码-乡助、企业微信
	 */
	private String childChannelCode;
	/**
	 * 客户来源
	 */
	private String customerSource;
	/**
	 * 渠道权限
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String branchCode;
	/**
	 * 渠道机构权限标识
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String channelBranchCode;
	/**
	 * 操作终端
	 */
	private String terminalType;
	/**
	 * 自定义参数
	 */
	private String customParam;

	/**
	 * 小鲸站长编号
	 */
	private String propagandistCode;

	/**
	 * 小鲸站长管控客户经理
	 */
	private String propagandistReferrerCode;

	/**
	 * 投保ip
	 */
	private String insureIp;

	/**
	 * 投保/核保时间
	 */
	private Date insureTime;

	/**
	 * 投保耗时
	 */
	private Integer insureTimeSecond;

	/**
	 * 投保激活状态 0未激活1已激活
	 */
	private Integer insureActivateStatus;
	/**
	 * 投保订单状态
	 */
	private Integer insureOrderStatus;
	/**
	 * 是否生成sts回溯文件
	 */
	private Integer isSts;
	/**
	 * sts文件地址
	 */
	private String stsFilePath;
	/**
	 * 是否赠险0:不是1:是
	 */
	private Integer isGive;
	/**
	 * 预订单号
	 */
	private String prepareOrderCode;
	/**
	 * 购买渠道
	 */
	private String buyChannel;
	/**
	 * 整村推进活动编码
	 */
	private String activityCode;
	/**
	 * 是否归属活动
	 */
	private String productActivityCode;
	/**
	 * 原投保保司计划编码
	 */
	private String sourcePolicyPlanCode;
	/**
	 * 撤单金额
	 */
	private BigDecimal refundAmount;
	/**
	 * 撤单操作人
	 */
	private String refundUser;
	/**
	 * 撤单发起时间
	 */
	private Date refundTime;
	/**
	 * 撤单完成时间
	 */
	private Date refundEndTime;
	/**
	 * 撤单原因
	 */
	private String refundReason;
	/**
	 * 退款失败原因
	 */
	private String refundFailReason;
	/**
	 * 村代名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String ruralProxyName;
	/**
	 * 村代证件号码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String ruralProxyIdNo;
	/**
	 * 四级分销-订单类型 1：四级分销，0：非四级分销
	 */
	private Integer ruralProxyOrderType;
	/**
	 * 唯一oneId
	 */
	private String oneId;
	/**
	 * 是否百川 0：否，1：是'
	 */
	private Integer isBaichuan;
	/**
	 * 百川渠道编码
	 */
	private String bcChannelCode;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private long revision;
}
