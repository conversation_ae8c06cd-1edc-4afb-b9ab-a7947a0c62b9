package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("bi_agent_info")
public class BiAgentInfoEntity implements Serializable {
    private static final long serialVersionUID = 4660680776184576383L;
    /**
     * 代理人编码
     */
    @TableId(type = IdType.INPUT)
    private String agentCode;
    /**
     * 代理人名称
     */
    private String agentName;
    /**
     *人员性质 字典
     */
    private String agentNature;
    /**
     * 人员性质描述
     */
    private String agentNatureDesc;
    /**
     *人员类型
     */
    private String agentType;
    /**
     * 人员类型描述
     */
    private String agentTypeDesc;
    /**
     * 入职日期
     */
    private String entryDate;
    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 展业地区
     */
    private String acquisitionArea;
    /**
     * 展业地区
     */
    private String acquisitionAreaDesc;
    /**
     * 服务省份
     */
    private String provinceCode;
    /**
     * 服务省份
     */
    private String provinceName;
    /**
     * 服务城市
     */
    private String cityCode;
    /**
     * 服务城市
     */
    private String cityName;
    /**
     * 代理人编码
     */
    private String orgCode;

    /**
     * 顶级机构编码
     */
    private String firstOrgCode;

    /**
     * 顶级机构名称
     */
    private String firstOrgName;

}
