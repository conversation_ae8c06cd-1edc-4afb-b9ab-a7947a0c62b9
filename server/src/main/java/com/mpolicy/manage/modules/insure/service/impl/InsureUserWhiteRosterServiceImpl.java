package com.mpolicy.manage.modules.insure.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.insure.dao.InsureUesrWhiteRosterDao;
import com.mpolicy.manage.modules.insure.entity.InsureUserWhiteRosterEntity;
import com.mpolicy.manage.modules.insure.enums.UserWhiteRosterEnum;
import com.mpolicy.manage.modules.insure.service.InsureUserWhiteRosterService;
import com.mpolicy.manage.modules.insure.vo.InsureUserWhiteRosterOut;
import com.mpolicy.manage.modules.insure.vo.InsureUserWhiteRosterVO;
import com.mpolicy.manage.modules.insure.vo.WhiteRosterFaceVo;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;


@Service("insureUesrWhiteRosterService")
@Slf4j
public class InsureUserWhiteRosterServiceImpl extends ServiceImpl<InsureUesrWhiteRosterDao, InsureUserWhiteRosterEntity> implements InsureUserWhiteRosterService {


    @Resource
    private SysDocumentService sysDocumentService;

    @Autowired
    private StorageService storageService;


    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String realName = RequestUtils.objectValueToString(params, "realName");
        String cardNo = RequestUtils.objectValueToString(params, "cardNo");
        String isStatus = RequestUtils.objectValueToString(params, "isStatus");
        String startDate = RequestUtils.objectValueToString(params, "startDate");
        String endDate = RequestUtils.objectValueToString(params, "endDate");
        String rosterType = RequestUtils.objectValueToString(params, "rosterType");
        IPage<InsureUserWhiteRosterEntity> page = this.page(
                new Query<InsureUserWhiteRosterEntity>().getPage(params),
                new LambdaQueryWrapper<InsureUserWhiteRosterEntity>()
                        .eq(StringUtils.isNotBlank(realName), InsureUserWhiteRosterEntity::getRealName, realName)
                        .eq(StringUtils.isNotBlank(cardNo), InsureUserWhiteRosterEntity::getCardNo, cardNo)
                        .eq(StringUtils.isNotBlank(isStatus), InsureUserWhiteRosterEntity::getIsStatus, isStatus)
                        .eq(StringUtils.isNotBlank(rosterType), InsureUserWhiteRosterEntity::getRosterType, rosterType)
                        .ge(StringUtils.isNotBlank(startDate), InsureUserWhiteRosterEntity::getUpdateTime, startDate + " 00:00:00")
                        .le(StringUtils.isNotBlank(endDate), InsureUserWhiteRosterEntity::getUpdateTime, endDate + " 23:59:59")
                        .orderByDesc(InsureUserWhiteRosterEntity::getUpdateTime)
        );
        List<InsureUserWhiteRosterOut> voList = new ArrayList<>();
        for (InsureUserWhiteRosterEntity entity : page.getRecords()) {
            InsureUserWhiteRosterOut out = new InsureUserWhiteRosterOut();
            BeanUtils.copyProperties(entity, out);
            out.setRosterTypeDesc(UserWhiteRosterEnum.decode(entity.getRosterType()).getName());
            out.setValidDate(entity.getIsLongTerm() == 1 ? "始终有效" : DateUtils.convertDate2String(entity.getStartDate()) + "至" + DateUtils.convertDate2String(entity.getEndDate()));
            out.setUpdateTime(DateUtils.convertDate2String(entity.getUpdateTime()));
            voList.add(out);

        }
        return new PageUtils<>(voList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public InsureUserWhiteRosterVO getWhiteRosterInfo(Integer id) {

        InsureUserWhiteRosterEntity entity = Optional.ofNullable(this.getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("白名单信息不存在")));

        InsureUserWhiteRosterVO vo = new InsureUserWhiteRosterVO();
        BeanUtils.copyProperties(entity, vo);
        if (StringUtils.isNotBlank(entity.getCardFileCode())) {
            JSONObject imgObject = new JSONObject();
            Optional.ofNullable(sysDocumentService.lambdaQuery()
                    .eq(SysDocumentEntity::getFileCode, entity.getCardFileCode())
                    .last("limit 1")
                    .one()).ifPresent(x -> {
                vo.setCardFileCode(entity.getCardFileCode());
                imgObject.put("fileCode", entity.getCardFileCode());
                imgObject.put("domainPath", x.getDomainPath());
                imgObject.put("fileName", x.getFileName());
                vo.setImgObject(imgObject);
            });
        }
        if (StringUtils.isNotBlank(entity.getFaceVideoCode())) {
            JSONObject videoObject = new JSONObject();
            Optional.ofNullable(sysDocumentService.lambdaQuery()
                    .eq(SysDocumentEntity::getFileCode, entity.getFaceVideoCode())
                    .last("limit 1")
                    .one()).ifPresent(x -> {
                vo.setFaceVideoCode(entity.getFaceVideoCode());
                videoObject.put("fileCode", entity.getFaceVideoCode());
                videoObject.put("domainPath", x.getDomainPath());
                videoObject.put("fileName", x.getFileName());
                vo.setVideoObject(videoObject);
            });
        }
        vo.setStartDate(DateUtils.convertDate2String(entity.getStartDate()));
        vo.setEndDate(DateUtils.convertDate2String(entity.getEndDate()));
        return vo;
    }

    @Override
    public void whiteRosterSaveOrUpdate(InsureUserWhiteRosterVO vo) {
        if (vo.getRosterType() == 1 || vo.getRosterType() == 0) {
            String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
            Pattern pattern = Pattern.compile(regex);
            if (!pattern.matcher(vo.getCardNo()).matches()) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请输入正确的身份证格式"));
            }
        }
        InsureUserWhiteRosterEntity entity = new InsureUserWhiteRosterEntity();
        if (vo.getId() != null) {
            entity = Optional.ofNullable(this.getById(vo.getId()))
                    .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("白名单信息获取失败")));
        }
        BeanUtils.copyProperties(vo, entity);
        if (vo.getIsLongTerm() == 1) {
            entity.setStartDate(new Date());
            entity.setEndDate(DateUtils.convertString2Date("2199-12-31 23:59:59"));
        } else {
            entity.setStartDate(DateUtils.convertString2Date(vo.getStartDate()));
            entity.setEndDate(DateUtils.convertString2Date(vo.getEndDate()));
        }
        entity.setCardNo(vo.getCardNo().toUpperCase());
        this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> listUpload(String fileCode) {

        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传失败")));

        // 生成到本地
        String localFilePath = tempPath.concat(document.getFileName());
        log.info("ossPath={}, localFilePath={}", document.getFilePath(), localFilePath);
        storageService.downloadFileToLocal(document.getFilePath(), localFilePath);
        List<Object> list = ExcelUtil.readExcel(localFilePath, new WhiteRosterFaceVo());
        if (CollectionUtils.isEmpty(list)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件内容为空"));
        }
        ArrayList<InsureUserWhiteRosterEntity> rosterList = Lists.newArrayList();
        List<String> errorList = Lists.newArrayList();
        for (int i = 0; i < list.size(); i++) {
            WhiteRosterFaceVo vo = new WhiteRosterFaceVo();
            BeanUtils.copyProperties(list.get(i), vo);
            if (StringUtils.isBlank(vo.getRealName())) {
                errorList.add("第" + (i + 2) + "行姓名为空");
            }
            if (StringUtils.isBlank(vo.getCardNo())) {
                errorList.add("第" + (i + 2) + "行身份证号码为空");
            }
            String zx = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
            if (StringUtils.isNotBlank(vo.getCardNo()) && !Pattern.matches(zx, vo.getCardNo().trim())) {
                errorList.add("第" + (i + 2) + "行身份证号码不是正确的身份证格式");
            }
            if (StringUtils.isBlank(vo.getStartDate())) {
                errorList.add("第" + (i + 2) + "行有效开始时间为空");
            }
            if (StringUtils.isBlank(vo.getEndDate())) {
                errorList.add("第" + (i + 2) + "行有效结束时间为空");
            }
            Date startDate = vo.convertStartDate();
            Date endDate = DateUtils.convertString2Date(DateUtils.convertDate2String(vo.convertEndDate(), "yyyy-MM-dd") + " 23:59:59");
            if (startDate == null || endDate == null || endDate.before(startDate)) {
                errorList.add("第" + (i + 2) + "行有效开始时间要在有效结束时间之前");
            }
            InsureUserWhiteRosterEntity entity = new InsureUserWhiteRosterEntity();
            entity.setRealName(StringUtils.isNotBlank(vo.getRealName()) ? vo.getRealName().trim() : null);
            entity.setCardNo(StringUtils.isNotBlank(vo.getCardNo()) ? vo.getCardNo().toUpperCase().trim() : null);
            entity.setStartDate(startDate);
            entity.setEndDate(endDate);
            entity.setRosterType(1);
            entity.setIsImport(1);
            entity.setPlatformType("mini_program");
            entity.setIsLongTerm(0);
            entity.setIsStatus(1);
            rosterList.add(entity);
        }
        if (CollectionUtils.isEmpty(errorList)) {
            this.saveBatch(rosterList);
        }
        return errorList;
    }

    @Override
    public void updateStatus(Integer id, Integer isStatus) {
        InsureUserWhiteRosterEntity entity = Optional.ofNullable(this.getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("白名单信息不存在")));
        entity.setIsStatus(isStatus);
        this.updateById(entity);
    }

    @Override
    public void delete(Integer id) {
        Optional.ofNullable(this.getById(id))
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("白名单信息不存在")));
        this.removeById(id);
    }
}
