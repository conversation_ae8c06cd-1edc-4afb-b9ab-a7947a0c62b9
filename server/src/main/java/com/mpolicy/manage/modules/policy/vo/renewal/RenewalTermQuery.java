package com.mpolicy.manage.modules.policy.vo.renewal;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.enums.RenewalTermStatusEnum;
import com.mpolicy.manage.modules.policy.vo.PageInfo;
import com.mpolicy.manage.utils.LocalDateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 续期列表查询条件
 *
 * <AUTHOR>
 */
@Data
public class RenewalTermQuery extends PageInfo {

    @ApiModelProperty("保单号")
    private String policyNo;

    @ApiModelProperty("业务类型：0=网销，1=线下")
    private Integer salesType;

    @ApiModelProperty("保险公司名")
    private List<String> companyCodeList;

    @ApiModelProperty("险种名字")
    private String riskName;

    @ApiModelProperty("续期状态")
    private String status;

    @ApiModelProperty("投保人姓名")
    private String applicantName;

    @ApiModelProperty("投保人证件号")
    private String applicantIdCard;

    @ApiModelProperty("被保人姓名")
    private String insuredName;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("销售渠道")
    private List<String> channelCodeList;

    @ApiModelProperty("登录人可查询的-渠道编码列表")
    private List<String> authChannelBranchCodeList;

    @ApiModelProperty("渠道推荐人")
    private List<String> referrerCodeList;

    @ApiModelProperty("代理人机构")
    private List<String> orgCodeList;

    @ApiModelProperty("当前登录人可查询的-代理人机构")
    private List<String> authOrgCodeList;

    @ApiModelProperty("代理人")
    private List<String> agentCodeList;

    @ApiModelProperty("应缴开始时间")
    private Date duePaymentStartTime;

    @ApiModelProperty("应缴结束时间")
    private Date duePaymentEndTime;

    @ApiModelProperty("实收金额情况:1=与应收一致; 2=与应收不一致")
    private int paymentComparison;

    @Deprecated
    @ApiModelProperty(value = "实收操作日期",hidden = true)
    private String paymentSubmitTime;

    @ApiModelProperty(value = "实收操作日期:yyyy-MM-dd")
    private String paymentSubmitTimeStart;

    @ApiModelProperty(value = "实收操作日期")
    private String paymentSubmitTimeEnd;

    @ApiModelProperty(value = "保单生效时间：查询开始时间",example = "yyyy-MM-dd")
    private String enforceTimeStart;

    @ApiModelProperty(value = "保单生效时间：查询结束时间",example = "yyyy-MM-dd")
    private String enforceTimeEnd;

    @ApiModelProperty(value = "实收日期：查询开始时间",example = "yyyy-MM-dd")
    private String paymentTimeStart;

    @ApiModelProperty(value = "实收时间：查询结束时间",example = "yyyy-MM-dd")
    private String paymentTimeEnd;

    @ApiModelProperty("分销渠道编码")
    private List<String> channelDisCodeList;

    /**
     * 参数校验
     */
    public void check() {
        int pageNo = super.page;
        if (pageNo <= 0 || size < 0) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分页参数错误，页码不能为负数"));
        }
        if (size > PageInfo.MAX_PAGE_SIZE) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分页参数错误，查询条数超过限制"));
        }

    }

    public void init() {
        super.init();
        if (StringUtils.isNotBlank(paymentSubmitTimeStart)) {
            if (!paymentSubmitTimeStart.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("实收操作日期-查询开始时间：格式不正确"));
            }
        }
        if (StringUtils.isNotBlank(paymentSubmitTimeEnd)) {
            if (!paymentSubmitTimeEnd.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("实收操作日期-查询结束时间：格式不正确"));
            }
            LocalDate queryTime = LocalDateUtil.parseLocalDate(paymentSubmitTimeEnd);
            queryTime = queryTime.plusDays(1);
            paymentSubmitTimeEnd = LocalDateUtil.formatLocalDate(queryTime);
        }

        if (StringUtils.isNotBlank(enforceTimeStart)) {
            if (!enforceTimeStart.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单生效日期-查询开始时间：格式不正确"));
            }
        }
        if (StringUtils.isNotBlank(enforceTimeEnd)) {
            if (!enforceTimeEnd.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单生效日期-查询结束时间：格式不正确"));
            }
            LocalDate queryTime = LocalDateUtil.parseLocalDate(enforceTimeEnd);
            queryTime = queryTime.plusDays(1);
            enforceTimeEnd = LocalDateUtil.formatLocalDate(queryTime);
        }

        if (StringUtils.isNotBlank(paymentTimeStart)) {
            if (!paymentTimeStart.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("续期实收日期-查询开始时间：格式不正确"));
            }
        }
        if (StringUtils.isNotBlank(paymentTimeEnd)) {
            if (!paymentTimeEnd.matches("\\d{4}\\-\\d{1,2}\\-\\d{1,2}")) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("续期实收日期-查询结束时间：格式不正确"));
            }
            LocalDate queryTime = LocalDateUtil.parseLocalDate(paymentTimeEnd);
            queryTime = queryTime.plusDays(1);
            paymentTimeEnd = LocalDateUtil.formatLocalDate(queryTime);
        }
    }

    public void convertCode() {
        this.status = RenewalTermStatusEnum.convert2Code(status);
    }
}
