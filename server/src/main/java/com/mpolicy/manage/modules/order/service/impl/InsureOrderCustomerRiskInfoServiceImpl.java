package com.mpolicy.manage.modules.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.order.dao.InsureOrderCustomerRiskInfoDao;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import com.mpolicy.manage.modules.order.enums.InsureOrderCustomerRiskDictionaryEnum;
import com.mpolicy.manage.modules.order.enums.IsNotEnum;
import com.mpolicy.manage.modules.order.enums.RiskSourceEnum;
import com.mpolicy.manage.modules.order.service.InsureOrderCustomerRiskInfoService;
import com.mpolicy.manage.modules.order.util.InsureOrderCustomerRiskDictionaryUtil;
import com.mpolicy.manage.modules.order.util.OrderMapper;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoExportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoImportExcel;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoQueryVO;
import com.mpolicy.manage.modules.order.vo.InsureOrderCustomerRiskInfoVO;
import com.mpolicy.manage.modules.policy.service.EpPolicyInsuredInfoService;
import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import com.mpolicy.service.common.service.DicCacheHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 保险订单客户风险信息服务类实现类
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Slf4j
@Service("insureOrderCustomerRiskInfoService")
public class InsureOrderCustomerRiskInfoServiceImpl extends ServiceImpl<InsureOrderCustomerRiskInfoDao, InsureOrderCustomerRiskInfoEntity> implements InsureOrderCustomerRiskInfoService {


    @Resource
    private EpPolicyInsuredInfoService epPolicyInsuredInfoService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private SysDocumentService sysDocumentService;

    @Resource
    private StorageService storageService;
    @Value("admin.temp-file-path")
    private String tempPath;

    @Override
    public IPage<InsureOrderCustomerRiskInfoVO> getByQueryVO(InsureOrderCustomerRiskInfoQueryVO queryVO) {
        long size = queryVO.getSize();
        if (size > 1000){
            log.warn("最大查询数据不能超过1000条 queryVO={}",JSON.toJSONString(queryVO));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("最大查询数据不能超过1000条"));
        }
        IPage<InsureOrderCustomerRiskInfoEntity> page = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(queryVO.getCustomerName()), InsureOrderCustomerRiskInfoEntity::getCustomerName, queryVO.getCustomerName())
                .eq(StringUtils.isNotBlank(queryVO.getIdentityNumber()), InsureOrderCustomerRiskInfoEntity::getIdentityNumber, queryVO.getIdentityNumber())
                .eq(StringUtils.isNotBlank(queryVO.getRatingLevel()), InsureOrderCustomerRiskInfoEntity::getRatingLevel, queryVO.getRatingLevel())
                .eq(StringUtils.isNotBlank(queryVO.getChannelSource()), InsureOrderCustomerRiskInfoEntity::getChannelSource, queryVO.getChannelSource())
                .eq(StringUtils.isNotBlank(queryVO.getGender()), InsureOrderCustomerRiskInfoEntity::getGender, queryVO.getGender())
                .eq(StringUtils.isNotBlank(queryVO.getMaritalStatus()), InsureOrderCustomerRiskInfoEntity::getMaritalStatus, queryVO.getMaritalStatus())
                .orderByDesc(InsureOrderCustomerRiskInfoEntity::getCreateTime)
                .page(queryVO);

        IPage<InsureOrderCustomerRiskInfoVO> insureOrderCustomerRiskInfoVOIPage = page.convert(insureOrderCustomerRiskInfoEntity -> {
            InsureOrderCustomerRiskInfoVO insureOrderCustomerRiskInfoVO = orderMapper.insureOrderCustomerRiskInfoEntity2InsureOrderCustomerRiskInfoVO(insureOrderCustomerRiskInfoEntity);
            String identityNumber = insureOrderCustomerRiskInfoVO.getIdentityNumber();
            Boolean acceptByIdNo = epPolicyInsuredInfoService.isAcceptByIdNo(identityNumber);
            insureOrderCustomerRiskInfoVO.setIsAccept(acceptByIdNo);
            return insureOrderCustomerRiskInfoVO;
        });

        return insureOrderCustomerRiskInfoVOIPage;

    }

    @Override
    public String importInsureOrderCustomerRiskInfoData(String fileCode) {
        List<InsureOrderCustomerRiskInfoImportExcel> insureOrderCustomerRiskInfoImportExcelList = getInsureOrderCustomerRiskInfoImportExcelListByFileCode(fileCode);

        // 进行数据校验并且返回提示语
        Map<Integer,String> verifyMessage = Maps.newHashMap();
        AtomicInteger index = new AtomicInteger(1);
        List<InsureOrderCustomerRiskInfoEntity> insureOrderCustomerRiskInfoEntityList = insureOrderCustomerRiskInfoImportExcelList.stream()
                .filter(insureOrderCustomerRiskInfoImportExcel -> {
                    boolean isMistake = true;
                    if (StringUtils.isBlank(insureOrderCustomerRiskInfoImportExcel.getCustomerName())
                            || StringUtils.isBlank(insureOrderCustomerRiskInfoImportExcel.getIdentityType())
                            || StringUtils.isBlank(insureOrderCustomerRiskInfoImportExcel.getIdentityNumber())
                            || StringUtils.isBlank(insureOrderCustomerRiskInfoImportExcel.getCustomerType())
                            || StringUtils.isBlank(insureOrderCustomerRiskInfoImportExcel.getRatingLevel())) {
                        verifyMessage.put(index.get(),"客户名称，证据类型，证据号码，客户类型，风险等级不能为空");
                        isMistake = false;
                    }else if (insureOrderCustomerRiskInfoImportExcel.getCustomerName().length() > 20){
                        verifyMessage.put(index.get(),"客户名称不能超过20个字符");
                        isMistake = false;
                    }else if (
                            Objects.isNull(InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.IDENTITY_TYPE,insureOrderCustomerRiskInfoImportExcel.getIdentityType()))
                        || Objects.isNull(InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.CUSTOMER_TYPE,insureOrderCustomerRiskInfoImportExcel.getCustomerType()))
                        || Objects.isNull(InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.MARITAL_STATUS,insureOrderCustomerRiskInfoImportExcel.getMaritalStatus()))
                        || Objects.isNull(InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.CHANNEL_SOURCE,insureOrderCustomerRiskInfoImportExcel.getChannelSource()))
                    ){
                        verifyMessage.put(index.get(),"证件类型值,客户类型,婚姻状况或渠道来源不符合要求");
                        isMistake = false;
                    }else if (containByIdentityNumber(insureOrderCustomerRiskInfoImportExcel.getIdentityNumber())){
                        verifyMessage.put(index.get(),insureOrderCustomerRiskInfoImportExcel.getIdentityNumber() + "该证件号码已存在");
                        isMistake = false;
                    }

                    index.getAndIncrement();
                    return isMistake;
                })
                .map(insureOrderCustomerRiskInfoImportExcel -> {
                    dictionaryConversion(insureOrderCustomerRiskInfoImportExcel);
                    InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoEntity = orderMapper.insureOrderCustomerRiskInfoImportExcel2InsureOrderCustomerRiskInfoEntity(insureOrderCustomerRiskInfoImportExcel);

                    String identityType = insureOrderCustomerRiskInfoEntity.getIdentityType();
                    String identityNumber = insureOrderCustomerRiskInfoEntity.getIdentityNumber();
                    if ("ORDER:ID_TYPE:SYS_LIBRARY_CERTI_TYPE:11".equals(identityType)) {
                        if (IdcardUtil.isValidCard(identityNumber)) {
                            Integer genderByIdCard = IdcardUtil.getGenderByIdCard(identityNumber);
                            log.info("genderByIdCard ={}",genderByIdCard);
                            // 0表示女性，1表示男性
                            if (genderByIdCard.equals(0)){
                                insureOrderCustomerRiskInfoEntity.setGender("ORDER:GENDER:SYS_LIBRARY_GENDER:2");
                            }else {
                                insureOrderCustomerRiskInfoEntity.setGender("ORDER:GENDER:SYS_LIBRARY_GENDER:1");
                            }

                            int age = IdcardUtil.getAgeByIdCard(identityNumber);
                            insureOrderCustomerRiskInfoEntity.setAge(age);
                        }
                    }
                    insureOrderCustomerRiskInfoEntity.setRiskSource(RiskSourceEnum.IMPORT.getCode());
                    return insureOrderCustomerRiskInfoEntity;
                })
                .collect(Collectors.toList());
        //过滤掉空数据
        if (CollectionUtil.isEmpty(insureOrderCustomerRiskInfoEntityList)){
            log.warn("上传文件存在无效数据 insureOrderCustomerRiskInfoImportExcelList={}", JSON.toJSONString(insureOrderCustomerRiskInfoImportExcelList));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("上传文件存在无效数据"));
        }
        // 出去一行示例
        insureOrderCustomerRiskInfoImportExcelList.remove(0);

        this.saveBatch(insureOrderCustomerRiskInfoEntityList);
        int size = insureOrderCustomerRiskInfoEntityList.size();

        return getMistakeMessage(size,verifyMessage);
    }

    /**
     *
     *
     * 反向字典转换
     *
     * @param insureOrderCustomerRiskInfoImportExcel
     *
     *
     * excel
     *
     */
    private void dictionaryConversion(InsureOrderCustomerRiskInfoImportExcel insureOrderCustomerRiskInfoImportExcel) {
        String idType = InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.IDENTITY_TYPE, insureOrderCustomerRiskInfoImportExcel.getIdentityType());
        String customerType = InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.CUSTOMER_TYPE,insureOrderCustomerRiskInfoImportExcel.getCustomerType());
        String maritalStatus = InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.MARITAL_STATUS,insureOrderCustomerRiskInfoImportExcel.getMaritalStatus());
        String channelSource = InsureOrderCustomerRiskDictionaryUtil.getKeyByValue(InsureOrderCustomerRiskDictionaryEnum.CHANNEL_SOURCE,insureOrderCustomerRiskInfoImportExcel.getChannelSource());

        insureOrderCustomerRiskInfoImportExcel.setIdentityType(idType);
        insureOrderCustomerRiskInfoImportExcel.setCustomerType(customerType);
        insureOrderCustomerRiskInfoImportExcel.setMaritalStatus(maritalStatus);
        insureOrderCustomerRiskInfoImportExcel.setChannelSource(channelSource);
    }


    /**
     *
     *
     * 将所有校验信息构建成一个字符串
     *
     * @param verifyMessage
     *
     * 校验信息
     *
     * @return
     *
     * 导入信息
     *
     */
    private String getMistakeMessage(Integer successCount,Map<Integer, String> verifyMessage) {
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("导入成功")
                .append(successCount)
                .append("条客户风险数据。")
                .append("\n");

        for (Map.Entry<Integer, String> entry : verifyMessage.entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            messageBuilder.append("第")
                    .append(++key)
                    .append("行数据,")
                    .append(value)
                    .append("\n");
        }
        return messageBuilder.toString();
    }


    /**
     *
     *
     * 从excel读取数据
     *
     * @param fileCode
     *
     * 文件编码
     *
     * @return
     *
     * excel对象数据
     *
     */
    private List<InsureOrderCustomerRiskInfoImportExcel> getInsureOrderCustomerRiskInfoImportExcelListByFileCode(String fileCode) {
        SysDocumentEntity sysDocumentEntity = sysDocumentService.getByFileCode(fileCode);
        if (Objects.isNull(sysDocumentEntity)){
            log.warn("文件不存在 fileCode={}",fileCode);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件不存在"));
        }
        String fileName = sysDocumentEntity.getFileName();
        String localTempFilePath = tempPath.concat(fileName);
        String domainPath = sysDocumentEntity.getFilePath();
        storageService.downloadFileToLocal(domainPath,localTempFilePath);

        List<Object> objects = ExcelUtil.readExcel(localTempFilePath, new InsureOrderCustomerRiskInfoImportExcel());
        if (CollectionUtil.isEmpty(objects)){
            log.warn("文件不存在数据 domainPath={}",domainPath);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("文件不存在数据"));
        }
        if (objects.size() > 1000) {
            log.warn("导入不能超过1000条 domainPath={}",domainPath);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("导入不能超过1000条"));
        }
        return objects
                .stream()
                .map(InsureOrderCustomerRiskInfoImportExcel.class::cast)
                .collect(Collectors.toList());
    }

    @Override
    public boolean containByIdentityNumber(String identityNumber) {
        return this.lambdaQuery()
                .eq(InsureOrderCustomerRiskInfoEntity::getIdentityNumber,identityNumber)
                .eq(InsureOrderCustomerRiskInfoEntity::getDeleted,0)
                .count() > 0 ;
    }

    @Override
    public List<InsureOrderCustomerRiskInfoExportExcel> getExportByQueryVO(InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO) {
        List<InsureOrderCustomerRiskInfoVO> insureOrderCustomerRiskInfoVOList = getListByQueryVO(insureOrderCustomerRiskInfoQueryVO);
        return insureOrderCustomerRiskInfoVOList
                .stream()
                .map(insureOrderCustomerRiskInfoVO ->{
                        String isAccept = insureOrderCustomerRiskInfoVO.getIsAccept()? IsNotEnum.IS.getCode():IsNotEnum.NOT.getCode();
                        String isWhite = insureOrderCustomerRiskInfoVO.getIsWhite().equals(1)? IsNotEnum.IS.getCode():IsNotEnum.NOT.getCode();
                        InsureOrderCustomerRiskInfoExportExcel insureOrderCustomerRiskInfoExportExcel = orderMapper.InsureOrderCustomerRiskInfoVO2InsureOrderCustomerRiskInfoExportExcel(insureOrderCustomerRiskInfoVO);
                        insureOrderCustomerRiskInfoExportExcel.setIsWhite(isWhite);
                        insureOrderCustomerRiskInfoExportExcel.setIsAccept(isAccept);
                        return insureOrderCustomerRiskInfoExportExcel;
                    }
                ).collect(Collectors.toList());
    }
    @Override
    public List<InsureOrderCustomerRiskInfoVO> getListByQueryVO(InsureOrderCustomerRiskInfoQueryVO insureOrderCustomerRiskInfoQueryVO) {
        Integer size = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getCustomerName()), InsureOrderCustomerRiskInfoEntity::getCustomerName, insureOrderCustomerRiskInfoQueryVO.getCustomerName())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getIdentityNumber()), InsureOrderCustomerRiskInfoEntity::getIdentityNumber, insureOrderCustomerRiskInfoQueryVO.getIdentityNumber())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getRatingLevel()), InsureOrderCustomerRiskInfoEntity::getRatingLevel, insureOrderCustomerRiskInfoQueryVO.getRatingLevel())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getChannelSource()), InsureOrderCustomerRiskInfoEntity::getChannelSource, insureOrderCustomerRiskInfoQueryVO.getChannelSource())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getGender()), InsureOrderCustomerRiskInfoEntity::getGender, insureOrderCustomerRiskInfoQueryVO.getGender())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getMaritalStatus()), InsureOrderCustomerRiskInfoEntity::getMaritalStatus, insureOrderCustomerRiskInfoQueryVO.getMaritalStatus())
                .count();

        if (size > 1000){
            log.warn("最大查询数据不能超过1000条 queryVO={}",JSON.toJSONString(insureOrderCustomerRiskInfoQueryVO));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("最大查询数据不能超过1000条"));
        }
        List<InsureOrderCustomerRiskInfoEntity> insureOrderCustomerRiskInfoEntityList = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getCustomerName()), InsureOrderCustomerRiskInfoEntity::getCustomerName, insureOrderCustomerRiskInfoQueryVO.getCustomerName())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getIdentityNumber()), InsureOrderCustomerRiskInfoEntity::getIdentityNumber, insureOrderCustomerRiskInfoQueryVO.getIdentityNumber())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getRatingLevel()), InsureOrderCustomerRiskInfoEntity::getRatingLevel, insureOrderCustomerRiskInfoQueryVO.getRatingLevel())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getChannelSource()), InsureOrderCustomerRiskInfoEntity::getChannelSource, insureOrderCustomerRiskInfoQueryVO.getChannelSource())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getGender()), InsureOrderCustomerRiskInfoEntity::getGender, insureOrderCustomerRiskInfoQueryVO.getGender())
                .eq(StringUtils.isNotBlank(insureOrderCustomerRiskInfoQueryVO.getMaritalStatus()), InsureOrderCustomerRiskInfoEntity::getMaritalStatus, insureOrderCustomerRiskInfoQueryVO.getMaritalStatus())
                .orderByDesc(InsureOrderCustomerRiskInfoEntity::getCreateTime)
                .list();

        List<InsureOrderCustomerRiskInfoVO> insureOrderCustomerRiskInfoVOList = insureOrderCustomerRiskInfoEntityList
                .stream()
                .map(insureOrderCustomerRiskInfoEntity -> {
                    dictionaryConversion(insureOrderCustomerRiskInfoEntity);
                    InsureOrderCustomerRiskInfoVO insureOrderCustomerRiskInfoVO = orderMapper.insureOrderCustomerRiskInfoEntity2InsureOrderCustomerRiskInfoVO(insureOrderCustomerRiskInfoEntity);

                    String identityNumber = insureOrderCustomerRiskInfoVO.getIdentityNumber();
                    Boolean acceptByIdNo = epPolicyInsuredInfoService.isAcceptByIdNo(identityNumber);
                    insureOrderCustomerRiskInfoVO.setIsAccept(acceptByIdNo);
                    return insureOrderCustomerRiskInfoVO;
                })
                .collect(Collectors.toList());


        return insureOrderCustomerRiskInfoVOList;
    }

    /**
     *
     *
     * 针对，性别等枚举进行转换
     *
     * @param insureOrderCustomerRiskInfoEntity
     *
     * 风险客户vo
     *
     */
    private void dictionaryConversion(InsureOrderCustomerRiskInfoEntity insureOrderCustomerRiskInfoEntity) {
        String identityType = insureOrderCustomerRiskInfoEntity.getIdentityType();
        if (StringUtils.isNotBlank(identityType)) {
            identityType = DicCacheHelper.getValue(identityType);
            insureOrderCustomerRiskInfoEntity.setIdentityType(identityType);
        }
        String customerType = insureOrderCustomerRiskInfoEntity.getCustomerType();
        if (StringUtils.isNotBlank(customerType)) {
            customerType = DicCacheHelper.getValue(customerType);
            insureOrderCustomerRiskInfoEntity.setCustomerType(customerType);
        }
        String gender = insureOrderCustomerRiskInfoEntity.getGender();
        if (StringUtils.isNotBlank(gender)) {
            gender = DicCacheHelper.getValue(gender);
            insureOrderCustomerRiskInfoEntity.setGender(gender);
        }
        String maritalStatus = insureOrderCustomerRiskInfoEntity.getMaritalStatus();
        if (StringUtils.isNotBlank(maritalStatus)) {
            maritalStatus = DicCacheHelper.getValue(maritalStatus);
            insureOrderCustomerRiskInfoEntity.setMaritalStatus(maritalStatus);
        }
        String riskSource = insureOrderCustomerRiskInfoEntity.getRiskSource();
        if (StringUtils.isNotBlank(riskSource)) {
            riskSource = DicCacheHelper.getValue(riskSource);
            insureOrderCustomerRiskInfoEntity.setRiskSource(riskSource);
        }
        String channelSource = insureOrderCustomerRiskInfoEntity.getChannelSource();
        if (StringUtils.isNotBlank(channelSource)) {
            channelSource = DicCacheHelper.getValue(channelSource);
            insureOrderCustomerRiskInfoEntity.setChannelSource(channelSource);
        }
    }

    @Override
    public boolean save(InsureOrderCustomerRiskInfoEntity entity) {
        String identityType = entity.getIdentityType();
        String identityNumber = entity.getIdentityNumber();
        if ("ORDER:ID_TYPE:SYS_LIBRARY_CERTI_TYPE:11".equals(identityType)) {
            if (!IdcardUtil.isValidCard(identityNumber)) {
                log.warn("身份证格式有误，证件号码={}",identityNumber);
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("身份证格式有误"));
            }
            Integer genderByIdCard = IdcardUtil.getGenderByIdCard(identityNumber);
            // 0表示女性，1表示男性
            if (genderByIdCard.equals(0)){
                entity.setGender("ORDER:GENDER:SYS_LIBRARY_GENDER:2");
            }else {
                entity.setGender("ORDER:GENDER:SYS_LIBRARY_GENDER:1");
            }

            int age = IdcardUtil.getAgeByIdCard(identityNumber);
            entity.setAge(age);
        }

        if (containByIdentityNumber(identityNumber)) {
            log.warn("根据证件号，该客户已经存在 identityNumber={}",identityNumber);
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("根据证件号，该客户已经存在"));
        }
        entity.setRiskSource(RiskSourceEnum.IMPORT.getCode());
        return super.save(entity);
    }
}
