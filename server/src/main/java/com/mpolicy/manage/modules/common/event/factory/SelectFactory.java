package com.mpolicy.manage.modules.common.event.factory;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.manage.enums.SelectEnum;
import com.mpolicy.manage.modules.common.event.handler.SelectHandler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class SelectFactory {

    private static Map<SelectEnum, SelectHandler> SELECT_MAP = new ConcurrentHashMap<>();

    public static SelectHandler getInvoke(int code) {
        SelectEnum selectEnum = SelectEnum.matchSearchCode(code);
        if (selectEnum == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未找到符合条件的类型"));
        }
        SelectHandler handler = SELECT_MAP.get(selectEnum);
        if (handler == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未找到符合条件的处理器"));
        }
        return handler;
    }

    /**
     * 注册
     * @param selectEnum
     * @param handler
     */
    public static void register(SelectEnum selectEnum,SelectHandler handler) {
        if (null == selectEnum || null == handler) {
            return;
        }
        SELECT_MAP.put(selectEnum, handler);
    }


}
