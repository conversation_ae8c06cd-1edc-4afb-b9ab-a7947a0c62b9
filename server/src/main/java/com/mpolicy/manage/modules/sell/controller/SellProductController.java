package com.mpolicy.manage.modules.sell.controller;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.annotation.InvalidCache;
import com.mpolicy.manage.annotation.SysDbLog;
import com.mpolicy.manage.common.utils.ExcelUtil;
import com.mpolicy.manage.enums.InvalidCacheEnum;
import com.mpolicy.manage.modules.common.service.impl.PublicBaseService;
import com.mpolicy.manage.modules.sell.entity.*;
import com.mpolicy.manage.modules.sell.service.ISellProductService;
import com.mpolicy.manage.modules.sell.vo.PolicyCommodityVo;
import com.mpolicy.open.common.cfpamf.pull.BmsBranchBaseData;
import com.mpolicy.product.common.portfolio.PortfolioPlanOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "售卖商品模块")
@RestController
@RequestMapping("sell/product")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SellProductController {

    private final ISellProductService sellProductService;

    private final PublicBaseService publicBaseService;

    @ApiOperation(value = "获取销售商品列表", notes = "获取销售商品列表")
    @GetMapping("list")
    @RequiresPermissions(value = {"product:goods:all"})
    public Result<PageUtils<SellProductListOut>> list(@Valid SellProductListVo vo) {
        PageUtils<SellProductListOut> page = sellProductService.findPageList(vo);
        return Result.success(page);
    }

    @PostMapping("export")
    @RequiresPermissions(value = {"product:goods:all"})
    @ApiOperation(value = "导出销售商品列表", notes = "导出销售商品列表")
    public void export(@RequestBody @Valid SellProductListVo vo, HttpServletResponse response) {
        // 执行规则文件加载
        List<ExportProductListOut> result = sellProductService.findExportList(vo);
        ExcelUtil.writeExcel(response, result, "商品列表");
    }

    @ApiOperation(value = "获取销售商品详情(基本信息)", notes = "获取销售商品详情(基本信息)")
    @GetMapping("info/{productCode}")
    @RequiresPermissions(value = {"product:goods:all"})
    public Result<SellProductInfoOut> info(@PathVariable(value = "productCode") String productCode) {
        SellProductInfoOut info = sellProductService.info(productCode);
        return Result.success(info);
    }

    @InvalidCache(modelCode = InvalidCacheEnum.PRODUCT)
    @ApiOperation(value = "修改销售商品信息", notes = "修改销售商品信息")
    @PostMapping("update")
    @RequiresPermissions(value = {"product:goods:all"})
    @SysDbLog("修改销售商品信息")
    public Result update(@RequestBody @Valid SellProductUpdateVo vo) {
        // 如果电子发票状态是启用 那么必须选择电子发票的模版
        if (vo.getInvoiceStatus() == 1 && StrUtil.isBlank(vo.getInvoiceTemplateCode())){
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("启用电子发票请选择开票模版"));
        }
        sellProductService.update(vo);
        return Result.success();
    }

    @InvalidCache(modelCode = InvalidCacheEnum.PRODUCT)
    @ApiOperation(value = "新增销售商品信息", notes = "获取销售商品列表")
    @PostMapping("save")
    @RequiresPermissions(value = {"product:goods:all"})
    @SysDbLog("修改销售商品信息")
    public Result save(@RequestBody @Valid SellProductSaveVo vo) {
        // 如果电子发票状态是启用 那么必须选择电子发票的模版
        if (vo.getInvoiceStatus() == 1 && StrUtil.isBlank(vo.getInvoiceTemplateCode())){
            return Result.error(BasicCodeMsg.PARAMETER_ERROR.setMsg("启用电子发票请选择开票模版"));
        }
        sellProductService.save(vo);
        return Result.success();
    }

    @InvalidCache(modelCode = InvalidCacheEnum.PRODUCT)
    @ApiOperation(value = "修改商品状态", notes = "获取销售商品列表")
    @PostMapping("updateStatus")
    @RequiresPermissions(value = {"product:goods:all"})
    @Deprecated
    public Result updateStatus(@RequestBody @Valid SellProductStatusVo vo) {
        sellProductService.updateStatus(vo);
        return Result.success();
    }

    @InvalidCache(modelCode = InvalidCacheEnum.PRODUCT)
    @ApiOperation(value = "修改商品状态版本V2", notes = "修改商品状态版本V2")
    @PostMapping("updateStatusV2")
    @RequiresPermissions(value = {"product:goods:all"})
    public Result updateStatusV2(@RequestBody @Valid TimeSellProductStatusVo vo) {
        sellProductService.updateStatusV2(vo);
        return Result.success();
    }


    @ApiOperation(value = "删除销售商品信息", notes = "获取销售商品列表")
    @PostMapping("delete/{productCode}")
    @RequiresPermissions(value = {"product:goods:all"})
    @InvalidCache(modelCode = InvalidCacheEnum.PRODUCT)
    public Result delete(@PathVariable(value = "productCode") String productCode) {
        sellProductService.delete(productCode);
        return Result.success();
    }

    @ApiOperation(value = "获取产品售卖地区", notes = "获取产品售卖地区")
    @GetMapping("findSellAreaList")
    public Result<List<SellAreaOut>> findSellAreaList() {
        List<SellAreaOut> sellArea = sellProductService.findSellAreaList();
        return Result.success(sellArea);
    }

    @ApiOperation(value = "获取农保地区", notes = "获取农保地区")
    @GetMapping("findRuralAreaList")
    public Result<List<SellAreaOut>> findRuralAreaList() {
        List<SellAreaOut> sellArea = sellProductService.findRuralAreaList();
        return Result.success(sellArea);
    }

    @ApiOperation(value = "获取产品关键词是否存在", notes = "获取产品关键词是否存在")
    @GetMapping("findIsSellKeyword")
    public Result<SellKeywordOut> findIsSellKeyword(@RequestParam(required = false)
                                                    @ApiParam(value = "产品关键词")
                                                    @NotBlank(message = "产品关键词不能为空") String sellKeyword) {
        SellKeywordOut out = sellProductService.findIsSellKeyword(sellKeyword);
        return Result.success(out);
    }

    @ApiOperation(value = "获取售卖商品下拉列表", notes = "获取售卖商品下拉列表")
    @GetMapping("findSellSelectList")
    public Result<List<SellSelectListOut>> findSellSelectList(SellSelectListVo vo) {
        List<SellSelectListOut> resultList = sellProductService.findSellSelectList(vo);
        return Result.success(resultList);
    }


    @ApiOperation(value = "获取产品工具箱列表", notes = "获取产品工具箱列表")
    @GetMapping("findSellToolList/{productCode}")
    public Result<List<SellToolListOut>> findSellToolList(@PathVariable(value = "productCode") String productCode) {
        List<SellToolListOut> resultList = sellProductService.findSellToolList(productCode);
        return Result.success(resultList);
    }


    @ApiOperation(value = "更新产品工具箱状态", notes = "更新产品工具箱状态")
    @PostMapping("updateSellTool")
    public Result<List<SellToolListOut>> updateSellTool(@RequestBody @Valid SellToolVo vo) {
        sellProductService.updateSellTool(vo);
        return Result.success();
    }


    @ApiOperation(value = "判断是否可以勾选分享计划", notes = "判断是否可以勾选分享计划")
    @GetMapping("findIsSharePlan/{portfolioCode}")
    public Result<PortfolioPlanOut> findIsSharePlan(@PathVariable(value = "portfolioCode") String portfolioCode) {
        PortfolioPlanOut out = sellProductService.findIsSharePlan(portfolioCode);
        return Result.success(out);
    }

    @ApiOperation(value = "投保确认签约", notes = "投保确认签约")
    @PostMapping("policyCommodity")
    public Result policyCommodity(@RequestBody @Valid PolicyCommodityVo vo) {
        sellProductService.policyCommodity(vo);
        return Result.success();
    }
    @ApiOperation(value = "获取投保确认签约", notes = "获取投保确认签约")
    @GetMapping("policyCommodity/{productCode}")
    public Result<PolicyCommodityVo> getPolicyCommodity(@PathVariable(value = "productCode") String productCode) {
        PolicyCommodityVo result = sellProductService.getPolicyCommodity(productCode);
        return Result.success(result);
    }

    @ApiOperation(value = "获取智能问答产品列表", notes = "获取智能问答产品列表")
    @GetMapping("findIntelligentSolutionList")
    public Result<List<SellProductListOut>> findIntelligentSolutionList() {
        List<SellProductListOut> intelligentSolutionList = sellProductService.findIntelligentSolutionList();
        return Result.success(intelligentSolutionList);
    }
    @ApiOperation(value = "获取BMS分支", notes = "获取BMS分支")
    @GetMapping("bms/branch")
    public Result<List<SellAreaOut>> getBmsBranchBase() {
        List<BmsBranchBaseData> list = publicBaseService.getBmsBranchBase();
        ArrayList<SellAreaOut> result = Lists.newArrayList();
        list.forEach(item -> {
            SellAreaOut out = new SellAreaOut();
            out.setLabel(item.getOrgName());
            out.setValue(item.getOrgCode());
            result.add(out);
        });
        return Result.success(result);
    }
}
