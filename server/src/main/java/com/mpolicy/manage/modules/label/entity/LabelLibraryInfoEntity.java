package com.mpolicy.manage.modules.label.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签库信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
@TableName("label_library_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelLibraryInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 标签库code
     */
    @TableId(value = "library_code", type = IdType.INPUT)
    private String libraryCode;
    /**
     * 标签库名称
     */
    private String libraryName;
    /**
     * 启用状态 1:启用;0:关闭
     */
    private Integer enabled;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
