package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 保司升级状态
 *
 * <AUTHOR>
 * @date 2022-10-09 13:42
 */
@Data
@ApiModel(value = "保司升级状态")
public class InsureConfineStatusVO {

    @ApiModelProperty(value = "升级控制编码", required = true, example = "IFC1234567890")
    @NotBlank(message = "编号不能为空")
    private String confineCode;

    @ApiModelProperty(value = "是否启用 0:停用;1:启用", required = true, example = "1")
    @NotNull(message = "启用状态不能为空")
    private Integer isStatus;
}
