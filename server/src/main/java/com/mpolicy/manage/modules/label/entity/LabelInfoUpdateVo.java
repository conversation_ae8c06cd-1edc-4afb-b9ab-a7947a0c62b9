package com.mpolicy.manage.modules.label.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LabelInfoUpdateVo extends LabelInfoSaveVo implements Serializable {
    private static final long serialVersionUID = -2973726467157663329L;

    @NotBlank(message = "标签编码不能为空")
    @ApiModelProperty(value = "标签编码")
    private String labelCode;
}
