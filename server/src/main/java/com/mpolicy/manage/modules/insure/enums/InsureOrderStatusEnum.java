package com.mpolicy.manage.modules.insure.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <p>
 * 投保订单状态
 * 待提交
 *  核保完成
 *  人工核保
 *    待支付
 *    承保
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/9
 */
@Getter
public enum InsureOrderStatusEnum {

    /**
     * 投保订单状态
     */
    STAY_SUBMIT(0,"待核保"),
    UNDERWRITE_FAILURE(1,"核保失败"),
    STAY_PAY(2,"待支付"),
    UNDERWRITING(3,"承保完成"),
    CLOSE(4,"订单关闭"),
    STAY_UNDERWRITING(5,"待承保"),
    FACE_RECOGNITION(6,"待人脸识别"),
    PERSON_REVIEW(7,"待人工审核"),
    INS_UPDATE(8,"待修改"),
    INS_REFUND(9,"退款中"),
    INS_REFUND_REFUND(10,"已退款"),
    INS_STAGING(11,"暂存"),
    COMPANY_REVIEW(12,"保司审核中"),
    ADD_INFORMATION(13,"待补材料"),
    ;

    /**
     * 编码
     */
    private final int code;

    /**
     * 名称
     */
    private final String name;

    InsureOrderStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static InsureOrderStatusEnum decode(int code) {
        return Arrays.stream(InsureOrderStatusEnum.values())
                .filter(x -> x.code == code)
                .findFirst().orElse(null);
    }
}
