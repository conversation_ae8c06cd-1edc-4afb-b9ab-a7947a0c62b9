package com.mpolicy.manage.modules.tools.enums;

import java.util.Arrays;

/**
 * 模块枚举类型
 *
 * <AUTHOR> [zhang<PERSON><PERSON><PERSON>@xiaowhale.onaliyun.com]
 * @date 2021-08-01 11:49:15
 */
public enum SchedulePushTypeEnum {
    //  智能测评
    INTE_EVALUATION("INTE_EVALUATION", "智能测评"),
    // 保单报告
    POLICY_REPORT("POLICY_REPORT", "保单报告"),
    // 计划书
    PLAN_PROGRAM("PLAN_PROGRAM", "计划书"),
    // 产品对比
    PRODUCT_CONTRAST("PRODUCT_CONTRAST", "产品对比"),
    ;

    private final String code;
    private final String name;

    SchedulePushTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static SchedulePushTypeEnum getNameByCode(String code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
