package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentExtendEntity;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentExtendVo;

import java.util.Map;

/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentExtendService extends IService<AgentExtendEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 获取代理人扩展信息
     * @param agentCode
     * @return
     */
    AgentExtendVo findAgentExtendByCode(String agentCode);
}

