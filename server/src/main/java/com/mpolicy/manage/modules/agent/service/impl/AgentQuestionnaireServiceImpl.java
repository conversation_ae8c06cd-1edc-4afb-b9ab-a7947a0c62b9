package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.agent.dao.AgentQuestionnaireDao;
import com.mpolicy.manage.modules.agent.entity.*;
import com.mpolicy.manage.modules.agent.enums.AgentQuestionnaireStatusEnum;
import com.mpolicy.manage.modules.agent.enums.QuestionTypeEnum;
import com.mpolicy.manage.modules.agent.service.*;
import com.mpolicy.manage.modules.agent.vo.questionnaire.*;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


@Slf4j
@Service("agentQuestionnaireService")
public class AgentQuestionnaireServiceImpl extends ServiceImpl<AgentQuestionnaireDao, AgentQuestionnaireEntity> implements AgentQuestionnaireService {

    @Autowired
    private AgentQuestionnaireService agentQuestionnaireService;

    @Autowired
    private AgentQuestionnaireQuestionService agentQuestionnaireQuestionService;

    @Autowired
    private AgentQuestionnaireAnswerService agentQuestionnaireAnswerService;

    @Autowired
    private AgentUserInfoService agentUserInfoService;

    /**
     * 代理人问卷表
     * @param params
     * @return
     */
    @Override
    public PageUtils<AgentQuestionnaireOut> queryQuestionnairePage(Map<String, Object> params) {
        IPage<AgentQuestionnaireOut> page = baseMapper.queryQuestionnairePage(new Page(Long.parseLong(params.get("page").toString()), Long.parseLong(params.get("limit").toString())), params);
        return new PageUtils(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    /**
     * 保存问卷
     * @param blAgentQuestionnaireVo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AgentQuestionnaireVo blAgentQuestionnaireVo) {
        Date now = new Date();
        AgentQuestionnaireEntity agentQuestionnaireEntity = new AgentQuestionnaireEntity();
        List<AgentQuestionnaireQuestionVo> questionList = blAgentQuestionnaireVo.getQuestionList();
        List<AgentQuestionnaireQuestionEntity> questionEntityList = Lists.newArrayList();
        BeanUtils.copyProperties(blAgentQuestionnaireVo, agentQuestionnaireEntity);
        agentQuestionnaireEntity.setPublishTime(now);
        Date investigateStartTime = DateUtils.stringToDate(blAgentQuestionnaireVo.getInvestigateStartTime()+" 00:00:00", DateUtils.DATE_TIME_PATTERN);
        Date investigateEndTime = DateUtils.stringToDate(blAgentQuestionnaireVo.getInvestigateEndTime()+" 23:59:59", DateUtils.DATE_TIME_PATTERN);
        agentQuestionnaireEntity.setInvestigateStartTime(investigateStartTime);
        agentQuestionnaireEntity.setStatus(AgentQuestionnaireStatusEnum.SUBMIT.getStatus());
        agentQuestionnaireEntity.setInvestigateEndTime(investigateEndTime);
        //保存问卷记录
        agentQuestionnaireService.save(agentQuestionnaireEntity);
        AgentQuestionnaireQuestionEntity questionEntity = null;
        for(AgentQuestionnaireQuestionVo questionVo : questionList){
            questionEntity = new AgentQuestionnaireQuestionEntity();
            questionVo.setQuestionnaireId(agentQuestionnaireEntity.getId());
            BeanUtils.copyProperties(questionVo, questionEntity);
            questionEntityList.add(questionEntity);
        }
        //保存问卷题目记录
        agentQuestionnaireQuestionService.saveBatch(questionEntityList);
        //赋值AgentQuestionnaireQuestionVo对象ID
        questionList = appendQuestionId(questionEntityList, questionList);
        List<AgentQuestionnaireAnswerEntity> answerEntityList = getAnswerEntityList(questionList);
        agentQuestionnaireAnswerService.saveBatch(answerEntityList);
    }

    /**
     * 查询代理人答题详情
     * @param agentCode
     * @param questionnaireId
     * @return
     */
    @Override
    public List<AgentQuestionnaireQuestionVo> queryAgentAnswerPage(String agentCode,Integer questionnaireId) {
        List<AgentQuestionAnswerDetail> agentAnswerDetailList = baseMapper.queryAgentAnswerPage(agentCode,questionnaireId);
        return buildQuestionList(agentAnswerDetailList);
    }

    /**
     * 删除问卷
     * @param questionnaireId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer questionnaireId) {
        //逻辑删除问卷
        deleteByQuestionnaireId(questionnaireId);
        List<AgentQuestionnaireQuestionEntity> questionEntityList = agentQuestionnaireQuestionService.queryByQuestionnaireId(questionnaireId);
        questionEntityList.forEach(questionEntity -> {
            //逻辑删除问卷题目选项
            agentQuestionnaireAnswerService.deleteByQuestionId(questionEntity.getId());
        });
        //逻辑删除问卷题目
        agentQuestionnaireQuestionService.deleteByQuestionnaireId(questionnaireId);
    }

    /**
     * 问卷详情
     * @param questionnaireId
     * @return
     */
    @Override
    public AgentQuestionnaireVo detail(Integer questionnaireId) {
        AgentQuestionnaireVo questionnaireVo = new AgentQuestionnaireVo();
        AgentQuestionnaireEntity agentQuestionnaireEntity = baseMapper.selectById(questionnaireId);
        questionnaireVo.setTitle(agentQuestionnaireEntity.getTitle());
        questionnaireVo.setInvestigateStartTime(DateUtils.format(agentQuestionnaireEntity.getInvestigateStartTime(), DateUtils.DATE_TIME_PATTERN));
        questionnaireVo.setInvestigateEndTime(DateUtils.format(agentQuestionnaireEntity.getInvestigateEndTime(), DateUtils.DATE_TIME_PATTERN));
        List<AgentQuestionAnswerDetail> list = baseMapper.details(questionnaireId);

        List<AgentQuestionnaireQuestionVo> questionList = buildQuestionList(list);
        questionnaireVo.setQuestionList(questionList);
        return questionnaireVo;
    }


    /**
     * 赋值AgentQuestionnaireQuestionVo对象ID
     * @param questionEntityList
     * @param questionList
     * @return
     */
    private List<AgentQuestionnaireQuestionVo> appendQuestionId(List<AgentQuestionnaireQuestionEntity> questionEntityList, List<AgentQuestionnaireQuestionVo> questionList){
        AgentQuestionnaireQuestionEntity questionEntity = null;
        int i = 0;
        for(AgentQuestionnaireQuestionVo questionVo : questionList){
            questionEntity = questionEntityList.get(i);
            if(!Objects.equals(questionVo.getTitle(), questionEntity.getTitle())){
                //todo 抛异常
            }
            questionVo.setId(questionEntity.getId());
            i++;
        }
        return questionList;
    }

    /**
     * 返回AgentQuestionnaireAnswerEntity对象
     * @param questionList
     * @return
     */
    private List<AgentQuestionnaireAnswerEntity> getAnswerEntityList(List<AgentQuestionnaireQuestionVo> questionList) {
        List<AgentQuestionnaireAnswerEntity> answerEntityList = Lists.newArrayList();
        AgentQuestionnaireAnswerEntity answerEntity = null;
        List<AgentQuestionnaireAnswerVo> answerList = null;
        for(AgentQuestionnaireQuestionVo questionVo : questionList){
            answerList = questionVo.getAnswerList();
            for(AgentQuestionnaireAnswerVo answerVo : answerList){
                answerEntity = new AgentQuestionnaireAnswerEntity();
                BeanUtils.copyProperties(answerVo, answerEntity);
                answerEntity.setQuestionId(questionVo.getId());
                answerEntityList.add(answerEntity);
            }
        }
        return answerEntityList;
    }

    /**
     * 构建问卷题目和选中的答案
     * @param answer
     * @param isSelected
     * @return
     */
    private AgentQuestionnaireAnswerVo buildAnswerVo(String answer, Integer isSelected){
        AgentQuestionnaireAnswerVo answerVo = new AgentQuestionnaireAnswerVo();
        answerVo.setAnswer(answer);
        answerVo.setIsSelected(isSelected);
        return answerVo;
    }

    /**
     * 构建问卷信息如问卷,题目,选项
     * @param list
     * @return
     */
    private List<AgentQuestionnaireQuestionVo> buildQuestionList(List<AgentQuestionAnswerDetail> list){
        List<AgentQuestionnaireQuestionVo> questionVoList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        AgentQuestionnaireQuestionVo questionVo = null;
        Map<Integer,AgentQuestionnaireQuestionVo> questMap = Maps.newHashMap();
        List<AgentQuestionnaireAnswerVo> answerList = null;
        AgentQuestionnaireAnswerVo answerVo = null;
        for(AgentQuestionAnswerDetail detail : list){
            if(questMap.containsKey(detail.getId())){
                questionVo = questMap.get(detail.getId());
                answerList = questionVo.getAnswerList();
                answerVo = buildAnswerVo(detail.getAnswer(),detail.getIsSelected());
                answerVo.setAnswer(detail.getAnswer());
                answerList.add(answerVo);
            }else{
                questionVo = new AgentQuestionnaireQuestionVo();
                questionVo.setType(detail.getType());
                questionVo.setTypeDesc(QuestionTypeEnum.getValueByKey(detail.getType()).getDesc());
                questionVo.setTitle(detail.getTitle());
                answerList = Lists.newArrayList();
                answerVo = buildAnswerVo(detail.getAnswer(),detail.getIsSelected());
                answerVo.setAnswer(detail.getAnswer());
                answerList.add(answerVo);
                questionVo.setAnswerList(answerList);
                questMap.put(detail.getId(),questionVo);
            }
        }
        for(Map.Entry<Integer,AgentQuestionnaireQuestionVo> en : questMap.entrySet()){
            questionVoList.add(en.getValue());
        }
        return questionVoList;
    }

    private void deleteByQuestionnaireId(Integer questionnaireId){
        boolean result = this.update(
                new UpdateWrapper<AgentQuestionnaireEntity>().lambda()
                        .eq(AgentQuestionnaireEntity::getId, questionnaireId)
                        .set(AgentQuestionnaireEntity::getDeleted, Constant.DELETE_FLAG));
        if(!result){
            log.warn("问卷删除失败 questionnaireId= {}",questionnaireId);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("问卷删除失败"));
        }
    }

    /**
     * 问卷进展分页查询
     * @param params
     * @return
     */
    @Override
    public PageUtils<AgentInvestigateOut> queryInvestigatePage(Map<String, Object> params) {
        IPage<AgentQuestionnaireOut> page = baseMapper.queryInvestigatePage(new Page(Long.parseLong(params.get("page").toString()), Long.parseLong(params.get("limit").toString())), params);
        return new PageUtils(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }
}
