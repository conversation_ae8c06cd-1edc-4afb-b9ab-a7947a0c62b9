package com.mpolicy.manage.modules.baichuan.controller;

import com.google.common.collect.Lists;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.common.utils.DateUtils;
import com.mpolicy.manage.modules.baichuan.service.BcChannelManageService;
import com.mpolicy.manage.modules.baichuan.vo.BcChannelDetail;
import com.mpolicy.manage.modules.baichuan.vo.BcChannelExportInfo;
import com.mpolicy.manage.modules.baichuan.vo.BcChannelInfo;
import com.mpolicy.web.common.validator.ValidatorUtils;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.util.ArrayList;
import java.util.Map;


/**
 * 百川渠道签约信息
 *
 * <AUTHOR>
 * @since 2023-08-27 00:26:26
 */
@RestController
@RequestMapping("bc/channel/manage")
@Api(tags = "百川渠道签约信息")
@Slf4j
public class BcChannelManageController {

    @Autowired
    private BcChannelManageService bcChannelManageService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取百川渠道签约信息列表", notes = "分页获取百川渠道签约信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "appName", dataType = "String", value = "渠道名称", example = "圣源祥"),
            @ApiImplicitParam(paramType = "query", name = "appCode", dataType = "String", value = "渠道编码", example = "BC202020202"),
            @ApiImplicitParam(paramType = "query", name = "channelGroup", dataType = "String", value = "渠道性质", example = "COMPANY"),
            @ApiImplicitParam(paramType = "query", name = "sellPlatform", dataType = "String", value = "出单平台", example = "H5"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "String", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", name = "isConfig", dataType = "String", value = "获取渠道配置信息"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"channel:baichuan:info:all","channel:baichuan:channel:all"},logical = Logical.OR)
    public Result<PageUtils<BcChannelInfo>> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params) {
        log.info("获取百川渠道签约信息列表，查询条件={}", params);
        return Result.success(bcChannelManageService.queryPage(params));
    }

    /**
     * 信息
     */
    @ApiOperation(value = "获取渠道签约信息", notes = "获取渠道签约信息")
    @GetMapping("/info/{id}")
    @RequiresPermissions(value = {"channel:baichuan:info:all","channel:baichuan:channel:all"},logical = Logical.OR)
    public Result<BcChannelDetail> info(@PathVariable("id") Integer id) {
        log.info("获取百川渠道签约信息列表，id={}", id);
        return Result.success(bcChannelManageService.queryBcChannelDetail(id));
    }

    @ApiOperation(value = "签约渠道保存", notes = "签约渠道保存")
    @PostMapping("/save")
    @RequiresPermissions(value = {"channel:baichuan:info:all","channel:baichuan:channel:all"},logical = Logical.OR)
    public Result<String> save(@RequestBody @ApiParam(name = "channel", value = "提交信息") BcChannelDetail channel) {
        // 校验
        ValidatorUtils.validateEntity(channel, AddGroup.class, Default.class);
        bcChannelManageService.saveBcChannel(channel);
        return Result.success("success");
    }

    @ApiOperation(value = "签约渠道修改", notes = "签约渠道修改")
    @PostMapping("/update")
    @RequiresPermissions(value = {"channel:baichuan:info:all","channel:baichuan:channel:all"},logical = Logical.OR)
    public Result<String> update(@RequestBody @ApiParam(name = "channel", value = "提交信息") BcChannelDetail channel) {
        // 校验
        ValidatorUtils.validateEntity(channel, UpdateGroup.class, Default.class);
        bcChannelManageService.updateBcChannel(channel);
        return Result.success("success");
    }

    /**
     * 信息
     */
    @ApiOperation(value = "操作渠道签约状态", notes = "操作渠道签约状态")
    @PostMapping("/ope_status/{id}")
    public Result<String> channelChangeStatus(@PathVariable("id") Integer id, @RequestParam(value = "channelStatus", required = true) Integer channelStatus) {
        log.info("操作百川渠道状态信息，id={} 操作状态={} ", id, channelStatus);
        bcChannelManageService.opeBcChannelStatus(id, channelStatus);
        return Result.success("success");
    }

    /**
     * 签约信息导出
     */
    @ApiOperation(value = "签约信息导出", notes = "签约信息导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "appName", dataType = "String", value = "渠道名称", example = "圣源祥"),
            @ApiImplicitParam(paramType = "query", name = "appCode", dataType = "String", value = "渠道编码", example = "BC202020202"),
            @ApiImplicitParam(paramType = "query", name = "channelGroup", dataType = "String", value = "渠道性质", example = "COMPANY"),
            @ApiImplicitParam(paramType = "query", name = "sellPlatform", dataType = "String", value = "出单平台", example = "H5"),
            @ApiImplicitParam(paramType = "query", name = "createTime", dataType = "String", value = "创建时间"),
            @ApiImplicitParam(paramType = "query", name = "isConfig", dataType = "String", value = "获取渠道配置信息"),
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils<BcChannelInfo> page = bcChannelManageService.queryPage(params);
        ArrayList<BcChannelExportInfo> list = Lists.newArrayList();
        page.getList().forEach(x -> {
            BcChannelExportInfo info = new BcChannelExportInfo();
            BeanUtils.copyProperties(x, info);
            if (x.getSignType() == 1) {
                info.setSignDate("长期有效");
            } else {
                info.setSignDate(DateUtils.format(x.getSignStartData()) + "--" + DateUtils.format(x.getSignEndData()));
            }
            list.add(info);
        });

        ExcelUtil.writeExcel(response, list, "渠道配置信息", "sheet1", new BcChannelExportInfo());
        return Result.success();
    }
}
