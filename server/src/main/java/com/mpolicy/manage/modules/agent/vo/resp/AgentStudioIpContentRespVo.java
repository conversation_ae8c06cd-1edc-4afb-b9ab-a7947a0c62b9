package com.mpolicy.manage.modules.agent.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/19 14:44
 */
@Data
public class AgentStudioIpContentRespVo {
    /**
     * ip内容编码
     */
    @ApiModelProperty(value = "ip内容编码")
    private String ipContentCode;

    /**
     * 经纪人编码
     */
    @ApiModelProperty(value = "经纪人编码")
    private String agentCode;
    /**
     * 经纪人姓名
     */
    @ApiModelProperty(value = "经纪人姓名")
    private String agentName;

    /**
     * 所属机构
     */
    @ApiModelProperty(value = "所属机构")
    private String orgName;

    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String businessCode;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;

    /**
     * 内容平台
     */
    @ApiModelProperty(value = "内容平台")
    private String platform;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 字数
     */
    @ApiModelProperty(value = "字数")
    private Integer textCount;




}
