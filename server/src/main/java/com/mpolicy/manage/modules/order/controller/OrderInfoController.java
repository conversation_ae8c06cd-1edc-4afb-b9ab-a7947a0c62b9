package com.mpolicy.manage.modules.order.controller;

import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.order.service.OrderInfoService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 订单信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-11-15 15:53:34
 */
@RestController
@RequestMapping("customer/OrderInfo")
@Api(tags = "订单信息")
public class OrderInfoController {

    @Autowired
    private OrderInfoService orderInfoService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取订单信息列表", notes = "获取订单信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query",  name = "companyName", dataType = "String", value = "公司名称"),
            @ApiImplicitParam(paramType = "query",  name = "policyNO", dataType = "String", value = "保单号"),
            @ApiImplicitParam(paramType = "query",  name = "applicantName", dataType = "String", value = "投保人姓名"),
            @ApiImplicitParam(paramType = "query",  name = "applicantIdCard", dataType = "String", value = "投保人证件号"),
            @ApiImplicitParam(paramType = "query",  name = "productName", dataType = "String", value = "产品名称"),
            @ApiImplicitParam(paramType = "query",  name = "orderStatus", dataType = "String", value = "订单状态"),
            @ApiImplicitParam(paramType = "query",  name = "applicantTime", dataType = "String", value = "投保日期"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")

    })
    @GetMapping("/list")
    public Result<PageUtils> list(@RequestParam(required = false) @ApiParam(hidden = true) Map<String, Object> params){
        PageUtils page =orderInfoService.queryPage(params);
        return Result.success(page);
    }



    /**
     * <p>
     *     查看回溯照片
     * </p>
     * @param orderCode
     * @return
     */
    @ApiOperation(value = "查看回溯照片", notes = "查看回溯照片")
    @GetMapping("/backPicture/{orderCode}")
    public Result<List> list(@PathVariable @ApiParam(name = "orderCode", value = "订单号") String orderCode){
        List list = orderInfoService.backPicture(orderCode);
        return Result.success(list);
    }
}
