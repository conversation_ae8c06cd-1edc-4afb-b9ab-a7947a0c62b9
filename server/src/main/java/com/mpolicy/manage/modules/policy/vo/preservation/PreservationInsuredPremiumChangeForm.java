package com.mpolicy.manage.modules.policy.vo.preservation;

import com.mpolicy.manage.modules.agent.validator.group.Group;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;



/**
 * 保单保费变更保全
 * <AUTHOR>
 */
@Data
@ApiModel("保单保费变更-被保人变更明细")
public class PreservationInsuredPremiumChangeForm implements Serializable {

    @ApiModelProperty("被保人计划(产品计划)")
    @NotBlank(message = "团险产品计划编码不能为空",groups = Group.class)
    private String planCode;

    @ApiModelProperty("被保人计划(产品计划)")
    private String planName;

    @ApiModelProperty("被保人计划(保障计划)")
    @NotBlank(message = "团险保障计划编码不能为空",groups = Group.class)
    private String virtualPlanCode;

    @ApiModelProperty("被保人计划(保障计划)")
    private String virtualPlanName;

    @ApiModelProperty(value = "保障期间类型:枚举[PolicyInsuredPeriodTypeEnum]", example = "INSURED_PERIOD_TYPE:1", required = true)
    private String insuredPeriodType;

    @ApiModelProperty(value = "保障时长", example = "10")
    private Integer insuredPeriod;

    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("被保人证件类型")
    private String insuredIdCardType;

    @ApiModelProperty("被保人变更前总保费")
    @NotNull(message = "变更前被保人保费不能为空")
    private BigDecimal beforePremium;

    @ApiModelProperty("被保人变更后总保费")
    @NotNull(message = "变更后被保人保费不能为空")
    private BigDecimal correctedPremium;

    @ApiModelProperty("被保人险种保费明细")
    private List<PreservationInsuredProductPremiumChangeForm> insuredProductPremiumChangeList;
}
