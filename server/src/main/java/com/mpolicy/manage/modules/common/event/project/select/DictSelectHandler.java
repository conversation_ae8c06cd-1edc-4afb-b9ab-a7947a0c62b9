package com.mpolicy.manage.modules.common.event.project.select;

import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.enums.SelectEnum;
import com.mpolicy.manage.modules.common.event.factory.SelectFactory;
import com.mpolicy.manage.modules.common.event.handler.SelectHandler;
import com.mpolicy.manage.modules.common.model.SelectOut;
import com.mpolicy.manage.modules.common.model.SelectVo;
import com.mpolicy.service.common.service.DicCacheHelper;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DictSelectHandler extends SelectHandler {


    @Override
    public void afterPropertiesSet() throws Exception {
        SelectFactory.register(SelectEnum.COMMISSION_SETTLEMENT_COMPANY, this);
    }

    /**
     * 获取字典列表
     *
     * @param select
     * @return
     */
    @Override
    public List<SelectOut> findSelectList(SelectVo select) {
        switch (Objects.requireNonNull(SelectEnum.matchSearchCode(select.getSelectType()))) {
            case COMMISSION_SETTLEMENT_COMPANY:
                return DicCacheHelper.getSons(Constant.SETTLEMENT_INSTITUTION_DIC).stream().map(m ->
                        SelectOut.builder()
                                .label(m.getValue())
                                .value(m.getKey())
                                .disabled(false)
                                .build()
                ).collect(Collectors.toList());
            default:
                return Collections.emptyList();
        }
    }
}
