package com.mpolicy.manage.modules.policy.client;

import com.mpolicy.common.result.Result;
import com.mpolicy.manage.modules.policy.vo.ai.ConversationRecordsResponseVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Insurance BFF Project 服务客户端
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@FeignClient(
        name = "insurance-bff-project",
        url = "${insurance.feign.bff-project:}",
        fallbackFactory = InsuranceBffClientFallback.class
)
public interface InsuranceBffClient {

    /**
     * 查询AI聊天对话记录
     *
     * @param page      页码
     * @param pageSize  每页大小
     * @param keyword   关键词
     * @param userName  用户名
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 对话记录响应结果
     */
    @ApiOperation(value = "查询AI聊天对话记录")
    @GetMapping("/ai-chat/conversation-records")
    Result<ConversationRecordsResponseVo> getConversationRecords(
            @RequestParam("page") Integer page,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime
    );
}
