package com.mpolicy.manage.modules.label.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.label.entity.LabelInfoEntity;
import com.mpolicy.manage.modules.label.entity.LabelInfoListOut;
import com.mpolicy.manage.modules.label.entity.LabelInfoListVo;

/**
 * 标签信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-01 13:39:18
 */
public interface LabelInfoDao extends BaseMapper<LabelInfoEntity> {

    /**
     * 获取列表数据
     *
     * @param page
     * @param params
     * @return
     */
    IPage<LabelInfoListOut> findPageList(Page page, LabelInfoListVo params);
}
