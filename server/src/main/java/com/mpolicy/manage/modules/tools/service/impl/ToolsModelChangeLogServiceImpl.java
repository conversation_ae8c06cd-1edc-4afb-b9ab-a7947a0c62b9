package com.mpolicy.manage.modules.tools.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.tools.dao.ToolsModelChangeLogDao;
import com.mpolicy.manage.modules.tools.entity.ToolsModelChangeLogEntity;
import com.mpolicy.manage.modules.tools.service.ToolsModelChangeLogService;
import com.mpolicy.manage.modules.tools.vo.ToolsModelChangeLogVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Objects;

/**
 * @author: yangdonglin
 * @create: 2023-06-12 11:37
 * @description: 模型变更记录Service
 */
@Service
public class ToolsModelChangeLogServiceImpl extends ServiceImpl<ToolsModelChangeLogDao, ToolsModelChangeLogEntity>
        implements ToolsModelChangeLogService {

    @Async
    @Override
    public void save(ToolsModelChangeLogVo logVo) {
        //操作人
        final String createUser = logVo.getCreateUser();
        Assert.isTrue(StringUtils.isNotBlank(createUser), "必要信息操作人为空");
        final ToolsModelChangeLogEntity toolsModelChangeLogEntity = new ToolsModelChangeLogEntity();
        final Date data = new Date();
        toolsModelChangeLogEntity.setType(logVo.getTypeEnum().getCode());
        toolsModelChangeLogEntity.setChangeId(logVo.getChangeId());
        toolsModelChangeLogEntity.setChangeNbr(logVo.getChangeNbr());
        toolsModelChangeLogEntity.setStartData(JSON.toJSONString(logVo.getStartDataObjects()));
        if (Objects.nonNull(logVo.getAfterMethod())) {
            toolsModelChangeLogEntity.setEndData(JSON
                    .toJSONString(logVo
                            .getAfterMethod()
                            .apply(logVo.getAfterMethodParameter())));
        }
        toolsModelChangeLogEntity.setDeleted(0);
        toolsModelChangeLogEntity.setCreateUser(createUser);
        toolsModelChangeLogEntity.setCreateTime(data);
        toolsModelChangeLogEntity.setUpdateUser(createUser);
        toolsModelChangeLogEntity.setUpdateTime(data);
        this.save(toolsModelChangeLogEntity);
    }
}
