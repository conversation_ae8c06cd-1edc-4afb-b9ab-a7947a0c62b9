package com.mpolicy.manage.modules.agent.controller;

import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.domain.DomainUtil;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.agent.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.manage.modules.agent.service.ChannelApplicationReferrerService;
import com.mpolicy.manage.modules.agent.vo.ChannelApplicationReferrerRowVo;
import com.mpolicy.manage.modules.agent.vo.ReferrerInfoVo;
import com.mpolicy.manage.modules.policy.vo.EpPolicyChannelInfoVo;
import com.mpolicy.web.common.annotation.NoRepeatSubmit;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 推荐人信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-04-12 18:54:12
 */
@RestController
@RequestMapping("agent/channelapplicationreferrer")
@Api(tags = "推荐人信息")
public class ChannelApplicationAllReferrerController {

    @Autowired
    private ChannelApplicationReferrerService channelApplicationReferrerService;

    /**
     * 列表
     */
    @ApiOperation(value = "获取推荐人信息列表", notes = "分页获取推荐人信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerWno", dataType = "String", value = "工号", example = "ZHNX0001"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerName", dataType = "String", value = "姓名", example = "张翠萍"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerMobile", dataType = "String", value = "手机号", example = "1234567890"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerRegion", dataType = "String", value = "所属区域", example = "referrerRegion:1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/list")
    @RequiresPermissions(value = {"channel:referrer:all"})
    public Result<PageUtils<ChannelApplicationReferrerEntity>> list(
            @RequestParam Map<String, Object> params) {
        PageUtils<ChannelApplicationReferrerEntity> page = channelApplicationReferrerService.queryPage(null, params);
        return Result.success(page);
    }

    /**
     * 列表
     */
    @ApiOperation(value = "获取推荐人信息列表", notes = "分页获取推荐人信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", required = true, name = "page", dataType = "int", value = "当前页数", example = "1"),
            @ApiImplicitParam(paramType = "query", required = true, name = "limit", dataType = "int", value = "每页显示记录数", example = "10"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerWno", dataType = "String", value = "工号", example = "ZHNX0001"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerName", dataType = "String", value = "姓名", example = "张翠萍"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerMobile", dataType = "String", value = "手机号", example = "1234567890"),
            @ApiImplicitParam(paramType = "query", required = true, name = "referrerRegion", dataType = "String", value = "所属区域", example = "referrerRegion:1"),
            @ApiImplicitParam(paramType = "query", name = "order", dataType = "String", value = "排序方式 asc/desc", example = "desc"),
            @ApiImplicitParam(paramType = "query", name = "sidx", dataType = "String", value = "排序字段", example = "id")
    })
    @GetMapping("/export")
    @RequiresPermissions(value = {"channel:referrer:all"})
    public Result export(
            HttpServletResponse response,
            @RequestParam Map<String, Object> params) {
        params.put("limit", "-1");
        PageUtils<ChannelApplicationReferrerEntity> page = channelApplicationReferrerService.queryPage(null, params);
        AtomicInteger i = new AtomicInteger(1);
        List<ChannelApplicationReferrerRowVo> list = page.getList().stream().map(x -> {
            ChannelApplicationReferrerRowVo target = new ChannelApplicationReferrerRowVo();
            BeanUtils.copyProperties(x, target);
            target.setNo(i.getAndIncrement());
            target.setEnabled(StatusEnum.getNameByCode(x.getEnabled()).getName());
            return target;
        }).collect(Collectors.toList());

        ExcelUtil.writeExcel(response, list, "推荐人信息", "sheet1", new ChannelApplicationReferrerRowVo());
        return Result.success();
    }

    /**
     * 新增或修改
     */
    @NoRepeatSubmit(keyName = "token")
    @ApiOperation(value = "新增或修改推荐人信息", notes = "新增或修改推荐人信息")
    @PostMapping("/saveOrUpdate")
    @RequiresPermissions(value = {"channel:referrer:all"})
    public Result<ChannelApplicationReferrerEntity> saveOrUpdate(
            @ApiParam(value = "渠道推荐人对象", required = true)
            @Validated @RequestBody ChannelApplicationReferrerEntity channelApplicationReferrerEntity) throws InterruptedException {
        boolean result = channelApplicationReferrerService.saveOrUpdateEntity(channelApplicationReferrerEntity);

        if (result) {
            channelApplicationReferrerEntity.setFilePath(DomainUtil.addOssDomainIfNotExist(channelApplicationReferrerEntity.getFilePath()));
            return Result.success(channelApplicationReferrerEntity);
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("操作失败"));
        }
    }

    /**
     * 获取渠道推荐人信息信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取渠道推荐人信息信息(核心业务-保单中心)")
    @PostMapping("/getChannelRecommender")
    public Result<List<EpPolicyChannelInfoVo>> getChannelRecommender(@RequestParam(required = false) @ApiParam(name = "referrerCodeOrName", value = "推荐人姓名/工号") String referrerCodeOrName) {
        List<EpPolicyChannelInfoVo> list=channelApplicationReferrerService.getChannelRecommender(referrerCodeOrName);

        return Result.success(list);
    }

    /**
     * 获取渠道推荐人信息信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取渠道推荐人信息信息(核心业务-保单中心) 权限")
    @PostMapping("/getChannelRecommenderPermission")
    public Result<List<EpPolicyChannelInfoVo>> getChannelRecommenderPermission(@RequestParam(required = false) @ApiParam(name = "referrerCodeOrName", value = "推荐人姓名/工号") String referrerCodeOrName) {
        List<EpPolicyChannelInfoVo> list=channelApplicationReferrerService.getChannelRecommender(referrerCodeOrName,true);

        return Result.success(list);
    }

    /**
     * 获取推荐人信息(核心业务-保单中心)
     */
    @ApiOperation(value = "获取推荐人信息(核心业务-保单中心)")
    @PostMapping("/getReferrer")
    @Deprecated
    public Result<List<ReferrerInfoVo>> getReferrer(@RequestParam(required = false) @ApiParam(name = "referrerCodeOrName", value = "推荐人姓名/工号") String referrerCodeOrName) {
        return Result.success();
    }

}
