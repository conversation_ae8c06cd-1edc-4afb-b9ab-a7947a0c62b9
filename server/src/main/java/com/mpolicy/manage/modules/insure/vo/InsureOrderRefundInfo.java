package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 订单撤单信息开始
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@Data
@ApiModel(value = "订单撤单信息开始", description = "订单撤单信息开始")
public class InsureOrderRefundInfo implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "投保唯一编号", required = true, example = "INS202204212020202")
    @NotBlank(message = "投保订单唯一编号不能为空")
    private String insureOrderCode;

    @ApiModelProperty(value = "撤单原因", required = true, example = "用户重复投保了")
    @NotBlank(message = "退款原因不能为空")
    private String refundReason;

}
