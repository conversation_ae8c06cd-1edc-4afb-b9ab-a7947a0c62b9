package com.mpolicy.manage.modules.settlement.vo.settlement.autocost;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/1/31 00:33
 * @Version 1.0
 */

@Data
public class SettlementDetailPolicyDimensionVo {


    /**
     * 结佣月份
     */
    @ApiModelProperty(value = "结佣月份")
    private String costSettlementCycle;

    /**
     * 结佣机构编码  当全部汇总是为空
     */
    @ApiModelProperty(value = "结佣机构编码")
    private String settlementInstitution;


    /**
     * 结佣机构名称
     */
    @ApiModelProperty(value = "结佣机构名称")
    private String settlementInstitutionName;

    @ApiModelProperty(value = "单据编号")
    private String documentCode;

    @ApiModelProperty(value = "记账日期")
    private String settlementDate;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "批单号")
    private String endorsementNo;

    @ApiModelProperty(value = "险种编码")
    private String productCode;

    @ApiModelProperty(value = "险种名称")
    private String productName;

    @ApiModelProperty(value = "投保期数")
    private Integer renewalPeriod;

    @ApiModelProperty(value = "缴费期限")
    private int paymentPeriod;

    @ApiModelProperty(value = "缴费期间类型")
    private String  paymentPeriodType;

    @ApiModelProperty(value = "保费")
    private BigDecimal premium;

    @ApiModelProperty(value = "费用类型")
    private String commissionType;

    @ApiModelProperty(value = "费用类型名称")
    private String commissionTypeName;

    @ApiModelProperty(value = "费用")
    private String amount;

    @ApiModelProperty(value = "支付比例")
    private BigDecimal grantRate;

    @ApiModelProperty(value = "支出金额")
    private BigDecimal grantAmount;

    @ApiModelProperty(value = "投保人 settlemnet_cost_policy_info")
    private String applicantName;

    @ApiModelProperty(value = "承保日期 settlemnet_cost_policy_info")
    private String approvedTime;

    @ApiModelProperty(value = "险种状态")
    private String productStatus;

    @ApiModelProperty(value = "长短险标记 0短险1长险 settlemnet_cost_policy_info")
    private String longShortFlag;

    @ApiModelProperty(value = "险种大类")
    private String productGroup;

    @ApiModelProperty(value = "二级分类编码")
    private String level2Code;

    @ApiModelProperty(value = "三级分类编码")
    private String level3Code;

    @ApiModelProperty(value = "业务数据类型")
    private String businessDataType;

    @ApiModelProperty(value = "源单据编号")
    private String sourceDocumentCode;

    @ApiModelProperty(value = "险种大类")
    private String productGroupName;

    @ApiModelProperty(value = "二级分类编码")
    private String level2CodeName;

    @ApiModelProperty(value = "三级分类编码")
    private String level3CodeName;

    /**
     * 应缴时间
     */
    @ApiModelProperty(value = "应缴时间")
    private Date payableTime;

    public String getLongShortFlag() {
        if (Objects.equals(this.longShortFlag, "0")) {
            return "短险";
        } else if (Objects.equals(this.longShortFlag, "1")) {
            return "长险";
        }
        return this.longShortFlag;
    }

}
