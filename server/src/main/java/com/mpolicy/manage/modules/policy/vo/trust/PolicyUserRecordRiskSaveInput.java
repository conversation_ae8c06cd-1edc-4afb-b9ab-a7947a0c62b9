package com.mpolicy.manage.modules.policy.vo.trust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * ClassName: PolicyUserRecordRiskSaveInput
 * Description: 险种配置请求信息
 * date: 2023/11/10 14:35
 *
 * <AUTHOR>
 */
@ApiModel(value = "险种配置请求信息")
@Data
public class PolicyUserRecordRiskSaveInput {

    /**
     * 配置编码
     */
    @ApiModelProperty(value = "社保配置编码code")
    private String code;

    /**
     * 社保类型
     */
    @ApiModelProperty(value = "社保类型 1：职工医保 2：居民医保 3：新农合",example = "1",required = true)
    @NotNull(message = "社保类型不能为空")
    private Integer socialType;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "险种编码",example = "BJSCZZGYB2309131",required = true)
    @NotBlank(message = "险种编码不能为空")
    private String productCode;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "险种名称",example = "北京市城镇职工医保",required = true)
    @NotBlank(message = "险种名称不能为空")
    private String productName;

    /**
     * 保单保额
     */
    @ApiModelProperty(value = "保额", example = "456", required = true)
    @NotNull(message = "保额不能为空")
    private BigDecimal policyCoverage;

    /**
     * 缴费期间类型
     */
    @ApiModelProperty(value = "缴费期间类型", example = "PAYMENT_PERIOD_TYPE:1", required = true)
    @NotBlank(message = "缴费期间类型不能为空")
    private String paymentPeriodBrief;
    /**
     * 缴费期间
     */
    @ApiModelProperty(value = "缴费期间", example = "1", required = true)
    private String paymentPeriod;

    /**
     * 保障期间
     */
    @ApiModelProperty(value = "保障期间", example = "INSURED_PERIOD_TYPE:0", required = true)
    @NotBlank(message = "保障期间类型不能为空")
    private String insuredPeriodBrief;
    /**
     * 保障期间详情
     */
    @ApiModelProperty(value = "保障期间", example = "1", required = true)
    private String insuredPeriod;

    /**
     * 保费
     */
    @ApiModelProperty(value = "保费", example = "456", required = true)
    @NotNull(message = "保费不能为空")
    private BigDecimal premium;

    /**
     * 投保地区
     */
    @ApiModelProperty(value = "投保地区", required = true)
    @Valid
    @NotEmpty(message = "投保地区不能为空")
    private List<PolicyUserRecordRiskAreaInput> areaList;
}
