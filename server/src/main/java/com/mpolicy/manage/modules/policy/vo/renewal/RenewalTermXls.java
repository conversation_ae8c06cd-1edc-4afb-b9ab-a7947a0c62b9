package com.mpolicy.manage.modules.policy.vo.renewal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "续期导出信息")
@Data
public class RenewalTermXls extends BaseRowModel implements Serializable {

    @ExcelProperty(value = "投保单号")
    private String proposalNo;

    @ExcelProperty("保单号")
    private String policyNo;

    @ExcelProperty("续期状态")
    private String status;

    @ExcelProperty("销售方式")
    private String salesType;

    @ExcelProperty("销售平台")
    private String salesPlatform;

    @ExcelProperty("保险公司名称")
    private String companyName;

    @ExcelProperty("主险名称")
    private String mainProductName;

    @ExcelProperty("协议险种名称")
    private String protocolProductName;

    @ExcelProperty("险种名称")
    private String productName;

    @ExcelProperty("险种编码")
    private String productCode;

    @ExcelProperty("是否主险")
    private String mainInsurance;

    @ExcelProperty("险种类型")
    private String prodType;

    @ExcelProperty("保险期间类型")
    private String insuredPeriodType;

    @ExcelProperty("保险期间")
    private String insuredPeriod;

    @ExcelProperty("缴费方式")
    private String periodType;

    @ExcelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ExcelProperty("缴费期间")
    private String paymentPeriod;

    @ExcelProperty("保单年度")
    private String policyPeriodYear;

    @ExcelProperty("缴费期次")
    private String period;

    @ExcelProperty("保单状态")
    private String policyStatus;

    @ExcelProperty("保费")
    private BigDecimal premium;

    @ExcelProperty("保额")
    private BigDecimal coverage;

    @ExcelProperty("代理人机构名称")
    private String agentOrgName;

    @ExcelProperty("部门名称")
    private String agentDepartmentName;

    @ExcelProperty("是否主代理人")
    private String mainAgentFlag;

    @ExcelProperty("代理人编码")
    private String agentCode;

    @ExcelProperty("代理人姓名")
    private String agentName;

    @ExcelProperty("分佣比例")
    private String commissionRate;

    @ExcelProperty("推荐人工号")
    private String policyReferrerCode;

    @ExcelProperty("推荐人姓名")
    private String policyReferrerName;

    @ExcelProperty("销售渠道")
    private String channelName;

    @ExcelProperty("分支名称")
    private String channelBranchName;

    @ExcelProperty("渠道推荐人工号")
    private String referrerCode;

    @ExcelProperty("渠道推荐人姓名")
    private String referrerName;

    @ExcelProperty(value = "生效时间", format = "yyyy-MM-dd")
    private Date effectiveTime;

    @ExcelProperty(value = "终止时间", format = "yyyy-MM-dd")
    private Date endTime;

    @ExcelProperty(value = "续期开始时间", format = "yyyy-MM-dd")
    private Date periodStartTime;

    @ExcelProperty(value = "续期结束时间", format = "yyyy-MM-dd")
    private Date periodEndTime;

    @ExcelProperty(value = "应收日期", format = "yyyy-MM-dd")
    private Date duePaymentTime;

    @ExcelProperty("应收保费")
    private String duePaymentAmount;

    @ExcelProperty(value = "实收日期", format = "yyyy-MM-dd")
    private Date paymentTime;

    @ExcelProperty("实收保费")
    private String paymentAmount;

    @ExcelProperty("宽限期剩余天数")
    private Integer graceDay;

    @ExcelProperty("实收操作人")
    private String operator;

    @ExcelProperty(value = "实收操作时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date paymentSubmitTime;

    @ExcelProperty("投保人姓名")
    private String applicantName;

    @ExcelProperty("投保人证件类型")
    private String applicantIdType;

    @ExcelProperty("投保人证件号")
    private String applicantIdCard;

    @ExcelProperty("投保人性别")
    private String applicantGender;

    @ExcelProperty("投保人电话")
    private String applicantMobile;

    @ExcelProperty("投保人出生日期")
    private Date applicantBirthday;

    @ExcelProperty("投保人详细地址")
    private String applicantAddress;

    @ExcelProperty("被保人是投保人的")
    private String insuredRelation;

    @ExcelProperty("被保人姓名")
    private String insuredName;

    @ExcelProperty("被保人证件类型")
    private String insuredIdType;

    @ExcelProperty("被保人证件号码")
    private String insuredIdCard;

    @ExcelProperty("被保人性别")
    private String insuredGender;

    @ExcelProperty("被保人电话")
    private String insuredMobile;

    @ExcelProperty("被保人出生日期")
    private String insuredBirthday;

    @ExcelProperty("被保人详细地址")
    private String insuredAddress;

    @ExcelProperty(value = "图例")
    private String channelDistributionName;

    private String settlementStatus;

    private String settlementYear;

    private String settlementMonth;


}
