package com.mpolicy.manage.modules.insure.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insure.entity.InsureBlacklistInfoEntity;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistInfo;
import com.mpolicy.manage.modules.insure.vo.InsureBlacklistList;

import java.util.Map;

/**
 * 投保黑名单信息表
 *
 * <AUTHOR>
 * @date 2022-11-08 13:58:37
 */
public interface InsureBlacklistService extends IService<InsureBlacklistInfoEntity> {

    /**
     * <p>
     * 投保黑名单分页查询
     * </p>
     *
     * @param paramMap paramMap
     * @return com.mpolicy.common.utils.PageUtils<com.mpolicy.manage.modules.insure.vo.InsureBlacklistList>
     * <AUTHOR>
     * @since 2022/11/8
     */
    PageUtils<InsureBlacklistList> queryPage(Map<String,Object> paramMap);


    /**
     * <p>
     * 新增投保黑名单用户
     * </p>
     *
     * @param blacklistUser 投保黑名单对象
     * @return void
     * <AUTHOR>
     * @since 2022/11/8
     */
    void saveBlacklistUser(InsureBlacklistInfo blacklistUser);

    /**
     * <p>
     * 修改投保黑名单用户
     * </p>
     *
     * @param blacklistUser 投保黑名单对象
     * @return void
     * <AUTHOR>
     * @since 2022/11/8
     */
    void updateBlacklistUser(InsureBlacklistInfo blacklistUser);
}

