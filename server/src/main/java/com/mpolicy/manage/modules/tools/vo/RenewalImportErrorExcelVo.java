package com.mpolicy.manage.modules.tools.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @author: yang<PERSON><PERSON>
 * @create: 2023-05-06 10:47
 * @description: 续保导出错误清单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RenewalImportErrorExcelVo extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行号
     */
    @ExcelProperty(value = "行号")
    private String seq;

    /**
     * 保单号
     */
    @ExcelProperty(value = "保单号")
    private String policyNo;

    /**
     * 失败原因号
     */
    @ExcelProperty(value = "失败原因号")
    private String message;
}
