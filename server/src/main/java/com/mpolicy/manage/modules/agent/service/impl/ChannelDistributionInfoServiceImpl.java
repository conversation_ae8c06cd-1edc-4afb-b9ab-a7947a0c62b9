package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.common.utils.ShiroUtils;
import com.mpolicy.manage.modules.agent.dao.ChannelDistributionInfoDao;
import com.mpolicy.manage.modules.agent.entity.ChannelDistributionInfoEntity;
import com.mpolicy.manage.modules.agent.service.ChannelDistributionInfoService;
import com.mpolicy.manage.modules.agent.vo.ChannelInfoVo;
import com.mpolicy.manage.modules.agent.vo.orginfo.TreeListOut;
import com.mpolicy.manage.modules.sys.entity.SysUserChannelDistributionEntity;
import com.mpolicy.manage.modules.sys.service.SysUserChannelDistributionService;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service("channelDistributionInfoService")
public class ChannelDistributionInfoServiceImpl extends ServiceImpl<ChannelDistributionInfoDao, ChannelDistributionInfoEntity> implements ChannelDistributionInfoService {

    @Autowired
    private IRedisService redisService;
    @Autowired
    private SysUserChannelDistributionService sysUserChannelDistributionService;

    private static final String XJ_CODE = "XJXH001";
    private static final Integer XJ_ENABLED = 0;
    private static final String ENABLED = "1";

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String channelName = RequestUtils.objectValueToString(params, "channelName");
        String channelCode = RequestUtils.objectValueToString(params, "channelCode");
        Integer enabled = RequestUtils.objectValueToInteger(params, "enabled");
        //分页查询
        IPage<ChannelDistributionInfoEntity> page = this.page(
                new Query<ChannelDistributionInfoEntity>().getPage(params),
                new QueryWrapper<ChannelDistributionInfoEntity>().lambda()
                        .like(StringUtils.isNotBlank(channelCode), ChannelDistributionInfoEntity::getChannelCode, channelCode)
                        .like(StringUtils.isNotBlank(channelName), ChannelDistributionInfoEntity::getChannelName, channelName)
                        .eq(Objects.nonNull(enabled), ChannelDistributionInfoEntity::getEnabled, enabled)
                        .orderByAsc(ChannelDistributionInfoEntity::getId)
        );
        return new PageUtils(page);
    }

    @Override
    public boolean saveOrUpdateEntity(ChannelDistributionInfoEntity channelDistributionInfo) {
        ChannelDistributionInfoEntity bean = getByCode(channelDistributionInfo.getChannelCode());
        if (Objects.nonNull(bean)) {
            if (Objects.equals(bean.getChannelCode(), XJ_CODE)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该渠道不允许变更"));
            }
            channelDistributionInfo.setId(bean.getId());
            channelDistributionInfo.setEnabled(null);
        }else{
            channelDistributionInfo.setEnabled(1);
        }
        boolean result = this.saveOrUpdate(channelDistributionInfo);
//        redisService.delete(AdminCommonKeys.CHANNEL,Constant.CHANNEL_DISTRIBUTION_INFO);
        return result;
    }

    private ChannelDistributionInfoEntity getByCode(String code) {
        return this.getOne(new QueryWrapper<ChannelDistributionInfoEntity>().lambda().eq(ChannelDistributionInfoEntity::getChannelCode, code));
    }

    @Override
    public boolean changeEnable(String code, Integer enabled) {
        if (StatusEnum.getNameByCode(enabled) == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("启用状态错误"));
        }
        if (Objects.equals(code, XJ_CODE) && Objects.equals(XJ_ENABLED, enabled)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("该渠道不允许变更及置为失效状态"));
        }
        ChannelDistributionInfoEntity updateBean = new ChannelDistributionInfoEntity();
        updateBean.setEnabled(enabled);
        boolean result = this.update(updateBean, new QueryWrapper<ChannelDistributionInfoEntity>().lambda().eq(ChannelDistributionInfoEntity::getChannelCode, code));
//        redisService.delete(AdminCommonKeys.CHANNEL,Constant.CHANNEL_DISTRIBUTION_INFO);
        return result;
    }

    @Override
    public List<ChannelInfoVo> getChannelList(boolean isNeedPermission) {
        List<String> collect = new ArrayList<>();
        if(isNeedPermission){
            if(StatusEnum.NORMAL.getCode().equals(ShiroUtils.getUserEntity().getIsAllChannelDistribution())){
                collect = sysUserChannelDistributionService.lambdaQuery()
                        .eq(SysUserChannelDistributionEntity::getUserId, ShiroUtils.getUserEntity().getUserId())
                        .list().stream().map(m -> m.getChannelCode()).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(collect)){
                    return  null;
                }
            }
        }
        // 查询渠道信息
        List<ChannelDistributionInfoEntity> list = this.lambdaQuery().eq(ChannelDistributionInfoEntity::getDeleted, Constant.NON_DELETE_FLAG)
                .eq(ChannelDistributionInfoEntity::getEnabled, ENABLED)
                .in(CollectionUtils.isNotEmpty(collect),ChannelDistributionInfoEntity::getChannelCode,collect).list();
        return list.stream().map(a -> {
            ChannelInfoVo channelInfoVo = new ChannelInfoVo();
            BeanUtils.copyProperties(a, channelInfoVo);
            return channelInfoVo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取渠道分销树
     *
     * @return
     */
    @Override
    public List<TreeListOut> findTreeList() {
        return lambdaQuery()
                .eq(ChannelDistributionInfoEntity::getEnabled, StatusEnum.NORMAL.getCode())
                .list().stream().map(m -> {
                    TreeListOut out = new TreeListOut();
                    out.setId(m.getChannelCode());
                    out.setLabel(m.getChannelName());
                    out.setParentId("");
                    return out;
                }).collect(Collectors.toList());
    }
}
