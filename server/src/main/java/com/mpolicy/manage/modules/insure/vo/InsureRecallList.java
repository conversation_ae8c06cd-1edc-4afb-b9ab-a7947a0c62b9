package com.mpolicy.manage.modules.insure.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 投保回溯信息
 *
 * <AUTHOR>
 * @date 2022-05-29 13:42
 */
@Data
@ApiModel(value = "订单回溯数据")
public class InsureRecallList implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 投保订单唯一编号编号
     */
    @ApiModelProperty(value = "投保订单唯一编号编号")
    private String insureOrderCode;

    /**
     * 影像件oss存储地址
     */
    @ApiModelProperty(value = "投保订单唯一编号编号", example = "https://api-test.xiaowhale.com/doc.html#/home")
    private String shotImgPath;

    /**
     * 页面描述
     */
    @ApiModelProperty(value = "页面描述", example = "协议签署")
    private String operatePage;

    /**
     * 采集页面地址
     */
    @ApiModelProperty(value = "页面描述", example = "https://api-test.xiaowhale.com/doc.html#/home")
    private String currentUrl;

    /**
     * 采集事件
     */
    @ApiModelProperty(value = "采集事件", example = "小鲸向海告知书")
    private String pageEvent;

    /**
     * 进入页面时间
     */
    @ApiModelProperty(value = "进入页面时间", example = "2020-02-02 12:12:12")
    private String enterPageTime;

    /**
     * 离开页面时间
     */
    @ApiModelProperty(value = "离开页面时间", example = "2020-02-02 12:12:12")
    private String quitPageTime;

    /**
     * 平台
     */
    @ApiModelProperty(value = "平台", example = "微信公众号")
    private String platform;

    /**
     * 操作ip
     */
    @ApiModelProperty(value = "操作ip", example = "127.0.0.1")
    private String operateIp;
    /**
     * 回溯类型
     */
    @ApiModelProperty(value = "回溯类型", example = "image,sts")
    private String recallType;
    /**
     * sts记录编号
     */
    @ApiModelProperty(value = "sts记录编号", example = "DFGHJKL45678JHVF")
    private String recordId;
}
