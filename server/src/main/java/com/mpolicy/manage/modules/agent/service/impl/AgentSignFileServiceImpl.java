package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.agent.dao.AgentSignFileDao;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileEntity;
import com.mpolicy.manage.modules.agent.service.AgentSignFileDetailService;
import com.mpolicy.manage.modules.agent.service.AgentSignFileService;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignFilePageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignNumOut;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description 代理人签约文件信息实现
 * @return
 * @Date 2023/5/29 18:07
 * <AUTHOR>
 **/
@Service("agentSignFileService")
public class AgentSignFileServiceImpl extends ServiceImpl<AgentSignFileDao, AgentSignFileEntity> implements AgentSignFileService {

    @Autowired
    private AgentSignFileDetailService agentSignFileDetailService;

    @Override
    public PageUtils<AgentSignFilePageList> pageList(Map<String, Object> params) {
        //分页查询
        //分页参数
        String fileName = RequestUtils.objectValueToString(params,"fileName");
        String createTime = RequestUtils.objectValueToString(params,"createTime");
        Integer enabled = RequestUtils.objectValueToInteger(params,"enabled");

        //分页查询
        IPage<AgentSignFileEntity> iPage = this.page(new Query<AgentSignFileEntity>().getPage(params),
                new LambdaQueryWrapper<AgentSignFileEntity>().like(Objects.nonNull(fileName), AgentSignFileEntity::getFileName, fileName)
                        .eq(Objects.nonNull(enabled), AgentSignFileEntity::getEnabled, enabled)
                        .apply(StringUtils.isNotBlank(createTime), "date_format(create_time,'%Y-%m-%d') = {0}", createTime)
                        .orderByDesc(AgentSignFileEntity::getCreateTime));

        //构建返回信息
        List<AgentSignFilePageList> list = Lists.newArrayList();
        iPage.getRecords().stream().forEach(a->{
            AgentSignFilePageList bean = new AgentSignFilePageList();
            BeanUtils.copyProperties(a,bean);
            bean.setCreateTime(DateUtils.convertDate2String(a.getCreateTime()));
            bean.setEnableTime(DateUtils.convertDate2String(a.getEnableTime()));
            String enabledName = a.getEnabled() == StatusEnum.NORMAL.getCode() ? "已启用":"未启用";
            bean.setEnabledName(enabledName);
            if(a.getEnabled().equals(StatusEnum.NORMAL.getCode())){
                //统计人数
                AgentSignNumOut agentSignNumOut = agentSignFileDetailService.querySignNumByCodeAndCompanyId(a.getFileCode(),null);
                bean.setAlreadyNum(agentSignNumOut.getAlreadyNum());
                bean.setUnsignedNum(agentSignNumOut.getUnsignedNum());
                bean.setShouldNum(agentSignNumOut.getShouldNum());
                bean.setDefaultFinishNum(agentSignNumOut.getDefaultFinishNum());
            }else{
                bean.setAlreadyNum(0);
                bean.setUnsignedNum(0);
                bean.setShouldNum(0);
                bean.setDefaultFinishNum(0);
            }
            list.add(bean);
        });
        return new PageUtils<>(list, (int)iPage.getTotal(), (int)iPage.getPages(), (int)iPage.getCurrent());
    }
}
