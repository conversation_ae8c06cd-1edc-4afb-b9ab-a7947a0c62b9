package com.mpolicy.manage.modules.agent.enums;

import java.util.Arrays;

/**
 * 推荐人查询类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 14:10
 */
public enum ReferrerQueryTypeEnum {

    /**
     * 姓名
     */
    NAME(0, "姓名"),
    /**
     * 工号
     */
    WORK_NO(1, "工号"),
    ;

    private final Integer code;
    private final String name;

    ReferrerQueryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static ReferrerQueryTypeEnum getNameByCode(Integer code) {
        return Arrays.stream(values()).filter((x) -> x.code.equals(code)).findFirst().orElse(null);
    }
}
