package com.mpolicy.manage.modules.agent.vo.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveAgentTrainInfoVo implements Serializable {
    private static final long serialVersionUID = -5148360809401168065L;


    @ApiModelProperty(value = "是否为暂存状态")
    private Integer isTemp;

    @ApiModelProperty(value = "培训主题")
    private String trainTopic;

    @ApiModelProperty(value = "培训内容")
    private String trainContent;

    @ApiModelProperty(value = "培训开始时间")
    private Date beginTime;

    @ApiModelProperty(value = "培训结束时间")
    private Date endTime;

    @ApiModelProperty(value = "培训的主持")
    private List<String> trainHostList;

    @ApiModelProperty(value = "参会人员列表")
    private List<String> participants;
}
