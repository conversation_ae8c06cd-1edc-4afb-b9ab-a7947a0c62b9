package com.mpolicy.manage.modules.policy.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 孤儿单页面
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 15:48
 */
@Data
public class PolicyBriefInfoVo extends BaseRowModel {
    private static final long serialVersionUID = 1L;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    @ExcelProperty(value = "保单号")
    private String policyNo;
    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    @ExcelProperty(value = "合同号")
    private String contractCode;
    /**
     * 保单类型
     */
    @ApiModelProperty(value = "保单类型")
    @ExcelProperty(value = "保单类型")
    private String policyProductType;
    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    @ExcelProperty(value = "保单状态")
    private String policyStatus;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称")
    @ExcelProperty(value = "险种名称")
    private String portfolioName;
    /**
     * 保费
     */
    @ApiModelProperty(value = "保费")
    @ExcelProperty(value = "保费")
    private BigDecimal premiumTotal;
    /**
     * 投保时间
     */
    @ApiModelProperty(value = "投保时间")
    @ExcelProperty(value = "投保时间")
    private String applicantTime;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    @ExcelProperty(value = "生效日期")
    private String enforceTime;
    /**
     * 保险公司
     */
    @ApiModelProperty(value = "保险公司")
    @ExcelProperty(value = "保险公司")
    private String companyName;
    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    @ExcelProperty(value = "投保人姓名")
    private String applicantName;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    @ExcelProperty(value = "被保人姓名")
    private String insuredName;
    /**
     * 代理人
     */
    @ApiModelProperty(value = "代理人")
    @ExcelProperty(value = "代理人")
    private String agentName;
    /**
     * 渠道推荐人
     */
    @ApiModelProperty(value = "渠道推荐人")
    @ExcelProperty(value = "渠道推荐人")
    private String referrerName;
    /**
     * 渠道分支
     */
    @ApiModelProperty(value = "渠道分支")
    @ExcelProperty(value = "渠道分支")
    private String channelBranchName;
    /**
     * 销售渠道
     */
    /*@ApiModelProperty(value = "销售渠道")
    @ExcelProperty(value = "销售渠道")
    private String channelName;*/
    /**
     * 销售渠道编码
     */
    @ApiModelProperty(value = "销售渠道编码")
    private String channelCode;
}
