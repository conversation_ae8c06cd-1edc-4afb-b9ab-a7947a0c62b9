package com.mpolicy.manage.modules.agent.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.enums.ReferrerQueryTypeEnum;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelApplicationReferrerVo;
import com.mpolicy.manage.modules.agent.vo.resp.ChannelCustomerVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/9 14:09
 */
public interface ChannelCustomerService {

    PageUtils<ChannelCustomerVo> queryPage(String applicationCode, Map<String, Object> params);

    /**
     * 获取渠道下所有客户数量
     *
     * @param applicationCode 应用编码
     * @return 客户数量
     */
    Integer getTotal(String applicationCode);

    /**
     * 变更客户所属推荐人
     *
     * @param customerCode 客户编码
     * @param referrerCode 推荐人编码
     */
    void updateReferrer(String customerCode, String referrerCode);

    /**
     * 根据类型查询
     *
     * @param applicationCode       应用编码
     * @param referrerQueryTypeEnum 查询字段
     * @param text                  查询内容
     * @return 推荐人列表
     */
    List<ChannelApplicationReferrerVo> queryReferrer(String applicationCode, ReferrerQueryTypeEnum referrerQueryTypeEnum, String text);
}
