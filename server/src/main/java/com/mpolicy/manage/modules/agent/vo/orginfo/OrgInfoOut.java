package com.mpolicy.manage.modules.agent.vo.orginfo;

import com.mpolicy.manage.modules.agent.entity.OrgInfoAccessoryEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrgInfoOut implements Serializable {
    private static final long serialVersionUID = -955959827616204803L;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "代理人类型0:机构 1:部门")
    private Integer orgType;

    @ApiModelProperty(value = "省份代码")
    private String orgProvince;

    @ApiModelProperty(value = "城市代码")
    private String orgCity;

    @ApiModelProperty(value = "详细住址")
    private String orgAddr;

    @ApiModelProperty(value = "上级组织编码")
    private String orgSuperiorCode;


    @ApiModelProperty(value = "组织编码树")
    private List<String> orgParentNodeCode;


    @ApiModelProperty(value = "营业状态")
    private Integer orgStatus;

    @ApiModelProperty(value = "负责人姓名")
    private String principalName;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系电话")
    private String contactTel;

    @ApiModelProperty(value = "版本号")
    private Integer revision;

    @ApiModelProperty(value = "附件信息")
    private List<OrgInfoAccessoryEntity> fileList;

    @ApiModelProperty(value = "注册地址")
    private String orgRegisterAddr;
    @ApiModelProperty(value = "许可证编号")
    private String orgLicence;
    @ApiModelProperty(value = "业务范围")
    private String orgScope;
}
