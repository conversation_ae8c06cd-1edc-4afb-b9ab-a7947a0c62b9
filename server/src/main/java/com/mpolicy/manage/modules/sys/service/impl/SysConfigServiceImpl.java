package com.mpolicy.manage.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.redis.RedisConvertUtils;
import com.mpolicy.common.redis.key.ConstantKeys;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sys.dao.SysConfigDao;
import com.mpolicy.manage.modules.sys.entity.SysConfigEntity;
import com.mpolicy.manage.modules.sys.service.SysConfigService;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("sysConfigService")
public class SysConfigServiceImpl extends ServiceImpl<SysConfigDao, SysConfigEntity> implements SysConfigService {

	/**
	 * 配置值展示最大长度
	 */
	private final static Integer MAX_VALUE_SIZE = 30;

	/**
	 * 超限后展示
	 */
	private final static String EXCEED_DEFAULT_DISPLAY = "长度超限，点击修改查看详情";

	@Autowired
	private IRedisService redisService;

	@Override
	public PageUtils queryPage(Map<String, Object> params) {
		String paramKey = (String)params.get("paramKey");
		IPage<SysConfigEntity> page = this.page(
				new Query<SysConfigEntity>().getPage(params),
				new QueryWrapper<SysConfigEntity>()
						.like(StringUtils.isNotBlank(paramKey), "param_key", paramKey)
						.eq("status", 1)
						.orderByAsc("id")
		);

		// 长度超过指定长度后，替换为指定字符
		List<SysConfigEntity> records = page.getRecords();
		if (records != null && !records.isEmpty()) {
			records = records.stream().peek(record -> {
				if (record.getParamValue().length() > MAX_VALUE_SIZE) {
					record.setParamValue(EXCEED_DEFAULT_DISPLAY);
				}
			}).collect(Collectors.toList());
		}
		page.setRecords(records);

		return new PageUtils(page);
	}


	@Override
	public void saveConfig(SysConfigEntity config) {
		this.save(config);
		redisService.set(ConstantKeys.fbmsConfig, config.getParamKey(), config);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(SysConfigEntity config) {
		this.updateById(config);
		redisService.set(ConstantKeys.fbmsConfig, config.getParamKey(), config);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateValueByKey(String key, String value) {
		baseMapper.updateValueByKey(key, value);
		redisService.delete(ConstantKeys.fbmsConfig, key);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBatch(Long[] ids) {
		for (Long id : ids) {
			SysConfigEntity config = this.getById(id);
			redisService.delete(ConstantKeys.fbmsConfig, config.getParamKey());
		}
		this.removeByIds(Arrays.asList(ids));
	}

	@Override
	public String getValue(String key, String defaultValue) {
		SysConfigEntity config = redisService.get(ConstantKeys.fbmsConfig, key, SysConfigEntity.class);
		// 缓存是否存在
		if (config == null) {
			config = baseMapper.queryByKey(key);
			// 数据库也不存在
			if(config == null){
				return defaultValue;
			}
			// 写入缓存
			redisService.set(ConstantKeys.fbmsConfig, config.getParamKey(), config);
		}
		return config.getParamValue() != null ? config.getParamValue() : defaultValue;
	}

	@Override
	public <T> T getConfigObject(String key, Class<T> clazz) {
		String value = getValue(key,null);
		if (StringUtils.isNotBlank(value)) {
			return RedisConvertUtils.stringToBean(value, clazz);
		}

		try {
			return clazz.newInstance();
		} catch (Exception e) {
			throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("获取参数失败"));
		}
	}

	@Override
	public void loadAll() {
		List<SysConfigEntity> list = this.list(new QueryWrapper<SysConfigEntity>().eq("status", 1));
		list.forEach(config ->{
			redisService.set(ConstantKeys.fbmsConfig, config.getParamKey(), config);
		});
	}
}
