package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentCertificateEntity;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentCertificateVo;

import java.util.List;
import java.util.Map;

/**
 * 经纪人人员证件信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
public interface AgentCertificateService extends IService<AgentCertificateEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void saveOrUpdateAgentCertificateList(String agentCode, List<AgentCertificateEntity> agentCertificateEntityList);

    boolean delete(String agentCode);

    /**
     * 代理人证件列表
     * @param agentCode
     * @return
     */
    List<AgentCertificateVo> findAgentCertificateList(String agentCode);
}