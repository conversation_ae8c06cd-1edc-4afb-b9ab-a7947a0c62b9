package com.mpolicy.manage.modules.policy.vo.claim;

import com.mpolicy.manage.modules.policy.enums.ClaimDocumentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 理赔申请详情纪录
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "理赔申请详情信息")
@Data
public class ClaimApplyDetail extends ClaimApplyInfo {

    /**
     * 理赔所需材料编码
     */
    @ApiModelProperty(value = "理赔所需材料集合")
    private List<ClaimNeedDocumentType> needDocumentList;

    /**
     * 预审转待更新材料说明
     */
    private String modifyDocumentSummary;

    /**
     * 理赔流水纪录
     */
    @ApiModelProperty(value = "理赔流水纪录")
    private List<ClaimApplyFlow> applyFlows;

    /**
     * 理赔已申请材料集合
     */
    @ApiModelProperty(value = "理赔已申请材料集合")
    private List<ClaimDocument> documents;

    /**
     * 拒赔说明
     */
    private String refusedDesc;
}
