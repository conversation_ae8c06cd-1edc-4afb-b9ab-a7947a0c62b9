package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentSignFileDetailEntity;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonDetailPageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentPersonFilePageList;
import com.mpolicy.manage.modules.agent.vo.sign.AgentSignNumOut;

import java.util.List;
import java.util.Map;

/**
 * 代理人签署文件详细信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-05-29 17:54:25
 */
public interface AgentSignFileDetailService extends IService<AgentSignFileDetailEntity> {

    /**
     * 根据文件编码统计签署人数
     * @param fileCode 文件编码
     * @param orgCodeList 文件编码
     * @return AgentSignNumOut 签署信息
     */
    AgentSignNumOut querySignNumByFileCode(String fileCode, List<String> orgCodeList);

    /**
     * 根据文件编码和公司统计签署人数
     * @param fileCode 文件编码
     * @param companyIdList 配置公司信息id
     * @return
     */
    AgentSignNumOut querySignNumByCodeAndCompanyId(String fileCode, List<Integer> companyIdList);

    /**
     * 代理人签署文件列表
     * @param params
     * @return
     */
    PageUtils<AgentPersonFilePageList> personFileList(Map<String, Object> params);

    /**
     * 指定代理人签署文件详情
     * @param agentCode 代理人编码
     * @param params 分页参数
     * @return
     */
    PageUtils<AgentPersonDetailPageList > detailPersonFileList(String agentCode, Map<String, Object> params);

    /**
     * 新代理人生成要签署的文件信息
     * @param agentCode 代理人编码
     */
    void generateFile(String agentCode);
}

