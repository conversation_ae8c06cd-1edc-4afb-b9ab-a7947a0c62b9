package com.mpolicy.manage.modules.common.service;

import cn.hutool.core.date.DateTime;
import com.mpolicy.manage.modules.agent.vo.agentinfo.UpdateAreaManagerRegionVo;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 */
public interface AsyncService {

    /**
     * 商品下架时间变更,发送提示信息
     *
     * @param productCode
     * @param productName
     * @param stopSellTime
     */
    @Async
    void sendSellStopTips(String productCode, String productName, DateTime stopSellTime);

    /**
     * 商品上架提醒
     *
     * @param productCode
     * @param productAbbreviation
     */
    @Async
    void sendSellStartTips(String productCode, String productAbbreviation);
    /**
     * 商品下架架提醒
     *
     * @param productCode
     * @param productAbbreviation
     */
    @Async
    void sendSellDownTips(String productCode, String productAbbreviation);

    /**
     * 转移客户
     * @param update
     */
    void transferCustomer(UpdateAreaManagerRegionVo update);

    /**
     * 转移保单
     * @param update
     */
    void transferPolicy(UpdateAreaManagerRegionVo update);
}
