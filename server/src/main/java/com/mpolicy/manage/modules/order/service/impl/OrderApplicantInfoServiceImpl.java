package com.mpolicy.manage.modules.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.web.common.utils.Query;
import java.util.Map;

import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.order.dao.OrderApplicantInfoDao;
import com.mpolicy.manage.modules.order.entity.OrderApplicantInfoEntity;
import com.mpolicy.manage.modules.order.service.OrderApplicantInfoService;


@Service("orderApplicantInfoService")
public class OrderApplicantInfoServiceImpl extends ServiceImpl<OrderApplicantInfoDao, OrderApplicantInfoEntity> implements OrderApplicantInfoService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<OrderApplicantInfoEntity> page = this.page(
                new Query<OrderApplicantInfoEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }
}
