package com.mpolicy.manage.modules.label.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelLibraryInfoListOut implements Serializable {
    private static final long serialVersionUID = 1241742990602418988L;

    @ApiModelProperty(value = "标签库编码")
    private String libraryCode;

    @ApiModelProperty(value = "标签库名称")
    private String libraryName;

    @ApiModelProperty(value = "标签库备注")
    private String remark;
}
