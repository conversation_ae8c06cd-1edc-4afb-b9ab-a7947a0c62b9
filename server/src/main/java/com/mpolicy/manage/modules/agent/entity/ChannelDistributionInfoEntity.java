package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mpolicy.web.common.validator.group.AddGroup;
import com.mpolicy.web.common.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 分销渠道信息表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2023-02-06 15:55:51
 */
@TableName("channel_distribution_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelDistributionInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(hidden = true)
    @TableId
    private Integer id;
    /**
     * 渠道编码
     */
    @ApiModelProperty(value = "渠道编码")
    @NotBlank(message = "渠道编码不能为空", groups = {AddGroup.class})
    @Length(max = 30,message = "渠道编码最多30个字符", groups = {AddGroup.class, UpdateGroup.class})
    private String channelCode;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称", required = true)
    @NotBlank(message = "渠道名称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @Length(max = 100,message = "渠道名称最多100个字符", groups = {AddGroup.class, UpdateGroup.class})
    private String channelName;
    /**
     * 启用状态 1:启用;0:关闭
     */
    @ApiModelProperty(value = "启用状态", example = "启用状态 1:启用;0:关闭")
    private Integer enabled;
    /**
     * 逻辑删除 0:存在;-1:删除
     */
    @ApiModelProperty(hidden = true)
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人" ,hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间",hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人",hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间",hidden = true)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
}
