package com.mpolicy.manage.modules.sell.service.impl;

import com.mpolicy.manage.modules.sell.service.ISellProductToolService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("sellProductToolService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SellProductToolServiceImpl implements ISellProductToolService {
}
