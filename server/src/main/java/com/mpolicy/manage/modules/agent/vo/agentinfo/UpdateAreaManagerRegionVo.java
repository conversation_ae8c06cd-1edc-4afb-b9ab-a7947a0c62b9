package com.mpolicy.manage.modules.agent.vo.agentinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @program: mpolicy-admin
 * @description: 变更代理人区域信息
 * @author: lsc
 * @created: 2022/09/20 12:29
 */
@Data
public class UpdateAreaManagerRegionVo implements Serializable {
    private static final long serialVersionUID = 7830300786619882278L;

    @ApiModelProperty(value = "代理人编码", example = "42113213")
    @NotBlank(message = "代理人编码不能为空")
    private String agentCode;

    @ApiModelProperty(value = "接受代理人编码", example = "42113213")
    @NotBlank(message = "接受代理人编码不能为空")
    private String replaceAgentCode;

    @ApiModelProperty(value = "转换的区域信息", example = "42113213")
    @NotBlank(message = "区域不能为空")
    private String areaManagerRegion;

    @ApiModelProperty(value = "转换的片区信息", example = "42113,213")
    private List<String> subregionList;

    @ApiModelProperty(value = "同时批量迁移所有原区域保险专家名下的客户", example = "0")
    private Integer isTransferCustomer;

    @ApiModelProperty(value = "同时批量迁移当前区域所有保单至新区域保险专家", example = "1")
    private Integer isTransferPolicy;

    @ApiModelProperty(value = "同时批量迁移当前区域所有客户和保单至新区域保险专家", example = "1")
    private Integer isTransferCustomerAndPolicy;

    @ApiModelProperty(value = "版本信息", example = "1")
    private long revision;


    @ApiModelProperty(value = "更新人", example = "42113213",hidden = true)
    private String updateUser;
}
