package com.mpolicy.manage.modules.policy.vo.claim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 理赔所需材料信息
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔所需材料信息")
@Data
public class ClaimNeedDocumentType {

    /**
     * 理赔材料类型
     */
    @ApiModelProperty(value = "理赔材料类型")
    private String claimDocumentCode;

    /**
     * 理赔材料类型
     */
    @ApiModelProperty(value = "理赔材料名称")
    private String claimDocumentName;
}
