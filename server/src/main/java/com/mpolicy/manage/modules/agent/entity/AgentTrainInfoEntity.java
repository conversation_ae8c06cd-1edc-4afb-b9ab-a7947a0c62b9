package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人培训信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-21 11:26:34
 */
@TableName("agent_train_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentTrainInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */

	private Integer id;
	/**
	 * 培训记录code
	 */
	@TableId(value = "train_code",type = IdType.INPUT)
	private String trainCode;
	/**
	 * 培训主题
	 */
	private String trainTopic;
	/**
	 * 培训内容
	 */
	private String trainContent;
	/**
	 * 培训开始时间
	 */
	private Date beginTime;
	/**
	 * 培训结束时间
	 */
	private Date endTime;
	/**
	 * 签到表处理状态0:未处理 1:已处理
	 */
	private Integer signStatus;
	/**
	 * 参会人数
	 */
	private Integer trainPeopleNumber;
	/**
	 * 参会时长(秒)
	 */
	private Long trainDuration;

	/**
	 * 是否为暂存 0:暂存 1:不是
	 */
	private Integer isTemp;
	/**
	 * 逻辑删除 0:存在;-1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
