package com.mpolicy.manage.modules.insure.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.CommonUtils;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.common.utils.date.DateUtils;
import com.mpolicy.manage.common.utils.RequestUtils;
import com.mpolicy.manage.modules.insurance.entity.InsuranceCompanyEntity;
import com.mpolicy.manage.modules.insurance.service.InsuranceCompanyService;
import com.mpolicy.manage.modules.insure.dao.InsureCompanyConfineDao;
import com.mpolicy.manage.modules.insure.entity.InsureCompanyConfineEntity;
import com.mpolicy.manage.modules.insure.service.InsureCompanyConfineService;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineOut;
import com.mpolicy.manage.modules.insure.vo.InsureCompanyConfineVO;
import com.mpolicy.manage.modules.insure.vo.InsureConfineStatusVO;
import com.mpolicy.web.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("insureCompanyConfineService")
public class InsureCompanyConfineServiceImpl extends ServiceImpl<InsureCompanyConfineDao, InsureCompanyConfineEntity> implements InsureCompanyConfineService {

    @Resource
    private InsureCompanyConfineDao insureCompanyConfineDao;
    @Resource
    private InsuranceCompanyService insuranceCompanyService;

    @Override
    public PageUtils<InsureCompanyConfineOut> queryPage(Map<String, Object> params) {

        String title = (String) params.get("title");
        String companyCodes = (String) params.get("companyCodes");
        String confineStartTime = (String) params.get("confineStartTime");
        String confineEndTime = (String) params.get("confineEndTime");
        String startTime = (String) params.get("startTime");
        String endTime = (String) params.get("endTime");
        Integer isStatus = RequestUtils.objectValueToInteger(params, "isStatus");
        String company = "";
        if (StringUtils.isNotBlank(companyCodes)) {
            List<String> companyList = Arrays.asList(companyCodes.split(","));
            for (int i = 0; i < companyList.size(); i++) {
                company += StrUtil.format("company_codes LIKE ( '%{}%' )", companyList.get(i));
                if (companyList.size() != i + 1) {
                    company += " or ";
                }
            }
            company = StrUtil.format("({})", company);
        }
        IPage<InsureCompanyConfineEntity> page = this.page(
                new Query<InsureCompanyConfineEntity>().getPage(params),
                new LambdaQueryWrapper<InsureCompanyConfineEntity>()
                        .eq(InsureCompanyConfineEntity::getDeleted, 0)
                        .like(StringUtils.isNotBlank(title), InsureCompanyConfineEntity::getTitle, title)
                        .eq(isStatus != null, InsureCompanyConfineEntity::getIsStatus, isStatus)
                        .apply(StringUtils.isNotBlank(company), company)
                        .apply(StringUtils.isNotBlank(confineStartTime), "date_format(confine_start_time,'%Y-%m-%d') >= {0}", confineStartTime)
                        .apply(StringUtils.isNotBlank(confineEndTime), "date_format(confine_end_time,'%Y-%m-%d') <= {0}", confineEndTime)
                        .apply(StringUtils.isNotBlank(startTime), "date_format(create_time,'%Y-%m-%d') >= {0}", startTime)
                        .apply(StringUtils.isNotBlank(endTime), "date_format(create_time,'%Y-%m-%d') <= {0}", endTime)
                        .orderByDesc(InsureCompanyConfineEntity::getId)
        );
        ArrayList<InsureCompanyConfineOut> outs = Lists.newArrayList();
        for (InsureCompanyConfineEntity entity : page.getRecords()) {
            InsureCompanyConfineOut out = new InsureCompanyConfineOut();
            BeanUtils.copyProperties(entity, out);
            out.setClientDesc(entity.getClientType() == 1 ? "小程序" : "未知");
            List<InsuranceCompanyEntity> companyList = insuranceCompanyService.lambdaQuery().in(InsuranceCompanyEntity::getCompanyCode, entity.getCompanyCodes().split(",")).list();
            out.setCompanyNames(companyList.stream().map(InsuranceCompanyEntity::getShortName).collect(Collectors.joining(",")));
            JSONObject param = new JSONObject();
            param.put("productCodes", Arrays.asList(entity.getProductCodes().split(",")));
            List<JSONObject> productList = insureCompanyConfineDao.getProductList(param);
            out.setProductNames(productList.stream().map(x -> x.getString("productName")).collect(Collectors.joining(",")));
            outs.add(out);
        }
        return new PageUtils<>(outs, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<JSONObject> getCompanyList() {
        return insureCompanyConfineDao.getCompanyList();
    }

    @Override
    public List<JSONObject> getProductList(List<String> companyCodes) {
        JSONObject param = new JSONObject();
        param.put("companyCodes", companyCodes);
        param.put("productStatus", "1");
        return insureCompanyConfineDao.getProductList(param);
    }

    @Override
    public void updateStatus(InsureConfineStatusVO insureConfineStatusVO) {
        InsureCompanyConfineEntity entity = Optional.ofNullable(this.lambdaQuery()
                .eq(InsureCompanyConfineEntity::getDeleted, 0)
                .eq(InsureCompanyConfineEntity::getConfineCode, insureConfineStatusVO.getConfineCode())
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("升级配置不存在")));
        entity.setIsStatus(insureConfineStatusVO.getIsStatus());
        this.updateById(entity);
    }

    @Override
    public void saveUpdate(InsureCompanyConfineVO vo) {
        InsureCompanyConfineEntity entity;
        if (StringUtils.isNotBlank(vo.getConfineCode())) {
            entity = Optional.ofNullable(this.lambdaQuery()
                    .eq(InsureCompanyConfineEntity::getDeleted, 0)
                    .eq(InsureCompanyConfineEntity::getConfineCode, vo.getConfineCode())
                    .last("limit 1")
                    .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("升级配置不存在")));
        } else {
            entity = new InsureCompanyConfineEntity();
            entity.setConfineCode(CommonUtils.createCodeLastNumber("IFC"));
        }
        entity.setTitle(vo.getTitle());
        entity.setCompanyCodes(vo.getCompanyCodes());
        entity.setProductCodes(vo.getProductCodes());
        entity.setIsStatus(vo.getIsStatus());
        entity.setConfineDesc(vo.getConfineDesc());
        entity.setConfineStartTime(DateUtils.convertString2Date(vo.getConfineStartTime()));
        entity.setConfineEndTime(DateUtils.convertString2Date(vo.getConfineEndTime()));
        this.saveOrUpdate(entity);
    }

    @Override
    public void deleteConfine(String confineCode) {
        InsureCompanyConfineEntity entity = Optional.ofNullable(this.lambdaQuery()
                .eq(InsureCompanyConfineEntity::getDeleted, 0)
                .eq(InsureCompanyConfineEntity::getConfineCode, confineCode)
                .last("limit 1")
                .one()).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("升级配置不存在")));
        this.removeById(entity);
    }
}
