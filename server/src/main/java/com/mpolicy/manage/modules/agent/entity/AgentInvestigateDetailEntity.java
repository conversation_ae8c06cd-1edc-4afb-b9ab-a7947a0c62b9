package com.mpolicy.manage.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理人问卷调查明细表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-10 16:03:04
 */
@TableName("bl_agent_investigate_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentInvestigateDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 经纪人工号
	 */
	private String agentCode;
	/**
	 * bl_agent_questionnaire表主键id
	 */
	private Integer questionnaireId;
	/**
	 * bl_agent_questionnaire_question表主键id
	 */
	private Integer questionId;
	/**
	 * bl_agent_questionnaire_answer表主键id
	 */
	private Integer answerId;
	/**
	 * 是否选中: 0:未选中 , 1:已选中
	 */
	private Integer isSelected;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@TableLogic
	private Integer deleted;
}
