package com.mpolicy.manage.modules.content.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.manage.modules.content.entity.ContentSaleProductRecordEntity;
import com.mpolicy.manage.modules.content.vo.ContentSaleProductVo;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.mpolicy.manage.modules.sys.entity.SysDocumentEntity;
import com.mpolicy.manage.modules.sys.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.content.dao.ContentSaleProductDao;
import com.mpolicy.manage.modules.content.entity.ContentSaleProductEntity;
import com.mpolicy.manage.modules.content.service.ContentSaleProductService;


@Service("contentSaleProductService")
@Slf4j
public class ContentSaleProductServiceImpl extends ServiceImpl<ContentSaleProductDao, ContentSaleProductEntity> implements ContentSaleProductService {
    @Autowired
    private StorageService storageService;

    @Autowired
    private SysDocumentService sysDocumentService;

    /**
     * 临时文件存储地址
     */
    @Value("${mp.download.folder:logs/}")
    protected String tempPath;

    @Override
    public ContentSaleProductRecordEntity resolveAndCount(String fileCode) {
        SysDocumentEntity document = Optional.ofNullable(
                sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("可售产品信息文件不存在")));

        // 生成到本地
        String localFilePath = tempPath.concat(document.getFileName());
        log.info("ossPath={}, localFilePath={}", document.getFilePath(), localFilePath);
        storageService.downloadFileToLocal(document.getFilePath(), localFilePath);

        List<Object> objects = ExcelUtil.readExcel(localFilePath, new ContentSaleProductVo());

        // 处理文件，转换为list
        List<ContentSaleProductEntity> contentSaleProductList = objects.stream().map(item -> {
            ContentSaleProductEntity contentSaleProduct = new ContentSaleProductEntity();
            BeanUtils.copyProperties(item, contentSaleProduct);
            return contentSaleProduct;
        }).collect(Collectors.toList());

        this.remove(new QueryWrapper<>());
        baseMapper.insertBatchSomeColumn(contentSaleProductList);


        return ContentSaleProductRecordEntity.builder()
                .productCount(contentSaleProductList.size())
                .filePath(document.getFilePath()).build();
    }
}
