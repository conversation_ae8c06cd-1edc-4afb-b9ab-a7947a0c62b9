package com.mpolicy.manage.modules.insure.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 投保客户经理人脸识别白名单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-02 14:37:30
 */
@TableName("insure_customer_white_roster")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsureCustomerWhiteRosterEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 客户编码
	 */
	private String customerCode;
	/**
	 * 姓名
	 */
	private String realName;
	/**
	 * 证件号码
	 */
	private String cardNo;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 农保最后一级分支
	 */
	private String referrerChannelName;
	/**
	 * 有效开始时间
	 */
	private Date startDate;
	/**
	 * 有效结束时间
	 */
	private Date endDate;
	/**
	 * 是否长期有效：0：否，1：是
	 */
	private Integer isLongTerm;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 是否删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
