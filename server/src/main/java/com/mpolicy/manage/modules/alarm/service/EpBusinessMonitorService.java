package com.mpolicy.manage.modules.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.manage.modules.alarm.entity.EpBusinessMonitorEntity;
import com.mpolicy.manage.modules.alarm.enums.EnumAlarm;

/**
 * 业务告警信息
 *
 * <AUTHOR>
 */
public interface EpBusinessMonitorService extends IService<EpBusinessMonitorEntity> {

    /**
     * 保存告警信息
     *
     * @param alarm
     * @param message
     * @return
     */
    int saveOne(EnumAlarm alarm, String message);


    /**
     * 保存告警信息
     *
     * @param alarm
     * @param template
     * @param param
     * @return
     */
    int saveOne(EnumAlarm alarm, String template, Object... param);
}
