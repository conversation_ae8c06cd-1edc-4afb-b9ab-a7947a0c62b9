package com.mpolicy.manage.modules.agent.dao;

import com.mpolicy.manage.modules.agent.entity.AgentStudioIpEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentStudioIpVo;

/**
 * 人员扩展信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-05-19 11:09:17
 */
public interface AgentStudioIpDao extends BaseMapper<AgentStudioIpEntity> {


    /**
     * 获取代理人工作室信息
     * @param agentCode
     * @return
     */
    AgentStudioIpVo findAgentStudioIpByCode(String agentCode);
}
