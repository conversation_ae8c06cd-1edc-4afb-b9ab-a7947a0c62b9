package com.mpolicy.manage.modules.insure.vo;

import com.mpolicy.order.common.order.PolicyBasicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 投保回溯详情
 *
 * <AUTHOR>
 * @date 2022-05-29 13:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "投保回溯详情")
public class InsureRecallOut extends PolicyBasicInfo {

    @ApiModelProperty(value = "订单回溯集合信息")
    private List<InsureRecallList> recallList;
}