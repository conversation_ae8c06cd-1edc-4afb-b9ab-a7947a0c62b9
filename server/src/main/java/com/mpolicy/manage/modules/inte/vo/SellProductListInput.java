package com.mpolicy.manage.modules.inte.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * ClassName: SellProductListInput
 * Description: 智能推荐销售产品列表查询条件
 * date: 2023/4/10 16:06
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "智能推荐销售产品列表查询条件")
public class SellProductListInput {

    @ApiModelProperty(value = "产品名称")
    @NotBlank(message = "查询条件不能为空")
    private String name;
}
