package com.mpolicy.manage.modules.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description
 *
 *
 * 导入时用到的数据对象
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Data
public class InsureOrderCustomerRiskInfoExportExcel extends BaseRowModel {

    /**
     * 客户姓名
     */
    @NotBlank
    @ExcelProperty(value = "客户姓名", index = 0)
    private String customerName;

    /**
     * 证件号码
     */
    @NotBlank
    @ExcelProperty(value = "证件号码", index = 1)
    private String identityNumber;

    /**
     * 证件类型
     */
    @NotBlank
    @ExcelProperty(value = "证件类型", index = 2)
    private String identityType;

    /**
     * 客户类型 0 个人，1是企业
     */
    @NotBlank
    @ExcelProperty(value = "客户类型", index = 3)
    private String customerType;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄", index = 4)
    private Integer age;

    /**
     * 性别 0是女，1是男
     */
    @ExcelProperty(value = "性别", index = 5)
    private String gender;

    /**
     * 婚姻状态 0未婚, 1已婚, 2离异, 3丧偶
     */
    @ExcelProperty(value = "婚姻状态", index = 6)
    private String maritalStatus;

    /**
     * 信用分值
     */
    @ExcelProperty(value = "信用分值", index = 7)
    private Integer creditScore;

    /**
     *
     *
     * 首次投保获取信息时投保产品名称
     *
     *
     */
    @ExcelProperty(value = "投保产品名称", index = 8)
    private String firstInsureProductName;

    /**
     * 评分等级
     */
    @ExcelProperty(value = "评分等级", index = 9)
    @NotBlank
    private String ratingLevel;

    /**
     * 风险来源 0系统，1导入
     */
    @NotBlank
    @ExcelProperty(value = "风险来源", index =10)

    private String riskSource;

    /**
     * 渠道来源 0中和农信渠道，1程序渠道
     */
    @ExcelProperty(value = "渠道来源", index = 11)
    private String channelSource;

    /**
     * 是否是白名单  0不是，1是
     */
    @ExcelProperty(value = "是否是白名单", index = 12)
    private String isWhite;


    /**
     *
     * 是否承保
     *
     */
    @ExcelProperty(value = "是否承保", index = 13)
    private String isAccept;
}
