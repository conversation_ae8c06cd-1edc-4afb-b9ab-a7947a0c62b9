package com.mpolicy.manage.modules.policy.vo.claim;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 理赔申请详情纪录
 *
 * <AUTHOR>
 * @date 2022-03-16 10:31
 */
@ApiModel(value = "理赔申请基本信息")
@Data
public class ClaimApplyInfo {

    /**
     * 理赔服务单号
     */
    @ApiModelProperty(value = "理赔服务单号")
    private String claimNo;

    /**
     * 申请客户编号
     */
    @ApiModelProperty(value = "申请客户编号")
    private String customerCode;

    /**
     * 申请客户名称
     */
    @ApiModelProperty(value = "申请客户名称")
    private String customerName;

    /**
     * 小程序客户证据号码
     */
    @ApiModelProperty(value = "小程序客户证据号码")
    private String certNo;

    /**
     * 保单中心保单唯一编号
     */
    @ApiModelProperty(value = "保单中心保单唯一编号")
    private String contractCode;

    /**
     * 理赔保单名称
     */
    @ApiModelProperty(value = "理赔保单号")
    private String policyCode;

    /**
     * 理赔保单名称
     */
    @ApiModelProperty(value = "理赔保单名称")
    private String policyName;

    /**
     * 被保人名称
     */
    @ApiModelProperty(value = "被保人名称")
    private String insuredName;

    /**
     * 投保人名称
     */
    @ApiModelProperty(value = "投保人名称")
    private String holderName;

    /**
     * 理赔联系手机号码
     */
    @ApiModelProperty(value = "理赔联系手机号码")
    private String claimMobile;

    /**
     * 理赔说明
     */
    @ApiModelProperty(value = "理赔说明")
    private String claimCommitSummary;

    /**
     * 创建来源 1客户申请2管理员
     */
    @ApiModelProperty(value = "创建来源 1客户申请2管理员", example = "1")
    private Integer createSource;

    /**
     * 理赔状态 1已创建2待上传材料3预审中4待更新材料5预审不通过6预审通过7已赔付8保险公司拒赔9已取消
     */
    @ApiModelProperty(value = "理赔状态 1已创建2待上传材料3预审中4待更新材料5预审不通过6预审通过7已赔付8保险公司拒赔9已取消", example = "1")
    private Integer claimStatus;

    /**
     * 理赔状态说明
     */
    @ApiModelProperty(value = "理赔状态说明", example = "待上传材料")
    private String claimStatusName;

    /**
     * 客户创建时间
     */
    @ApiModelProperty(value = "理赔创建时间", example = "2019-01-01 15:12")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
}
