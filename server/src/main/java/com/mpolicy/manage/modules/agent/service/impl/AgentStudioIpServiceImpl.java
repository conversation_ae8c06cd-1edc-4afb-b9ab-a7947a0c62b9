package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.dao.AgentStudioIpDao;
import com.mpolicy.manage.modules.agent.entity.AgentStudioIpEntity;
import com.mpolicy.manage.modules.agent.enums.AgentIpContentPlatformEnum;
import com.mpolicy.manage.modules.agent.service.AgentStudioIpService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentStudioIpVo;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("agentStudioIpService")
public class AgentStudioIpServiceImpl extends ServiceImpl<AgentStudioIpDao, AgentStudioIpEntity> implements AgentStudioIpService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentStudioIpEntity> page = this.page(
                new Query<AgentStudioIpEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Override
    public boolean changeCount(String agentCode, AgentIpContentPlatformEnum contentPlatformEnum, int size) {
        AgentStudioIpEntity agentStudioIpEntity = new AgentStudioIpEntity();

        switch (contentPlatformEnum) {
            case WEI_BO:
                agentStudioIpEntity.setWeiboCount(size);
                break;
            case DOU_YIN:
                agentStudioIpEntity.setDouyinCount(size);
                break;
            case WECHAT_PUBLIC_PLATFORM:
                agentStudioIpEntity.setWechatPublicPlatformCount(size);
                break;
            case ZHI_HU:
                agentStudioIpEntity.setZhihuCount(size);
                break;
            default:
                return false;
        }

        return this.update(
                agentStudioIpEntity,
                Wrappers.<AgentStudioIpEntity>lambdaQuery()
                        .eq(AgentStudioIpEntity::getAgentCode, agentCode)
        );
    }


    /**
     * 获取代理人工作室信息
     * @param agentCode
     * @return
     */
    @Override
    public AgentStudioIpVo findAgentStudioIpByCode(String agentCode) {
        return baseMapper.findAgentStudioIpByCode(agentCode);
    }
}
