package com.mpolicy.manage.modules.policy.vo.preservation;

import com.mpolicy.manage.modules.policy.validate.PreservationSupplementValidate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel("保全-险种&被保人信息")
public class PreservationProductInsuredMapVo {
    @ApiModelProperty("被保人编号")
    private String insuredCode;

    @ApiModelProperty("被保人证件号")
    private String insuredIdCard;

    @ApiModelProperty("被保人名字")
    private String insuredName;

    @ApiModelProperty("保单险种编码")
    private String policyProductCode;

    @ApiModelProperty("保单主险标识")
    private String mainInsurance;

    @ApiModelProperty("险种编码")
    @NotBlank(message = "险种编码不能为空",groups = {PreservationSupplementValidate.class})
    private String productCode;

    @ApiModelProperty("险种编码")
    private String productName;

    @ApiModelProperty("计划编码")
    private String planCode;

    @ApiModelProperty("被保人险种的状态")
    @NotBlank(message = "被保人险种的状态不能为空",groups = {PreservationSupplementValidate.class})
    private String productStatus;

    @ApiModelProperty("被保人险种承保保费")
    @NotNull(message = "被保人险种承保保费不能为空")
    private BigDecimal premium;

    @ApiModelProperty("被保人险种-退保保费")
    private BigDecimal surrenderPremium;

    @ApiModelProperty("保障期间类型")
    private String insuredPeriodType;

    @ApiModelProperty("保障时长")
    private Integer insuredPeriod;

    @ApiModelProperty("缴费方式-年交/半年交/季交/月交/趸交/不定期交/短险一次交清")
    private String periodType;

    @ApiModelProperty("缴费期间类型")
    private String paymentPeriodType;

    @ApiModelProperty("缴费时长")
    private Integer paymentPeriod;

    @ApiModelProperty("保额")
    private BigDecimal coverage;

    @ApiModelProperty("业务变更-保费")
    @NotNull(message = "业务变更保费不能为空",groups = {PreservationSupplementValidate.class})
    private BigDecimal businessPremium;
}
