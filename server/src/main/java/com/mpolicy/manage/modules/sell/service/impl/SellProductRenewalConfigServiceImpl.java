package com.mpolicy.manage.modules.sell.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.common.Constant;
import com.mpolicy.manage.modules.insurance.entity.InsurancePortfolioInfoEntity;
import com.mpolicy.manage.modules.insurance.service.InsurancePortfolioInfoService;
import com.mpolicy.manage.modules.insurance.vo.CompanyItem;
import com.mpolicy.manage.modules.sell.dao.SellProductConfigDao;
import com.mpolicy.manage.modules.sell.dao.SellProductRenewalConfigDao;
import com.mpolicy.manage.modules.sell.entity.SellProductInfoEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductRenewalConfigEntity;
import com.mpolicy.manage.modules.sell.enums.RenewalChannelEnum;
import com.mpolicy.manage.modules.sell.enums.RenewalTypeEnum;
import com.mpolicy.manage.modules.sell.service.ISellProductService;
import com.mpolicy.manage.modules.sell.service.SellProductRenewalConfigService;
import com.mpolicy.manage.modules.sell.vo.RenewalProductConfigBase;
import com.mpolicy.manage.modules.sell.vo.RenewalProductConfigExcel;
import com.mpolicy.manage.modules.sell.vo.RenewalProductConfigInfo;
import com.mpolicy.manage.modules.sell.vo.RenewalProductItem;
import com.mpolicy.service.common.service.ConstantCacheHelper;
import com.mpolicy.web.common.utils.Query;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;


@Service("sellProductRenewalConfigService")
@Slf4j
public class SellProductRenewalConfigServiceImpl extends ServiceImpl<SellProductRenewalConfigDao, SellProductRenewalConfigEntity> implements SellProductRenewalConfigService {


    @Autowired
    private InsurancePortfolioInfoService insurancePortfolioInfoService;

    @Autowired
    private ISellProductService iSellProductService;

    @Override
    public List<CompanyItem> queryRenewalConfigFinishCompanyList(RenewalChannelEnum channel) {
        return lambdaQuery()
                .eq(channel != null, SellProductRenewalConfigEntity::getRenewalChannel, channel != null ? channel.getCode() : "")
                .list().stream().map(x -> {
                    CompanyItem item = new CompanyItem();
                    item.setCompanyCode(x.getCompanyCode());
                    item.setCompanyName(x.getCompanyName());
                    return item;
                }).distinct().collect(Collectors.toList());
    }

    @Override
    public List<CompanyItem> queryRenewalConfigCompanyList(RenewalChannelEnum channel) {
        List<CompanyItem> result = new ArrayList<>();
        switch (channel) {
            case XIAOWHALE: {
                log.info("小鲸");
                // 获取所有上架商品的保司信息
                result = insurancePortfolioInfoService.lambdaQuery()
                        .inSql(InsurancePortfolioInfoEntity::getPortfolioCode, "select portfolio_code from sell_product_info where deleted = 0")
                        .list().stream().map(x -> {
                            CompanyItem bean = new CompanyItem();
                            bean.setCompanyCode(x.getCompanyCode());
                            bean.setCompanyName(x.getCompanyName());
                            return bean;
                        }).distinct().collect(Collectors.toList());
                break;
            }
            case ZHNX: {
                // 读取redis配置的农保上架商品包含的保司集合信息
                result = JSON.parseArray(ConstantCacheHelper.getValue(Constant.ZHNX_RENEWAL_PRODUCT_LIST, "[]")).stream().map(x -> {
                    JSONObject item = (JSONObject) x;
                    CompanyItem bean = new CompanyItem();
                    bean.setCompanyCode(item.getString("companyCode"));
                    bean.setCompanyName(item.getString("companyName"));
                    return bean;
                }).distinct().collect(Collectors.toList());
                break;
            }
            default: {
                log.warn("暂不支持此渠道来源配置续购，渠道编码={}", channel.getCode());
            }
        }
        return result;
    }

    @Override
    public List<RenewalProductItem> queryRenewalConfigFinishProductList(RenewalChannelEnum channel, String companyCode, int type) {
        return lambdaQuery()
                .eq(StringUtils.isNotBlank(companyCode), SellProductRenewalConfigEntity::getCompanyCode, companyCode)
                .eq(channel != null, SellProductRenewalConfigEntity::getRenewalChannel, channel != null ? channel.getCode() : "")
                .list().stream().map(x -> {
                    RenewalProductItem item = new RenewalProductItem();
                    if (type == 1) {
                        item.setProductCode(x.getSourceProductCode());
                        item.setProductName(x.getSourceProductName());
                    } else {
                        item.setProductCode(x.getRenewalProductCode());
                        item.setProductName(x.getRenewalProductName());
                    }
                    return item;
                }).distinct().collect(Collectors.toList());
    }

    @Override
    public List<RenewalProductItem> queryRenewalConfigProductList(RenewalChannelEnum channel, String companyCode) {
        List<RenewalProductItem> result = new ArrayList<>();
        switch (channel) {
            case XIAOWHALE: {
                log.info("小鲸");
                result = iSellProductService.lambdaQuery()
                        .inSql(SellProductInfoEntity::getPortfolioCode, StrUtil.format("select portfolio_code from insurance_portfolio_info where company_code = '{}' and deleted = 0", companyCode))
                        .list().stream().map(x -> {
                            RenewalProductItem bean = new RenewalProductItem();
                            bean.setProductCode(x.getProductCode());
                            bean.setProductName(x.getProductName());
                            return bean;
                        }).distinct().collect(Collectors.toList());
                break;
            }
            case ZHNX: {
                log.info("农保");
                // 读取redis配置的农保上架商品包含的保司集合信息
                result = JSON.parseArray(ConstantCacheHelper.getValue(Constant.ZHNX_RENEWAL_PRODUCT_LIST, "[]")).stream().filter(f -> {
                    JSONObject item = (JSONObject) f;
                    return StringUtils.equals(companyCode, item.getString("companyCode"));
                }).map(x -> {
                    JSONObject item = (JSONObject) x;
                    RenewalProductItem bean = new RenewalProductItem();
                    bean.setProductCode(item.getString("productCode"));
                    bean.setProductName(item.getString("productName"));
                    return bean;
                }).collect(Collectors.toList());
                break;
            }
            default: {
                log.warn("暂不支持此渠道来源配置续购，渠道编码={}", channel.getCode());
            }
        }
        return result;
    }


    @Override
    public PageUtils<RenewalProductConfigBase> queryRenewalConfigPage(Map<String, Object> params) {
        String renewalChannel = (String) params.get("renewalChannel");
        String companyCode = (String) params.get("companyCode");
        String sourceProductCode = (String) params.get("sourceProductCode");
        String renewalProductCode = (String) params.get("renewalProductCode");

        // 分页查询
        IPage<SellProductRenewalConfigEntity> page = this.page(
                new Query<SellProductRenewalConfigEntity>().getPage(params),
                new LambdaQueryWrapper<SellProductRenewalConfigEntity>()
                        .eq(StringUtils.isNotBlank(renewalChannel), SellProductRenewalConfigEntity::getRenewalChannel, renewalChannel)
                        .eq(StringUtils.isNotBlank(companyCode), SellProductRenewalConfigEntity::getCompanyCode, companyCode)
                        .eq(StringUtils.isNotBlank(sourceProductCode), SellProductRenewalConfigEntity::getSourceProductCode, sourceProductCode)
                        .eq(StringUtils.isNotBlank(renewalProductCode), SellProductRenewalConfigEntity::getRenewalProductCode, renewalProductCode)
                        .orderByDesc(SellProductRenewalConfigEntity::getId)
        );

        // 构建返回的vo list
        List<RenewalProductConfigBase> result = new ArrayList<>();
        page.getRecords().forEach(x -> {
            RenewalProductConfigBase bean = new RenewalProductConfigBase();
            BeanUtils.copyProperties(x, bean);
            // 类型枚举转换
            Optional.ofNullable(RenewalChannelEnum.decode(x.getRenewalChannel())).ifPresent(t -> {
                bean.setRenewalChannelName(t.getName());
            });
            Optional.ofNullable(RenewalTypeEnum.decode(x.getRenewalType())).ifPresent(t -> {
                bean.setRenewalTypeName(t.getName());
            });
            result.add(bean);
        });

        // 重新构建分页返回对象
        return new PageUtils(result, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public void saveRenewalConfig(RenewalProductConfigInfo info) {
        // 校验是否存在重复配置续保
        List<SellProductRenewalConfigEntity> list = lambdaQuery()
                .eq(SellProductRenewalConfigEntity::getSourceProductCode, info.getSourceProductCode()).list();
        if (!list.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("已存在续购配置纪录，产品编码={}", info.getSourceProductCode())));
        }
        if (StringUtils.equals(RenewalTypeEnum.RENEWAL.getCode(), info.getRenewalType()) && StringUtils.isBlank(info.getAgainProductCode())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购类型重新投保信息不能为空"));
        }
        // 初始化保存对象
        SellProductRenewalConfigEntity bean = new SellProductRenewalConfigEntity();
        // 赋值
        BeanUtils.copyProperties(info, bean);
        save(bean);
    }

    @Override
    public void updateRenewalConfig(RenewalProductConfigInfo info) {
        // 校验是否存在重复配置续保
        List<SellProductRenewalConfigEntity> list = lambdaQuery()
                .eq(SellProductRenewalConfigEntity::getSourceProductCode, info.getSourceProductCode())
                .ne(SellProductRenewalConfigEntity::getId, info.getId()).list();
        if (!list.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("已存在续购配置纪录，产品编码={}", info.getSourceProductCode())));
        }
        if (StringUtils.equals(RenewalTypeEnum.RENEWAL.getCode(), info.getRenewalType()) && StringUtils.isBlank(info.getAgainProductCode())) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("续购类型重新投保信息不能为空"));
        }
        // 初始化保存对象
        SellProductRenewalConfigEntity bean = new SellProductRenewalConfigEntity();
        // 赋值
        BeanUtils.copyProperties(info, bean);
        if (!updateById(bean)) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("操作更新失败，请刷新后重试"));
        }
    }

    @Override
    public List<RenewalProductConfigExcel> queryRenewalConfigExport(Map<String, Object> params) {

        params.put("limit", "2000");
        PageUtils<RenewalProductConfigBase> renewalProductConfigBasePageUtils = queryRenewalConfigPage(params);
        List<RenewalProductConfigBase> list = renewalProductConfigBasePageUtils.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<String, SellProductInfoEntity> productMap = iSellProductService.lambdaQuery()
                .in(SellProductInfoEntity::getProductCode, list.stream().map(RenewalProductConfigBase::getRenewalProductCode).distinct().collect(Collectors.toList()))
                .list()
                .stream().collect(Collectors.toMap(SellProductInfoEntity::getProductCode, Function.identity()));

        List<RenewalProductConfigExcel> collect = list.stream().map(s -> {
            RenewalProductConfigExcel renewalProductConfigExcel = new RenewalProductConfigExcel();
            BeanUtils.copyProperties(s, renewalProductConfigExcel);
            SellProductInfoEntity orDefault = productMap.getOrDefault(s.getRenewalProductCode(), new SellProductInfoEntity());
            renewalProductConfigExcel.setRenewalProductState(Objects.equals(orDefault.getProductStatus(), 1) ? "上架" : "下架");
            renewalProductConfigExcel.setRenewalDuration(String.format("前%d天 ~ 后%d天`",
                    Objects.isNull(s.getEndBeforeDay()) ? 0 : s.getEndBeforeDay(),
                    Objects.isNull(s.getEndAfterDay()) ? 0 : s.getEndAfterDay()));
            return renewalProductConfigExcel;
        }).collect(Collectors.toList());
        return collect;
    }
}
