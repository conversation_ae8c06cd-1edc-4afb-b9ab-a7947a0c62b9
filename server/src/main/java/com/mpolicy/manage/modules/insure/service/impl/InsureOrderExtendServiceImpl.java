package com.mpolicy.manage.modules.insure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.manage.modules.insure.dao.InsureOrderExtendDao;
import com.mpolicy.manage.modules.insure.entity.InsureOrderExtendEntity;
import com.mpolicy.manage.modules.insure.service.InsureOrderExtendService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投保订单扩展信息接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27
 */
@Service("insureOrderExtendService")
public class InsureOrderExtendServiceImpl extends ServiceImpl<InsureOrderExtendDao, InsureOrderExtendEntity> implements InsureOrderExtendService {


}
