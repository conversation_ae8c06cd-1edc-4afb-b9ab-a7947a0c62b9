package com.mpolicy.manage.modules.agent.controller;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mpolicy.common.result.BasicCodeMsg;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.mpolicy.manage.modules.agent.entity.AgentCertificateEntity;
import com.mpolicy.manage.modules.agent.service.AgentCertificateService;
import com.mpolicy.common.result.Result;


/**
 * 经纪人人员证件信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-03 15:33:47
 */
@RestController
@RequestMapping("sys/agentcertificate")
@Api(tags = "经纪人人员证件信息")
public class AgentCertificateController {

    @Autowired
    private AgentCertificateService agentCertificateService;


    /**
     * 列表
     */
    // @ApiOperation(value = "获取经纪人人员证件信息列表")
    @GetMapping("/list/{agentCode}")
    public Result<List<AgentCertificateEntity>> list(
            @ApiParam(value = "要获取的经纪人code", required = true)
            @PathVariable("agentCode") String agentCode) {
        List<AgentCertificateEntity> list = agentCertificateService.list(
                Wrappers.<AgentCertificateEntity>lambdaQuery()
                        .eq(AgentCertificateEntity::getAgentCode, agentCode)
        );
        return Result.success(list);
    }

    /**
     * 信息
     */
    // @ApiOperation(value = "获取经纪人人员证件列表")
    @GetMapping("/info/{id}")
    public Result<AgentCertificateEntity> info(@PathVariable("id") Integer id) {
        AgentCertificateEntity agentCertificate = agentCertificateService.getById(id);

        return Result.success(agentCertificate);
    }

    /**
     * 保存经纪人人员证件
     */
    // @ApiOperation(value = "保存经纪人人员证件", notes = "保存经纪人人员证件")
    @PostMapping("/save")
    public Result saveEntity(
            @ApiParam(value = "要保存人员证件的对象", required = true)
            @Validated @RequestBody AgentCertificateEntity agentCertificateEntity) {
        boolean save = agentCertificateService.save(agentCertificateEntity);
        if (save) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("保存失败"));
        }
    }

    /**
     * 修改经纪人人员证件
     */
    // @ApiOperation(value = "修改经纪人人员证件", notes = "修改经纪人人员证件")
    @PostMapping("/update")
    public Result updateEntity(
            @ApiParam(value = "要修改人员证件的对象", required = true)
            @Validated @RequestBody AgentCertificateEntity agentCertificateEntity) {
        boolean save = agentCertificateService.updateById(agentCertificateEntity);
        if (save) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("修改失败"));
        }
    }

    /**
     * 删除经纪人人员证件
     */
    // @ApiOperation(value = "删除经纪人人员证件", notes = "删除经纪人人员证件")
    @PostMapping("/delete/{id}")
    public Result delete(
            @ApiParam(value = "要删除人员证件的id", required = true)
            @PathVariable("id") Integer id) {
        boolean delete = agentCertificateService.removeById(id);
        if (delete) {
            return Result.success();
        } else {
            return Result.error(BasicCodeMsg.SERVER_ERROR.setMsg("删除失败"));
        }
    }
}
