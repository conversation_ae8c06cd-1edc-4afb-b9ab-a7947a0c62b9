package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.dao.AgentExtendDao;
import com.mpolicy.manage.modules.agent.entity.AgentExtendEntity;
import com.mpolicy.manage.modules.agent.service.AgentExtendService;
import com.mpolicy.manage.modules.agent.vo.agentinfo.AgentExtendVo;
import com.mpolicy.web.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("agentExtendService")
public class AgentExtendServiceImpl extends ServiceImpl<AgentExtendDao, AgentExtendEntity> implements AgentExtendService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<AgentExtendEntity> page = this.page(
                new Query<AgentExtendEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }


    /**
     * 获取代理人扩展信息
     * @param agentCode
     * @return
     */
    @Override
    public AgentExtendVo findAgentExtendByCode(String agentCode) {
        return baseMapper.findAgentExtendByCode(agentCode);
    }


}
