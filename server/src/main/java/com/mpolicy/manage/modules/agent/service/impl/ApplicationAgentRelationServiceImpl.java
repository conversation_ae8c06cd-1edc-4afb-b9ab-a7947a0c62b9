package com.mpolicy.manage.modules.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.ApplicationAgentRelationEntity;
import com.mpolicy.manage.modules.agent.vo.AgentInfoVo;
import com.mpolicy.web.common.utils.Query;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


import org.springframework.stereotype.Service;
import com.mpolicy.manage.modules.agent.dao.ApplicationAgentRelationDao;
import com.mpolicy.manage.modules.agent.service.ApplicationAgentRelationService;
import org.springframework.transaction.annotation.Transactional;


@Service("ApplicationAgentRelationService")
public class ApplicationAgentRelationServiceImpl extends ServiceImpl<ApplicationAgentRelationDao, ApplicationAgentRelationEntity> implements ApplicationAgentRelationService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<ApplicationAgentRelationEntity> page = this.page(
                new Query<ApplicationAgentRelationEntity>().getPage(params),
                new QueryWrapper<>()
        );
        return new PageUtils(page);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void saveOrUpdateAgentRelation(String applicationCode, List<AgentInfoVo> agentInfoVoList) {
        // 1.1、删除之前的代理人关系
        this.remove(new QueryWrapper<ApplicationAgentRelationEntity>().lambda().eq(ApplicationAgentRelationEntity::getApplicationCode, applicationCode));
        if (agentInfoVoList != null && agentInfoVoList.size() > 0) {
            // 1.2.1、准备代理人数据
            List<ApplicationAgentRelationEntity> collect = agentInfoVoList.stream().map(agentInfoVo -> {
                ApplicationAgentRelationEntity agentRelationEntity = new ApplicationAgentRelationEntity();
                agentRelationEntity.setAgentCode(agentInfoVo.getAgentCode());
                agentRelationEntity.setApplicationCode(applicationCode);
                return agentRelationEntity;
            }).collect(Collectors.toList());

            // 1.2.2、保存新的代理人
            baseMapper.insertBatchSomeColumn(collect);
        }
    }
}
