package com.mpolicy.manage.modules.sell.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.sell.entity.SellProductParamEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductSubscribeEntity;
import com.mpolicy.manage.modules.sell.entity.SellProductSubscribeListOut;
import com.mpolicy.manage.modules.sell.entity.SellProductSubscribeListVo;
import com.mpolicy.manage.modules.sell.vo.SellProductParamVo;

/**
 * 产品参数
 *
 * <AUTHOR>
 */
public interface SellProductParamService extends IService<SellProductParamEntity> {
    /**
     * 根据产品代码查询产品信息。
     *
     * @param productCode 产品的唯一标识码。
     * @return 返回包含产品详细信息的SellProductParamVo对象。
     */
    SellProductParamVo info(String productCode);

    /**
     * 保存或更新产品信息。
     *
     * @param productCode 产品的唯一标识码。
     * @param sellProductParamVo 包含待保存或更新的产品信息的SellProductParamVo对象。
     * @return 返回操作的结果，通常是一个表示操作成功或失败的整数。
     */
    Integer save(String productCode, SellProductParamVo sellProductParamVo);
}

