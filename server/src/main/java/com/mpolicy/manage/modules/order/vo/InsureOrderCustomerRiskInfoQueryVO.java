package com.mpolicy.manage.modules.order.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mpolicy.manage.modules.order.entity.InsureOrderCustomerRiskInfoEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 *
 *
 *
 * 订单风险客户查询VO
 *
 *
 *
 * @create 2024/10/17
 * @since 1.0.0
 */
@Data
public class InsureOrderCustomerRiskInfoQueryVO extends Page<InsureOrderCustomerRiskInfoEntity> {

    /**
     *
     * 客户名称
     *
     */
    private String customerName;

    /**
     *
     * 客户证件号
     *
     */
    private String identityNumber;

    /**
     *
     * 渠道来源
     *
     */
    private String channelSource;

    /**
     *
     *
     * 评分等级
     *
     *
     */
    private String ratingLevel;


    /**
     *
     *
     * 性别
     *
     *
     */
    private String gender;


    /**
     *
     *
     * 婚姻状态
     *
     *
     */
    private String maritalStatus;

}
