package com.mpolicy.manage.modules.agent.vo.train;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AgentTrainInfoListOut implements Serializable {
    private static final long serialVersionUID = -6956124507610275406L;

    @ApiModelProperty(value = "培训记录code")
    private String trainCode;

    @ApiModelProperty(value = "培训主题")
    private String trainTopic;

    @ApiModelProperty(value = "培训内容")
    private String trainContent;

    @ApiModelProperty(value = "培训开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @ApiModelProperty(value = "培训结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "培训的主持")
    private String trainHostName;

    @ApiModelProperty(value = "参会时长(小时)")
    private Long trainDuration;

    @ApiModelProperty(value = "考核结果0:未处理 1:通过 2:失败")
    private Integer assessmentResult;

    @ApiModelProperty(value = "签到状态0:未处理  1:已签到 2:未签到")
    private Integer signStatus;

}
