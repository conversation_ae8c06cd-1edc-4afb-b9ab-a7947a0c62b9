package com.mpolicy.manage.modules.policy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.policy.entity.PolicyUserRecordImageEntity;

import java.util.List;
import java.util.Map;

/**
 * 用户上传图片信息
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-03-11 17:24:10
 */
public interface PolicyUserRecordImageService extends IService<PolicyUserRecordImageEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 根据上传编号获取图片信息列表
     *
     * @param recordSn 上传编号
     * @return 图片信息列表
     */
    List<PolicyUserRecordImageEntity> list(String recordSn);
}

