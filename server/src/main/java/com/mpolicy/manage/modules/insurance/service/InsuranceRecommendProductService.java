package com.mpolicy.manage.modules.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.insurance.entity.InsuranceRecommendProductEntity;
import com.mpolicy.manage.modules.insurance.vo.RecommendGoodsBean;

import java.util.List;
import java.util.Map;

/**
 * 产品中心智能推荐产品信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-09-06 12:00:44
 */
public interface InsuranceRecommendProductService extends IService<InsuranceRecommendProductEntity> {

    /**
     * 分页查询
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    void move(List<RecommendGoodsBean> recommendGoodsBeanList);


}

