package com.mpolicy.manage.modules.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.manage.modules.agent.entity.AgentUserAccountEntity;

import java.util.Map;

/**
 * 经纪人账号表
 *
 * <AUTHOR> [<EMAIL>]
 * @date 2021-02-08 11:05:50
 */
public interface AgentUserAccountService extends IService<AgentUserAccountEntity> {

    /**
     * 分页查询
     *
     * @param paramMap
     * @return PageUtils
     */
    PageUtils queryPage(Map<String, Object> paramMap);

    /**
     * 创建或修改账号
     *
     * @param mobile    手机号
     * @param password  密码
     * @param agentCode 经纪人code
     * @param agentName 经纪人姓名
     * @param type      账号类型
     */
    void createOrUpdateAccount(String mobile, String password, String agentCode, String agentName, Integer type);
}

