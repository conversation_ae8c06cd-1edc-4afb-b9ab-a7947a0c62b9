package com.mpolicy.manage.modules.insure.dao;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mpolicy.manage.modules.insure.entity.InsureCompanyConfineEntity;

import java.util.List;

/**
 * 保司产品升级控制
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 10:34:28
 */
public interface InsureCompanyConfineDao extends BaseMapper<InsureCompanyConfineEntity> {

    /**
     * 获取有产品的保司
     * <AUTHOR>
     * @date 2022/10/9 14:54

     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     */
    List<JSONObject> getCompanyList();
    /**
     * 获取保司下的产品
     *
     * <AUTHOR>
     * @date 2022/10/9 15:53
     * @param param:
     * @return : java.util.List<com.alibaba.fastjson.JSONObject>
     */
    List<JSONObject> getProductList(JSONObject param);
}
