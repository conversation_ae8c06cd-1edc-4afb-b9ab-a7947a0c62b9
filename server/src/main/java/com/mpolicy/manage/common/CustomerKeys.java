package com.mpolicy.manage.common;

import com.mpolicy.common.redis.key.BasePrefix;

/**
 * 客户管理redis常量
 *
 * @ClassName: CustomerKeys
 * @description: redisKey
 * @author: haijun.sun
 * @date: 2021-01-26 17:21
 */
public class CustomerKeys extends BasePrefix {

    private CustomerKeys(String prefix) {
        super(prefix);
    }

    private CustomerKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
    }

    public static CustomerKeys SEARCH_TAG_NAME = new CustomerKeys(60 * 60 * 5, "SEARCH_TAG_NAME");

    /**
     * 代理人文件签署弹窗标识
     */
    public static CustomerKeys FILESTATUS = new CustomerKeys(60 * 60 * 24 * 30 * 12, "FILESTATUS");
}