package com.mpolicy.manage.common.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/2/22 18:12
 */
public class RequestUtils {
    /**
     * 从params的map中根据field名将空串转为null
     *
     * @param params map对象
     * @param field  字段名
     * @return 是否为null
     */
    public static String objectValueToString(Map<String, Object> params, Object field) {
        String fieldStr = null;
        if (params.get(field) != null && StringUtils.isNotBlank(params.get(field).toString())) {
            fieldStr = params.get(field).toString();
        }
        return fieldStr;
    }

    /**
     * 从params的map中根据field名将空串转为null
     *
     * @param params map对象
     * @param field  字段名
     * @return 是否为null
     */
    public static String[] objectValueToStringList(Map<String, Object> params, Object field) {
        if (!StrUtil.isBlankIfStr(params.get(field)) && !StrUtil.isBlankOrUndefined(params.get(field).toString())) {
            String fieldStr = params.get(field).toString();
            return fieldStr.split(",");
        }
        return null;
    }

    /**
     * 从params的map中根据field名将空串转为null
     *
     * @param params map对象
     * @param field  字段名
     * @return 是否为null
     */
    public static List<String> objectValueToList(Map<String, Object> params, Object field) {
        if (!StrUtil.isBlankIfStr(params.get(field)) && !StrUtil.isBlankOrUndefined(params.get(field).toString())) {
            String fieldStr = params.get(field).toString();
            return Arrays.asList(fieldStr.split(","));
        }
        return null;
    }

    /**
     * 从params的map中根据field名将空串转为null
     *
     * @param params map对象
     * @param field  字段名
     * @return 是否为null
     */
    public static Integer objectValueToInteger(Map<String, Object> params, Object field) {
        Integer result = null;
        if (!StrUtil.isBlankIfStr(params.get(field)) && !StrUtil.isBlankOrUndefined(params.get(field).toString())) {
            result = Integer.valueOf(params.get(field).toString());
        }
        return result;
    }

    /**
     * 空日期变为null
     *
     * @param original 原始日期
     * @return 若日期为空返回null，否则返回日期
     */
    public static String nonDateToBlankString(String original) {
        return StringUtils.isBlank(original) ? null : original;
    }
}
