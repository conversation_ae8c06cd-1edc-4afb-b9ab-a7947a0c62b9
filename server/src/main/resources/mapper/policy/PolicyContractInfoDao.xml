<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.policy.dao.PolicyContractInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <select id="export" resultType="com.mpolicy.manage.modules.policy.vo.PolicyExportVo">
        SELECT c.policy_no,
               c.policy_status,
               c.portfolio_name,
               c.applicant_time,
               c.reply_effective_date,
               c.revisit_time,
               c.company_name,
               c.approved_time,
               ci.channel_name,
               p.product_name,
               p.payment_period_type,
               p.payment_period,
               p.insured_period_type,
               p.insured_period,
               p.period_type,
               p.coverage,
               p.premium,
               p.effective_date,
               pai.applicant_name,
               pai.applicant_gender,
               pai.applicant_age,
               i.insured_name,
               i.insured_gender,
               i.insured_relation,
               a.agent_name,
               a.business_code
        FROM policy_product_info p
                 LEFT JOIN policy_insured_info i ON p.insured_code = i.insured_code
            AND i.deleted = 0
                 LEFT JOIN policy_contract_info c ON i.contract_code = c.contract_code
            AND c.deleted = 0
                 LEFT JOIN channel_info ci ON c.channel_code = ci.channel_code
            AND ci.deleted = 0
                 LEFT JOIN policy_applicant_info pai ON pai.contract_code = c.contract_code
            AND pai.deleted = 0
                 LEFT JOIN agent_user_info a ON c.agent_code = a.agent_code
        WHERE p.deleted = 0
          AND policy_source != 'POLICY_SOURCE:0'
          AND (YEAR(applicant_time) = YEAR(now()) OR YEAR(approved_time) = YEAR(now()));
    </select>

    <select id="getExportPolicyMaxEpcid" resultType="java.lang.Integer">
        SELECT epci.id AS id
        FROM ep_policy_contract_info epci
                 LEFT JOIN channel_info ci ON epci.channel_code = ci.channel_code
                 LEFT JOIN ep_policy_contract_extend epce ON epce.contract_code = epci.contract_code
                 LEFT JOIN channel_application_referrer car ON epci.referrer_code = car.referrer_code
                 LEFT JOIN agent_user_info auir ON epci.referrer_code = auir.agent_code
                 LEFT JOIN org_info oib ON epci.channel_branch_code = oib.org_code
                 LEFT JOIN channel_branch_info cbi ON epci.channel_branch_code = cbi.branch_code
                 LEFT JOIN ep_policy_vehicle_info eppvi ON eppvi.contract_code = epci.contract_code
                 LEFT JOIN ep_policy_agent_info epagi ON epagi.contract_code = epci.contract_code
            AND epagi.deleted = 0
                 LEFT JOIN agent_user_info aui ON epagi.agent_code = aui.agent_code
                 LEFT JOIN org_info oi ON epagi.org_code = oi.org_code
                 LEFT JOIN ep_policy_applicant_info epai ON epci.contract_code = epai.contract_code
                 LEFT JOIN ep_policy_product_info eppi ON eppi.contract_code = epci.contract_code
            AND eppi.deleted = 0 AND epci.policy_product_type != 'PRODUCT:PRODUCTGROUP:1'
                 LEFT JOIN ep_policy_insured_info epii ON epii.contract_code = epci.contract_code
            AND epii.deleted = 0 AND epci.policy_product_type = 'PRODUCT:PRODUCTGROUP:1'
            LEFT JOIN ep_policy_product_insured_map eppim ON eppim.insured_code = epii.insured_code
            AND eppim.deleted = 0
            ${ew.customSqlSegment}
    </select>

    <select id="exportPolicy" resultType="com.mpolicy.manage.modules.policy.vo.EpPolicyExportVo">
        SELECT epci.id                                                                     AS id,
               epci.policy_type                                                            AS policyType,
               epci.policy_product_type                                                    AS policyProductType,
               epci.sales_type                                                             AS salesType,
               epci.applicant_policy_no                                                    AS applicantPolicyNo,
               epci.source_policy_no                                                       AS sourcePolicyNo,
               epci.policy_no                                                              AS policyNo,
               epci.company_name                                                           AS companyName,
               epci.main_product_name                                                      AS mainProductName,
               epci.sales_platform                                                         AS salesPlatform,
               epci.admin_policy_status                                                    AS policyStatus,
               epci.self_preservation                                                      AS selfPreservation,
               epci.settlement_status                                                      AS settlementStatus,
               epci.settlement_year                                                        AS settlementYear,
               epci.settlement_month                                                       AS settlementMonth,
               epci.supervise_channel_code                                                 AS superviseChannelCode,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_name, eppi.product_name)                                   AS productName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.protocol_product_name,
                  eppi.protocol_product_name)                                              AS protocolProductName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_status,
                  eppi.product_status)                                                     AS productStatus,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.main_insurance,
                  eppi.main_insurance)                                                     AS mainInsurance,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period_type,
                  eppi.payment_period_type)                                                AS paymentPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period,
                  eppi.payment_period)                                                     AS paymentPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period_type,
                  eppi.insured_period_type)                                                AS insuredPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period,
                  eppi.insured_period)                                                     AS insuredPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.period_type,
                  eppi.period_type)                                                        AS periodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.premium, eppi.premium)                                             AS premium,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.coverage, eppi.coverage)                                           AS coverage,
               IF(epci.sales_type = 1, epagi.main_flag, NULL)                              AS mainFlag,
               IF(epci.sales_type = 1, aui.agent_name, NULL)                               AS agentName,
               IF(epci.sales_type = 1, aui.business_code, NULL)                            AS businessCode,
               IF(epci.sales_type = 1, oi.org_name, NULL)                                  AS orgName,
               IF(epci.sales_type = 1, oi.org_code, NULL)                                  AS orgCode,
               IF(epci.sales_type = 1, epagi.commission_rate, NULL)                        AS commissionRate,
               IF(epci.referrer_type = 0, car.referrer_name, auir.agent_name)              AS referrerName,
               IF(epci.referrer_type = 0, car.referrer_wno, auir.business_code)            AS referrerWno,
               IF(epci.referrer_type = 0, cbi.branch_name, oib.org_name)                   AS channelBranchName,
               ci.channel_name                                                             AS channelName,
               aui.business_code                                                           AS policyReferrerWno,
               aui.agent_name                                                              AS policyReferrerName,
               epci.order_time                                                             AS orderTime,
               epci.applicant_time                                                         AS applicantTime,
               approved_time                                                               AS approvedTime,
               enforce_time                                                                AS enforceTime,
               receipt_sign_time                                                           AS receiptSignTime,
               revisit_time                                                                AS revisitTime,
               revisit_result                                                              AS revisitResult,
               revisit_fail_reason                                                         AS revisitFailReason,
               cancel_time                                                                 AS cancelTime,
               decline_time                                                                AS declineTime,
               postpone_approved_time                                                      AS postponeApprovedTime,
               hesitate_period                                                             AS hesitatePeriod,
               over_hesitate_period                                                        AS overHesitatePeriod,
               epci.surrender_time                                                         AS surrenderTime,
               approved_record_time                                                        AS approvedRecordTime,
               receipt_record_time                                                         AS receiptRecordTime,
               failure_time                                                                AS failureTime,
               termination_time                                                            AS terminationTime,
               reply_effective_time                                                        AS replyEffectiveTime,
               applicant_type                                                              AS applicantType,
               epai.applicant_name                                                         AS applicantName,
               applicant_gender                                                            AS applicantGender,
               applicant_birthday                                                          AS applicantBirthday,
               applicant_id_type                                                           AS applicantIdType,
               applicant_id_card                                                           AS applicantIdCard,
               concat(DATE_FORMAT(applicant_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(applicant_id_card_permanent = 1, '永远',
                         DATE_FORMAT(applicant_id_card_validity_end, '%Y-%m-%d')))            applicantIdCardValidityEnd,
               applicant_mobile                                                            AS applicantMobile,
               applicant_nation                                                            AS applicantNation,
               applicant_marital                                                           AS applicantMarital,
               applicant_career                                                            AS applicantCareer,
               applicant_company                                                           AS applicantCompany,
               applicant_address                                                           AS applicantAddress,
               applicant_industry_category                                                 AS applicantIndustryCategory,
               epai.company_nature                                                         AS companyNature,
               epai.company_social_security_num                                            AS companySocialSecurityNum,
               epai.company_employee_num                                                   AS companyEmployeeNum,
               epai.company_contact_name                                                   AS companyContactName,
               epai.company_contact_mobile                                                 AS companyContactMobile,
               epai.legal_person_name                                                      AS legalPersonName,
               epai.legal_person_id_type                                                   AS legalPersonIdType,
               epai.legal_person_id_card                                                   AS legalPersonIdCard,
               concat(DATE_FORMAT(epai.legal_person_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(epai.legal_person_id_card_permanent = 1, '永远',
                         DATE_FORMAT(epai.legal_person_id_card_validity_end, '%Y-%m-%d'))) AS legalPersonIdCardValidityEnd,
               bank_name                                                                   AS bankName,
               card_no                                                                     AS cardNo,
               card_name                                                                   AS cardName,
               insured_type                                                                AS insuredType,
               insured_relation                                                            AS insuredRelation,
               epii.insured_name                                                           AS insuredName,
               insured_id_type                                                             AS insuredIdType,
               insured_id_card                                                             AS insuredIdCard,
               insured_gender                                                              AS insuredGender,
               insured_birthday                                                            AS insuredBirthday,
               concat(DATE_FORMAT(insured_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(insured_id_card_permanent = 1, '永远',
                         DATE_FORMAT(insured_id_card_validity_end, '%Y-%m-%d')))           AS insuredIdCardValidityEnd,
               insured_mobile                                                              AS insuredMobile,
               insured_nation                                                              AS insuredNation,
               insured_marital                                                             AS insuredMarital,
               insured_career                                                              AS insuredCareer,
               insured_company                                                             AS insuredCompany,
               insured_address                                                             AS insuredAddress,
               insured_email                                                               AS insuredEmail,
               insured_education                                                           AS insuredEducation,
               insurance_subject_matter                                                    AS insuranceSubjectMatter,
               vehicle_owner_name                                                          AS vehicleOwnerName,
               vehicle_owner_mobile                                                        AS vehicleOwnerMobile,
               vehicle_owner_card_type                                                     AS vehicleOwnerCardType,
               vehicle_owner_nature                                                        AS vehicleOwnerNature,
               vehicle_owner_card_no                                                       AS vehicleOwnerCardNo,
               vehicle_owner_gender                                                        AS vehicleOwnerGender,
               vehicle_owner_birthday                                                      AS vehicleOwnerBirthday,
               vehicle_license_plate_number                                                AS vehicleLicensePlateNumber,
               vehicle_initial_registry_date                                               AS vehicleInitialRegistryDate,
               vehicle_usage                                                               AS vehicleUsage,
               vehicle_brand_name                                                          AS vehicleBrandName,
               vehicle_type                                                                AS vehicleType,
               vehicle_frame_number                                                        AS vehicleFrameNumber,
               vehicle_engine_number                                                       AS vehicleEngineNumber,
               vehicle_variety                                                             AS vehicleVariety,
               vehicle_size                                                                AS vehicleSize,
               vehicle_tonnage                                                             AS vehicleTonnage,
               vehicle_volume                                                              AS vehicleVolume,
               vehicle_power                                                               AS vehiclePower,
               vehicle_travel_miles                                                        AS vehicleTravelMiles,
               vehicle_price                                                               AS vehiclePrice,
               vehicle_paid_times                                                          AS vehiclePaidTimes,
               vehicle_is_imported                                                         AS vehicleIsImported,
               vehicle_new                                                                 AS vehicleNew,
               vehicle_transfer_ownership                                                  AS vehicleTransferOwnership,
               cdi.channel_name                                                            As channelDistributionName
        FROM ep_policy_contract_info epci
                 LEFT JOIN channel_info ci ON epci.channel_code = ci.channel_code
                 LEFT JOIN ep_policy_contract_extend epce ON epce.contract_code = epci.contract_code
                 LEFT JOIN channel_application_referrer car ON epci.referrer_code = car.referrer_code
                 LEFT JOIN agent_user_info auir ON epci.referrer_code = auir.agent_code
                 LEFT JOIN org_info oib ON epci.channel_branch_code = oib.org_code
                 LEFT JOIN channel_branch_info cbi ON epci.channel_branch_code = cbi.branch_code
                 LEFT JOIN ep_policy_vehicle_info eppvi ON eppvi.contract_code = epci.contract_code
                 LEFT JOIN ep_policy_agent_info epagi ON epagi.contract_code = epci.contract_code
            AND epagi.deleted = 0
                 LEFT JOIN agent_user_info aui ON epagi.agent_code = aui.agent_code
                 LEFT JOIN org_info oi ON epagi.org_code = oi.org_code
                 LEFT JOIN ep_policy_applicant_info epai ON epci.contract_code = epai.contract_code
                 LEFT JOIN ep_policy_product_info eppi ON eppi.contract_code = epci.contract_code
            AND eppi.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:3', 'PRODUCT:PRODUCTGROUP:4')
                 LEFT JOIN ep_policy_insured_info epii ON epii.contract_code = epci.contract_code
            AND epii.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2')
                 LEFT JOIN ep_policy_product_insured_map eppim ON eppim.insured_code = epii.insured_code
            AND eppim.deleted = 0
                 LEFT JOIN channel_distribution_info cdi on epci.channel_distribution_code = cdi.channel_code
            ${ew.customSqlSegment}
    </select>
    <!--2022-11-20 pc006 exportPolicy功能变更为 exportPolicyV2-->
    <select id="exportPolicyV2" resultType="com.mpolicy.manage.modules.policy.vo.EpV2PolicyExportVo">
        SELECT epci.id                                                                     AS id,
               epci.policy_type                                                            AS policyType,
               epci.policy_product_type                                                    AS policyProductType,
               epci.sales_type                                                             AS salesType,
               epci.applicant_policy_no                                                    AS applicantPolicyNo,
               epci.policy_no                                                              AS policyNo,
               ic.company_name                                                           AS companyName,
               epci.main_product_name                                                      AS mainProductName,
               eppi.prod_type_code                                                         AS prodTypeCode,
               epci.sales_platform                                                         AS salesPlatform,
               epci.admin_policy_status                                                    AS adminPolicyStatus,
               epci.self_preservation                                                      AS selfPreservation,
               epci.settlement_status                                                      AS settlementStatus,
               epci.settlement_year                                                        AS settlementYear,
               epci.settlement_month                                                       AS settlementMonth,
               epci.supervise_channel_code                                                 AS superviseChannelCode,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_name, eppi.product_name)                                   AS productName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.protocol_product_name,
                  eppi.protocol_product_name)                                              AS protocolProductName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_status,
                  eppi.product_status)                                                     AS productStatus,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.main_insurance,
                  eppi.main_insurance)                                                     AS mainInsurance,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period_type,
                  eppi.payment_period_type)                                                AS paymentPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period,
                  eppi.payment_period)                                                     AS paymentPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period_type,
                  eppi.insured_period_type)                                                AS insuredPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period,
                  eppi.insured_period)                                                     AS insuredPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.period_type,
                  eppi.period_type)                                                        AS periodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.premium, eppi.premium)                                             AS premium,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.coverage, eppi.coverage)                                           AS coverage,
               IF(epci.policy_product_type = 'PRODUCT:PRODUCTGROUP:1', eppim.surrender_amount, eppi.surrender_amount) AS surrenderAmount,
               IF(epci.sales_type = 1, epagi.main_flag, NULL)                              AS mainFlag,
               IF(epci.sales_type = 1, aui.agent_name, NULL)                               AS agentName,
               IF(epci.sales_type = 1, aui.business_code, NULL)                            AS businessCode,
               IF(epci.sales_type = 1, oi.org_name, NULL)                                  AS orgName,
               IF(epci.sales_type = 1, oi.org_code, NULL)                                  AS orgCode,
               IF(epci.sales_type = 1, epagi.commission_rate, NULL)                        AS commissionRate,
               IF(epci.referrer_type = 0, car.referrer_name, auir.agent_name)              AS referrerName,
               IF(epci.referrer_type = 0, car.referrer_wno, auir.business_code)            AS referrerWno,
               IF(epci.referrer_type = 0, cbi.branch_name, oib.org_name)                   AS channelBranchName,
               ci.channel_name                                                             AS channelName,
               aui.business_code                                                           AS policyReferrerWno,
               aui.agent_name                                                              AS policyReferrerName,
               epci.order_time                                                             AS orderTime,
               epci.applicant_time                                                         AS applicantTime,
               approved_time                                                               AS approvedTime,
               enforce_time                                                                AS enforceTime,
               receipt_sign_time                                                           AS receiptSignTime,
               is_revisit                                                                  AS isRevisit,
               revisit_time                                                                AS revisitTime,
               revisit_result                                                              AS revisitResult,
               revisit_fail_reason                                                         AS revisitFailReason,
               cancel_time                                                                 AS cancelTime,
               decline_time                                                                AS declineTime,
               postpone_approved_time                                                      AS postponeApprovedTime,
               hesitate_period                                                             AS hesitatePeriod,
               over_hesitate_period                                                        AS overHesitatePeriod,
               epci.surrender_time                                                         AS surrenderTime,
               epci.create_time                                                            AS createTime,
               approved_record_time                                                        AS approvedRecordTime,
               is_receipt                                                                  AS isReceipt,
               receipt_record_time                                                         AS receiptRecordTime,
               revisit_record_time                                                         AS revisitRecordTime,
               failure_time                                                                AS failureTime,
               termination_time                                                            AS terminationTime,
               reply_effective_time                                                        AS replyEffectiveTime,
               applicant_type                                                              AS applicantType,
               epai.applicant_name                                                         AS applicantName,
               applicant_gender                                                            AS applicantGender,
               applicant_birthday                                                          AS applicantBirthday,
               applicant_id_type                                                           AS applicantIdType,
               applicant_id_card                                                           AS applicantIdCard,
               concat(DATE_FORMAT(applicant_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(applicant_id_card_permanent = 1, '永远',
                         DATE_FORMAT(applicant_id_card_validity_end, '%Y-%m-%d')))            applicantIdCardValidityEnd,
               applicant_mobile                                                            AS applicantMobile,
               applicant_nation                                                            AS applicantNation,
               applicant_marital                                                           AS applicantMarital,
               applicant_career                                                            AS applicantCareer,
               applicant_company                                                           AS applicantCompany,
               applicant_address                                                           AS applicantAddress,
               applicant_industry_category                                                 AS applicantIndustryCategory,
               epai.applicant_age                                                                          AS applicantAge,
               epai.company_nature                                                         AS companyNature,
               epai.company_social_security_num                                            AS companySocialSecurityNum,
               epai.company_employee_num                                                   AS companyEmployeeNum,
               epai.company_contact_name                                                   AS companyContactName,
               epai.company_contact_mobile                                                 AS companyContactMobile,
               epai.legal_person_name                                                      AS legalPersonName,
               epai.legal_person_id_type                                                   AS legalPersonIdType,
               epai.legal_person_id_card                                                   AS legalPersonIdCard,
               concat(DATE_FORMAT(epai.legal_person_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(epai.legal_person_id_card_permanent = 1, '永远',
                         DATE_FORMAT(epai.legal_person_id_card_validity_end, '%Y-%m-%d'))) AS legalPersonIdCardValidityEnd,
               bank_name                                                                   AS bankName,
               card_no                                                                     AS cardNo,
               card_name                                                                   AS cardName,
               insured_type                                                                AS insuredType,
               insured_relation                                                            AS insuredRelation,
               epii.insured_name                                                           AS insuredName,
               insured_id_type                                                             AS insuredIdType,
               insured_id_card                                                             AS insuredIdCard,
               insured_gender                                                              AS insuredGender,
               insured_birthday                                                            AS insuredBirthday,
               concat(DATE_FORMAT(insured_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(insured_id_card_permanent = 1, '永远',
                         DATE_FORMAT(insured_id_card_validity_end, '%Y-%m-%d')))           AS insuredIdCardValidityEnd,
               insured_mobile                                                              AS insuredMobile,
               insured_nation                                                              AS insuredNation,
               insured_marital                                                             AS insuredMarital,
               insured_career                                                              AS insuredCareer,
               insured_company                                                             AS insuredCompany,
               insured_address                                                             AS insuredAddress,
               insured_email                                                               AS insuredEmail,
               insured_education                                                           AS insuredEducation,
               insured_policy_age                                                          AS insuredPolicyAge,
               insurance_subject_matter                                                    AS insuranceSubjectMatter,
               vehicle_owner_name                                                          AS vehicleOwnerName,
               vehicle_owner_mobile                                                        AS vehicleOwnerMobile,
               vehicle_owner_card_type                                                     AS vehicleOwnerCardType,
               vehicle_owner_nature                                                        AS vehicleOwnerNature,
               vehicle_owner_card_no                                                       AS vehicleOwnerCardNo,
               vehicle_owner_gender                                                        AS vehicleOwnerGender,
               vehicle_owner_birthday                                                      AS vehicleOwnerBirthday,
               vehicle_license_plate_number                                                AS vehicleLicensePlateNumber,
               vehicle_initial_registry_date                                               AS vehicleInitialRegistryDate,
               vehicle_usage                                                               AS vehicleUsage,
               vehicle_brand_name                                                          AS vehicleBrandName,
               vehicle_type                                                                AS vehicleType,
               vehicle_frame_number                                                        AS vehicleFrameNumber,
               vehicle_engine_number                                                       AS vehicleEngineNumber,
               vehicle_variety                                                             AS vehicleVariety,
               vehicle_size                                                                AS vehicleSize,
               vehicle_tonnage                                                             AS vehicleTonnage,
               vehicle_volume                                                              AS vehicleVolume,
               vehicle_power                                                               AS vehiclePower,
               vehicle_travel_miles                                                        AS vehicleTravelMiles,
               vehicle_price                                                               AS vehiclePrice,
               vehicle_paid_times                                                          AS vehiclePaidTimes,
               vehicle_is_imported                                                         AS vehicleIsImported,
               vehicle_new                                                                 AS vehicleNew,
               vehicle_transfer_ownership                                                  AS vehicleTransferOwnership,
               cdi.channel_name                                                            As channelDistributionName,
               CASE epci.offline_product_sign_status WHEN 0 THEN '否'WHEN 1 THEN '是' ELSE '' END AS offlineProductSignStatusName
        FROM ep_policy_contract_info epci
                 LEFT JOIN channel_info ci ON epci.channel_code = ci.channel_code
                 LEFT JOIN ep_policy_contract_extend epce ON epce.contract_code = epci.contract_code
                 LEFT JOIN channel_application_referrer car ON epci.referrer_code = car.referrer_code
                 LEFT JOIN agent_user_info auir ON epci.referrer_code = auir.agent_code
                 LEFT JOIN org_info oib ON epci.channel_branch_code = oib.org_code
                 LEFT JOIN channel_branch_info cbi ON epci.channel_branch_code = cbi.branch_code
                 LEFT JOIN ep_policy_vehicle_info eppvi ON eppvi.contract_code = epci.contract_code
                 LEFT JOIN ep_policy_agent_info epagi ON epagi.contract_code = epci.contract_code
            AND epagi.deleted = 0
                 LEFT JOIN agent_user_info aui ON epagi.agent_code = aui.agent_code
                 LEFT JOIN org_info oi ON epagi.org_code = oi.org_code
                 LEFT JOIN ep_policy_applicant_info epai ON epci.contract_code = epai.contract_code
                 LEFT JOIN ep_policy_product_info eppi ON eppi.contract_code = epci.contract_code
            AND eppi.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:3', 'PRODUCT:PRODUCTGROUP:4')
                 LEFT JOIN ep_policy_insured_info epii ON epii.contract_code = epci.contract_code
            AND epii.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2')
                 LEFT JOIN ep_policy_product_insured_map eppim ON eppim.insured_code = epii.insured_code
            AND eppim.deleted = 0
                LEFT JOIN channel_distribution_info cdi on epci.channel_distribution_code = cdi.channel_code
                LEFT JOIN insurance_company ic on ic.company_code = epci.company_code
            ${ew.customSqlSegment}
    </select>


    <select id="policyList" resultType="com.mpolicy.manage.modules.policy.vo.PolicyInfoVo">
        select epci.policy_no,
               epci.policy_product_type,
               epci.applicant_policy_no,
               epci.policy_status,
               epci.channel_code,
               epci.portfolio_name,
               epci.premium_total,
               epci.order_time,
               epci.enforce_time,
               epci.company_name,
               epci.policy_source,
               epci.company_code,
               epci.applicant_name,
               epci.policy_referrer_code,
               epci.channel_branch_code,
               epci.policy_type,
               epai.agent_code,
               epai.agent_name,
               epii.insured_name
        from ep_policy_contract_info epci
                 left join ep_policy_agent_info epai on epci.contract_code = epai.contract_code and epai.main_flag = '1'
                 left join ep_policy_insured_info epii
                           on epci.contract_code = epii.contract_code and epii.main_flag = '0'
                               ${ew.customSqlSegment}
    </select>

    <select id="queryEpV2PolicyExportVo" resultType="com.mpolicy.manage.modules.policy.vo.EpV2PolicyExportVo">
        SELECT epci.id                                                                     AS id,
               epci.policy_type                                                            AS policyType,
               epci.policy_product_type                                                    AS policyProductType,
               epci.sales_type                                                             AS salesType,
               epci.applicant_policy_no                                                    AS applicantPolicyNo,
               epci.policy_no                                                              AS policyNo,
               epci.source_policy_no                                                       AS sourcePolicyNo,
               ic.company_name                                                           AS companyName,
               epci.main_product_name                                                      AS mainProductName,
               eppi.prod_type_code                                                         AS prodTypeCode,
               epci.sales_platform                                                         AS salesPlatform,
               epci.admin_policy_status                                                    AS adminPolicyStatus,
               epci.self_preservation                                                      AS selfPreservation,
               epci.settlement_status                                                      AS settlementStatus,
               epci.settlement_year                                                        AS settlementYear,
               epci.settlement_month                                                       AS settlementMonth,
               epci.supervise_channel_code                                                 AS superviseChannelCode,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_name, eppi.product_name)                                   AS productName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.protocol_product_name,
                  eppi.protocol_product_name)                                              AS protocolProductName,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.product_status,
                  eppi.product_status)                                                     AS productStatus,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.main_insurance,
                  eppi.main_insurance)                                                     AS mainInsurance,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period_type,
                  eppi.payment_period_type)                                                AS paymentPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.payment_period,
                  eppi.payment_period)                                                     AS paymentPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period_type,
                  eppi.insured_period_type)                                                AS insuredPeriodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.insured_period,
                  eppi.insured_period)                                                     AS insuredPeriod,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.period_type,
                  eppi.period_type)                                                        AS periodType,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.premium, eppi.premium)                                             AS premium,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
                  eppim.coverage, eppi.coverage)                                           AS coverage,
               IF(epci.policy_product_type = 'PRODUCT:PRODUCTGROUP:1', eppim.surrender_amount, eppi.surrender_amount) AS surrenderAmount,
               IF(epci.sales_type = 1, epagi.main_flag, NULL)                              AS mainFlag,
               IF(epci.sales_type = 1, aui.agent_name, NULL)                               AS agentName,
               IF(epci.sales_type = 1, aui.business_code, NULL)                            AS businessCode,
               IF(epci.sales_type = 1, oi.org_name, NULL)                                  AS orgName,
               IF(epci.sales_type = 1, oi.org_code, NULL)                                  AS orgCode,
               IF(epci.sales_type = 1, epagi.commission_rate, NULL)                        AS commissionRate,
               IF(epci.referrer_type = 0, car.referrer_name, auir.agent_name)              AS referrerName,
               IF(epci.referrer_type = 0, car.referrer_wno, auir.business_code)            AS referrerWno,
                epci.customer_manager_code                                                 AS customerManagerCode,
               epci.customer_manager_channel_code                                          AS customerManagerChannelCode,
               IF(epci.referrer_type = 0, cbi.branch_name, oib.org_name)                   AS channelBranchName,
               ci.channel_name                                                             AS channelName,
               aui.business_code                                                           AS policyReferrerWno,
               aui.agent_name                                                              AS policyReferrerName,
               epci.order_time                                                             AS orderTime,
               epci.applicant_time                                                         AS applicantTime,
               approved_time                                                               AS approvedTime,
               enforce_time                                                                AS enforceTime,
               receipt_sign_time                                                           AS receiptSignTime,
               is_revisit                                                                  AS isRevisit,
               revisit_time                                                                AS revisitTime,
               revisit_result                                                              AS revisitResult,
               revisit_fail_reason                                                         AS revisitFailReason,
               cancel_time                                                                 AS cancelTime,
               decline_time                                                                AS declineTime,
               postpone_approved_time                                                      AS postponeApprovedTime,
               hesitate_period                                                             AS hesitatePeriod,
               over_hesitate_period                                                        AS overHesitatePeriod,
               epci.surrender_time                                                         AS surrenderTime,
               epci.create_time                                                            AS createTime,
               approved_record_time                                                        AS approvedRecordTime,
               is_receipt                                                                  AS isReceipt,
               receipt_record_time                                                         AS receiptRecordTime,
               revisit_record_time                                                         AS revisitRecordTime,
               epci.insured_period_end_time                                                AS failureTime,
               termination_time                                                            AS terminationTime,
               reply_effective_time                                                        AS replyEffectiveTime,
               applicant_type                                                              AS applicantType,
               epci.applicant_name                                                         AS applicantName,
               applicant_gender                                                            AS applicantGender,
               applicant_birthday                                                          AS applicantBirthday,
               applicant_id_type                                                           AS applicantIdType,
               applicant_id_card                                                           AS applicantIdCard,
               concat(DATE_FORMAT(applicant_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(applicant_id_card_permanent = 1, '永远',
                         DATE_FORMAT(applicant_id_card_validity_end, '%Y-%m-%d')))            applicantIdCardValidityEnd,
               applicant_mobile                                                            AS applicantMobile,
               applicant_nation                                                            AS applicantNation,
               applicant_marital                                                           AS applicantMarital,
               applicant_career                                                            AS applicantCareer,
               applicant_company                                                           AS applicantCompany,
               applicant_address                                                           AS applicantAddress,
               applicant_industry_category                                                 AS applicantIndustryCategory,
               epai.applicant_age                                                                          AS applicantAge,
               epai.company_nature                                                         AS companyNature,
               epai.company_social_security_num                                            AS companySocialSecurityNum,
               epai.company_employee_num                                                   AS companyEmployeeNum,
               epai.company_contact_name                                                   AS companyContactName,
               epai.company_contact_mobile                                                 AS companyContactMobile,
               epai.legal_person_name                                                      AS legalPersonName,
               epai.legal_person_id_type                                                   AS legalPersonIdType,
               epai.legal_person_id_card                                                   AS legalPersonIdCard,
               concat(DATE_FORMAT(epai.legal_person_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(epai.legal_person_id_card_permanent = 1, '永远',
                         DATE_FORMAT(epai.legal_person_id_card_validity_end, '%Y-%m-%d'))) AS legalPersonIdCardValidityEnd,
               bank_name                                                                   AS bankName,
               card_no                                                                     AS cardNo,
               card_name                                                                   AS cardName,
               insured_type                                                                AS insuredType,
               insured_relation                                                            AS insuredRelation,
               epii.insured_name                                                           AS insuredName,
               insured_id_type                                                             AS insuredIdType,
               insured_id_card                                                             AS insuredIdCard,
               insured_gender                                                              AS insuredGender,
               insured_birthday                                                            AS insuredBirthday,
               concat(DATE_FORMAT(insured_id_card_validity_start, '%Y-%m-%d'), '至',
                      IF(insured_id_card_permanent = 1, '永远',
                         DATE_FORMAT(insured_id_card_validity_end, '%Y-%m-%d')))           AS insuredIdCardValidityEnd,
               insured_mobile                                                              AS insuredMobile,
               insured_nation                                                              AS insuredNation,
               insured_marital                                                             AS insuredMarital,
               insured_career                                                              AS insuredCareer,
               insured_company                                                             AS insuredCompany,
               insured_address                                                             AS insuredAddress,
               insured_email                                                               AS insuredEmail,
               insured_education                                                           AS insuredEducation,
               insured_policy_age                                                          AS insuredPolicyAge,
               insurance_subject_matter                                                    AS insuranceSubjectMatter,
               vehicle_owner_name                                                          AS vehicleOwnerName,
               vehicle_owner_mobile                                                        AS vehicleOwnerMobile,
               vehicle_owner_card_type                                                     AS vehicleOwnerCardType,
               vehicle_owner_nature                                                        AS vehicleOwnerNature,
               vehicle_owner_card_no                                                       AS vehicleOwnerCardNo,
               vehicle_owner_gender                                                        AS vehicleOwnerGender,
               vehicle_owner_birthday                                                      AS vehicleOwnerBirthday,
               vehicle_license_plate_number                                                AS vehicleLicensePlateNumber,
               vehicle_initial_registry_date                                               AS vehicleInitialRegistryDate,
               vehicle_usage                                                               AS vehicleUsage,
               vehicle_brand_name                                                          AS vehicleBrandName,
               vehicle_type                                                                AS vehicleType,
               vehicle_frame_number                                                        AS vehicleFrameNumber,
               vehicle_engine_number                                                       AS vehicleEngineNumber,
               vehicle_variety                                                             AS vehicleVariety,
               vehicle_size                                                                AS vehicleSize,
               vehicle_tonnage                                                             AS vehicleTonnage,
               vehicle_volume                                                              AS vehicleVolume,
               vehicle_power                                                               AS vehiclePower,
               vehicle_travel_miles                                                        AS vehicleTravelMiles,
               vehicle_price                                                               AS vehiclePrice,
               vehicle_paid_times                                                          AS vehiclePaidTimes,
               vehicle_is_imported                                                         AS vehicleIsImported,
               vehicle_new                                                                 AS vehicleNew,
               vehicle_transfer_ownership                                                  AS vehicleTransferOwnership,
               cdi.channel_name                                                            As channelDistributionName,
               CASE epci.offline_product_sign_status WHEN 0 THEN '否'WHEN 1 THEN '是' ELSE '' END AS offlineProductSignStatusName,
               ic.company_code                                                             AS companyCode,
               IF(epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2'),
               eppim.product_code, eppi.product_code)                                      AS productCode
        FROM ep_policy_contract_info epci
                 LEFT JOIN channel_info ci ON epci.channel_code = ci.channel_code
                 LEFT JOIN ep_policy_contract_extend epce ON epce.contract_code = epci.contract_code
                 LEFT JOIN channel_application_referrer car ON epci.referrer_code = car.referrer_code
                 LEFT JOIN agent_user_info auir ON epci.referrer_code = auir.agent_code
                 LEFT JOIN org_info oib ON epci.channel_branch_code = oib.org_code
                 LEFT JOIN channel_branch_info cbi ON epci.channel_branch_code = cbi.branch_code
                 LEFT JOIN ep_policy_vehicle_info eppvi ON eppvi.contract_code = epci.contract_code
                 LEFT JOIN ep_policy_agent_info epagi ON epagi.contract_code = epci.contract_code
            AND epagi.deleted = 0
                 LEFT JOIN agent_user_info aui ON epagi.agent_code = aui.agent_code
                 LEFT JOIN org_info oi ON epagi.org_code = oi.org_code
                 LEFT JOIN ep_policy_applicant_info epai ON epci.contract_code = epai.contract_code
                 LEFT JOIN ep_policy_product_info eppi ON eppi.contract_code = epci.contract_code
            AND eppi.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:3', 'PRODUCT:PRODUCTGROUP:4')
                 LEFT JOIN ep_policy_insured_info epii ON epii.contract_code = epci.contract_code
            AND epii.deleted = 0 AND epci.policy_product_type in ('PRODUCT:PRODUCTGROUP:1', 'PRODUCT:PRODUCTGROUP:2')
                 LEFT JOIN ep_policy_product_insured_map eppim ON eppim.insured_code = epii.insured_code
            AND eppim.deleted = 0
                 LEFT JOIN channel_distribution_info cdi on epci.channel_distribution_code = cdi.channel_code
                 LEFT JOIN insurance_company ic on ic.company_code = epci.company_code
        WHERE
            epci.id
        IN
        <foreach collection="ids" index="index" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </select>

    <select id="queryEpV2PolicyExportId" resultType="java.lang.Integer">
       SELECT ID FROM EP_POLICY_CONTRACT_INFO ${ew.customSqlSegment}
    </select>

    <select id="queryRiskChannels" resultType="com.mpolicy.manage.modules.sys.excel.policy.upload.PolicyRiskChannelExecel">
        select
            b.protocol_flag,
            b.product_channel,
            count(*) as sumCount,
            sum(a.premium_total) as premiumSum,
            c.company_name,
            d.channel_name
        from
            ep_policy_contract_info a
        left join
            insurance_product_info b on a.main_product_code = b.product_code
        left join
            insurance_company c on b.product_channel =c.company_code
        left join
            channel_distribution_info d on b.product_channel =d.channel_code
        where
            a.id
        IN
        <foreach collection="ids" index="index" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
        group by
        protocol_flag,
        b.product_channel
    </select>
</mapper>
