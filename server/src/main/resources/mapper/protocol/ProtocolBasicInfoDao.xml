<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mpolicy.manage.modules.protocol.dao.ProtocolBasicInfoDao">
    <!--获取满足条件的协议列表-->
    <select id="findXjReconcileProtocolList"
            resultType="com.mpolicy.manage.modules.settlement.entity.XjReconcileProtocolOut">
        SELECT epbi.external_signatory_type,
               epbi.external_signatory_name,
               epbi.protocol_code,
               epbi.settlement_period_type,
               epbi.settlement_underwriting_start_dimension,
               epbi.settlement_underwriting_start_day,
               epbi.settlement_underwriting_end_dimension,
               epbi.settlement_underwriting_end_day,
               epbi.settlement_receipt_dimension,
               epbi.settlement_receipt_day,
               epbi.settlement_revisit_dimension,
               epbi.settlement_revisit_day,
               epc.company_type,
               epc.company_short_name,
               epc.company_code
        FROM ep_protocol_basic_info epbi
                 LEFT JOIN ep_protocol_company epc ON epbi.protocol_code = epc.protocol_code
        WHERE protocol_type = 'PRODUCT:PROTOCOL_TYPE:1'
          AND #{dateTime,jdbcType=TIMESTAMP} BETWEEN epbi.start_date AND epbi.end_date
        ORDER BY epbi.id
    </select>

    <!--分页获取协议列表-->
    <select id="findProtocolBasicInfoList" resultType="com.mpolicy.manage.modules.protocol.vo.ProtocolBasicInfoListOut">
        SELECT epbi.id,
               epbi.protocol_code,
               epbi.protocol_name,
               epbi.protocol_type,
               epbi.protocol_status,
               epbi.inner_signatory_name,
               epbi.external_signatory_name,
               epbi.start_date,
               epbi.end_date,
               epbi.final_end_date,
               epbi.auto_delay_flag,
               epbi.product_prem_flag,
               epbi.product_prem_file_path
        FROM ep_protocol_basic_info epbi
                 LEFT JOIN ep_protocol_company epc on epbi.protocol_code = epc.protocol_code and epc.deleted = 0
                 LEFT JOIN ep_protocol_product_prem eppp on epbi.protocol_code = eppp.protocol_code and eppp.deleted = 0
        where epbi.deleted = 0
        <if test="input.innerSignatoryCode != null and input.innerSignatoryCode != ''">
            and epbi.inner_signatory_code = #{input.innerSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolStatus != null and input.protocolStatus != ''">
            and epbi.protocol_status = #{input.protocolStatus,jdbcType=VARCHAR}
        </if>
        <if test="input.externalSignatoryCode != null and input.externalSignatoryCode != ''">
            and epbi.external_signatory_code = #{input.externalSignatoryCode,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolCode != null and input.protocolCode != ''">
            and epbi.protocol_code = #{input.protocolCode,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolName != null and input.protocolName != ''">
            and epbi.protocol_name like concat('%', #{input.protocolName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="input.insuranceProductName != null and input.insuranceProductName != ''">
            and eppp.insurance_product_name like concat('%', #{input.insuranceProductName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="input.insuranceProductCode != null and input.insuranceProductCode != ''">
            and eppp.insurance_product_code = #{input.insuranceProductCode,jdbcType=VARCHAR}
        </if>
        <if test="input.externalSignatoryType != null and input.externalSignatoryType != ''">
            and epbi.external_signatory_type = #{input.externalSignatoryType,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolType != null and input.protocolType != ''">
            and epbi.protocol_type = #{input.protocolType,jdbcType=VARCHAR}
        </if>
        <if test="input.startDate != null and input.startDate != ''">
            and epbi.start_date &gt;= #{input.startDate,jdbcType=VARCHAR}
        </if>
        <if test="input.endDate != null and input.endDate != ''">
            and epbi.end_date &lt;= #{input.endDate,jdbcType=VARCHAR}
        </if>
        <if test="input.finalEndDate != null and input.finalEndDate != ''">
            and epbi.final_end_date &lt;= #{input.finalEndDate,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolCompanyCode != null and input.protocolCompanyCode != ''">
            and epc.company_code = #{input.protocolCompanyCode,jdbcType=VARCHAR}
        </if>
        <if test="input.orgCode != null and input.orgCode != ''">
            and eppp.org_code = #{input.orgCode,jdbcType=VARCHAR}
        </if>
        <if test="input.premCode != null and input.premCode != ''">
            and eppp.prem_code = #{input.premCode,jdbcType=VARCHAR}
        </if>
        <if test="input.protocolCompanyCodeList != null and input.protocolCompanyCodeList.size() > 0">
            and epc.company_code in
            <foreach collection="input.protocolCompanyCodeList" item="protocolCompanyCode" open="(" close=")"
                     separator=",">
                #{protocolCompanyCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by epbi.id, epbi.start_date
        order by epbi.start_date desc
    </select>
</mapper>
